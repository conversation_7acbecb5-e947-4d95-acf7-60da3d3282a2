import matplotlib.pyplot as plt
import numpy as np
import matplotlib
from matplotlib import rcParams

# 字体配置（仅保留英文字体）
config = {
    "font.family": 'serif',
    "font.size": 35,
    "font.serif": ['Times New Roman'],  # 统一使用衬线字体
    "axes.unicode_minus": False
}

matplotlib.rcParams.update(config)

# 数据保持不变
prefix_lengths = ['/8', '/12', '/16', '/20', '/24', '/28']
build_times = [1.098, 1.366, 1.680, 1.986, 2.289, 3.495]
search_times = [1.051, 1.440, 1.884, 2.399, 2.577, 2.984]

x = np.arange(len(prefix_lengths))
width = 0.35

# 创建合并图表
plt.figure(figsize=(16, 10))

# 修改标签为英文
plt.bar(x - width/2, build_times, width, label='Building Time')
plt.bar(x + width/2, search_times, width, label='Search Time', color='orange')

# 设置英文标签
plt.xlabel('IP Prefix Length')
plt.ylabel('Time ($\mu$s)')  # 使用 LaTeX 的微秒符号

plt.xticks(x, prefix_lengths)
plt.legend()
plt.grid(True, axis='y')
#plt.savefig('F://fig11.png', bbox_inches='tight')
plt.show()
plt.close()
'''
import matplotlib.pyplot as plt
import numpy as np
import matplotlib
from matplotlib import rcParams

# 配置中文字体（宋体）和字号
config = {
    "font.family": 'serif',          # 主字体类型
    "font.size": 35,                 # 保持原字号大小
    "font.serif": ['SimSun', 'Times New Roman'],  # 优先用宋体，英文字体用Times New Roman
    "axes.unicode_minus": False      # 解决负号显示问题
}

matplotlib.rcParams.update(config)

# 数据保持不变
prefix_lengths = ['/8', '/12', '/16', '/20', '/24', '/28']
build_times = [1.098, 1.366, 1.680, 1.986, 2.289, 3.495]
search_times = [1.051, 1.440, 1.884, 2.399, 2.577, 2.984]

x = np.arange(len(prefix_lengths))
width = 0.35

# 创建图表（保持原尺寸）
plt.figure(figsize=(16, 10))

# 绘制双柱状图（修改标签为中文）
plt.bar(x - width/2, build_times, width, label='构建时间')
plt.bar(x + width/2, search_times, width, label='查找时间', color='orange')

# 设置中文标签
plt.xlabel('IP前缀长度')
plt.ylabel('时间（$\mu$s）')  # 保留微秒符号

plt.xticks(x, prefix_lengths)
plt.legend()  # 自动识别中文图例
plt.grid(True, axis='y')  # 保持网格线

# 保存或显示图表
# plt.savefig('F://fig11.png', bbox_inches='tight')
plt.show()
plt.close()'''