from matplotlib import rcParams
import matplotlib.pyplot as plt
import numpy as np
import matplotlib
matplotlib.use('TkAgg') 

# 全局字体配置
config = {
    "font.family": "sans-serif",
    "font.sans-serif": ["Times New Roman"],  # 默认英文字体
    "font.size": 35,
    #"font.serif": ["SimSun"],  # 显式指定中文字体
}
rcParams.update(config)

plt.figure(figsize=(16, 8))
# 你的数据
data = {
    "trie_group_1.dat": {13: 32, 14: 78, 15: 148, 16: 2046, 17: 751, 18: 1489, 19: 2812, 20: 4451, 21: 4506, 22: 10439, 23: 5991, 24: 38756, 25: 9, 26: 7, 27: 14, 28: 37, 29: 25},
    "trie_group_2.dat": {13: 32, 14: 78, 15: 149, 16: 2047, 17: 755, 18: 1492, 19: 2819, 20: 4461, 21: 4474, 22: 10425, 23: 5977, 24: 38607, 25: 11, 26: 13, 27: 14, 28: 37, 29: 25},
    "trie_group_3.dat": {13: 32, 14: 78, 15: 149, 16: 2052, 17: 759, 18: 1502, 19: 2841, 20: 4458, 21: 4335, 22: 10452, 23: 5911, 24: 38378, 25: 16, 26: 9, 27: 14, 28: 37, 29: 25},
    "trie_group_4.dat": {13: 32, 14: 78, 15: 150, 16: 2051, 17: 765, 18: 1508, 19: 2835, 20: 4457, 21: 4371, 22: 10422, 23: 5909, 24: 38375, 25: 8, 26: 7, 27: 14, 28: 37, 29: 25},
    "trie_group_5.dat": {13: 32, 14: 78, 15: 152, 16: 2054, 17: 766, 18: 1520, 19: 2810, 20: 4449, 21: 4311, 22: 10362, 23: 5874, 24: 38087, 25: 8, 26: 7, 27: 14, 28: 37, 29: 25},
    "trie_group_6.dat": {13: 32, 14: 78, 15: 150, 16: 2051, 17: 773, 18: 1519, 19: 2816, 20: 4445, 21: 4322, 22: 10425, 23: 5909, 24: 38547, 25: 8, 26: 7, 27: 12, 28: 37, 29: 25}
}

# 日期映射
date_ranges = {
    "trie_group_1.dat": "2023-01-01 to 2023-01-05",
    "trie_group_2.dat": "2023-01-06 to 2023-01-10",
    "trie_group_3.dat": "2023-01-11 to 2023-01-15",
    "trie_group_4.dat": "2023-01-16 to 2023-01-20",
    "trie_group_5.dat": "2023-01-21 to 2023-01-25",
    "trie_group_6.dat": "2023-01-26 to 2023-01-30"
}

# 选择13到29的前缀长度
selected_prefixes = list(range(13, 30))

# 设置 x 轴
x = np.arange(len(selected_prefixes))

# 绘图
bar_width = 0.12
colors = ['b', 'g', 'r', 'c', 'm', 'y']
fig, ax = plt.subplots(figsize=(10, 6))

# 画柱状图
for i, (group, group_data) in enumerate(data.items()):
    values = [group_data.get(prefix, 0) for prefix in selected_prefixes]
    ax.bar(x + i * bar_width, values, bar_width, label=date_ranges[group], color=colors[i])

# 设置标签
ax.set_xlabel('IP Prefix Length')
ax.set_ylabel('Count')
ax.set_xticks(x + bar_width * (len(data) - 1) / 2)
ax.set_xticklabels([f'/{prefix}' for prefix in selected_prefixes])

ax.set_yticks([10000, 20000, 30000, 40000])
ax.set_yticklabels(['10k', '20k', '30k', '40k'])
ax.legend(loc='upper left',fontsize=30)

# 添加网格
ax.grid(True, axis='both', linestyle='--', linewidth=0.5)

plt.tight_layout()
plt.show()
plt.close()

