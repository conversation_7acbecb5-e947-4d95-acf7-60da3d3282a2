import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib import rcParams

# ================== 数据转换（确定性 → 不确定性） ==================
categories = ["1", "2", "3", "4", "5", "6"]

# 原数据（确定性）
stable_po = [0.7255098794, 0.7357693488, 0.7575993041, 0.7667383554, 0.7899938783, 0.7901102130]
unstable_po = [0.3736736650, 0.3042109571, 0.2998523911, 0.299846440, 0.2998988440, 0.2999331390]
stable_po_2 = [0.8713142814, 0.8729130762, 0.8977288168, 0.8977246839, 0.8966275471, 0.8963922746]

# 转换为不确定性：1 - 确定性
stable_po_uncertainty = [1 - x for x in stable_po]
unstable_po_uncertainty = [1 - x for x in unstable_po]
stable_po_2_uncertainty = [1 - x for x in stable_po_2]

# ================== 全局样式设置 ==================
font_path = 'C:/Windows/Fonts/times.ttc'  # 确认字体路径正确
font_size = 35

rcParams.update({
    "font.family": "sans-serif",
    "font.sans-serif": ["Times New Roman"],  # 默认英文字体
    'font.size': font_size,
    'axes.titlesize': font_size,
    'axes.labelsize': font_size,
    'xtick.labelsize': font_size,
    'ytick.labelsize': font_size,
    'legend.fontsize': font_size
})

# ================== 绘图设置 ==================
plt.figure(figsize=(16, 8))
line_config = {
    'linewidth': 2.2,
    'markersize': 8,
    'markeredgewidth': 1.5
}

# 使用转换后的数据绘图
plt.plot(categories, unstable_po_uncertainty, 
         marker='s', 
         color='#d62728', 
         label='Invalid P/O Pairs',
         **line_config)

plt.plot(categories, stable_po_uncertainty, 
         marker='o', 
         color='#1f77b4', 
         label='New Valid P/O Pairs',
         **line_config)
 
plt.plot(categories, stable_po_2_uncertainty, 
         marker='D', 
         color='#2ca02c', 
         label='Stable P/O Pairs',
         **line_config)

# 图例与标签
plt.legend(
    loc='upper right',
    bbox_to_anchor=(1, 0.8),
    edgecolor='#333333',
    framealpha=0.9
)
plt.xlabel("Number of Sliding Window")
plt.ylabel("Uncertainty Degree")

# 网格与布局
plt.grid(True, linestyle='--', alpha=0.4, color='gray')
plt.subplots_adjust(right=0.85)

#plt.savefig('F://fig9.png', bbox_inches='tight')
plt.show()
plt.close()

# ================== 第二个图表开始 ==================
import matplotlib.pyplot as plt
import numpy as np
import matplotlib as mpl
import matplotlib.ticker as ticker  # 新增导入

# ================== 全局样式设置 ==================
mpl.rcParams.update({
    'font.family': 'Times New Roman',
    'font.size': 25,
    'axes.titlesize': 25,
    'axes.labelsize': 25,
    'xtick.labelsize': 20,
    'ytick.labelsize': 20,
    'figure.figsize': (15, 5)
})

# ================== 数据准备 ==================
months = ['7', '8', '9', '10', '11', '12']

datasets = {
    "Time Persistence": {
        "Ex": [0.8004, 0.8005, 0.8001, 0.7996, 0.7991, 0.8120],
        "En": [0.2376, 0.2373, 0.2375, 0.2374, 0.2375, 0.2308],
        "He": [0.0998, 0.0995, 0.0995, 0.0993, 0.0993, 0.0990]
    },
    "Spatial Consistency": {
        "Ex": [1.5278, 1.5307, 1.5027, 1.5075, 1.5117, 1.4665],
        "En": [1.8489, 1.8513, 1.8104, 1.8116, 1.8125, 1.8073],
        "He": [0.8249, 0.8251, 0.8050, 0.8054, 0.8052, 0.8119]
    },
    "Data Source": {
        "Ex": [0.6986, 0.6989, 0.6991, 0.6993, 0.6995, 0.7245],
        "En": [0.0727, 0.0729, 0.0732, 0.0736, 0.0737, 0.0932],
        "He": [0.0312, 0.0311, 0.0311, 0.0311, 0.0311, 0.0407]
    }
}

all_values = []
for category in datasets.values():
    for param in ['Ex', 'En', 'He']:
        all_values.extend(category[param])
y_min = min(all_values) * 0.95
y_max = max(all_values) * 1.05

# ================== 可视化函数 ==================
PARAM_LABELS = {"Ex": "Ex", "En": "En", "He": "He"}

def plot_parameter(parameter, data_dict, ax):
    markers = ['o', 's', 'D']
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
    
    for idx, (metric, values) in enumerate(data_dict.items()):
        ax.plot(months, values,
                marker=markers[idx],
                markersize=8,
                linewidth=2.5,
                color=colors[idx],
                label=metric)
    
    ax.set_ylim(y_min, y_max)
    ax.set_xlabel("Month", labelpad=12)
    ax.set_ylabel(PARAM_LABELS[parameter], rotation=360, labelpad=15)
    ax.set_xticks(range(len(months)))
    ax.set_xticklabels(months, ha='right')
    ax.yaxis.label.set_family('Times New Roman')
    ax.yaxis.label.set_style('italic')
    
    ax.grid(
        True,
        linestyle=(0, (5, 3)),
        linewidth=0.8,
        color='#999999',
        alpha=0.6
    )

# ================== 生成图表 ==================
fig, axes = plt.subplots(1, 3, figsize=(15, 5))

for ax in axes:
    ax.set_box_aspect(1)

plot_parameter("Ex", {
    "Time Persistence": datasets["Time Persistence"]["Ex"],
    "Spatial Consistency": datasets["Spatial Consistency"]["Ex"],
    "Data Source": datasets["Data Source"]["Ex"]
}, axes[0])

plot_parameter("En", {
    "Time Persistence": datasets["Time Persistence"]["En"],
    "Spatial Consistency": datasets["Spatial Consistency"]["En"], 
    "Data Source": datasets["Data Source"]["En"]
}, axes[1])

plot_parameter("He", {
    "Time Persistence": datasets["Time Persistence"]["He"],
    "Spatial Consistency": datasets["Spatial Consistency"]["He"],
    "Data Source": datasets["Data Source"]["He"]
}, axes[2])

axes[2].set_ylim(0, 1)  # 单独设置第三个子图的Y轴范围

# 统一设置Y轴刻度数量（新增代码）
for ax in axes:
    ax.yaxis.set_major_locator(ticker.MaxNLocator(nbins=5))  # 控制主刻度数量为5

# ================== 图例设置 ==================
legend = fig.legend(axes[0].get_lines(),
                  ['Time Persistence', 'Spatial Consistency', 'Data Source'],
                  loc='upper center',
                  bbox_to_anchor=(0.5,1),
                  ncol=3,
                  frameon=True,
                  edgecolor='#333333',
                  fontsize=20)

# ================== 布局调整 ==================
plt.tight_layout(pad=0.3, w_pad=1)
plt.subplots_adjust(top=0.85)

# ================== 保存图片 ==================
import os
output_dir = "/data/代码数据"
os.makedirs(output_dir, exist_ok=True)
output_path = os.path.join(output_dir, "fig9.png")
plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
print(f"图片已保存到: {output_path}")

plt.show()
plt.close()