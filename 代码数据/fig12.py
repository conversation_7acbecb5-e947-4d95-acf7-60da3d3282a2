# import matplotlib.pyplot as plt
# import numpy as np
# import matplotlib as mpl

# # ================== 字体设置 ==================
# mpl.rcParams.update({
#     'font.family': 'serif',
#     'font.serif': 'Times New Roman',
#     'axes.labelsize': 30,
#     'xtick.labelsize': 30,
#     'ytick.labelsize': 30,
#     'legend.fontsize': 25
# })

# # ================== 数据准备 ==================
# dates = np.arange(15, 32)
# methods = {
#     "CloudTrie": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 3],
#     "Artemis": [5, 3, 2, 4, 1, 12, 2, 4, 5, 8, 4, 5, 9, 6, 5, 5, 5],
#     "BEAM": [0, 1, 1, 13, 1, 3, 9, 4, 3, 4, 1, 3, 6, 4, 2, 11, 1],
#     "BGPviewer": [19, 3, 13, 10, 14, 19, 7, 0, 7, 0, 0, 0, 5, 2, 2, 0, 0],
#     "BGPvector": [1, 4, 19, 10, 33, 4, 3, 5, 4, 8, 8, 7, 51, 36, 5, 11, 50]
# }

# # ================== 可视化参数 ==================
# bar_width = 1.0
# num_methods = len(methods)
# group_width = bar_width * num_methods
# day_spacing = group_width + bar_width  # 组间留一个柱宽的空隙

# COLOR_PALETTE = ['#C44E52', '#DD8452', '#55A868', '#4C72B0', '#8172B2']

# # ================== 创建图表 ==================
# fig, ax = plt.subplots(figsize=(20, 8))

# # 计算每个日期的x位置
# x = np.arange(len(dates)) * day_spacing

# # 绘制每组方法的柱子
# for i, (method, data) in enumerate(methods.items()):
#     offset = i * bar_width
#     ax.bar(x + offset, data, width=bar_width, color=COLOR_PALETTE[i],
#            edgecolor='black', linewidth=0.8, label=method)

# # 设置坐标轴
# ax.set_xlabel('Date (Day of Dec 2024)', labelpad=10)
# ax.set_ylabel('Number of Detected Anomalies', labelpad=12)
# ax.set_xticks(x + group_width / 2)
# ax.set_xticklabels([str(d) for d in dates])
# ax.set_xlim(-0.5, x[-1] + group_width + 0.5)

# # 图例
# ax.legend(loc='upper left', bbox_to_anchor=(0.02, 1), ncol=2,
#           framealpha=0.9, edgecolor='#333333', columnspacing=1.2, handletextpad=0.4)

# # 网格
# ax.grid(axis='y', linestyle=(0, (5, 2)), alpha=0.4, color='#999999')

# plt.tight_layout()
# plt.savefig("fig12.png", bbox_inches='tight')

import matplotlib.pyplot as plt
import numpy as np
import matplotlib as mpl

# ================== 字体设置 ==================
mpl.rcParams.update({
    'font.family': 'serif',
    'font.serif': 'Times New Roman',
    'axes.labelsize': 30,
    'xtick.labelsize': 30,
    'ytick.labelsize': 30,
    'legend.fontsize': 25
})

# ================== 数据准备 ==================
dates = np.arange(15, 32)
methods = {
    "CloudTrie": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 3],
    "Artemis": [5, 3, 2, 4, 1, 12, 2, 4, 5, 8, 4, 5, 9, 6, 5, 5, 5],
    "BEAM": [0, 1, 1, 13, 1, 3, 9, 4, 3, 4, 1, 3, 6, 4, 2, 11, 1],
    "BGPviewer": [19, 3, 13, 10, 14, 19, 7, 0, 7, 0, 0, 0, 5, 2, 2, 0, 0],
    "BGPvector": [1, 4, 19, 10, 33, 4, 3, 5, 4, 8, 8, 7, 51, 36, 5, 11, 50]
}

# ================== 可视化参数 ==================
LINE_STYLES = ['-', '--', '-.', ':', (0, (3, 1, 1, 1))]  # 不同线型
MARKER_SIZE = 10
LINE_WIDTH = 3.5
COLOR_PALETTE = ['#C44E52', '#DD8452', '#55A868', '#4C72B0', '#8172B2']

# ================== 创建图表 ==================
fig, ax = plt.subplots(figsize=(20, 8))

# x轴位置（使用日期索引）
x = np.arange(len(dates))

# 绘制折线
for i, (method, data) in enumerate(methods.items()):
    ax.plot(x, data, 
            color=COLOR_PALETTE[i],
            linestyle=LINE_STYLES[i % len(LINE_STYLES)],
            linewidth=LINE_WIDTH,
            marker='o',
            markersize=MARKER_SIZE,
            label=method)

# 设置坐标轴
ax.set_xlabel('Date (Day of Dec 2024)', labelpad=10)
ax.set_ylabel('Number of Detected Anomalies', labelpad=12)
ax.set_xticks(x)
ax.set_xticklabels([str(d) for d in dates])
ax.set_xlim(-0.5, len(dates)-0.5)

# 图例
ax.legend(loc='upper left', 
          bbox_to_anchor=(0.02, 1),
          ncol=2,
          framealpha=0.9,
          edgecolor='#333333',
          columnspacing=1.2,
          handletextpad=0.4)

# 网格
ax.grid(axis='y', linestyle=(0, (5, 2)), alpha=0.4, color='#999999')

plt.tight_layout()
plt.show()