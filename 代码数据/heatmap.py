'''import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 全局字体配置
config = {
    "font.family": "sans-serif",
    "font.sans-serif": ["Times New Roman"],  # 默认英文字体
    "font.size": 26,
    "axes.unicode_minus": False  # 解决负号显示问题
}
rcParams.update(config)

# 原始数据
data = np.array([[1033923, 304616, 6890],
                 [304616, 497560, 2765],
                 [6890, 2765, 147332]])


# 定义颜色映射
cmap = plt.get_cmap('YlGnBu')

# 定义格式化标签函数
def format_label(value):
    if value >= 1000:
        return f"{value/1000:.1f}K"
    return str(value)

# 绘制热力图
fig, ax = plt.subplots(figsize=(5, 5))
im = ax.imshow(data, cmap=cmap)

# 设置刻度和标签
ax.set_xticks(np.arange(3))
ax.set_yticks(np.arange(3))
ax.set_xticklabels(['RIB', 'RPKI', 'IRR'])
ax.set_yticklabels(['RIB', 'RPKI', 'IRR'])

# 添加单元格文本标签（左上角用白色字体）
for i in range(3):
    for j in range(3):
        # 左上角单元格（i=0,j=0）使用白色字体
        text_color = 'white' if (i == 0 and j == 0) else 'black'
        ax.text(j, i, format_label(data[i, j]),
                ha="center", va="center", 
                color=text_color,  # 动态设置颜色
                fontsize=30)       # 适当调小字号

# 颜色条设置
cbar = ax.figure.colorbar(im, ax=ax, shrink=0.8)
cbar.ax.tick_params(labelsize=30)  # 颜色条字号

plt.tight_layout()
#plt.savefig("F:\\heatmap.png", dpi=600, bbox_inches='tight')
plt.show()'''
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 全局字体配置
config = {
    "font.family": "sans-serif",
    "font.sans-serif": ["Times New Roman"],  # 默认英文字体
    "font.size": 22,
    "axes.unicode_minus": False  # 解决负号显示问题
}
rcParams.update(config)

# 原始标签和数据（注意数据顺序要倒过来）
labels = ['RIB', 'RPKI', 'IRR']
data = np.array([[61.58, 38.42],
                 [29.64, 70.36],
                 [8.78, 91.22]])  # RIB数据在最后

# Y轴位置
y = np.arange(len(labels))
height = 0.9  # 增加高度，使条形之间几乎没有间隙

# 拆分堆叠数据
lefts = data[:, 0]
rights = data[:, 1]

# 绘图
fig, ax = plt.subplots(figsize=(8, 6))

# 绘制堆叠条形
bar1 = ax.barh(y, lefts, height, label='Overlapped ', color='#4C72B0')
bar2 = ax.barh(y, rights, height, left=lefts, label=' Non-Overlapped', color='#55A868')

# 添加文字标签
for i in range(len(y)):
    ax.text(lefts[i]/2, y[i], f'{lefts[i]:.1f}%', va='center', ha='center', color='white', fontsize=20)
    ax.text(lefts[i] + rights[i]/2, y[i], f'{rights[i]:.1f}%', va='center', ha='center', color='white', fontsize=20)

# 美化图形
ax.set_yticks(y)
ax.set_yticklabels(labels)
ax.set_xlim(0, 100)
ax.invert_yaxis()  # 核心！让 RIB 在最上面
ax.legend(loc='upper center', bbox_to_anchor=(0.5, -0.05), ncol=2, frameon=False,fontsize=22)

plt.tight_layout()
plt.show()
