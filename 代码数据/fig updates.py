import pandas as pd
import matplotlib
import matplotlib.dates as mdates 
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 全局字体配置（使用Tinos字体，Times New Roman的开源替代）
config = {
    "font.family": "serif",
    "font.serif": ["Tinos", "Times New Roman", "DejaVu Serif"],  # 字体优先级列表
    "font.size": 40,
    "axes.unicode_minus": False
}
rcParams.update(config)

# 解析数据（保持原始数据不变）
data = {
    "date": ["2021-01-01", "2021-01-02", "2021-01-03", "2021-01-04", "2021-01-05", "2021-01-06", 
             "2021-01-07", "2021-01-08", "2021-01-09", "2021-01-10", "2021-01-11", "2021-01-12", 
             "2021-01-13", "2021-01-14", "2021-01-15", "2021-01-16", "2021-01-17", "2021-01-18", 
             "2021-01-19", "2021-01-20", "2021-01-21", "2021-01-22", "2021-01-23", "2021-01-24", 
             "2021-01-25", "2021-01-26", "2021-01-27", "2021-01-28", "2021-01-29", "2021-01-30"],
    "unchanged": [286, 281, 285, 302, 293, 291, 283, 288, 278, 285, 301, 298, 293, 298, 282, 274, 291, 
                  283, 305, 323, 316, 311, 309, 298, 310, 293, 294, 301, 304, 300],
    "withdrawn": [16978, 32396, 14939, 22538, 21114, 36728, 24950, 19955, 33936, 31943, 25211, 57070, 
                  22137, 21966, 27877, 33216, 91492, 25372, 73105, 15356, 24057, 21340, 13324, 15567, 15187, 
                  20161, 17537, 18679, 20245, 142213],
    "updated": [846141, 532896, 314615, 480485, 398975, 951206, 419559, 1199121, 4106494, 501001, 362561, 
                518532, 426683, 340540, 424965, 613423, 572734, 369787, 850928, 363268, 513285, 553450, 
                524625, 334807, 340717, 392065, 339566, 244119, 490595, 366220]
}

df = pd.DataFrame(data)

# 创建图形
fig, ax = plt.subplots(figsize=(16, 8))

# 绘制折线图（修改标签为英文）
ax.plot(df['date'], df['unchanged'], label='Unchanged', marker='o', linestyle='-', color='b')
ax.plot(df['date'], df['withdrawn'], label='Withdrawn', marker='s', linestyle='-', color='r')
ax.plot(df['date'], df['updated'], label='Updated', marker='^', linestyle='-', color='g')
	# 设置日期格式化（关键修改）
# 日期格式与刻度设置（新增次刻度）
date_format = mdates.DateFormatter('%m.%d')
ax.xaxis.set_major_formatter(date_format)
ax.xaxis.set_major_locator(mdates.DayLocator(interval=5))  # 主刻度每5天
 
# 新增次刻度设置（关键修改）
ax.xaxis.set_minor_locator(mdates.DayLocator(interval=1))  # 每天都有次刻度线
 
# 微调标签显示
ax.set_xlabel('Date', labelpad=15)  # 增加标签与坐标轴的间距
ax.set_ylabel('Count', labelpad=15)
plt.xticks(ha='center')  # 优化旋转角度和对齐方式
 
# 设置图例
ax.legend(loc='upper right')  # 将图例移到右侧
 
# 保存图像
plt.tight_layout()
plt.show()
plt.close()