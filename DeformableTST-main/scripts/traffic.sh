python -u run.py --is_training 1 --model DeformableTST --model_id Traffic --root_path ./all_datasets/traffic/ --data_path traffic.csv --data custom --n_vars 862 --seq_len 96 --stem_ratio 1 --pred_len 96 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 32 --learning_rate 0.001 --train_epochs 50 --warmup_epochs 5 --optimizer AdamW --weight_decay 0.05 --lradj cos

python -u run.py --is_training 1 --model DeformableTST --model_id Traffic --root_path ./all_datasets/traffic/ --data_path traffic.csv --data custom --n_vars 862 --seq_len 96 --stem_ratio 1 --pred_len 192 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 32 --learning_rate 0.001 --train_epochs 50 --warmup_epochs 5 --optimizer AdamW --weight_decay 0.05 --lradj cos

python -u run.py --is_training 1 --model DeformableTST --model_id Traffic --root_path ./all_datasets/traffic/ --data_path traffic.csv --data custom --n_vars 862 --seq_len 96 --stem_ratio 1 --pred_len 336 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 32 --learning_rate 0.001 --train_epochs 50 --warmup_epochs 5 --optimizer AdamW --weight_decay 0.05 --lradj cos

python -u run.py --is_training 1 --model DeformableTST --model_id Traffic --root_path ./all_datasets/traffic/ --data_path traffic.csv --data custom --n_vars 862 --seq_len 96 --stem_ratio 1 --pred_len 720 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 32 --learning_rate 0.001 --train_epochs 50 --warmup_epochs 5 --optimizer AdamW --weight_decay 0.05 --lradj cos

python -u run.py --is_training 1 --model DeformableTST --model_id Traffic --root_path ./all_datasets/traffic/ --data_path traffic.csv --data custom --n_vars 862 --seq_len 384 --stem_ratio 4 --pred_len 96 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 32 --learning_rate 0.001 --train_epochs 50 --warmup_epochs 5 --optimizer AdamW --weight_decay 0.05 --lradj cos

python -u run.py --is_training 1 --model DeformableTST --model_id Traffic --root_path ./all_datasets/traffic/ --data_path traffic.csv --data custom --n_vars 862 --seq_len 384 --stem_ratio 4 --pred_len 192 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 32 --learning_rate 0.001 --train_epochs 50 --warmup_epochs 5 --optimizer AdamW --weight_decay 0.05 --lradj cos

python -u run.py --is_training 1 --model DeformableTST --model_id Traffic --root_path ./all_datasets/traffic/ --data_path traffic.csv --data custom --n_vars 862 --seq_len 384 --stem_ratio 4 --pred_len 336 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 32 --learning_rate 0.001 --train_epochs 50 --warmup_epochs 5 --optimizer AdamW --weight_decay 0.05 --lradj cos

python -u run.py --is_training 1 --model DeformableTST --model_id Traffic --root_path ./all_datasets/traffic/ --data_path traffic.csv --data custom --n_vars 862 --seq_len 384 --stem_ratio 4 --pred_len 720 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 32 --learning_rate 0.001 --train_epochs 50 --warmup_epochs 5 --optimizer AdamW --weight_decay 0.05 --lradj cos

python -u run.py --is_training 1 --model DeformableTST --model_id Traffic --root_path ./all_datasets/traffic/ --data_path traffic.csv --data custom --n_vars 862 --seq_len 768 --stem_ratio 8 --pred_len 96 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 32 --learning_rate 0.001 --train_epochs 50 --warmup_epochs 5 --optimizer AdamW --weight_decay 0.05 --lradj cos

python -u run.py --is_training 1 --model DeformableTST --model_id Traffic --root_path ./all_datasets/traffic/ --data_path traffic.csv --data custom --n_vars 862 --seq_len 768 --stem_ratio 8 --pred_len 192 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 32 --learning_rate 0.001 --train_epochs 50 --warmup_epochs 5 --optimizer AdamW --weight_decay 0.05 --lradj cos

python -u run.py --is_training 1 --model DeformableTST --model_id Traffic --root_path ./all_datasets/traffic/ --data_path traffic.csv --data custom --n_vars 862 --seq_len 768 --stem_ratio 8 --pred_len 336 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 32 --learning_rate 0.001 --train_epochs 50 --warmup_epochs 5 --optimizer AdamW --weight_decay 0.05 --lradj cos

python -u run.py --is_training 1 --model DeformableTST --model_id Traffic --root_path ./all_datasets/traffic/ --data_path traffic.csv --data custom --n_vars 862 --seq_len 768 --stem_ratio 8 --pred_len 720 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 32 --learning_rate 0.001 --train_epochs 50 --warmup_epochs 5 --optimizer AdamW --weight_decay 0.05 --lradj cos
