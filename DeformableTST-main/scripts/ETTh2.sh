python -u run.py --is_training 1 --model DeformableTST --model_id ETTh2 --root_path ./all_datasets/ETT-small/ --data_path ETTh2.csv --data ETTh2 --n_vars 7 --seq_len 96 --stem_ratio 1 --pred_len 96 --depths 1 1 1 1 --stage_spec D D D D --dims 16 32 64 128 --expansion 1 --batch_size 512 --learning_rate 0.001 --train_epochs 10 --warmup_epochs 0 --optimizer Adam --weight_decay 0.05 --lradj type1

python -u run.py --is_training 1 --model DeformableTST --model_id ETTh2 --root_path ./all_datasets/ETT-small/ --data_path ETTh2.csv --data ETTh2 --n_vars 7 --seq_len 96 --stem_ratio 1 --pred_len 192 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 512 --learning_rate 0.001 --train_epochs 10 --warmup_epochs 0 --optimizer Adam --weight_decay 0.05 --lradj type1

python -u run.py --is_training 1 --model DeformableTST --model_id ETTh2 --root_path ./all_datasets/ETT-small/ --data_path ETTh2.csv --data ETTh2 --n_vars 7 --seq_len 96 --stem_ratio 1 --pred_len 336 --depths 1 1 1 1 --stage_spec D D D D --dims 16 32 64 128 --expansion 1 --batch_size 512 --learning_rate 0.001 --train_epochs 10 --warmup_epochs 0 --optimizer Adam --weight_decay 0.05 --lradj type1

python -u run.py --is_training 1 --model DeformableTST --model_id ETTh2 --root_path ./all_datasets/ETT-small/ --data_path ETTh2.csv --data ETTh2 --n_vars 7 --seq_len 96 --stem_ratio 1 --pred_len 720 --depths 1 1 1 1 --stage_spec D D D D --dims 16 32 64 128 --expansion 1 --batch_size 512 --learning_rate 0.001 --train_epochs 10 --warmup_epochs 0 --optimizer Adam --weight_decay 0.05 --lradj type1

python -u run.py --is_training 1 --model DeformableTST --model_id ETTh2 --root_path ./all_datasets/ETT-small/ --data_path ETTh2.csv --data ETTh2 --n_vars 7 --seq_len 384 --stem_ratio 4 --pred_len 96 --depths 1 1 1 1 --stage_spec D D D D --dims 16 32 64 128 --expansion 1 --batch_size 512 --learning_rate 0.001 --train_epochs 10 --warmup_epochs 0 --optimizer Adam --weight_decay 0.05 --lradj type1

python -u run.py --is_training 1 --model DeformableTST --model_id ETTh2 --root_path ./all_datasets/ETT-small/ --data_path ETTh2.csv --data ETTh2 --n_vars 7 --seq_len 384 --stem_ratio 4 --pred_len 192 --depths 1 1 1 1 --stage_spec D D D D --dims 16 32 64 128 --expansion 1 --batch_size 512 --learning_rate 0.001 --train_epochs 10 --warmup_epochs 0 --optimizer Adam --weight_decay 0.05 --lradj type1

python -u run.py --is_training 1 --model DeformableTST --model_id ETTh2 --root_path ./all_datasets/ETT-small/ --data_path ETTh2.csv --data ETTh2 --n_vars 7 --seq_len 384 --stem_ratio 4 --pred_len 336 --depths 1 1 1 1 --stage_spec D D D D --dims 16 32 64 128 --expansion 1 --batch_size 512 --learning_rate 0.001 --train_epochs 10 --warmup_epochs 0 --optimizer Adam --weight_decay 0.05 --lradj type1

python -u run.py --is_training 1 --model DeformableTST --model_id ETTh2 --root_path ./all_datasets/ETT-small/ --data_path ETTh2.csv --data ETTh2 --n_vars 7 --seq_len 384 --stem_ratio 4 --pred_len 720 --depths 1 1 1 1 --stage_spec D D D D --dims 16 32 64 128 --expansion 1 --batch_size 512 --learning_rate 0.001 --train_epochs 10 --warmup_epochs 0 --optimizer Adam --weight_decay 0.05 --lradj type1

python -u run.py --is_training 1 --model DeformableTST --model_id ETTh2 --root_path ./all_datasets/ETT-small/ --data_path ETTh2.csv --data ETTh2 --n_vars 7 --seq_len 768 --stem_ratio 8 --pred_len 96 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 2 --batch_size 512 --learning_rate 0.001 --train_epochs 10 --warmup_epochs 0 --optimizer Adam --weight_decay 0.05 --lradj type1

python -u run.py --is_training 1 --model DeformableTST --model_id ETTh2 --root_path ./all_datasets/ETT-small/ --data_path ETTh2.csv --data ETTh2 --n_vars 7 --seq_len 768 --stem_ratio 8 --pred_len 192 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 2 --batch_size 512 --learning_rate 0.001 --train_epochs 10 --warmup_epochs 0 --optimizer Adam --weight_decay 0.05 --lradj type1

python -u run.py --is_training 1 --model DeformableTST --model_id ETTh2 --root_path ./all_datasets/ETT-small/ --data_path ETTh2.csv --data ETTh2 --n_vars 7 --seq_len 768 --stem_ratio 8 --pred_len 336 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 4 --batch_size 512 --learning_rate 0.001 --train_epochs 10 --warmup_epochs 0 --optimizer Adam --weight_decay 0.05 --lradj type1

python -u run.py --is_training 1 --model DeformableTST --model_id ETTh2 --root_path ./all_datasets/ETT-small/ --data_path ETTh2.csv --data ETTh2 --n_vars 7 --seq_len 768 --stem_ratio 8 --pred_len 720 --depths 1 1 1 1 --stage_spec D D D D --dims 32 64 128 256 --expansion 8 --batch_size 512 --learning_rate 0.001 --train_epochs 10 --warmup_epochs 0 --optimizer Adam --weight_decay 0.05 --lradj type1
