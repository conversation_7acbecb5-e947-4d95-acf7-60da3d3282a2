from multiprocessing import Process
import datetime,time
from URLGetter import *
import requests
from constant import *
import urllib3
import urllib3.exceptions
import time,random,os
from minio import Minio
from minio.error import S3Error
from minio.commonconfig import Tags
import io
from tools import *
from ftplib import FTP
from tqdm import tqdm

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

MINUTE=60

def download_file_with_progress(url, destination):
    # 创建一个带有进度条的下载器
    response = requests.get(url, stream=True)
    response.raise_for_status()  # 如果请求返回了不成功的状态码，会引发HTTPError异常
    total_size = int(response.headers.get('content-length', 0))  # 获取文件总大小
    block_size = 1024  # 每次读取的块大小
    progress_bar = tqdm(total=total_size, unit='B', unit_scale=True, desc='Downloading')
    with response.iter_content(block_size) as block:
        for data in block:
            progress_bar.update(len(data))  # 更新进度条
        progress_bar.close()  # 关闭进度条
        return response

class downloadProcess(Process):
    def __init__(self,base_type:str,urllist:list,destination,minio_enable:bool):
        super(downloadProcess,self).__init__()
        self.base_type = base_type
        self.urllist = urllist
        self.client = None
        self.minio_enable = minio_enable
        if minio_enable:
            self.client = Minio(
                "minio.lab:9000",
                # "minio:90",
                access_key="cnic",
                secret_key="Cnic2022_",
                secure=False
            )
        self.destination = destination
        self.ErrorList=[]
        self.AnomalList=[]
                 
    def run(self):
        total = len(self.urllist)
        i = 0
        pid = os.getpid()
        for data in self.urllist:
            mod = data[0]
            url = data[1]
            if mod == HTTPS:
                print("start:"+url)
                self.downloadByHTTP(url,isError=False)
            elif mod == MINIO:
                self.downloadByMINIO(url)
            elif mod == FTPN:
                self.downloadByFTP(url,isError=False)
                pass
            else:
                print("Not Supported Yet!")
                pass
            i+=1
            print("process %u done %d/%d %s"%(pid,i,total,url))
        self.HandleErrorList()
        self.PrintErrorInfo()
        return


    def downloadByHTTP(self,url:str,isError:bool):
        headers = {
            'User-Agent': 'Wget/1.21.1'
        }
        name,timeout,tags = getInfoFromURL(self.base_type,url)
        if isError:
            timeout *= 2
        if timeout == LARGE_TIMEOUT:
            try:
                r = requests.head(url)
                os.system(f"wget -c {url} -O {name}")
                if self.minio_enable:
                    self.client.fput_object(MINIO_BUCKET_NAME[self.base_type], tranPath(name), name, content_type=r.headers['Content-Type'], part_size=4*1024*1024*1024)
                if self.destination=="":
                    os.system(f"rm {name}")
                else:
                    os.system(f"mv {name} {self.destination}")
            except Exception as e:
                print(e)
                print("http error %s"%(url))
                if isError==False:
                    self.ErrorList.append((HTTPS,url))
        else:
            try:
                with requests.get(url, headers=headers, allow_redirects=True, verify=False, timeout=timeout, stream=True) as r:
                    r.raise_for_status()
                    total_size = int(r.headers.get('Content-Length', 0))
                    chunk_size = 1024
                    with tqdm(total=total_size, unit='iB', unit_scale=True) as pbar:
                        content = io.BytesIO()
                        for chunk in r.iter_content(chunk_size=chunk_size):
                            pbar.update(len(chunk))
                            content.write(chunk)
                        content.seek(0)
                        if self.minio_enable:
                            self.client.put_object(MINIO_BUCKET_NAME[self.base_type], tranPath(name), content, length=total_size, content_type=r.headers['Content-Type'], part_size=4*1024*1024*1024)
                    if self.destination != "":
                        with open(f"{self.destination}/{name}", 'wb') as f:
                            f.write(content.getvalue())
                    print("http done: " + url)
            except Exception as e:
                print(e)
                print("http error %s"%(url))
                if isError==False:
                    self.ErrorList.append((HTTPS,url))
    
    def downloadByFTP(self,url:str,isError:bool):
        host=url[0]
        path=url[1]
        sources=url[2]
        # handle
        ftp=FTP(host)
        ftp.login()
        ftp.cwd(path)
        errlist = []
        for s in sources:
            filename=s.split("/")[-1]
            downloaded_file = io.BytesIO()
            try:
                ftp.retrbinary('RETR '+ s, downloaded_file.write)
                downloaded_file.seek(0)
                if self.minio_enable:
                    self.client.put_object(MINIO_BUCKET_NAME["IRR"],s.split("/")[-1],downloaded_file,length=downloaded_file.getbuffer().nbytes)
                if self.destination!="":
                    with open(self.destination+"/"+filename,'wb') as local_file:
                        downloaded_file.seek(0)
                        local_file.write(downloaded_file.read())
                print("ftp done: "+filename)
            except Exception as e:
                print(e)
                errlist.append(s)
                print("ftp error %s"%(s))
        if len(errlist)>0:
            if isError==False:
                self.ErrorList.append((FTPN,(host,path,errlist)))
    
    def downloadByMINIO(self,url:str):
        if self.destination!="":
            try:
                filename = getFileName(self.base_type,url)
                self.client.fget_object(MINIO_BUCKET_NAME[self.base_type],tranPath(filename),"%s/%s"%(self.destination,filename))
                print("minio done: "+url)
            except Exception as e:
                if "http" in url:
                    self.downloadByHTTP(url,False)
                else:
                    self.downloadByFTP(url,False)
    
    def HandleErrorList(self):
        i = 0
        pid = os.getpid()
        while i < len(self.ErrorList):
            data=self.ErrorList[i]
            mod = data[0]
            url = data[1]
            try:
                if mod == HTTPS:
                    print("start fixing: "+url)
                    self.downloadByHTTP(url,isError=True)
                elif mod == FTPN:
                    self.downloadByFTP(url,isError=True)
                    pass
                else:
                    print("Not Supported Yet!")
                    pass
            except Exception as e:
                print(e)
                self.AnomalList.append(data)
            i+=1
            print("process %u fix %d/%d"%(pid,i,len(self.ErrorList)))
    
    def PrintErrorInfo(self):
        f=open("./errorInfo.txt","a")
        for url in self.AnomalList:
            f.writelines("%s|%s\n"%(str(url[0]),str(url[1])))

if __name__ == "__main__":
    urlgetter=rpkiGetter(base_params(
        rpkicollectors=["josephine.sobornost.net"],
        start_time="2020-12-26-05:00",
        end_time="2020-12-26-06:10"
    ))
    urllist = urlgetter.getURL()
    p = downloadProcess(
        s=urllist,
        destination="rpkidata"
    )
    p.start()
    p.join()
    



