#!/usr/bin/env python3
"""
一键式BGP异常事件检测系统
用于分析 /data/data/anomaly-event-routedata 目录中的异常事件

功能：
1. 自动解析事件信息
2. 下载基线数据（RIB、RPKI、IRR、AS关系）
3. 构建DetectTrie
4. 检测异常事件
5. 生成性能评估报告
"""

import os
import sys
import csv
import json
import subprocess
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import ipaddress
from collections import defaultdict
import pickle
import time
from tqdm import tqdm
import logging
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from logging.handlers import TimedRotatingFileHandler
import json

# GPU加速支持
try:
    import torch
    GPU_AVAILABLE = torch.cuda.is_available()
    if GPU_AVAILABLE:
        print(f"🚀 检测到GPU: {torch.cuda.get_device_name(0)}")
    else:
        print("⚠️  未检测到GPU，将使用CPU计算")
except ImportError:
    GPU_AVAILABLE = False

# CloudTrie DetectTrie构建常量 - 按照原始实现
DETECTTRIE_THRESHOLD = 0.1  # 置信度阈值
ANYCAST_PREFIXES = {'0.0.0.0/24', '*******/24', '*******/24'}
WINDOW_SIZE = 5    # 时间窗口长度
SOURCE_WEIGHTS = {
    'RPKI': 0.9,
    'IRR': 0.7,
    'RIB': 0.6
}

try:
    import torch
    if torch.cuda.is_available():
        GPU_AVAILABLE = True
        print("🚀 检测到GPU:", torch.cuda.get_device_name(0))
    else:
        print("⚠️  GPU不可用，将使用CPU计算")
except ImportError:
    print("⚠️  未安装PyTorch，将使用CPU计算")

# 添加CloudTrie-Code到路径
sys.path.append('/data/CloudTrie-Code')
from IPTrie import IPTrie, TrieNode, default_list, default_source_dict, default_peer_dict
# 使用CloudTrie原始DetectTrie实现
class CloudTrieDetectTrieNode:
    """CloudTrie DetectTrie节点优化结构 - 完全按照原始实现"""
    __slots__ = ['children', 'source_as_set']  # 内存优化

    def __init__(self):
        self.children = {'0': None, '1': None}
        self.source_as_set = set()  # 存储合法AS集合

class CloudTrieDetectTrie:
    """CloudTrie高效前缀检测树 - 完全按照原始实现"""
    def __init__(self):
        self.root = CloudTrieDetectTrieNode()
        self.conflict_log = defaultdict(set)  # 记录冲突事件

    def insert(self, binary_prefix: str, source_as: int):
        """插入二进制格式前缀（带冲突检测）- 完全按照CloudTrie原始实现"""
        node = self.root
        for bit in binary_prefix:
            if not node.children[bit]:
                node.children[bit] = CloudTrieDetectTrieNode()
            node = node.children[bit]

        # CloudTrie原始冲突检测逻辑
        if node.source_as_set:
            # 如果已有AS，检查是否冲突
            if source_as not in node.source_as_set:
                # 冲突：记录但不添加新AS（CloudTrie原始行为）
                self.log_conflict(binary_prefix, node.source_as_set.copy(), source_as)
        else:
            # 如果没有AS，添加第一个AS
            node.source_as_set.add(source_as)

    def log_conflict(self, prefix, existing_as, new_as):
        """记录前缀冲突事件 - 完全按照原始实现"""
        self.conflict_log[prefix].update(existing_as)
        self.conflict_log[prefix].add(new_as)

    def batch_insert(self, po_pairs):
        """批量插入P/O对 - 完全按照原始实现"""
        for prefix, asn in po_pairs:
            self.insert(prefix, asn)

    def save(self, filepath):
        """保存DetectTrie - 完全按照原始实现"""
        with open(filepath, 'wb') as f:
            pickle.dump(self.root, f)

    @classmethod
    def load(cls, filepath):
        """加载DetectTrie - 完全按照原始实现"""
        trie = cls()
        with open(filepath, 'rb') as f:
            trie.root = pickle.load(f)
        return trie

    def search(self, binary_prefix: str) -> set:
        """搜索二进制前缀对应的合法AS集合 - 完全按照原始实现"""
        try:
            node = self.root
            for bit in binary_prefix:
                if bit not in ['0', '1']:
                    # 无效的二进制位
                    return None
                if node.children[bit] is None:
                    return None  # 前缀不存在
                node = node.children[bit]
            return node.source_as_set if node.source_as_set else None
        except Exception as e:
            # 如果出现任何错误，返回None
            print(f"   ⚠️  DetectTrie搜索错误: prefix={binary_prefix}, error={e}")
            return None

    def search_with_fallback(self, binary_prefix: str) -> tuple:
        """搜索二进制前缀，支持回溯到父前缀

        Returns:
            tuple: (as_set, matched_prefix_length, is_exact_match)
            - as_set: 匹配到的AS集合
            - matched_prefix_length: 匹配的前缀长度
            - is_exact_match: 是否为精确匹配
        """
        # 首先尝试精确匹配
        exact_match = self.search(binary_prefix)
        if exact_match:
            return exact_match, len(binary_prefix), True

        # 精确匹配失败，开始回溯查找父前缀
        for prefix_len in range(len(binary_prefix) - 1, 0, -1):
            parent_prefix = binary_prefix[:prefix_len]
            parent_as_set = self.search(parent_prefix)

            if parent_as_set:
                return parent_as_set, prefix_len, False

        # 没有找到任何匹配
        return None, 0, False

# 为了兼容性，创建别名
DetectTrie = CloudTrieDetectTrie
# CloudTrie一致的云模型计算函数
def generate_t_s_cloud_drops(ex, en, he, num_drops=1000):
    """生成时间-空间二维正态云滴（CloudTrie原始实现）"""
    drops = []
    for _ in range(num_drops):
        # 生成非负熵值
        en_time = abs(np.random.normal(en[0], he[0]))  # 取绝对值保证非负
        en_space = abs(np.random.normal(en[1], he[1]))

        # 生成云滴坐标
        x = np.random.normal(ex[0], max(en_time, 1e-6))  # 最小标准差保护
        y = np.random.normal(ex[1], max(en_space, 1e-6))

        # 计算隶属度（CloudTrie原始公式）
        mu = 1-np.exp(-(
            (x - ex[0])**2 / (2 * (en_time**2 + 1e-6)) +
            (y - ex[1])**2 / (2 * (en_space**2 + 1e-6))
        ))
        mu = np.clip(mu, 0, 1)  # 限制在[0,1]范围
        drops.append((x, y, mu))
    return np.array(drops)

def generate_t_m_cloud_drops(ex, en, he, num_drops=1000):
    """生成时间-数据源二维正态云滴（CloudTrie原始实现）"""
    drops = []
    for _ in range(num_drops):
        # 生成非负熵值
        en_time = abs(np.random.normal(en[0], he[0]))  # 取绝对值保证非负
        en_space = abs(np.random.normal(en[2], he[2]))

        # 生成云滴坐标
        x = np.random.normal(ex[0], max(en_time, 1e-6))  # 最小标准差保护
        y = np.random.normal(ex[2], max(en_space, 1e-6))

        # 计算隶属度（CloudTrie原始公式）
        mu = 1-np.exp(-(
            (x - ex[0])**2 / (2 * (en_time**2 + 1e-6)) +
            (y - ex[2])**2 / (2 * (en_space**2 + 1e-6))
        ))
        mu = np.clip(mu, 0, 1)  # 限制在[0,1]范围
        drops.append((x, y, mu))
    return np.array(drops)

def generate_s_m_cloud_drops(ex, en, he, num_drops=1000):
    """生成空间-数据源二维正态云滴（CloudTrie原始实现）"""
    drops = []
    for _ in range(num_drops):
        # 生成非负熵值
        en_time = abs(np.random.normal(en[1], he[1]))  # 取绝对值保证非负
        en_space = abs(np.random.normal(en[2], he[2]))

        # 生成云滴坐标
        x = np.random.normal(ex[1], max(en_time, 1e-6))  # 最小标准差保护
        y = np.random.normal(ex[2], max(en_space, 1e-6))

        # 计算隶属度（CloudTrie原始公式）
        mu = 1-np.exp(-(
            (x - ex[1])**2 / (2 * (en_time**2 + 1e-6)) +
            (y - ex[2])**2 / (2 * (en_space**2 + 1e-6))
        ))
        mu = np.clip(mu, 0, 1)  # 限制在[0,1]范围
        drops.append((x, y, mu))
    return np.array(drops)

def calculate_time_persistence_single_day(time_announced):
    """计算单天数据的时间持续性（修改版）"""
    # 对于单天数据，我们假设time_announced是一个值（0或1）
    # 或者是单天内的宣告次数
    if isinstance(time_announced, list):
        if len(time_announced) == 1:
            return float(time_announced[0])
        else:
            # 如果有多个值，使用平均值
            return np.mean(time_announced)
    else:
        return float(time_announced)

def calculate_space_consistency(peers):
    """计算空间一致性（与CloudTrie一致）"""
    # 假设每个Peer AS的权重为1
    return len(peers)

def calculate_membership(source_type):
    """计算数据源可信度（与CloudTrie一致）"""
    # 可信度分配
    weights = {'RPKI': 0.9, 'IRR': 0.7, 'RIB': 0.6}
    return weights.get(source_type, 0.5)

def bootstrap_he_parallel(scores, n_bootstrap=1000, n_jobs=-1):
    """Bootstrap方法计算超熵He（并行版本）"""
    from joblib import Parallel, delayed
    import numpy as np

    def single_bootstrap(scores, n):
        """单次Bootstrap采样"""
        sample = np.random.choice(scores, size=n, replace=True)
        return np.std(sample)

    n = len(scores)

    # 并行计算Bootstrap样本
    en_samples = Parallel(n_jobs=n_jobs, backend='threading')(
        delayed(single_bootstrap)(scores, n) for _ in range(n_bootstrap)
    )

    # He是En样本的标准差
    return np.std(en_samples)

def bootstrap_he(scores, n_bootstrap=1000):
    """Bootstrap方法计算超熵He（兼容版本）"""
    # 如果数据量小，使用串行计算
    if len(scores) < 10 or n_bootstrap < 100:
        en_samples = []
        n = len(scores)
        for _ in range(n_bootstrap):
            sample = np.random.choice(scores, size=n, replace=True)
            en = np.std(sample)
            en_samples.append(en)
        return np.std(en_samples)
    else:
        # 使用并行计算
        return bootstrap_he_parallel(scores, n_bootstrap)

def calculate_prefix_uncertainty_cloudtrie_original(trie, target_prefix, num_drops=1000):
    """完全按照CloudTrie原始实现的不确定度计算"""
    current_node = trie.root
    for bit in target_prefix:
        if bit not in ['0', '1']:
            raise ValueError(f"Invalid prefix character: {bit}")
        if not current_node.children[bit]:
            continue
        current_node = current_node.children[bit]

    po_pairs = []
    if current_node.is_end_of_prefix:
        for source_as in current_node.sources:
            for source_type in current_node.sources[source_as]:
                time_data = current_node.sources[source_as][source_type]
                peers = list(current_node.peers[source_as].keys())
                po_pairs.append({
                    "time_announced": time_data,
                    "peers": peers,
                    "source_type": source_type
                })

    if not po_pairs:
        return 0.0

    # 计算各维度指标（完全按照CloudTrie原始逻辑）
    time_scores = [calculate_time_persistence_single_day(po["time_announced"]) for po in po_pairs]
    space_scores = [calculate_space_consistency(po["peers"]) for po in po_pairs]
    member_scores = [calculate_membership(po["source_type"]) for po in po_pairs]

    # 计算云模型参数（完全按照CloudTrie）
    ex = (np.mean(time_scores), np.mean(space_scores), np.mean(member_scores))
    en = (np.std(time_scores), np.std(space_scores), np.std(member_scores))

    # 使用CloudTrie原始Bootstrap计算（不使用并行避免GPU冲突）
    def bootstrap_he_original(scores, n_bootstrap=1000):
        en_samples = []
        n = len(scores)
        for _ in range(n_bootstrap):
            sample = np.random.choice(scores, size=n, replace=True)
            en = np.std(sample)
            en_samples.append(en)
        return np.std(en_samples)

    he = (bootstrap_he_original(time_scores), bootstrap_he_original(space_scores), bootstrap_he_original(member_scores))

    # 生成三个二维云模型（完全按照CloudTrie）
    t_s_drops = generate_t_s_cloud_drops(ex, en, he, num_drops)
    t_m_drops = generate_t_m_cloud_drops(ex, en, he, num_drops)
    s_m_drops = generate_s_m_cloud_drops(ex, en, he, num_drops)

    # 计算平均不确定性（完全按照CloudTrie）
    avg_uncertainty = np.mean([
        t_s_drops[:,2].mean(),
        t_m_drops[:,2].mean(),
        s_m_drops[:,2].mean()
    ])

    return round(avg_uncertainty, 4)

def calculate_confidence(asn, sources, peer_count):
    """CloudTrie原始置信度计算 - 完全修复版本"""

    # 时间持续性（分数据源计算）
    time_weights = np.array([np.exp(-i/2) for i in range(5)])

    # 正确处理sources数据结构
    # sources格式: {source_type: {sub_key: [0,0,0,0,0]}}
    rib_vector = [0] * 5
    if 'RIB' in sources:
        rib_data = sources['RIB']
        if isinstance(rib_data, dict) and rib_data:
            # 获取第一个子键的时间序列数据
            first_key = next(iter(rib_data.keys()))
            rib_vector = rib_data[first_key]
            # 确保是长度为5的列表
            if len(rib_vector) != 5:
                rib_vector = (rib_vector + [0] * 5)[:5]

    time_score = np.dot(rib_vector, time_weights) * SOURCE_WEIGHTS['RIB']

    # 空间一致性（观测点平方根平滑）
    space_score = np.sqrt(peer_count) * 0.3

    # 隶属度（最高权重）
    available_sources = list(sources.keys())
    if available_sources:
        member_score = max(SOURCE_WEIGHTS[src] for src in available_sources if src in SOURCE_WEIGHTS) * 0.2
    else:
        member_score = 0

    # CloudTrie原始逻辑：如果有RPKI或IRR，直接返回高置信度
    if 'RPKI' in sources or 'IRR' in sources:
        return 0.9
    else:
        confidence = 0.5*time_score + 0.3*space_score + member_score
        return confidence

def binary_to_ip(binary_str):
    """CloudTrie原始二进制转IP函数 - 完全按照原始实现"""
    if not isinstance(binary_str, str):
        raise TypeError(f"Expected string, got {type(binary_str)}")

    cidr_len = len(binary_str)
    padded = binary_str.ljust(32, '0')
    octets = [str(int(padded[i:i+8], 2)) for i in range(0, 32, 8)]
    return f"{'.'.join(octets)}/{cidr_len}"

class IRRValidator:
    """IRR数据验证器 - 完全按照CloudTrie原始实现"""
    def __init__(self, irr_data_dir, max_lines=None):
        self.irr_data = self.load_irr_data(irr_data_dir, max_lines)

    def load_irr_data(self, dir_path, max_lines=None):
        """加载IRR数据 - 按照CloudTrie原始实现"""
        irr_data = {}
        for filename in os.listdir(dir_path):
            if filename.endswith('.irr') or filename.endswith('.db'):
                file_path = os.path.join(dir_path, filename)
                print(f"     📖 解析IRR文件: {filename}")
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                        current_prefix = None
                        line_count = 0
                        for line in file:
                            line = line.strip()
                            line_count += 1

                            if line.startswith('route:'):
                                parts = line.split()
                                if len(parts) >= 2:
                                    current_prefix = parts[1]
                                    irr_data[current_prefix] = {'maintained_by': [], 'org': []}
                            elif line.startswith('mnt-by:') and current_prefix:
                                parts = line.split()
                                if len(parts) >= 2:
                                    maintainer = parts[1]
                                    # CloudTrie原始逻辑：从维护者标识符提取AS号码
                                    # MAINT-AS8966 -> 8966
                                    if maintainer.startswith('MAINT-AS'):
                                        as_number = maintainer[8:]  # 提取AS号码部分
                                        irr_data[current_prefix]['maintained_by'].append(as_number)
                                    else:
                                        # 如果不是MAINT-AS格式，直接存储
                                        irr_data[current_prefix]['maintained_by'].append(maintainer)
                            elif line.startswith('org:') and current_prefix:
                                parts = line.split()
                                if len(parts) >= 2:
                                    org = parts[1]
                                    irr_data[current_prefix]['org'].append(org)
                            elif line == '':
                                current_prefix = None

                            # 每10万行显示进度
                            if line_count % 100000 == 0 and line_count > 0:
                                print(f"     📊 已解析 {line_count:,} 行，找到 {len(irr_data)} 个路由对象")

                except Exception as e:
                    print(f"     ❌ 解析IRR文件失败: {e}")
                    continue

        print(f"     ✅ IRR数据加载完成: {len(irr_data)} 个路由对象")
        return irr_data

    def validate_as_relationship(self, prefix, announcing_as):
        """验证AS与前缀的IRR关系 - 按照CloudTrie原始实现"""
        # 调试：记录IRR验证尝试次数
        if not hasattr(self, 'irr_debug_count'):
            self.irr_debug_count = 0

        self.irr_debug_count += 1

        # 显示前5次IRR验证的详细信息
        if self.irr_debug_count <= 5:
            print(f"         🔍 IRR验证 {self.irr_debug_count}: prefix={prefix}, announcing_as={announcing_as}")

            # 检查精确匹配
            exact_match = prefix in self.irr_data
            print(f"            精确匹配: {exact_match}")

            if exact_match:
                maintained_as = self.irr_data[prefix].get('maintained_by', [])
                print(f"            维护AS: {maintained_as}")
                # CloudTrie原始逻辑：直接比较AS号码和维护AS列表
                result = str(announcing_as) in maintained_as
                print(f"            验证结果: {result}")
                return result
            else:
                # 显示IRR数据中的一些示例前缀
                sample_prefixes = list(self.irr_data.keys())[:3]
                print(f"            IRR数据示例: {sample_prefixes}")

        # CloudTrie原始验证逻辑
        if prefix in self.irr_data:
            maintained_as = self.irr_data[prefix].get('maintained_by', [])
            return str(announcing_as) in maintained_as
        return False

class RipeStatValidator:
    """RIPE Stat API验证器 - 完全按照CloudTrie原始实现"""
    def __init__(self, cache_ttl=3600, request_interval=0.1, log_dir="failed_logs"):
        # 内存缓存配置（使用字典替代diskache）
        self.cache = {}  # 结构：{prefix: (timestamp, [asn1, asn2])}
        self.cache_ttl = cache_ttl
        self.request_interval = request_interval
        self.last_request = 0

        # 网络会话配置（保持不变）
        self.session = requests.Session()
        retries = Retry(
            total=5,
            backoff_factor=0.5,
            status_forcelist=[500, 502, 503, 504]
        )
        self.session.mount('https://', HTTPAdapter(max_retries=retries))

        # 日志配置（保持不变）
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger('RipeStatValidator')
        self.logger.setLevel(logging.ERROR)

        handler = TimedRotatingFileHandler(
            filename=self.log_dir / 'failed_queries.log',
            when='midnight',
            backupCount=7,
            encoding='utf-8',
            utc=True
        )
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    def validate_prefix(self, prefix, suspected_asn):
        """验证前缀的合法性 - 按照CloudTrie原始实现"""
        current_time = time.time()

        # 速率限制（保持不变）
        if current_time - self.last_request < self.request_interval:
            time.sleep(self.request_interval - (current_time - self.last_request))

        # 检查内存缓存
        if prefix in self.cache:
            cached_time, cached_asns = self.cache[prefix]
            if current_time - cached_time < self.cache_ttl:
                return suspected_asn not in cached_asns

        # 执行查询（保持不变）
        asns = self._query_api(prefix)
        self.last_request = time.time()

        # 更新内存缓存
        if asns is not None:
            self.cache[prefix] = (self.last_request, asns)
        else:
            # 查询失败时保留旧缓存（如果有）
            if prefix not in self.cache:
                self.cache[prefix] = (self.last_request, [])

        return suspected_asn not in asns if asns else True

    def _query_api(self, prefix):
        """执行带重试和缓存的API查询 - 按照CloudTrie原始实现"""
        url = f"https://stat.ripe.net/data/network-info/data.json?resource={prefix}"
        try:
            response = self.session.get(
                url,
                timeout=(3.05, 10),
                headers={'User-Agent': 'BGP-Monitor/1.0'}
            )
            response.raise_for_status()
            data = response.json()

            if not isinstance(data.get('data', {}).get('asns', []), list):
                raise ValueError("Invalid API response format")

            return [str(asn) for asn in data['data']['asns']] or []

        except Exception as e:
            error_info = {
                "timestamp": datetime.utcnow().isoformat(),
                "prefix": prefix,
                "error_type": type(e).__name__,
                "error_details": str(e),
                "url": url
            }
            self.logger.error(json.dumps(error_info))
            return None

class BGPAnomalyDetectionSystem:
    def __init__(self, base_dir="/data/data", work_dir="/data/bgp_analysis"):
        self.base_dir = Path(base_dir)
        self.work_dir = Path(work_dir)
        self.anomaly_data_dir = self.base_dir / "anomaly-event-routedata"
        self.event_info_file = self.base_dir / "anomaly-event-info.csv"
        self.bgpdownloader_dir = Path("/data/bgpdownloader-master/bgpdownloader-master")
        
        # 创建工作目录
        self.work_dir.mkdir(exist_ok=True, parents=True)
        
        # 配置参数
        self.baseline_days = 5  # 基线数据天数（与CloudTrie-Code保持一致）
        self.collector = "rrc00"  # BGP采集点

        # 初始化验证器
        self.as_relationships = {}
        self.irr_validator = None
        self.ripe_validator = None

        # 验证统计
        self.validation_stats = {
            'total_checks': 0,
            'irr_passed': 0,
            'as_rel_passed': 0,
            'final_anomalies': 0
        }

        print(f"🚀 BGP异常检测系统初始化完成")
        print(f"   工作目录: {self.work_dir}")

    def load_as_relationships(self, asrel_dir):
        """加载CAIDA AS关系数据 - 按照CloudTrie原始实现"""
        relationships = {}
        rel_files = sorted(Path(asrel_dir).glob("*.as-rel*.txt"), reverse=True)  # 按时间倒序，支持as-rel和as-rel2

        if not rel_files:
            print(f"No CAIDA files found in {asrel_dir}")
            return relationships

        for file_path in rel_files:
            print(f"Loading CAIDA data: {file_path.name}")
            try:
                with open(file_path) as f:
                    for line in f:
                        if line.startswith('#'):
                            continue
                        parts = line.strip().split('|')
                        if len(parts) >= 3:
                            as1, as2, rel_type = parts[0], parts[1], parts[2]
                            # 以最新文件覆盖旧数据
                            relationships.setdefault(as1, {})[as2] = int(rel_type)
            except Exception as e:
                print(f"Error in {file_path.name}: {str(e)}")
                continue

        print(f"Loaded {len(relationships)} AS relationships")
        return relationships

    def is_valid_commercial_relationship(self, origin_as, announcing_as):
        """检查商业关系是否允许该宣告 - 按照CloudTrie原始实现"""
        try:
            # 确保AS号码是字符串格式
            origin_as = str(origin_as)
            announcing_as = str(announcing_as)

            rel = self.as_relationships.get(origin_as, {}).get(announcing_as, None)
            rel2 = self.as_relationships.get(announcing_as, {}).get(origin_as, None)
            # 允许情况：origin是announcing的供应商（p2c）
            if rel == -1 or rel2 == -1:
                return True

            if rel == 0 or rel2 == 0:
                return True

            return False
        except Exception as e:
            # 如果出现任何错误，返回False（不允许）
            print(f"   ⚠️  商业关系检查错误: origin_as={origin_as}, announcing_as={announcing_as}, error={e}")
            return False

    def _validate_bgp_announcement(self, prefix, source_as, registered_as_set, detect_trie):
        """三层BGP验证：DetectTrie + IRR + AS关系"""
        # 如果DetectTrie中没有找到注册的AS集合，不是劫持
        if not registered_as_set or source_as in registered_as_set:
            return False

        is_hijack = True
        self.validation_stats['total_checks'] += 1

        # 第1层验证：检查IRR白名单
        if self.irr_validator and self.irr_validator.validate_as_relationship(prefix, source_as):
            is_hijack = False
            self.validation_stats['irr_passed'] += 1

        # 第2层验证：检查商业关系
        if is_hijack and self.as_relationships:
            for legit_as in registered_as_set:
                if self.is_valid_commercial_relationship(legit_as, source_as):
                    is_hijack = False
                    self.validation_stats['as_rel_passed'] += 1
                    break

        # 如果所有验证都失败，确认为劫持
        if is_hijack:
            self.validation_stats['final_anomalies'] += 1

        return is_hijack

    def calculate_po_confidence(self, ip_trie):
        """使用CloudTrie云模型计算P/O对的置信度

        Args:
            ip_trie: 构建好的IPTrie

        Returns:
            dict: 包含置信度统计的字典
        """
        print(f"📊 使用CloudTrie云模型计算P/O对置信度...")

        confidence_stats = {
            'total_po_pairs': 0,
            'high_confidence': 0,  # 不确定度 <= 0.2 (高可信)
            'medium_confidence': 0,  # 0.2 < 不确定度 <= 0.5 (中等可信)
            'low_confidence': 0,  # 不确定度 > 0.5 (低可信)
            'uncertainty_distribution': [],
            'source_distribution': defaultdict(int),
            'rpki_coverage': 0,
            'irr_coverage': 0,
            'cloud_model_success': 0
        }

        def analyze_node_confidence(node, prefix=""):
            """使用云模型递归分析节点置信度"""
            if node.is_end_of_prefix:
                # 使用云模型计算前缀不确定度
                uncertainty = calculate_prefix_uncertainty(ip_trie, prefix)
                confidence_stats['cloud_model_success'] += 1

                for asn, sources in node.sources.items():
                    confidence_stats['total_po_pairs'] += 1
                    confidence_stats['uncertainty_distribution'].append(uncertainty)

                    # 基于不确定度分类
                    if uncertainty <= 0.2:  # 高可信
                        confidence_stats['high_confidence'] += 1
                    elif uncertainty <= 0.5:  # 中等可信
                        confidence_stats['medium_confidence'] += 1
                    else:  # 低可信
                        confidence_stats['low_confidence'] += 1

                    # 数据源统计
                    for source in sources:
                        confidence_stats['source_distribution'][source] += 1

                    # RPKI/IRR覆盖统计
                    if 'RPKI' in sources:
                        confidence_stats['rpki_coverage'] += 1
                    if 'IRR' in sources:
                        confidence_stats['irr_coverage'] += 1

            # 递归处理子节点
            for bit in ['0', '1']:
                if node.children[bit] is not None:
                    analyze_node_confidence(node.children[bit], prefix + bit)

        # 从根节点开始分析
        analyze_node_confidence(ip_trie.root)

        # 计算统计信息
        if confidence_stats['total_po_pairs'] > 0:
            confidence_stats['avg_uncertainty'] = np.mean(confidence_stats['uncertainty_distribution'])
            confidence_stats['median_uncertainty'] = np.median(confidence_stats['uncertainty_distribution'])
            confidence_stats['rpki_coverage_rate'] = confidence_stats['rpki_coverage'] / confidence_stats['total_po_pairs']
            confidence_stats['irr_coverage_rate'] = confidence_stats['irr_coverage'] / confidence_stats['total_po_pairs']
            confidence_stats['cloud_model_success_rate'] = 1.0  # 总是使用云模型

        return confidence_stats

    def normalize_as_number(self, as_str):
        """统一AS号码格式，去掉AS前缀 - 按照CloudTrie原始实现"""
        return str(as_str).upper().replace("AS", "").strip()

    def is_ipv4(self, ip_str):
        """校验是否为合法IPv4前缀 - 按照CloudTrie原始实现"""
        try:
            return ipaddress.ip_network(ip_str, strict=False).version == 4
        except:
            return False

    def ip_to_binary(self, prefix):
        """IP前缀转二进制 - 按照CloudTrie原始实现"""
        try:
            network = ipaddress.ip_network(prefix, strict=False)
            # 生成32位二进制字符串
            packed = network.network_address.packed
            binary_str = ''.join(f'{byte:08b}' for byte in packed)
            # 截取前缀长度
            return binary_str[:network.prefixlen]
        except Exception as e:
            print(f"Error converting {prefix}: {str(e)}")
            return None

    def generate_cloud_drops_gpu(self, ex, en, he, num_drops=3):
        """GPU加速的云滴生成"""
        if not GPU_AVAILABLE:
            return self.generate_cloud_drops_cpu(ex, en, he, num_drops)

        device = torch.device('cuda')

        # 转换为GPU张量
        ex_tensor = torch.tensor(ex, device=device, dtype=torch.float32)
        en_tensor = torch.tensor(en, device=device, dtype=torch.float32)
        he_tensor = torch.tensor(he, device=device, dtype=torch.float32)

        # 批量生成所有三个维度的云滴
        all_drops = []

        # 时间-空间维度
        en_time = torch.abs(torch.normal(en_tensor[0].expand(num_drops), he_tensor[0].expand(num_drops)))
        en_space = torch.abs(torch.normal(en_tensor[1].expand(num_drops), he_tensor[1].expand(num_drops)))

        x = torch.normal(ex_tensor[0].expand(num_drops), torch.clamp(en_time, min=1e-6))
        y = torch.normal(ex_tensor[1].expand(num_drops), torch.clamp(en_space, min=1e-6))

        mu_ts = 1 - torch.exp(-(
            (x - ex_tensor[0])**2 / (2 * (en_time**2 + 1e-6)) +
            (y - ex_tensor[1])**2 / (2 * (en_space**2 + 1e-6))
        ))
        mu_ts = torch.clamp(mu_ts, 0, 1)

        # 时间-数据源维度
        en_time2 = torch.abs(torch.normal(en_tensor[0].expand(num_drops), he_tensor[0].expand(num_drops)))
        en_member = torch.abs(torch.normal(en_tensor[2].expand(num_drops), he_tensor[2].expand(num_drops)))

        x2 = torch.normal(ex_tensor[0].expand(num_drops), torch.clamp(en_time2, min=1e-6))
        y2 = torch.normal(ex_tensor[2].expand(num_drops), torch.clamp(en_member, min=1e-6))

        mu_tm = 1 - torch.exp(-(
            (x2 - ex_tensor[0])**2 / (2 * (en_time2**2 + 1e-6)) +
            (y2 - ex_tensor[2])**2 / (2 * (en_member**2 + 1e-6))
        ))
        mu_tm = torch.clamp(mu_tm, 0, 1)

        # 空间-数据源维度
        en_space2 = torch.abs(torch.normal(en_tensor[1].expand(num_drops), he_tensor[1].expand(num_drops)))
        en_member2 = torch.abs(torch.normal(en_tensor[2].expand(num_drops), he_tensor[2].expand(num_drops)))

        x3 = torch.normal(ex_tensor[1].expand(num_drops), torch.clamp(en_space2, min=1e-6))
        y3 = torch.normal(ex_tensor[2].expand(num_drops), torch.clamp(en_member2, min=1e-6))

        mu_sm = 1 - torch.exp(-(
            (x3 - ex_tensor[1])**2 / (2 * (en_space2**2 + 1e-6)) +
            (y3 - ex_tensor[2])**2 / (2 * (en_member2**2 + 1e-6))
        ))
        mu_sm = torch.clamp(mu_sm, 0, 1)

        # 计算平均不确定度
        avg_uncertainty = torch.mean(torch.stack([mu_ts.mean(), mu_tm.mean(), mu_sm.mean()]))

        return avg_uncertainty.cpu().item()

    def generate_cloud_drops_cpu(self, ex, en, he, num_drops=10):
        """CPU版本的云滴生成（备用）"""
        # 简化的CPU版本
        t_s_mu = []
        t_m_mu = []
        s_m_mu = []

        for _ in range(num_drops):
            # 时间-空间
            en_time = abs(np.random.normal(en[0], he[0]))
            en_space = abs(np.random.normal(en[1], he[1]))
            x = np.random.normal(ex[0], max(en_time, 1e-6))
            y = np.random.normal(ex[1], max(en_space, 1e-6))
            mu = 1-np.exp(-((x - ex[0])**2 / (2 * (en_time**2 + 1e-6)) +
                           (y - ex[1])**2 / (2 * (en_space**2 + 1e-6))))
            t_s_mu.append(np.clip(mu, 0, 1))

            # 时间-数据源
            en_time = abs(np.random.normal(en[0], he[0]))
            en_member = abs(np.random.normal(en[2], he[2]))
            x = np.random.normal(ex[0], max(en_time, 1e-6))
            y = np.random.normal(ex[2], max(en_member, 1e-6))
            mu = 1-np.exp(-((x - ex[0])**2 / (2 * (en_time**2 + 1e-6)) +
                           (y - ex[2])**2 / (2 * (en_member**2 + 1e-6))))
            t_m_mu.append(np.clip(mu, 0, 1))

            # 空间-数据源
            en_space = abs(np.random.normal(en[1], he[1]))
            en_member = abs(np.random.normal(en[2], he[2]))
            x = np.random.normal(ex[1], max(en_space, 1e-6))
            y = np.random.normal(ex[2], max(en_member, 1e-6))
            mu = 1-np.exp(-((x - ex[1])**2 / (2 * (en_space**2 + 1e-6)) +
                           (y - ex[2])**2 / (2 * (en_member**2 + 1e-6))))
            s_m_mu.append(np.clip(mu, 0, 1))

        return np.mean([np.mean(t_s_mu), np.mean(t_m_mu), np.mean(s_m_mu)])

    def calculate_prefix_uncertainty_optimized(self, trie, target_prefix, num_drops=10):
        """GPU加速的前缀不确定度计算

        Args:
            trie: IPTrie对象
            target_prefix: 目标前缀（二进制字符串）
            num_drops: 云滴数量（默认500）

        Returns:
            float: 不确定度值
        """
        # 使用本文件中定义的CloudTrie一致函数

        # 找到目标节点
        current_node = trie.root
        for bit in target_prefix:
            if bit not in ['0', '1']:
                raise ValueError(f"Invalid prefix character: {bit}")
            if not current_node.children[bit]:
                continue
            current_node = current_node.children[bit]

        po_pairs = []
        if current_node.is_end_of_prefix:
            for source_as in current_node.sources:
                for source_type in current_node.sources[source_as]:
                    time_data = current_node.sources[source_as][source_type]
                    peers = list(current_node.peers[source_as].keys())
                    po_pairs.append({
                        "time_announced": time_data,
                        "peers": peers,
                        "source_type": source_type
                    })

        if not po_pairs:
            return 0.0

        # 计算各维度指标（使用改进的时间持续性计算）
        time_scores = []
        for po in po_pairs:
            if po["source_type"] == 'RIB':
                # 对RIB数据使用特殊的时间持续性计算
                time_score = self.calculate_time_persistence_for_rib(po["time_announced"])
            else:
                # 对RPKI/IRR数据使用标准计算
                time_score = calculate_time_persistence_single_day(po["time_announced"])
            time_scores.append(time_score)

        space_scores = [calculate_space_consistency(po["peers"]) for po in po_pairs]
        member_scores = [calculate_membership(po["source_type"]) for po in po_pairs]

        # 云模型参数计算 - 按照CloudTrie原始实现
        ex = (np.mean(time_scores), np.mean(space_scores), np.mean(member_scores))
        en = (np.std(time_scores), np.std(space_scores), np.std(member_scores))
        # 使用Bootstrap方法计算He参数
        he = (self.bootstrap_he(time_scores), self.bootstrap_he(space_scores), self.bootstrap_he(member_scores))

        # 使用GPU加速的云滴生成
        avg_uncertainty = self.generate_cloud_drops_gpu(ex, en, he, num_drops)

        return round(avg_uncertainty, 4)

    def bootstrap_he(self, scores, n_bootstrap=1000):
        """Bootstrap方法计算He参数 - 按照CloudTrie原始实现"""
        if len(scores) == 0:
            return 0.01

        # 生成Bootstrap样本并计算En的分布
        en_samples = []
        n = len(scores)
        for _ in range(n_bootstrap):
            sample = np.random.choice(scores, size=n, replace=True)
            en = np.std(sample)
            en_samples.append(en)

        # He是En样本的标准差 - 完全按照CloudTrie原始实现
        he = np.std(en_samples)
        return he  # 不添加最小值保护，与CloudTrie完全一致

    def calculate_time_persistence_for_rib(self, time_announced):
        """为RIB数据特殊处理的时间持续性计算"""
        total_h = sum(time_announced)
        if total_h == 1:  # 只有一次观测（RIB快照）
            return 0.6  # 给予中等分数，表示RIB快照的中等可信度
        elif total_h == 0:
            return 0.0  # 没有观测
        else:
            # 正常的持续性计算（多次观测）
            weights = [1.0, 0.8, 0.6, 0.4, 0.2]
            weighted_sum = sum(h * w for h, w in zip(time_announced, weights))
            return weighted_sum / total_h

    def batch_calculate_uncertainty_gpu(self, trie, node_prefix_pairs, threshold):
        """批量GPU计算P/O对不确定度 - 重写版本"""
        if not GPU_AVAILABLE:
            # 如果没有GPU，回退到单个计算
            results = {}
            for node, prefix in node_prefix_pairs:
                prefix_results = []
                if node.is_end_of_prefix:
                    for source_as in node.sources:
                        uncertainty = self.calculate_prefix_uncertainty_optimized(trie, prefix)
                        prefix_results.append({
                            "asn": str(source_as),
                            "uncertainty": float(uncertainty)
                        })
                if prefix_results:
                    results[prefix] = prefix_results
            return results

        device = torch.device('cuda')

        # 收集所有P/O对，每个AS单独计算
        all_po_data = []  # 存储所有P/O对的信息
        prefix_to_results = {}  # 最终结果

        for node, prefix in node_prefix_pairs:
            if node.is_end_of_prefix:
                prefix_results = []

                for source_as in node.sources:
                    # 为每个AS收集所有数据源的P/O对
                    as_po_pairs = []
                    for source_type in node.sources[source_as]:
                        time_data = node.sources[source_as][source_type]
                        peers = list(node.peers[source_as].keys())
                        as_po_pairs.append({
                            "time_announced": time_data,
                            "peers": peers,
                            "source_type": source_type
                        })

                    if as_po_pairs:
                        all_po_data.append({
                            "prefix": prefix,
                            "asn": str(source_as),
                            "po_pairs": as_po_pairs
                        })

        if not all_po_data:
            return {}

        # 批量计算所有P/O对的不确定度
        batch_uncertainties = []

        for po_data in all_po_data:
            po_pairs = po_data["po_pairs"]

            # 计算各维度指标
            time_scores = []
            space_scores = []
            member_scores = []

            # CloudTrie原始计算函数
            def calculate_time_persistence_cloudtrie(time_announced):
                """CloudTrie原始时间持续性计算"""
                weights = [1.0, 0.8, 0.6, 0.4, 0.2]  # 5天周期的线性递减权重
                weighted_sum = sum(h * w for h, w in zip(time_announced, weights))
                total_h = sum(time_announced)
                return weighted_sum / total_h if total_h != 0 else 0

            def calculate_space_consistency_cloudtrie(peers):
                """CloudTrie原始空间一致性计算"""
                return len(peers)

            def calculate_membership_cloudtrie(source_type):
                """CloudTrie原始数据源可信度计算"""
                weights = {'RPKI': 0.9, 'IRR': 0.7, 'RIB': 0.6}
                return weights.get(source_type, 0.5)

            for po in po_pairs:
                # 使用CloudTrie原始计算
                time_score = calculate_time_persistence_cloudtrie(po["time_announced"])
                time_scores.append(time_score)

                # 空间一致性
                space_score = calculate_space_consistency_cloudtrie(po["peers"])
                space_scores.append(space_score)

                # 数据源可信度
                member_score = calculate_membership_cloudtrie(po["source_type"])
                member_scores.append(member_score)

            # 计算云模型参数（使用并行Bootstrap）
            ex = (np.mean(time_scores), np.mean(space_scores), np.mean(member_scores))
            en = (np.std(time_scores), np.std(space_scores), np.std(member_scores))

            # 使用fastbootstrap加速超熵计算
            def bootstrap_he_fast(scores, n_bootstrap=100):
                import numpy as np  # 确保numpy在函数开始就导入

                try:
                    from fastbootstrap import bootstrap

                    if len(scores) == 0:
                        return 0.01

                    # 使用fastbootstrap计算标准差的Bootstrap分布
                    def std_func(x):
                        return np.std(x)

                    # fastbootstrap计算
                    bootstrap_results = bootstrap(scores, std_func, n_bootstrap=n_bootstrap)
                    he_value = np.std(bootstrap_results)
                    return max(0.01, he_value)

                except ImportError:
                    # 回退到原始方法
                    en_samples = []
                    n = len(scores)
                    if n == 0:
                        return 0.01
                    for _ in range(n_bootstrap):
                        sample = np.random.choice(scores, size=n, replace=True)
                        en = np.std(sample)
                        en_samples.append(en)
                    return max(0.01, np.std(en_samples))

            he = (bootstrap_he_fast(time_scores), bootstrap_he_fast(space_scores), bootstrap_he_fast(member_scores))

            batch_uncertainties.append((ex, en, he))

        # 批量GPU计算云模型
        if batch_uncertainties:
            # 转换为张量进行批量计算
            ex_batch = torch.tensor([f[0] for f in batch_uncertainties], device=device, dtype=torch.float32)
            en_batch = torch.tensor([f[1] for f in batch_uncertainties], device=device, dtype=torch.float32)
            he_batch = torch.tensor([f[2] for f in batch_uncertainties], device=device, dtype=torch.float32)

            # 批量生成云滴（平衡速度和精度）
            num_drops = 300  # 调整为300个云滴
            batch_size = ex_batch.shape[0]

            # 生成三个二维云模型的云滴（完全按照CloudTrie原始逻辑）

            # 1. 时间-空间云 (t_s)
            en_time_ts = torch.abs(torch.normal(en_batch[:, 0:1].expand(-1, num_drops), he_batch[:, 0:1].expand(-1, num_drops)))
            en_space_ts = torch.abs(torch.normal(en_batch[:, 1:2].expand(-1, num_drops), he_batch[:, 1:2].expand(-1, num_drops)))

            x_ts = torch.normal(ex_batch[:, 0:1].expand(-1, num_drops), torch.clamp(en_time_ts, min=1e-6))
            y_ts = torch.normal(ex_batch[:, 1:2].expand(-1, num_drops), torch.clamp(en_space_ts, min=1e-6))

            mu_ts = 1 - torch.exp(-((x_ts - ex_batch[:, 0:1].expand(-1, num_drops))**2 / (2 * (en_time_ts**2 + 1e-6)) +
                                   (y_ts - ex_batch[:, 1:2].expand(-1, num_drops))**2 / (2 * (en_space_ts**2 + 1e-6))))
            mu_ts = torch.clamp(mu_ts, 0, 1)

            # 2. 时间-数据源云 (t_m) - 重新生成坐标
            en_time_tm = torch.abs(torch.normal(en_batch[:, 0:1].expand(-1, num_drops), he_batch[:, 0:1].expand(-1, num_drops)))
            en_member_tm = torch.abs(torch.normal(en_batch[:, 2:3].expand(-1, num_drops), he_batch[:, 2:3].expand(-1, num_drops)))

            x_tm = torch.normal(ex_batch[:, 0:1].expand(-1, num_drops), torch.clamp(en_time_tm, min=1e-6))
            y_tm = torch.normal(ex_batch[:, 2:3].expand(-1, num_drops), torch.clamp(en_member_tm, min=1e-6))

            mu_tm = 1 - torch.exp(-((x_tm - ex_batch[:, 0:1].expand(-1, num_drops))**2 / (2 * (en_time_tm**2 + 1e-6)) +
                                   (y_tm - ex_batch[:, 2:3].expand(-1, num_drops))**2 / (2 * (en_member_tm**2 + 1e-6))))
            mu_tm = torch.clamp(mu_tm, 0, 1)

            # 3. 空间-数据源云 (s_m) - 重新生成坐标
            en_space_sm = torch.abs(torch.normal(en_batch[:, 1:2].expand(-1, num_drops), he_batch[:, 1:2].expand(-1, num_drops)))
            en_member_sm = torch.abs(torch.normal(en_batch[:, 2:3].expand(-1, num_drops), he_batch[:, 2:3].expand(-1, num_drops)))

            x_sm = torch.normal(ex_batch[:, 1:2].expand(-1, num_drops), torch.clamp(en_space_sm, min=1e-6))
            y_sm = torch.normal(ex_batch[:, 2:3].expand(-1, num_drops), torch.clamp(en_member_sm, min=1e-6))

            mu_sm = 1 - torch.exp(-((x_sm - ex_batch[:, 1:2].expand(-1, num_drops))**2 / (2 * (en_space_sm**2 + 1e-6)) +
                                   (y_sm - ex_batch[:, 2:3].expand(-1, num_drops))**2 / (2 * (en_member_sm**2 + 1e-6))))
            mu_sm = torch.clamp(mu_sm, 0, 1)

            # 计算平均不确定度（CloudTrie方式）
            avg_uncertainties = (mu_ts.mean(dim=1) + mu_tm.mean(dim=1) + mu_sm.mean(dim=1)) / 3.0
            uncertainties = avg_uncertainties.cpu().numpy()
        else:
            uncertainties = []

        # 组装最终结果
        results = {}
        for i, po_data in enumerate(all_po_data):
            prefix = po_data["prefix"]
            asn = po_data["asn"]

            if i < len(uncertainties):
                uncertainty = float(uncertainties[i])
            else:
                uncertainty = 1.0  # 默认高不确定度

            if prefix not in results:
                results[prefix] = []

            results[prefix].append({
                "asn": asn,
                "uncertainty": uncertainty
            })

        return results

    def calculate_detecttrie_confidence(self, ip_trie, po_pairs):
        """计算基于DetectTrie的P/O对置信度统计

        Args:
            ip_trie: 原始IPTrie
            po_pairs: 进入DetectTrie的P/O对列表

        Returns:
            dict: DetectTrie的置信度统计
        """
        print(f"   📊 分析进入DetectTrie的{len(po_pairs)}个P/O对...")

        detecttrie_stats = {
            'total_po_pairs': len(po_pairs),
            'rpki_coverage': 0,
            'irr_coverage': 0,
            'rib_only': 0,
            'source_distribution': defaultdict(int)
        }

        # 为每个P/O对查找其在IPTrie中的数据源信息
        for binary_prefix, asn in po_pairs:
            # 在IPTrie中查找这个前缀
            node = ip_trie.root
            for bit in binary_prefix:
                if node.children[bit] is None:
                    break
                node = node.children[bit]
            else:
                # 找到了对应的节点
                if node.is_end_of_prefix and asn in node.sources:
                    sources = node.sources[asn]

                    # 统计数据源
                    for source in sources:
                        detecttrie_stats['source_distribution'][source] += 1

                    # RPKI/IRR覆盖统计
                    if 'RPKI' in sources:
                        detecttrie_stats['rpki_coverage'] += 1
                    if 'IRR' in sources:
                        detecttrie_stats['irr_coverage'] += 1
                    if len(sources) == 1 and 'RIB' in sources:
                        detecttrie_stats['rib_only'] += 1

        # 计算覆盖率
        if detecttrie_stats['total_po_pairs'] > 0:
            detecttrie_stats['rpki_coverage_rate'] = detecttrie_stats['rpki_coverage'] / detecttrie_stats['total_po_pairs']
            detecttrie_stats['irr_coverage_rate'] = detecttrie_stats['irr_coverage'] / detecttrie_stats['total_po_pairs']
            detecttrie_stats['rib_only_rate'] = detecttrie_stats['rib_only'] / detecttrie_stats['total_po_pairs']

        return detecttrie_stats

    def list_available_events(self):
        """列出所有可用的BGP异常事件"""
        print("📋 可用的BGP异常事件:")
        print("=" * 60)

        events = self.load_event_info()

        for i, event in enumerate(events, 1):
            event_name = event['event_name']
            start_time = event['start_time'].strftime('%Y-%m-%d %H:%M')
            end_time = event['end_time'].strftime('%Y-%m-%d %H:%M')
            event_type = event.get('event_type', 'Unknown')

            print(f"{i:2d}. {event_name}")
            print(f"    类型: {event_type}")
            print(f"    时间: {start_time} - {end_time}")
            print(f"    数据: {'✅' if self.check_event_data_exists(event_name) else '❌'}")
            print()

        return events

    def check_event_data_exists(self, event_name):
        """检查事件的原始数据是否存在"""
        event_data_dir = self.anomaly_data_dir / event_name
        return event_data_dir.exists() and len(list(event_data_dir.glob("*.txt"))) > 0

    def download_rpki_data(self, rpki_dir, event_date):
        """从RIPE下载多个观测点的RPKI数据"""
        try:
            import urllib.request
            import lzma

            # 构建RPKI数据URL
            year = event_date.year
            month = f"{event_date.month:02d}"
            day = f"{event_date.day:02d}"

            # 如果年份小于2012，RPKI数据不存在
            if year < 2012:
                print(f"     ⚠️  {year}年RPKI数据不存在（RPKI系统2012年后才有数据）")
                return False

            # 使用单一RPKI观测点（arin）以确保数据一致性
            rpki_url = f"https://ftp.ripe.net/rpki/arin.tal/{year}/{month}/{day}/roas.csv.xz"
            source_name = "arin"

            print(f"     📥 从{source_name}观测点下载RPKI数据...")
            print(f"     🔗 {source_name}: {rpki_url}")

            rpki_file = rpki_dir / "roas.csv.xz"
            urllib.request.urlretrieve(rpki_url, str(rpki_file))

            # 检查文件大小
            file_size = rpki_file.stat().st_size
            if file_size > 1024:  # 大于1KB认为下载成功
                print(f"     ✅ {source_name} RPKI数据下载成功 ({file_size / 1024:.1f}KB)")

                # 解压并检查内容
                extracted_file = rpki_dir / "roas.csv"
                with lzma.open(rpki_file, 'rt') as f_in:
                    with open(extracted_file, 'w') as f_out:
                        f_out.write(f_in.read())

                print(f"     ✅ {source_name} RPKI数据解压完成")
                return True
            else:
                print(f"     ❌ {source_name} RPKI数据文件过小，可能下载失败")
                return False

        except Exception as e:
            print(f"     ❌ RPKI数据下载失败: {e}")
            return False

    def download_irr_data(self, irr_dir, event_date=None):
        """从RADB下载历史IRR数据"""
        try:
            import urllib.request
            import gzip
            from datetime import datetime
            import re

            # 如果没有提供事件日期，使用当前数据
            if event_date is None:
                radb_url = "ftp://ftp.radb.net/radb/dbase/radb.db.gz"
                print(f"     📥 从RADB下载当前IRR数据...")
            else:
                # 解析事件日期，构建历史数据URL
                try:
                    # 从事件名称中提取日期：hijack-20151204-xxx -> 2015-12-04
                    date_match = re.search(r'(\d{4})(\d{2})(\d{2})', event_date)
                    if date_match:
                        year, month, day = date_match.groups()
                        event_datetime = datetime(int(year), int(month), int(day))

                        # 构建历史数据URL：年份从2016年开始有归档
                        if int(year) >= 2016:
                            # 格式：radb.db.YYMMDD.gz
                            date_str = f"{year[2:]}{month}{day}"
                            radb_url = f"ftp://ftp.radb.net/radb/dbase/archive/{year}/radb.db.{date_str}.gz"
                            print(f"     📥 从RADB下载历史IRR数据 ({year}-{month}-{day})...")
                        else:
                            # 2016年之前没有归档，使用2016年最早的数据
                            radb_url = "ftp://ftp.radb.net/radb/dbase/archive/2016/radb.db.160801.gz"
                            print(f"     📥 事件日期早于2016年，使用2016年8月1日的IRR数据...")
                    else:
                        # 无法解析日期，使用当前数据
                        radb_url = "ftp://ftp.radb.net/radb/dbase/radb.db.gz"
                        print(f"     📥 无法解析事件日期，使用当前IRR数据...")
                except Exception as e:
                    radb_url = "ftp://ftp.radb.net/radb/dbase/radb.db.gz"
                    print(f"     ⚠️ 日期解析失败，使用当前IRR数据: {e}")

            irr_file = irr_dir / "radb.db.gz"

            # 尝试下载历史数据，如果失败则回退到当前数据
            try:
                urllib.request.urlretrieve(radb_url, str(irr_file))
            except Exception as e:
                if "archive" in radb_url:
                    print(f"     ⚠️ 历史数据下载失败，回退到当前数据: {e}")
                    radb_url = "ftp://ftp.radb.net/radb/dbase/radb.db.gz"
                    urllib.request.urlretrieve(radb_url, str(irr_file))
                else:
                    raise e

            # 检查文件大小
            file_size = irr_file.stat().st_size
            if file_size > 1024 * 1024:  # 大于1MB认为下载成功
                print(f"     ✅ IRR数据下载成功 ({file_size / (1024*1024):.1f}MB)")

                # 解压并检查内容
                extracted_file = irr_dir / "radb.db"
                with gzip.open(irr_file, 'rb') as f_in:
                    with open(extracted_file, 'wb') as f_out:
                        f_out.write(f_in.read())

                print(f"     ✅ IRR数据解压完成")
                return True
            else:
                print(f"     ❌ IRR数据文件过小，可能下载失败")
                return False

        except Exception as e:
            print(f"     ❌ IRR数据下载失败: {e}")
            return False

    def has_anomaly_labels(self, event_name):
        """检查事件是否有异常标注"""
        try:
            ground_truth = self.load_ground_truth(event_name, verbose=False)
            if ground_truth:
                anomaly_count = sum(1 for label in ground_truth.values() if label == 1)
                return anomaly_count > 0
            return False
        except:
            return False

    def load_event_info(self):
        """加载事件信息 - 只加载有异常标注的hijack事件"""
        print("📋 加载事件信息（仅有异常标注的hijack事件）...")

        events = []
        total_events = 0
        hijack_events = 0
        valid_hijack_events = 0

        with open(self.event_info_file, 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                total_events += 1

                # 只处理hijack事件
                if row['event_type'].lower() != 'hijack':
                    continue

                hijack_events += 1
                event_name = row['event_name']

                # 解析时间格式
                try:
                    start_time = datetime.strptime(row['start_time'], '%Y/%m/%d %H:%M')
                    end_time = datetime.strptime(row['end_time'], '%Y/%m/%d %H:%M')

                    event = {
                        'event_type': row['event_type'],
                        'event_name': event_name,
                        'start_time': start_time,
                        'end_time': end_time,
                        'prefix': row.get('prefix', ''),
                        'hijacked_prefix': row.get('hijacked_prefix', ''),
                        'hijack_as': row.get('hijack_as', ''),
                        'victim_as': row.get('vicitim_as', ''),  # 注意原文件中的拼写
                        'outage_as': row.get('outage_as', ''),
                        'leak_as': row.get('leak_as', ''),
                        'info': row.get('info', '')
                    }

                    # 检查是否有数据和异常标注
                    if self.check_event_data_exists(event_name):
                        if self.has_anomaly_labels(event_name):
                            events.append(event)
                            valid_hijack_events += 1
                            print(f"   ✅ {event_name}: 有异常标注")
                        else:
                            print(f"   ⚠️  {event_name}: 无异常标注，跳过")
                    else:
                        print(f"   ❌ {event_name}: 数据不完整，跳过")

                except ValueError as e:
                    print(f"⚠️  跳过hijack事件 {event_name}: 时间解析错误 - {e}")
                    continue

        print(f"\n📊 事件统计:")
        print(f"   总事件数: {total_events}")
        print(f"   Hijack事件数: {hijack_events}")
        print(f"   有效Hijack事件数: {valid_hijack_events}")
        print(f"   过滤率: {(hijack_events-valid_hijack_events)/hijack_events*100:.1f}%")

        return events

    def check_event_data_exists(self, event_name):
        """检查事件数据是否存在"""
        event_dir = self.anomaly_data_dir / event_name
        origin_dir = event_dir / "origin"
        minute_labels_file = event_dir / "minute_labels.csv"
        
        return (event_dir.exists() and 
                origin_dir.exists() and 
                minute_labels_file.exists() and
                len(list(origin_dir.glob("*.gz"))) > 0)

    def check_baseline_data_exists(self, event):
        """检查基线数据是否已存在"""
        event_name = event['event_name']
        event_data_dir = self.work_dir / event_name

        if not event_data_dir.exists():
            return {'rib': False, 'rpki': False, 'irr': False, 'asrel': False, 'all_exist': False}

        # 检查各类数据是否存在
        rib_dir = event_data_dir / "rib"
        rpki_dir = event_data_dir / "rpki"
        irr_dir = event_data_dir / "irr"
        asrel_dir = event_data_dir / "asrel"

        # 检查RIB数据（至少需要一些文件）
        rib_exists = rib_dir.exists() and len(list(rib_dir.glob("*.gz"))) > 10

        # 检查RPKI数据（需要至少一个日期目录下有RPKI文件）
        rpki_exists = False
        if rpki_dir.exists():
            # 检查是否有任何日期目录包含RPKI文件
            for date_dir in rpki_dir.iterdir():
                if date_dir.is_dir():
                    # 检查多观测点RPKI文件
                    rpki_files = list(date_dir.glob("roas_*.csv"))
                    if rpki_files:
                        rpki_exists = True
                        break
                    # 兼容旧格式：roas.csv
                    if (date_dir / "roas.csv").exists():
                        rpki_exists = True
                        break
            # 兼容旧格式：直接在rpki目录下的文件
            if not rpki_exists:
                old_format_files = list(rpki_dir.glob("roas*.csv"))
                if old_format_files:
                    rpki_exists = True

        # 检查IRR数据（需要radb.db文件）
        irr_exists = irr_dir.exists() and (irr_dir / "radb.db").exists()

        # 检查AS关系数据
        asrel_files = []
        if asrel_dir.exists():
            asrel_files = list(asrel_dir.glob("*.as-rel*.txt"))
        asrel_exists = len(asrel_files) > 0

        return {
            'rib': rib_exists,
            'rpki': rpki_exists,
            'irr': irr_exists,
            'asrel': asrel_exists,
            'all_exist': rib_exists and rpki_exists and irr_exists and asrel_exists
        }

    def download_baseline_data(self, event):
        """为指定事件下载基线数据 - 增量下载模式"""
        event_name = event['event_name']
        start_time = event['start_time']

        print(f"📥 为事件 {event_name} 下载基线数据...")

        # 检查已存在的数据
        existing_data = self.check_baseline_data_exists(event)

        print(f"   📊 数据存在性检查:")
        print(f"     RIB数据: {'✅' if existing_data['rib'] else '❌'}")
        print(f"     RPKI数据: {'✅' if existing_data['rpki'] else '❌'}")
        print(f"     IRR数据: {'✅' if existing_data['irr'] else '❌'}")
        print(f"     AS关系数据: {'✅' if existing_data['asrel'] else '❌'}")

        # 如果所有数据都存在，跳过下载
        if existing_data['all_exist']:
            print(f"   ✅ 所有基线数据已存在，跳过下载")
            return True

        # 计算基线数据时间范围（事件开始前7天）
        baseline_end = start_time - timedelta(days=1)  # 事件前一天
        baseline_start = baseline_end - timedelta(days=self.baseline_days-1)  # 再往前6天

        # 创建事件专用数据目录
        event_data_dir = self.work_dir / event_name
        event_data_dir.mkdir(exist_ok=True, parents=True)

        # 标记是否有数据被重新下载（需要重建DetectTrie）
        data_updated = False
        
        rib_dir = event_data_dir / "rib"
        rpki_dir = event_data_dir / "rpki" 
        irr_dir = event_data_dir / "irr"
        asrel_dir = event_data_dir / "asrel"
        
        # 格式化时间字符串
        start_str = baseline_start.strftime("%Y-%m-%d-00:00")
        end_str = baseline_end.strftime("%Y-%m-%d-23:59")
        
        print(f"   基线时间范围: {start_str} 到 {end_str}")
        
        success_count = 0

        # 1. 按需下载RIB数据
        if not existing_data['rib']:
            print(f"   📥 下载RIB数据...")
            rib_dir.mkdir(exist_ok=True, parents=True)

            rib_cmd = [
                "python", "download.py",
                "-s", start_str,
                "-e", end_str,
                "-t", "BGP:RIBS",
                "-c", self.collector,
                "-d", str(rib_dir)
            ]

            try:
                result = subprocess.run(
                    rib_cmd,
                    cwd=self.bgpdownloader_dir,
                    capture_output=True,
                    text=True,
                    timeout=3600
                )

                if result.returncode == 0:
                    print(f"   ✅ RIB数据下载成功")
                    success_count += 1
                    data_updated = True
                else:
                    print(f"   ❌ RIB数据下载失败: {result.stderr}")

            except subprocess.TimeoutExpired:
                print(f"   ⏰ RIB数据下载超时")
            except Exception as e:
                print(f"   ❌ RIB数据下载异常: {e}")
        else:
            print(f"   ✅ RIB数据已存在，跳过下载")
            success_count += 1

        # 2. 按需下载RPKI数据（使用基线期间的数据）
        if not existing_data['rpki']:
            print(f"   🔐 下载RPKI数据（{self.baseline_days}天基线期间）...")
            rpki_dir.mkdir(exist_ok=True, parents=True)
            rpki_success_count = 0

            # 下载基线期间每一天的RPKI数据
            for i in range(self.baseline_days):
                rpki_date = start_time - timedelta(days=self.baseline_days - i)
                print(f"     📅 下载 {rpki_date.strftime('%Y-%m-%d')} 的RPKI数据...")

                # 为每天创建单独的子目录
                daily_rpki_dir = rpki_dir / rpki_date.strftime('%Y%m%d')
                daily_rpki_dir.mkdir(exist_ok=True, parents=True)

                if self.download_rpki_data(daily_rpki_dir, rpki_date):
                    rpki_success_count += 1
                else:
                    print(f"     ⚠️  {rpki_date.strftime('%Y-%m-%d')} RPKI数据下载失败，继续下载其他日期")

            if rpki_success_count > 0:
                print(f"   ✅ RPKI数据下载完成，成功下载 {rpki_success_count}/{self.baseline_days} 天")
                success_count += 1
                data_updated = True
            else:
                print(f"   ❌ RPKI数据下载失败，所有日期都无法下载")
        else:
            print(f"   ✅ RPKI数据已存在，跳过下载")
            success_count += 1

        # 3. 按需下载IRR数据
        if not existing_data['irr']:
            print(f"   📋 下载IRR数据...")
            irr_dir.mkdir(exist_ok=True, parents=True)
            irr_success = self.download_irr_data(irr_dir, event_name)
            if irr_success:
                success_count += 1
                data_updated = True
        else:
            print(f"   ✅ IRR数据已存在，跳过下载")
            success_count += 1

        # 4. 按需复制AS关系数据
        if not existing_data['asrel']:
            print(f"   📋 复制AS关系数据...")
            as_rel_source_dir = Path(f"/data/data/node_event/RIB/{event_name}")
            if as_rel_source_dir.exists():
                asrel_dir.mkdir(exist_ok=True, parents=True)

                # 查找AS关系文件（支持两种格式）
                as_rel_files = list(as_rel_source_dir.glob("*.as-rel.txt")) + list(as_rel_source_dir.glob("*.as-rel2.txt"))
                if as_rel_files:
                    import shutil
                    for as_rel_file in as_rel_files:
                        dest_file = asrel_dir / as_rel_file.name
                        shutil.copy2(as_rel_file, dest_file)
                        print(f"     ✅ 复制AS关系文件: {as_rel_file.name}")
                    success_count += 1
                    data_updated = True
                else:
                    print(f"     ⚠️  未找到AS关系文件")
            else:
                print(f"     ⚠️  AS关系数据目录不存在: {as_rel_source_dir}")
        else:
            print(f"   ✅ AS关系数据已存在，跳过复制")
            success_count += 1

        # 如果有数据更新，删除现有DetectTrie强制重建
        if data_updated:
            detect_trie_file = event_data_dir / "detect_trie.dat"
            if detect_trie_file.exists():
                detect_trie_file.unlink()
                print(f"   🗑️  删除现有DetectTrie，将重新构建")

        print(f"   📊 数据源状态: {success_count}/4 个成功")

        # 检查是否有足够的数据源（RIB + IRR是必需的，RPKI和AS关系是可选的）
        if success_count >= 2:  # 至少需要2个数据源（RIB + IRR）
            if success_count == 4:
                print(f"   ✅ 所有基线数据准备完成")
            else:
                print(f"   ⚠️  部分数据源不可用，但核心数据源已准备完成 ({success_count}/4)")
            return True
        else:
            print(f"   ❌ 基线数据不完整，缺少核心数据源")
            return False

    def build_detect_trie(self, event):
        """构建DetectTrie"""
        event_name = event['event_name']
        print(f"🌳 为事件 {event_name} 构建DetectTrie...")

        event_data_dir = self.work_dir / event_name
        detect_trie_file = event_data_dir / "detect_trie.dat"

        # 检查DetectTrie文件是否已存在
        if detect_trie_file.exists():
            print(f"   🔄 DetectTrie文件已存在，但验证逻辑已更新为三层验证，需要重新构建")
            print(f"   🗑️  删除旧的DetectTrie文件: {detect_trie_file}")
            detect_trie_file.unlink()  # 删除旧文件，强制重新构建
        
        # 数据目录（使用当前事件的工作目录）
        rib_dir = event_data_dir / "rib"
        rpki_dir = event_data_dir / "rpki"
        irr_dir = event_data_dir / "irr"
        asrel_dir = event_data_dir / "asrel"

        # 初始化验证器
        print(f"   🔧 初始化验证器...")
        print(f"   📂 AS关系数据路径: {asrel_dir}")
        print(f"   📂 IRR数据路径: {irr_dir}")

        # 加载AS关系数据
        if asrel_dir.exists():
            self.as_relationships = self.load_as_relationships(str(asrel_dir))
            print(f"   ✅ 加载AS关系数据: {len(self.as_relationships)} 个AS")
        else:
            print(f"   ⚠️  AS关系数据目录不存在: {asrel_dir}")

        # 初始化IRR验证器（完整数据模式）
        if irr_dir.exists():
            self.irr_validator = IRRValidator(str(irr_dir))
            print(f"   ✅ 初始化IRR验证器: {len(self.irr_validator.irr_data)} 个前缀")
        else:
            print(f"   ⚠️  IRR数据目录不存在: {irr_dir}")

        # 不再使用RIPE验证器
        self.ripe_validator = None
        print(f"   ✅ 跳过RIPE验证器（已禁用）")
        
        # 检查数据是否存在
        if not rib_dir.exists() or len(list(rib_dir.glob("**/*.gz"))) == 0:
            print(f"   ❌ RIB数据不存在，跳过构建")
            return None
        
        try:
            # 定义文件路径
            ip_trie_file = event_data_dir / "ip_trie.dat"

            # 获取基线日期范围（事件开始前5天）- 无论是否需要构建都需要这些变量
            event_start = event['start_time']

            # 确保event_start是datetime对象
            if isinstance(event_start, str):
                try:
                    event_start = datetime.strptime(event_start, '%Y/%m/%d %H:%M')
                except ValueError:
                    try:
                        event_start = datetime.strptime(event_start, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        print(f"   ❌ 无法解析事件开始时间: {event_start}")
                        return None

            baseline_dates = []
            for i in range(self.baseline_days):
                baseline_date = event_start - timedelta(days=i+1)
                baseline_dates.append(baseline_date.strftime('%Y%m%d'))

            # 检查IPTrie是否已存在
            need_build_iptrie = True
            if ip_trie_file.exists():
                print(f"   📂 加载已有IPTrie: {ip_trie_file}")
                try:
                    with open(ip_trie_file, 'rb') as f:
                        ip_trie = pickle.load(f)
                    print(f"   ✅ IPTrie加载成功，跳过构建")
                    need_build_iptrie = False
                except Exception as e:
                    print(f"   ⚠️  IPTrie文件损坏，重新构建: {e}")
                    ip_trie = IPTrie()
            else:
                print(f"   🌳 开始构建新的IPTrie")
                # 初始化IPTrie
                ip_trie = IPTrie()

            # 如果IPTrie已存在，直接跳到步骤2
            if not need_build_iptrie:
                print(f"   ✅ 步骤1完成：使用已有IPTrie")
                # 直接跳转到步骤2，不执行任何构建操作
            else:
                # 只有在需要构建时才执行构建逻辑
                print(f"   📅 基线日期范围: {baseline_dates}")

                # 重新格式化基线日期为date对象
                baseline_date_objects = []
                for i in range(self.baseline_days):
                    baseline_date = (event_start - timedelta(days=i+1)).date()
                    baseline_date_objects.append(baseline_date)
                baseline_date_objects.reverse()  # 按时间顺序排列
                print(f"   📅 基线日期范围: {baseline_date_objects[0]} 到 {baseline_date_objects[-1]}")

                # 处理RIB数据 - 只使用事件开始时间之前最近的一个RIB文件
                print("   📊 处理RIB数据...")
                rib_files = sorted(list(rib_dir.glob("**/*.gz")))
                rib_count = 0

                if not rib_files:
                    print("   ❌ 未找到RIB文件")
                    return None

                print(f"   📁 找到 {len(rib_files)} 个RIB文件")

                # 找到事件开始时间之前最近的一个RIB文件
                # event_start已经在前面确保是datetime对象了，不需要重新赋值
                closest_rib_file = None
                closest_time_diff = None

                for rib_file in rib_files:
                    try:
                        filename = rib_file.name
                        # 解析文件名中的时间信息，例如：rib.20151201.0000.gz
                        parts = filename.split('.')
                        if len(parts) >= 3:
                            date_str = parts[1]  # 20151201
                            time_str = parts[2]  # 0000

                            # 构建完整的时间戳
                            year = int(date_str[:4])
                            month = int(date_str[4:6])
                            day = int(date_str[6:8])
                            hour = int(time_str[:2])
                            minute = int(time_str[2:4])

                            file_datetime = datetime(year, month, day, hour, minute)

                            # 只考虑事件开始时间之前的RIB文件
                            if file_datetime < event_start:
                                time_diff = event_start - file_datetime
                                if closest_time_diff is None or time_diff < closest_time_diff:
                                    closest_rib_file = rib_file
                                    closest_time_diff = time_diff
                    except (IndexError, ValueError):
                        continue

                if closest_rib_file is None:
                    print("   ❌ 未找到事件开始时间之前的RIB文件")
                    return None

                print(f"   📄 选择最近的RIB文件: {closest_rib_file.name}")
                print(f"   ⏰ 距离事件开始时间: {closest_time_diff}")

                # 处理选定的RIB文件
                print(f"     📁 处理文件: {closest_rib_file.name}")

                try:
                    # 使用bgpdump解析RIB文件
                    result = subprocess.run([
                        "/data/bgpdump/bgpdump", "-q", "-m", "-u", str(closest_rib_file)
                    ], capture_output=True, text=True, timeout=300)

                    if result.returncode != 0:
                        print(f"     ❌ RIB文件解析失败: {closest_rib_file.name}")
                        return None

                    # 解析输出
                    lines = result.stdout.strip().split('\n')
                    print(f"     📊 解析得到 {len(lines):,} 条记录")

                    # 使用最近的基线日期作为时间戳，转换为date对象
                    baseline_date_str = baseline_dates[-1]
                    baseline_date = datetime.strptime(baseline_date_str, '%Y%m%d').date()

                    # 添加进度条显示RIB处理进度
                    print(f"     📊 处理 {len(lines)} 行RIB记录")

                    for line in tqdm(lines, desc="     处理RIB记录", unit="条"):
                        if not line.strip():
                            continue

                        parts = line.split('|')
                        if len(parts) < 7:
                            continue

                        prefix = parts[5]
                        as_path = parts[6]
                        peer_as = parts[4]

                        if as_path and as_path != '-':
                            as_path_list = as_path.split()
                            source_as = as_path_list[-1] if as_path_list else None

                            if source_as:
                                try:
                                    # 转换为二进制前缀
                                    ip_network = ipaddress.ip_network(prefix, strict=False)
                                    if ip_network.version == 4:
                                        prefix_length = ip_network.prefixlen
                                        ip_prefix = ''.join(format(int(octet), '08b')
                                                           for octet in ip_network.network_address.packed)
                                        binary_prefix = ip_prefix[:prefix_length]

                                        # 插入到Trie（使用CloudTrie格式，统一AS格式）
                                        normalized_as = self.normalize_as_number(source_as)
                                        ip_trie.insert(binary_prefix, normalized_as, 'RIB', True, baseline_date, peer_as)
                                        rib_count += 1

                                except ValueError:
                                    continue

                except subprocess.TimeoutExpired:
                    print(f"     ⏰ RIB文件处理超时: {closest_rib_file.name}")
                    return None
                except Exception as e:
                    print(f"     ❌ RIB文件处理失败: {closest_rib_file.name} - {e}")
                    return None

                print(f"   ✅ RIB文件处理完成: {rib_count:,} 个有效前缀")
                print(f"   ✅ 处理了 {rib_count} 个RIB前缀")

                # 处理RPKI数据（如果存在）
                if rpki_dir.exists():
                    print("   🔐 处理RPKI数据...")
                    rpki_files = list(rpki_dir.glob("**/*.csv"))
                    rpki_count = 0
                
                for rpki_file in rpki_files:
                    try:
                        df = pd.read_csv(rpki_file)
                        if 'IP Prefix' in df.columns and 'ASN' in df.columns:
                            for _, row in df.iterrows():
                                prefix = row['IP Prefix']
                                asn_raw = str(row['ASN'])
                                # 处理ASN格式：AS46648 -> 46648
                                if asn_raw.startswith('AS'):
                                    asn = asn_raw[2:]
                                else:
                                    asn = asn_raw
                                
                                try:
                                    ip_network = ipaddress.ip_network(prefix, strict=False)
                                    if ip_network.version == 4:
                                        prefix_length = ip_network.prefixlen
                                        ip_prefix = ''.join(format(int(octet), '08b') 
                                                           for octet in ip_network.network_address.packed)
                                        binary_prefix = ip_prefix[:prefix_length]
                                        
                                        # 为RPKI数据使用所有基线日期（因为RPKI是持续有效的）
                                        normalized_asn = self.normalize_as_number(asn)
                                        for baseline_date_str in baseline_dates:
                                            baseline_date_obj = datetime.strptime(baseline_date_str, '%Y%m%d').date()
                                            ip_trie.insert(binary_prefix, normalized_asn, 'RPKI', True, baseline_date_obj, None)
                                        rpki_count += 1
                                        
                                except ValueError:
                                    continue
                                    
                    except Exception as e:
                        print(f"   ❌ 处理RPKI文件失败: {rpki_file.name} - {e}")
                        continue
                
                print(f"   ✅ 处理了 {rpki_count} 个RPKI前缀")

                # 处理IRR数据（如果存在）
                irr_count = 0
                if irr_dir.exists():
                    print("   📋 处理IRR数据...")
                    irr_files = list(irr_dir.glob("radb.db"))

                    for irr_file in irr_files:
                        try:
                            with open(irr_file, 'r', encoding='utf-8', errors='ignore') as f:
                                current_route = None
                                current_origin = None

                                for line in f:
                                    line = line.strip()
                                    if line.startswith('route:'):
                                        current_route = line.split(':', 1)[1].strip()
                                    elif line.startswith('origin:'):
                                        current_origin = line.split(':', 1)[1].strip()
                                    elif line == '' and current_route and current_origin:
                                        # 处理完整的路由对象
                                        try:
                                            ip_network = ipaddress.ip_network(current_route, strict=False)
                                            if ip_network.version == 4:
                                                prefix_length = ip_network.prefixlen
                                                ip_prefix = ''.join(format(int(octet), '08b')
                                                                   for octet in ip_network.network_address.packed)
                                                binary_prefix = ip_prefix[:prefix_length]

                                                # 为IRR数据使用所有基线日期（因为IRR是持续有效的）
                                                normalized_origin = self.normalize_as_number(current_origin)
                                                for baseline_date_str in baseline_dates:
                                                    baseline_date_obj = datetime.strptime(baseline_date_str, '%Y%m%d').date()
                                                    ip_trie.insert(binary_prefix, normalized_origin, 'IRR', True, baseline_date_obj, None)
                                                irr_count += 1
                                        except ValueError:
                                            pass

                                        # 重置
                                        current_route = None
                                        current_origin = None

                        except Exception as e:
                            print(f"   ⚠️  IRR文件处理失败: {irr_file.name} - {e}")
                            continue

                    print(f"   ✅ 处理了 {irr_count} 个IRR前缀")
                else:
                    print("   ⚠️  IRR数据目录不存在，跳过IRR处理")

                # IPTrie构建完成，保存到文件
                print(f"   ✅ IPTrie构建完成: {rib_count + rpki_count + irr_count} 个总前缀")

                # 保存IPTrie到文件
                print(f"   💾 保存IPTrie到: {ip_trie_file}")
                try:
                    with open(ip_trie_file, 'wb') as f:
                        pickle.dump(ip_trie, f)
                    print(f"   ✅ 步骤1完成：IPTrie已保存")
                except Exception as e:
                    print(f"   ❌ IPTrie保存失败: {e}")
                    return None

            # ========== 步骤2: 计算云模型不确定度 ==========
            print("\n" + "="*60)
            print("📊 步骤2: 计算云模型不确定度")
            print("="*60)

            # 检查是否已有不确定度结果
            uncertainty_file = event_data_dir / "prefix_uncertainties.json"
            if uncertainty_file.exists():
                print(f"   📂 发现已有不确定度结果: {uncertainty_file}")
                try:
                    with open(uncertainty_file, 'r', encoding='utf-8') as f:
                        uncertainty_result = json.load(f)

                    # 从保存的结果中提取低不确定度P/O对
                    low_uncertainty_pairs = []
                    high_uncertainty_count = 0

                    uncertainties = uncertainty_result.get('uncertainties', {})
                    uncertainty_threshold = 0.52  # 调整不确定度阈值为0.52，最优平衡点

                    print(f"   📊 加载不确定度结果: {len(uncertainties)} 个前缀")
                    print(f"   🎯 使用不确定度阈值: {uncertainty_threshold}")

                    for prefix, prefix_data in uncertainties.items():
                        for po_data in prefix_data:
                            asn = po_data['asn']
                            uncertainty = po_data['uncertainty']

                            if uncertainty <= uncertainty_threshold:
                                low_uncertainty_pairs.append((prefix, asn, uncertainty))
                            else:
                                high_uncertainty_count += 1

                    print(f"   ✅ 从保存结果加载: {len(low_uncertainty_pairs)} 个低不确定度P/O对")
                    print(f"   📊 高不确定度前缀: {high_uncertainty_count} 个")

                    # 为了后续代码兼容性，收集前缀节点信息
                    prefix_nodes = []
                    def collect_prefix_nodes(node, prefix=""):
                        if node.is_end_of_prefix:
                            prefix_nodes.append((node, prefix))
                        for bit in ['0', '1']:
                            child = node.children[bit]
                            if child:
                                collect_prefix_nodes(child, prefix + bit)

                    collect_prefix_nodes(ip_trie.root)
                    print(f"   📊 收集到 {len(prefix_nodes)} 个前缀节点用于后续处理")
                    print(f"   ✅ 步骤2完成：使用已有不确定度结果")

                    # 直接跳转到DetectTrie构建，跳过所有云计算
                    uncertainty_threshold = uncertainty_result.get('parameters', {}).get('uncertainty_threshold', 0.52)
                    batch_size = uncertainty_result.get('parameters', {}).get('batch_size', 2000)
                    total_batches = 1  # 不需要批次处理，设为1
                    high_uncertainty_count = 0  # 从加载的结果中获取

                    # 跳过云计算，直接进入DetectTrie构建
                    print(f"   🚀 跳过云计算，直接使用已有的 {len(low_uncertainty_pairs)} 个低不确定度P/O对")

                except Exception as e:
                    print(f"   ⚠️  加载不确定度结果失败，重新计算: {e}")
                    uncertainty_file = None  # 标记需要重新计算
            else:
                uncertainty_file = None  # 标记需要重新计算

            # 如果没有有效的不确定度结果，则重新计算
            if uncertainty_file is None:
                print("   🌩️  开始计算云模型不确定度...")

                # 调用专门的云模型不确定度计算脚本
                try:
                    print("   🌩️  调用专门的云模型计算脚本...")

                    # 使用subprocess调用专门的计算脚本，启用GPU加速
                    result = subprocess.run([
                        'python', '/data/calculate_cloud_uncertainty.py',
                        '--event-name', event['event_name']
                        # 移除--no-gpu参数，启用GPU加速
                    ], capture_output=True, text=True, cwd='/data')

                    if result.returncode == 0:
                        print("   ✅ 云模型不确定度计算完成")
                        uncertainty_result = True
                    else:
                        print(f"   ❌ 云模型计算失败: {result.stderr}")
                        uncertainty_result = False

                    if uncertainty_result:
                        print("   ✅ 云模型不确定度计算完成")
                        # 重新检查不确定度文件
                        uncertainty_file = event_data_dir / "prefix_uncertainties.json"
                        if not uncertainty_file.exists():
                            print("   ❌ 不确定度文件未生成")
                            return None
                    else:
                        print("   ❌ 云模型不确定度计算失败")
                        return None

                except Exception as e:
                    print(f"   ❌ 云模型不确定度计算出错: {e}")
                    return None

            # 如果执行到这里，说明已经成功加载了不确定度结果，直接进入DetectTrie构建

            # ========== 步骤3: 构建DetectTrie ==========
            print("\n" + "="*60)
            print("🔨 步骤3: 构建DetectTrie（云模型+置信度双重筛选）")
            print("🔄 注意: 验证逻辑已更新为三层验证（DetectTrie + IRR + AS关系）")
            print("="*60)
            detect_trie = DetectTrie()

            # 第一步：云模型筛选后，再用置信度计算进一步筛选
            print("   📊 对低不确定度P/O对进行置信度计算...")

            final_po_pairs = []
            anycast_filtered = 0
            confidence_filtered = 0
            confidence_passed = 0

            # 为了置信度计算，需要重新获取节点信息
            prefix_to_node = {}
            def map_prefix_to_node(node, prefix=""):
                if node.is_end_of_prefix:
                    prefix_to_node[prefix] = node
                for bit in ['0', '1']:
                    child = node.children[bit]
                    if child:
                        map_prefix_to_node(child, prefix + bit)

            map_prefix_to_node(ip_trie.root)

            for prefix, asn, uncertainty in tqdm(low_uncertainty_pairs, desc="   🔍 置信度筛选", unit="对"):
                # 检查ANYCAST前缀
                try:
                    ip = binary_to_ip(prefix)
                    if ip in ANYCAST_PREFIXES:
                        anycast_filtered += 1
                        continue
                except:
                    continue

                # 获取节点信息进行置信度计算
                if prefix in prefix_to_node:
                    node = prefix_to_node[prefix]
                    if asn in node.sources:
                        sources = node.sources[asn]
                        peer_count = len(node.peers.get(asn, {}))

                        # 计算置信度 - 使用CloudTrie原始算法
                        confidence = calculate_confidence(asn, sources, peer_count)

                        if confidence >= DETECTTRIE_THRESHOLD:
                            final_po_pairs.append((prefix, asn))
                            confidence_passed += 1
                        else:
                            confidence_filtered += 1

            print(f"   📊 双重筛选结果:")
            print(f"      云模型筛选后: {len(low_uncertainty_pairs)} 个P/O对")
            print(f"      过滤ANYCAST前缀: {anycast_filtered} 个")
            print(f"      置信度筛选通过: {confidence_passed} 个")
            print(f"      置信度筛选过滤: {confidence_filtered} 个")
            print(f"      最终P/O对数量: {len(final_po_pairs)} 个")

            po_pairs = final_po_pairs


            # 批量插入P/O对到DetectTrie（带冲突检测）
            print(f"   🔨 批量插入 {len(po_pairs)} 个P/O对到DetectTrie...")

            conflict_count = 0
            inserted_count = 0

            for prefix, asn in tqdm(po_pairs, desc="   📥 插入DetectTrie", unit="对"):
                # 检查插入前的状态
                existing_as_set = detect_trie.search(prefix)

                # 插入P/O对（insert方法内部会处理冲突检测）
                detect_trie.insert(prefix, asn)

                # 检查插入后的状态来统计
                new_as_set = detect_trie.search(prefix)
                if existing_as_set and asn not in existing_as_set:
                    # 发生了冲突（新AS没有被添加）
                    conflict_count += 1
                else:
                    # 成功插入
                    inserted_count += 1

            print(f"   📊 DetectTrie插入结果:")
            print(f"      成功插入: {inserted_count} 个P/O对")
            print(f"      检测到冲突: {conflict_count} 个")
            print(f"      冲突前缀数: {len(detect_trie.conflict_log)} 个")



            # 保存DetectTrie
            detect_trie_file = event_data_dir / "detect_trie.dat"
            detect_trie.save(str(detect_trie_file))

            # CloudTrie原始统计信息（包含冲突检测）
            print(f"   📊 生成CloudTrie格式统计信息...")
            confidence_stats = {
                'total_po_pairs': len(po_pairs),
                'uncertainty_threshold': uncertainty_threshold,
                'confidence_threshold': DETECTTRIE_THRESHOLD,
                'processed_prefixes': len(prefix_nodes),
                'low_uncertainty_pairs': len(low_uncertainty_pairs),
                'high_uncertainty_count': high_uncertainty_count,
                'filtered_anycast': anycast_filtered,
                'confidence_passed': confidence_passed,
                'confidence_filtered': confidence_filtered,
                'final_po_pairs': len(po_pairs),
                'inserted_count': inserted_count,
                'conflict_count': conflict_count,
                'conflict_prefixes': len(detect_trie.conflict_log)
            }
            detecttrie_stats = {
                'input_po_pairs': len(po_pairs),  # 输入的P/O对数量
                'inserted_po_pairs': inserted_count,  # 实际插入的P/O对数量
                'conflict_po_pairs': conflict_count,  # 冲突的P/O对数量
                'conflict_prefixes': len(detect_trie.conflict_log),  # 冲突的前缀数量
                'rpki_coverage': 0,
                'irr_coverage': 0,
                'rib_only': inserted_count,  # 实际插入的数量
                'source_distribution': {'RIB': inserted_count}
            }

            # 保存置信度统计
            confidence_file = event_data_dir / "po_confidence_stats.json"
            with open(confidence_file, 'w', encoding='utf-8') as f:
                # 转换numpy类型为Python原生类型以便JSON序列化
                json_stats = {}
                for key, value in confidence_stats.items():
                    if isinstance(value, np.ndarray):
                        json_stats[key] = value.tolist()
                    elif isinstance(value, (np.integer, np.floating)):
                        json_stats[key] = value.item()
                    elif isinstance(value, defaultdict):
                        json_stats[key] = dict(value)
                    else:
                        json_stats[key] = value
                json.dump(json_stats, f, indent=2, ensure_ascii=False)

            print(f"   ✅ DetectTrie构建完成，保存到: {detect_trie_file}")
            print(f"   📊 IPTrie总体置信度统计:")
            print(f"      总P/O对数: {confidence_stats['total_po_pairs']}")
            if confidence_stats['total_po_pairs'] > 0:
                print(f"      云模型成功率: {confidence_stats.get('cloud_model_success_rate', 0)*100:.1f}%")
                print(f"      平均不确定度: {confidence_stats.get('avg_uncertainty', 0):.3f}")
                print(f"      RPKI覆盖率: {confidence_stats.get('rpki_coverage_rate', 0)*100:.1f}%")
                print(f"      IRR覆盖率: {confidence_stats.get('irr_coverage_rate', 0)*100:.1f}%")

            print(f"   📊 DetectTrie实际使用的P/O对统计:")
            print(f"      输入P/O对数: {detecttrie_stats['input_po_pairs']}")
            print(f"      成功插入: {detecttrie_stats['inserted_po_pairs']}")
            print(f"      检测到冲突: {detecttrie_stats['conflict_po_pairs']}")
            print(f"      冲突前缀数: {detecttrie_stats['conflict_prefixes']}")
            if detecttrie_stats['inserted_po_pairs'] > 0:
                rpki_rate = detecttrie_stats['rpki_coverage'] / detecttrie_stats['inserted_po_pairs'] * 100 if detecttrie_stats['inserted_po_pairs'] > 0 else 0
                irr_rate = detecttrie_stats['irr_coverage'] / detecttrie_stats['inserted_po_pairs'] * 100 if detecttrie_stats['inserted_po_pairs'] > 0 else 0
                rib_rate = detecttrie_stats['rib_only'] / detecttrie_stats['inserted_po_pairs'] * 100 if detecttrie_stats['inserted_po_pairs'] > 0 else 0
                print(f"      RPKI覆盖: {detecttrie_stats['rpki_coverage']} ({rpki_rate:.1f}%)")
                print(f"      IRR覆盖: {detecttrie_stats['irr_coverage']} ({irr_rate:.1f}%)")
                print(f"      仅RIB: {detecttrie_stats['rib_only']} ({rib_rate:.1f}%)")
                print(f"      数据源分布: {dict(detecttrie_stats['source_distribution'])}")
            print(f"   💾 置信度统计已保存到: {confidence_file}")

            return str(detect_trie_file)
            
        except Exception as e:
            print(f"   ❌ DetectTrie构建失败: {e}")
            return None



    def detect_anomalies(self, event, detect_trie_file):
        """检测异常事件"""
        event_name = event['event_name']
        print(f"🔍 检测事件 {event_name} 中的异常...")

        # 加载DetectTrie
        try:
            detect_trie = DetectTrie.load(detect_trie_file)
        except Exception as e:
            print(f"   ❌ 加载DetectTrie失败: {e}")
            return None

        # 获取事件数据目录 - 使用已解析的文本文件
        event_dir = self.anomaly_data_dir / event_name
        data_dir = event_dir / "data"

        # 获取事件时间范围
        start_time = event['start_time']
        end_time = event['end_time']
        # 明确指定UTC时区进行时间戳转换
        from datetime import timezone
        start_timestamp = int(start_time.replace(tzinfo=timezone.utc).timestamp())
        end_timestamp = int(end_time.replace(tzinfo=timezone.utc).timestamp())

        # 初始化时间窗口分析
        time_window_analysis = {
            'actual_start': start_time,
            'actual_end': end_time,
            'actual_duration_hours': (end_time - start_time).total_seconds() / 3600,
            'detected_start': None,
            'detected_end': None,
            'detected_duration_hours': 0,
            'coverage_rate': 0,
            'detection_delay_minutes': 0,
            'detection_early_minutes': 0
        }

        print(f"   📅 事件时间范围: {start_time} 到 {end_time}")
        print(f"   📅 时间戳范围: {start_timestamp} 到 {end_timestamp}")

        # 调试：显示时间戳转换详情（使用UTC时区）
        from datetime import timezone
        print(f"   🔍 调试时间戳转换:")
        print(f"      start_time类型: {type(start_time)}, 值: {start_time}")
        print(f"      start_timestamp: {start_timestamp} (UTC: {datetime.fromtimestamp(start_timestamp, timezone.utc)})")
        print(f"      end_time类型: {type(end_time)}, 值: {end_time}")
        print(f"      end_timestamp: {end_timestamp} (UTC: {datetime.fromtimestamp(end_timestamp, timezone.utc)})")

        # 获取已解析的BGP Updates文件
        all_update_files = sorted(list(data_dir.glob("*.txt")))
        if not all_update_files:
            print(f"   ❌ 未找到已解析的BGP Updates文件")
            return None

        # 过滤出事件时间范围内的文件
        update_files = []
        for update_file in all_update_files:
            # 从文件名提取时间：rrc00_updates.20160221.HHMM.txt
            try:
                filename = update_file.name
                if filename.startswith('rrc00_updates.'):
                    parts = filename.split('.')
                    if len(parts) >= 3:
                        date_str = parts[1]  # 20160221
                        time_str = parts[2]  # HHMM

                        # 构建完整的时间戳
                        year = int(date_str[:4])
                        month = int(date_str[4:6])
                        day = int(date_str[6:8])
                        hour = int(time_str[:2])
                        minute = int(time_str[2:4])

                        file_datetime = datetime(year, month, day, hour, minute)
                        file_timestamp = int(file_datetime.timestamp())

                        # 检查是否在事件时间范围内（扩展30分钟缓冲）
                        buffer_seconds = 30 * 60  # 30分钟缓冲
                        if (start_timestamp - buffer_seconds) <= file_timestamp <= (end_timestamp + buffer_seconds):
                            update_files.append(update_file)
            except (ValueError, IndexError):
                # 如果文件名格式不符合预期，跳过
                continue

        if not update_files:
            print(f"   ❌ 在事件时间范围内未找到BGP Updates文件")
            return None

        print(f"   📁 总文件数: {len(all_update_files)}, 事件时间范围内文件数: {len(update_files)}")

        # 按分钟统计异常 - 按照CloudTrie原始实现
        minute_anomalies = defaultdict(int)  # {minute_timestamp: anomaly_count}
        total_updates = 0
        total_anomalies = 0
        hijacks = []  # CloudTrie原始实现使用的列表

        # 处理每个已解析的Updates文件
        for update_file in update_files:
            print(f"   📄 处理文件: {update_file.name}")

            try:
                # 首先检查文件大小，如果太大则分割处理
                with open(update_file, 'r') as f:
                    lines = f.readlines()

                # 统一处理所有文件（无论大小），添加进度条
                file_updates = 0
                file_anomalies = 0
                file_anomaly_details = []  # 存储异常路由详情

                # 为大文件显示行数信息
                total_lines = len(lines)
                if total_lines > 10000:
                    print(f"     📊 文件较大({total_lines:,}行)，正在处理...")

                # 添加BGP记录时间戳调试计数器
                bgp_timestamp_debug_count = 0

                # 使用进度条处理所有行
                for line in tqdm(lines, desc=f"     🔍 处理{update_file.name}", unit="行", leave=False):
                        line = line.strip()
                        if not line:
                            continue

                        parts = line.split('|')
                        if len(parts) < 6:  # 至少需要6个字段
                            continue

                        # 处理字段数量不同的情况
                        if len(parts) >= 7 and parts[2] != 'A':  # 7字段格式，只处理宣告(A)
                            continue
                        elif len(parts) == 6 and parts[2] != 'A':  # 6字段格式，只处理宣告(A)
                            continue

                        try:
                            timestamp = int(parts[1])
                            prefix = parts[5] if len(parts) >= 6 else parts[4]
                            as_path = parts[6] if len(parts) >= 7 else parts[5] if len(parts) >= 6 else ""
                            peer_as = parts[4] if len(parts) >= 7 else parts[3] if len(parts) >= 6 else ""

                            # 调试：显示前5个BGP记录的时间戳信息（使用UTC时区）
                            bgp_timestamp_debug_count += 1
                            if bgp_timestamp_debug_count <= 5:
                                import datetime as dt
                                bgp_time = dt.datetime.fromtimestamp(timestamp, dt.timezone.utc)
                                print(f"         🔍 BGP记录 {bgp_timestamp_debug_count}: 原始时间戳={timestamp}, 转换时间={bgp_time} UTC")
                                print(f"            事件范围: {start_timestamp} ~ {end_timestamp}")
                                print(f"            在范围内: {start_timestamp <= timestamp <= end_timestamp}")

                            # 检查是否在事件时间范围内
                            if not (start_timestamp <= timestamp <= end_timestamp):
                                continue

                            file_updates += 1

                            # 提取源AS
                            if as_path and as_path != '-':
                                as_path_list = as_path.split()
                                source_as = as_path_list[-1] if as_path_list else None

                                if source_as:
                                    # 转换前缀为二进制
                                    try:
                                        ip_network = ipaddress.ip_network(prefix, strict=False)
                                        if ip_network.version == 4:
                                            prefix_length = ip_network.prefixlen
                                            ip_prefix = ''.join(format(int(octet), '08b')
                                                               for octet in ip_network.network_address.packed)
                                            binary_prefix = ip_prefix[:prefix_length]

                                            # 在DetectTrie中查找 - 按照CloudTrie原始实现
                                            registered_as_set = detect_trie.search(binary_prefix)

                                            # 调试信息：检查前几个查询
                                            if file_updates <= 5:
                                                print(f"   🔍 调试: prefix={prefix}, binary_prefix={binary_prefix[:20]}..., found_as_set={registered_as_set}")

                                            # CloudTrie原始检测逻辑
                                            source_as = self.normalize_as_number(source_as)

                                            # 使用三层验证方法
                                            is_hijack = self._validate_bgp_announcement(prefix, source_as, registered_as_set, detect_trie)

                                            if is_hijack:
                                                # 确认为异常，记录劫持事件
                                                hijacks.append({
                                                    'timestamp': timestamp,
                                                    'prefix': prefix,
                                                    'legit_as': ', '.join(registered_as_set),
                                                    'source_as': source_as
                                                })
                                                minute_timestamp = (timestamp // 60) * 60
                                                minute_anomalies[minute_timestamp] += 1
                                                file_anomalies += 1

                                    except ValueError:
                                        continue

                        except (ValueError, IndexError):
                            continue

                # 累加到总统计
                total_updates += file_updates
                total_anomalies += file_anomalies
                if file_anomalies > 0:
                    print(f"     🚨 发现 {file_anomalies} 个异常 (共 {file_updates} 个更新)")

                    # 显示前5个异常路由详情
                    if len(file_anomaly_details) > 0:
                        print(f"     📋 异常路由详情 (显示前5个):")
                        for i, detail in enumerate(file_anomaly_details[:5]):
                            print(f"       [{i+1}] {detail['time_str']} | 前缀: {detail['prefix']} | "
                                  f"宣告AS: {detail['announced_as']} | 注册AS: {detail['registered_as_set']}")

                        if len(file_anomaly_details) > 5:
                            print(f"       ... 还有 {len(file_anomaly_details) - 5} 个异常路由")

            except Exception as e:
                print(f"   ❌ 文件处理失败: {update_file.name} - {e}")
                continue

        print(f"   📊 检测完成: 总更新 {total_updates}, 总异常 {total_anomalies}")
        print(f"   📊 异常分布在 {len(minute_anomalies)} 个时间分钟内")

        # 输出验证统计
        print(f"\n🔍 3层验证统计:")
        print(f"   总检查数: {self.validation_stats['total_checks']:,}")
        print(f"   IRR验证通过: {self.validation_stats['irr_passed']:,}")
        print(f"   AS关系验证通过: {self.validation_stats['as_rel_passed']:,}")
        print(f"   最终异常数: {self.validation_stats['final_anomalies']:,}")

        # 显示IRR验证调试总结
        if hasattr(self.irr_validator, 'irr_debug_count'):
            print(f"   IRR验证尝试次数: {self.irr_validator.irr_debug_count:,}")
            print(f"   IRR数据总前缀数: {len(self.irr_validator.irr_data):,}")

        if self.validation_stats['total_checks'] > 0:
            irr_rate = (self.validation_stats['irr_passed'] / self.validation_stats['total_checks']) * 100
            as_rate = (self.validation_stats['as_rel_passed'] / self.validation_stats['total_checks']) * 100
            final_rate = (self.validation_stats['final_anomalies'] / self.validation_stats['total_checks']) * 100

            print(f"   IRR验证通过率: {irr_rate:.1f}%")
            print(f"   AS关系验证通过率: {as_rate:.1f}%")
            print(f"   最终异常率: {final_rate:.1f}%")

        # 保存异常路由详情到文件
        event_data_dir = self.work_dir / event['event_name']
        event_data_dir.mkdir(exist_ok=True, parents=True)

        print(f"🔍 异常路由检测结果:")
        print(f"   总异常路由数: {len(hijacks)}")
        print(f"   保存目录: {event_data_dir}")

        if hijacks:
            # 保存JSON格式
            anomaly_file = event_data_dir / "anomaly_routes.json"
            print(f"💾 保存异常路由详情到: {anomaly_file}")

            try:
                with open(anomaly_file, 'w', encoding='utf-8') as f:
                    import json
                    json.dump({
                        'event_name': event['event_name'],
                        'detection_time': datetime.now().isoformat(),
                        'detection_method': 'enhanced_bgp_anomaly_detection',
                        'baseline_days': self.baseline_days,
                        'total_anomalies': len(hijacks),
                        'anomaly_details': hijacks
                    }, f, indent=2, ensure_ascii=False)
                print(f"✅ JSON文件保存成功: {anomaly_file}")
            except Exception as e:
                print(f"❌ JSON文件保存失败: {e}")

            # 保存增强的CSV格式
            csv_file = event_data_dir / "anomaly_routes.csv"
            try:
                with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                    import csv
                    writer = csv.writer(f)
                    writer.writerow(['timestamp', 'prefix', 'legit_as', 'source_as'])
                    for hijack in hijacks:
                        writer.writerow([
                            hijack['timestamp'],
                            hijack['prefix'],
                            hijack['legit_as'],
                            hijack['source_as']
                        ])
                print(f"✅ CSV文件保存成功: {csv_file}")
            except Exception as e:
                print(f"❌ CSV文件保存失败: {e}")

            # 保存增强的TXT格式（可读性强）
            txt_file = event_data_dir / "anomaly_routes.txt"
            try:
                with open(txt_file, 'w', encoding='utf-8') as f:
                    f.write(f"BGP异常路由检测结果 - {event['event_name']}\n")
                    f.write(f"检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"检测方法: 增强BGP异常检测系统\n")
                    f.write(f"基线数据天数: {self.baseline_days}天\n")
                    f.write(f"总异常路由数: {len(hijacks)}\n")
                    f.write("=" * 80 + "\n\n")

                    for i, hijack in enumerate(hijacks, 1):
                        f.write(f"劫持事件 #{i}:\n")
                        f.write(f"  时间戳: {hijack['timestamp']}\n")
                        f.write(f"  前缀: {hijack['prefix']}\n")
                        f.write(f"  合法AS: {hijack['legit_as']}\n")
                        f.write(f"  源AS: {hijack['source_as']}\n")
                        f.write("-" * 40 + "\n")
                print(f"✅ TXT文件保存成功: {txt_file}")
            except Exception as e:
                print(f"❌ TXT文件保存失败: {e}")

            # 保存特定劫持事件的详细分析（如果适用）
            self._save_specific_event_analysis(event, hijacks, event_data_dir)

            print(f"📋 已保存 {len(hijacks)} 个劫持事件详情到多种格式文件")
        else:
            print("📋 未检测到异常路由")
            # 即使没有异常，也保存一个空的结果文件
            empty_file = event_data_dir / "no_anomalies_detected.txt"
            try:
                with open(empty_file, 'w', encoding='utf-8') as f:
                    f.write(f"BGP异常路由检测结果 - {event['event_name']}\n")
                    f.write(f"检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"结果: 未检测到异常路由\n")
                    f.write(f"总处理更新数: {total_updates}\n")
                print(f"📝 已保存空结果文件: {empty_file}")
            except Exception as e:
                print(f"❌ 空结果文件保存失败: {e}")

        # 完善时间窗口分析
        if time_window_analysis['detected_start'] and time_window_analysis['detected_end']:
            detected_duration = (time_window_analysis['detected_end'] - time_window_analysis['detected_start']).total_seconds() / 3600
            time_window_analysis['detected_duration_hours'] = detected_duration

            # 计算覆盖率
            actual_duration = time_window_analysis['actual_duration_hours']
            if actual_duration > 0:
                overlap_start = max(time_window_analysis['actual_start'], time_window_analysis['detected_start'])
                overlap_end = min(time_window_analysis['actual_end'], time_window_analysis['detected_end'])
                if overlap_start <= overlap_end:
                    overlap_duration = (overlap_end - overlap_start).total_seconds() / 3600
                    time_window_analysis['coverage_rate'] = overlap_duration / actual_duration

                # 计算检测延迟或提前时间
                delay_seconds = (time_window_analysis['detected_start'] - time_window_analysis['actual_start']).total_seconds()
                if delay_seconds > 0:
                    time_window_analysis['detection_delay_minutes'] = delay_seconds / 60
                else:
                    time_window_analysis['detection_early_minutes'] = abs(delay_seconds) / 60

        # 生成检测结果
        detection_result = {
            'event_name': event_name,
            'total_updates': total_updates,
            'total_anomalies': total_anomalies,
            'anomaly_minutes': len(minute_anomalies),
            'minute_anomalies': dict(minute_anomalies),
            'time_window_analysis': time_window_analysis
        }

        return detection_result

    def load_ground_truth(self, event_name, verbose=True):
        """加载真实标注"""
        event_dir = self.anomaly_data_dir / event_name
        minute_labels_file = event_dir / "minute_labels.csv"

        if not minute_labels_file.exists():
            if verbose:
                print(f"   ⚠️  未找到标注文件: {minute_labels_file}")
            return None

        try:
            df = pd.read_csv(minute_labels_file)
            ground_truth = {}

            for _, row in df.iterrows():
                timestamp = int(row['timestamp'])
                label = int(row['label'])
                ground_truth[timestamp] = label

            return ground_truth

        except Exception as e:
            if verbose:
                print(f"   ❌ 加载标注失败: {e}")
            return None

    def evaluate_performance(self, detection_result, ground_truth):
        """评估检测性能"""
        if not detection_result or not ground_truth:
            return None

        print(f"📊 评估检测性能...")

        # 获取检测结果
        minute_anomalies = detection_result['minute_anomalies']

        # 统计性能指标
        true_positives = 0   # 正确检测到的异常
        false_positives = 0  # 误报的异常
        true_negatives = 0   # 正确识别的正常
        false_negatives = 0  # 漏检的异常

        # 获取所有时间戳
        all_timestamps = set(ground_truth.keys()) | set(minute_anomalies.keys())

        for timestamp in all_timestamps:
            # 真实标签
            true_label = ground_truth.get(timestamp, 0)

            # 检测结果（如果该分钟有异常则为1，否则为0）
            detected_anomaly = 1 if minute_anomalies.get(timestamp, 0) > 0 else 0

            if true_label == 1 and detected_anomaly == 1:
                true_positives += 1
            elif true_label == 0 and detected_anomaly == 1:
                false_positives += 1
            elif true_label == 0 and detected_anomaly == 0:
                true_negatives += 1
            elif true_label == 1 and detected_anomaly == 0:
                false_negatives += 1

        # 计算性能指标
        total_samples = len(all_timestamps)
        total_true_anomalies = sum(1 for label in ground_truth.values() if label == 1)
        total_detected_anomalies = len([ts for ts, count in minute_anomalies.items() if count > 0])

        # 基本指标
        accuracy = (true_positives + true_negatives) / total_samples if total_samples > 0 else 0
        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

        # 误报率
        false_positive_rate = false_positives / (false_positives + true_negatives) if (false_positives + true_negatives) > 0 else 0

        evaluation = {
            'event_name': detection_result['event_name'],
            'total_samples': total_samples,
            'total_true_anomalies': total_true_anomalies,
            'total_detected_anomalies': total_detected_anomalies,
            'true_positives': true_positives,
            'false_positives': false_positives,
            'true_negatives': true_negatives,
            'false_negatives': false_negatives,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'false_positive_rate': false_positive_rate,
            'detected_any_anomaly': total_detected_anomalies > 0,
            'detection_rate': total_detected_anomalies / total_true_anomalies if total_true_anomalies > 0 else 0,
            'time_window_analysis': detection_result.get('time_window_analysis', {})
        }

        return evaluation

    def generate_report(self, evaluations):
        """生成性能评估报告"""
        print("📋 生成性能评估报告...")

        if not evaluations:
            print("   ⚠️  没有评估结果")
            return

        # 创建报告目录
        report_dir = self.work_dir / "reports"
        report_dir.mkdir(exist_ok=True)

        # 生成详细报告
        report_file = report_dir / f"detection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        # 计算总体统计
        total_events = len(evaluations)
        successful_detections = sum(1 for eval in evaluations if eval['detected_any_anomaly'])

        total_tp = sum(eval['true_positives'] for eval in evaluations)
        total_fp = sum(eval['false_positives'] for eval in evaluations)
        total_tn = sum(eval['true_negatives'] for eval in evaluations)
        total_fn = sum(eval['false_negatives'] for eval in evaluations)

        overall_accuracy = (total_tp + total_tn) / (total_tp + total_fp + total_tn + total_fn) if (total_tp + total_fp + total_tn + total_fn) > 0 else 0
        overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        overall_f1 = 2 * (overall_precision * overall_recall) / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0
        overall_fpr = total_fp / (total_fp + total_tn) if (total_fp + total_tn) > 0 else 0

        # 按事件类型统计
        by_type = defaultdict(list)
        for eval in evaluations:
            event_name = eval['event_name']
            event_type = event_name.split('-')[0]  # hijack, leak, outage
            by_type[event_type].append(eval)

        type_stats = {}
        for event_type, type_evals in by_type.items():
            type_detected = sum(1 for eval in type_evals if eval['detected_any_anomaly'])
            type_total = len(type_evals)

            type_tp = sum(eval['true_positives'] for eval in type_evals)
            type_fp = sum(eval['false_positives'] for eval in type_evals)
            type_fn = sum(eval['false_negatives'] for eval in type_evals)

            type_precision = type_tp / (type_tp + type_fp) if (type_tp + type_fp) > 0 else 0
            type_recall = type_tp / (type_tp + type_fn) if (type_tp + type_fn) > 0 else 0

            type_stats[event_type] = {
                'total_events': type_total,
                'detected_events': type_detected,
                'detection_rate': type_detected / type_total if type_total > 0 else 0,
                'precision': type_precision,
                'recall': type_recall,
                'avg_fpr': np.mean([eval['false_positive_rate'] for eval in type_evals])
            }

        # 生成报告数据
        report_data = {
            'generation_time': datetime.now().isoformat(),
            'system_config': {
                'baseline_days': self.baseline_days,
                'collector': self.collector,
                'work_dir': str(self.work_dir)
            },
            'overall_statistics': {
                'total_events': total_events,
                'successful_detections': successful_detections,
                'overall_detection_rate': successful_detections / total_events if total_events > 0 else 0,
                'overall_accuracy': overall_accuracy,
                'overall_precision': overall_precision,
                'overall_recall': overall_recall,
                'overall_f1_score': overall_f1,
                'overall_false_positive_rate': overall_fpr,
                'confusion_matrix': {
                    'true_positives': total_tp,
                    'false_positives': total_fp,
                    'true_negatives': total_tn,
                    'false_negatives': total_fn
                }
            },
            'by_event_type': type_stats,
            'individual_results': evaluations
        }

        # 保存JSON报告
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)

        # 生成简要文本报告
        summary_file = report_dir / f"detection_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

        with open(summary_file, 'w') as f:
            f.write("BGP异常事件检测系统 - 性能评估报告\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"基线天数: {self.baseline_days} 天\n")
            f.write(f"采集点: {self.collector}\n\n")

            f.write("总体性能指标:\n")
            f.write("-" * 20 + "\n")
            f.write(f"总事件数: {total_events}\n")
            f.write(f"成功检测事件数: {successful_detections}\n")
            f.write(f"总体检测率: {successful_detections/total_events*100:.1f}%\n")
            f.write(f"总体准确率: {overall_accuracy*100:.1f}%\n")
            f.write(f"总体精确率: {overall_precision*100:.1f}%\n")
            f.write(f"总体召回率: {overall_recall*100:.1f}%\n")
            f.write(f"总体F1分数: {overall_f1*100:.1f}%\n")
            f.write(f"总体误报率: {overall_fpr*100:.1f}%\n\n")

            f.write("按事件类型统计:\n")
            f.write("-" * 20 + "\n")
            for event_type, stats in type_stats.items():
                f.write(f"{event_type.upper()}:\n")
                f.write(f"  事件数: {stats['total_events']}\n")
                f.write(f"  检测数: {stats['detected_events']}\n")
                f.write(f"  检测率: {stats['detection_rate']*100:.1f}%\n")
                f.write(f"  精确率: {stats['precision']*100:.1f}%\n")
                f.write(f"  召回率: {stats['recall']*100:.1f}%\n")
                f.write(f"  平均误报率: {stats['avg_fpr']*100:.1f}%\n\n")

            f.write("详细结果:\n")
            f.write("-" * 20 + "\n")
            for eval in evaluations:
                f.write(f"{eval['event_name']}:\n")
                f.write(f"  检测到异常: {'是' if eval['detected_any_anomaly'] else '否'}\n")
                f.write(f"  真实异常分钟数: {eval['total_true_anomalies']}\n")
                f.write(f"  检测异常分钟数: {eval['total_detected_anomalies']}\n")
                f.write(f"  准确率: {eval['accuracy']*100:.1f}%\n")
                f.write(f"  精确率: {eval['precision']*100:.1f}%\n")
                f.write(f"  召回率: {eval['recall']*100:.1f}%\n")
                f.write(f"  误报率: {eval['false_positive_rate']*100:.1f}%\n")

                # 添加时间窗口分析
                if 'time_window_analysis' in eval:
                    twa = eval['time_window_analysis']
                    f.write(f"  时间窗口分析:\n")
                    f.write(f"    实际事件时间: {twa.get('actual_start', 'N/A')} - {twa.get('actual_end', 'N/A')}\n")
                    f.write(f"    实际持续时间: {twa.get('actual_duration_hours', 0):.1f} 小时\n")
                    if twa.get('detected_start'):
                        f.write(f"    检测事件时间: {twa['detected_start']} - {twa['detected_end']}\n")
                        f.write(f"    检测持续时间: {twa.get('detected_duration_hours', 0):.1f} 小时\n")
                        f.write(f"    时间覆盖率: {twa.get('coverage_rate', 0)*100:.1f}%\n")
                        if twa.get('detection_delay_minutes', 0) > 0:
                            f.write(f"    检测延迟: {twa['detection_delay_minutes']:.1f} 分钟\n")
                        elif twa.get('detection_early_minutes', 0) > 0:
                            f.write(f"    提前检测: {twa['detection_early_minutes']:.1f} 分钟\n")
                    else:
                        f.write(f"    检测事件时间: 未检测到异常\n")
                f.write("\n")

        print(f"✅ 报告已生成:")
        print(f"   详细报告: {report_file}")
        print(f"   简要报告: {summary_file}")

        # 打印简要统计
        print(f"\n📊 检测结果概览:")
        print(f"   总事件数: {total_events}")
        print(f"   成功检测: {successful_detections} ({successful_detections/total_events*100:.1f}%)")
        print(f"   总体精确率: {overall_precision*100:.1f}%")
        print(f"   总体召回率: {overall_recall*100:.1f}%")
        print(f"   总体误报率: {overall_fpr*100:.1f}%")

    def run_full_analysis(self, max_events=None, skip_download=False, target_event=None):
        """运行完整分析"""
        print("🚀 开始BGP异常事件检测分析")
        print("=" * 50)

        # 加载事件信息
        events = self.load_event_info()

        # 如果指定了目标事件，只分析该事件
        if target_event:
            events = [event for event in events if event['event_name'] == target_event]
            if not events:
                print(f"❌ 未找到目标事件: {target_event}")
                return []
            print(f"🎯 专门分析事件: {target_event}")

        if max_events:
            events = events[:max_events]
            print(f"🔢 限制分析事件数量: {max_events}")

        evaluations = []

        for i, event in enumerate(events, 1):
            event_name = event['event_name']
            print(f"\n📍 处理事件 {i}/{len(events)}: {event_name}")
            print("-" * 40)

            # 检查事件数据是否存在
            if not self.check_event_data_exists(event_name):
                print(f"   ⚠️  事件数据不完整，跳过")
                continue

            try:
                # 下载基线数据
                if not skip_download:
                    if not self.download_baseline_data(event):
                        print(f"   ❌ 基线数据下载失败，跳过")
                        continue
                else:
                    print(f"   ⏭️  跳过数据下载")

                # 构建DetectTrie
                detect_trie_file = self.build_detect_trie(event)
                if not detect_trie_file:
                    print(f"   ❌ DetectTrie构建失败，跳过")
                    continue

                # 检测异常
                detection_result = self.detect_anomalies(event, detect_trie_file)
                if not detection_result:
                    print(f"   ❌ 异常检测失败，跳过")
                    continue

                # 加载真实标注
                ground_truth = self.load_ground_truth(event_name)
                if not ground_truth:
                    print(f"   ❌ 真实标注加载失败，跳过")
                    continue

                # 评估性能
                evaluation = self.evaluate_performance(detection_result, ground_truth)
                if evaluation:
                    evaluations.append(evaluation)
                    print(f"   ✅ 事件分析完成")
                    print(f"   📊 检测到异常: {'是' if evaluation['detected_any_anomaly'] else '否'}")
                    print(f"   📊 精确率: {evaluation['precision']*100:.1f}%")
                    print(f"   📊 召回率: {evaluation['recall']*100:.1f}%")
                else:
                    print(f"   ❌ 性能评估失败")

            except Exception as e:
                print(f"   ❌ 事件处理异常: {e}")
                continue

        # 生成报告
        if evaluations:
            print(f"\n📋 分析完成，共处理 {len(evaluations)} 个事件")
            self.generate_report(evaluations)
        else:
            print(f"\n⚠️  没有成功处理的事件")

        return evaluations

    def _save_specific_event_analysis(self, event, anomaly_details, event_data_dir):
        """保存特定事件的详细分析"""
        event_name = event['event_name']

        # 针对已知的劫持事件进行特殊分析
        if 'hijack' in event_name.lower():
            # 尝试从事件名称中提取劫持信息
            hijack_analysis = self._analyze_hijack_event(event, anomaly_details)
            if hijack_analysis:
                analysis_file = event_data_dir / "hijack_event_analysis.txt"
                try:
                    with open(analysis_file, 'w', encoding='utf-8') as f:
                        f.write(f"劫持事件详细分析 - {event_name}\n")
                        f.write("=" * 80 + "\n\n")
                        f.write(f"事件类型: {event.get('event_type', 'hijack')}\n")
                        f.write(f"受害AS: {event.get('victim_as', 'N/A')}\n")
                        f.write(f"劫持AS: {event.get('hijacker_as', 'N/A')}\n")
                        f.write(f"劫持前缀: {event.get('hijacked_prefix', 'N/A')}\n")
                        f.write(f"检测到的异常数量: {len(anomaly_details)}\n\n")

                        if hijack_analysis['target_anomalies']:
                            f.write(f"目标劫持检测结果:\n")
                            f.write(f"检测到 {len(hijack_analysis['target_anomalies'])} 个相关异常\n\n")

                            for i, anomaly in enumerate(hijack_analysis['target_anomalies'], 1):
                                f.write(f"相关异常 #{i}:\n")
                                f.write(f"  时间: {anomaly['time_str']}\n")
                                f.write(f"  前缀: {anomaly['prefix']}\n")
                                f.write(f"  宣告AS: {anomaly['announced_as']}\n")
                                f.write(f"  注册AS: {', '.join(anomaly['registered_as_set'])}\n")
                                f.write(f"  AS路径: {anomaly.get('as_path', 'N/A')}\n")
                                f.write("-" * 40 + "\n")
                        else:
                            f.write("⚠️  未检测到与已知劫击信息匹配的异常\n")
                            f.write("这可能表明:\n")
                            f.write("1. 检测系统存在假阴性\n")
                            f.write("2. 基线数据不完整\n")
                            f.write("3. 需要更复杂的检测算法\n")

                    print(f"✅ 保存劫持事件分析: {analysis_file}")
                except Exception as e:
                    print(f"❌ 劫持事件分析保存失败: {e}")

    def _analyze_hijack_event(self, event, anomaly_details):
        """分析劫持事件的具体情况"""
        event_name = event['event_name']

        # 从事件信息中提取劫持相关信息
        victim_as = event.get('victim_as')
        hijacker_as = event.get('hijacker_as')
        hijacked_prefix = event.get('hijacked_prefix')

        # 如果事件信息中没有这些字段，尝试从事件名称解析
        if not hijacker_as and 'hijack' in event_name.lower():
            # 对于特定的已知事件，硬编码一些信息
            if 'BackConnect_hijacked_VolumeDrive' in event_name:
                hijacker_as = '203959'
                hijacked_prefix = '172.81.129.0/24'
                victim_as = '48884'

        # 查找与劫持相关的异常
        target_anomalies = []
        if hijacker_as or hijacked_prefix:
            for anomaly in anomaly_details:
                is_target = False
                if hijacker_as and anomaly['announced_as'] == hijacker_as:
                    is_target = True
                if hijacked_prefix and anomaly['prefix'] == hijacked_prefix:
                    is_target = True
                if is_target:
                    target_anomalies.append(anomaly)

        return {
            'victim_as': victim_as,
            'hijacker_as': hijacker_as,
            'hijacked_prefix': hijacked_prefix,
            'target_anomalies': target_anomalies
        }


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="BGP异常检测系统")
    parser.add_argument("--event-name", type=str, help="指定要分析的事件名称")
    parser.add_argument("--list-events", action="store_true", help="列出所有可用事件")
    parser.add_argument("--max-events", type=int, help="限制处理的事件数量")
    parser.add_argument("--skip-download", action="store_true", help="跳过数据下载（使用已有数据）")
    parser.add_argument("--work-dir", default="/data/bgp_analysis", help="工作目录")
    parser.add_argument("--start-from-step", type=int, choices=[1, 2, 3], default=1,
                       help="从指定步骤开始: 1=构建IPTrie, 2=计算不确定度, 3=构建DetectTrie")
    parser.add_argument("--only-step", type=int, choices=[1, 2, 3],
                       help="只执行指定步骤: 1=构建IPTrie, 2=计算不确定度, 3=构建DetectTrie")

    args = parser.parse_args()

    # 创建检测系统
    system = BGPAnomalyDetectionSystem(work_dir=args.work_dir)

    # 如果要求列出事件
    if args.list_events:
        system.list_available_events()
        return

    # 运行分析
    if args.event_name:
        print(f"🎯 分析指定事件: {args.event_name}")
        evaluations = system.run_full_analysis(
            max_events=1,
            skip_download=args.skip_download,
            target_event=args.event_name
        )
    else:
        print("🔄 分析所有事件")
        evaluations = system.run_full_analysis(
            max_events=args.max_events,
            skip_download=args.skip_download
        )

    if evaluations:
        print(f"\n✅ 分析完成，处理了 {len(evaluations)} 个事件")
    else:
        print("\n❌ 未找到匹配的事件或分析失败")


if __name__ == "__main__":
    main()
