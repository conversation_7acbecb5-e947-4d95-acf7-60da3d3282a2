
CC	= @CC@ -fPIC
CFLAGS	= @CFLAGS@
COMPILE  = $(CC) $(CFLAGS) $(CPPFLAGS) $(INCLUDES)

LD	= @CC@
LDFLAGS	= @LDFLAGS@
SOFLAGS = @SOFLAGS@
RANLIB	= @RANLIB@

SYS_LIBS= @LIBS@

INSTALL  = install

prefix   = @prefix@
exec_prefix = @exec_prefix@
bindir   = @bindir@
libdir   = @libdir@
includedir = @includedir@

LIB_H	 = bgpdump_attr.h bgpdump_formats.h bgpdump_lib.h bgpdump_mstream.h
LIB_O	 = bgpdump_lib.o bgpdump_mstream.o cfile_tools.o util.o inet_ntop.o
OTHER    = *.in configure bgpdump.spec README* ChangeLog COPYING*

all: libbgpdump.so bgpdump 

libbgpdump.a: $(LIB_H) $(LIB_O) Makefile cfile_tools.h util.h
	ar r libbgpdump.a $(LIB_O)
	$(RANLIB) libbgpdump.a

libbgpdump.so: libbgpdump.a
	$(COMPILE) $(LDFLAGS) $(SOFLAGS) -o libbgpdump.so $(LIB_O) $(SYS_LIBS)

example: example.c libbgpdump.a
	$(COMPILE) $(LDFLAGS) -o example example.c libbgpdump.a $(SYS_LIBS)

bgpdump: bgpdump.c libbgpdump.a
	$(COMPILE) $(LDFLAGS) -o bgpdump bgpdump.c libbgpdump.a $(SYS_LIBS)

check-clean:
	rm -f test_out/*.bgp.gz

check: check-clean bgpdump
	./test.sh

clean: check-clean
	rm -f libbgpdump.so libbgpdump.a example bgpdump $(LIB_O)

distclean: clean
	rm -Rf config.log config.status *.dSYM core *.core autom4te.cache bgpdump-config.h Makefile
	rm -Rf $(PKG)

install: all
	$(INSTALL) -d $(DESTDIR)$(bindir) $(DESTDIR)$(includedir) $(DESTDIR)$(libdir)
	$(INSTALL) bgpdump $(DESTDIR)$(bindir)
	$(INSTALL) -m 0644 $(LIB_H) $(DESTDIR)$(includedir)
	$(INSTALL) libbgpdump.so libbgpdump.a $(DESTDIR)$(libdir)

PKG=@PACKAGE_NAME@-@PACKAGE_VERSION@
dist:
	mkdir $(PKG)
	ln *.h *.c $(OTHER) $(PKG)
	tar -czf $(PKG).tgz $(PKG)
	rm $(PKG)/* && rmdir $(PKG)

rpm: dist
	rpmbuild -v -ta $(PKG).tgz
