name: CodeQL analysis

on:
  push:
  pull_request:
  schedule:
    # build the master branch every Monday morning
    - cron: '4 6 * * 1'
  workflow_dispatch:

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    strategy:
      fail-fast: false
      matrix:
        language: [ 'cpp' ]
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    - name: Install application build dependencies
      run: sudo apt-get install libbz2-dev zlib1g-dev
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: ${{ matrix.language }}
    - name: Build Application using script
      run: |
        ./bootstrap.sh
        make
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
      with:
        category: "/language:${{matrix.language}}"
