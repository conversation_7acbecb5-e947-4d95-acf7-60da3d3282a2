dnl Process this file with autoconf to produce a configure script.
AC_REVISION($Revision$)

AC_INIT([libbgpdump], 1.6.2, [<EMAIL>])
AC_CONFIG_SRCDIR([bgpdump_lib.c])
AC_CONFIG_HEADERS([bgpdump-config.h])

CFLAGS="-g $CFLAGS -Wall"
CFLAGS="$CFLAGS -Wsystem-headers -Wno-format-y2k -Wno-sign-compare -Wcast-align"
CFLAGS="$CFLAGS -Wmissing-prototypes -Wpointer-arith -Wreturn-type -Wswitch -Wshadow"

# Checks for programs.
AC_PROG_CC
AC_PROG_RANLIB

# Checks for header files.
AC_CHECK_HEADERS([arpa/inet.h netinet/in.h syslog.h])
AC_STRUCT_TM

# Check for u_*_t
AC_CHECK_TYPE(u_char_t, , AC_DEFINE(u_char_t, uchar_t, [Define if system headers do not define u_char_t]))
AC_CHECK_TYPE(u_int8_t, , AC_DEFINE(u_int8_t, uint8_t, [Define if system headers do not define u_int8_t]))
AC_CHECK_TYPE(u_int16_t, , AC_DEFINE(u_int16_t, uint16_t, [Define if system headers do not define u_int16_t]))
AC_CHECK_TYPE(u_int32_t, , AC_DEFINE(u_int32_t, uint32_t, [Define if system headers do not define u_int32_t]))

AC_CHECK_LIB(z, gzopen, [], AC_MSG_ERROR([libz not found],1))
AC_CHECK_LIB(bz2, BZ2_bzReadOpen, [], AC_MSG_ERROR([libbzip2 not found],1))

# check for strlcat, used to not be in Linux but is now appearing in newer glibc
AC_CHECK_FUNCS([strlcat])

# Check for inet_ntoa in -lnsl if not found (Solaris)
AC_CHECK_FUNCS(inet_ntoa, [], AC_CHECK_LIB(nsl, inet_ntoa, [], AC_MSG_ERROR([inet_ntoa not found],1)))
AC_CHECK_FUNCS(inet_ntop, [], AC_CHECK_LIB(nsl, inet_ntop, [], AC_MSG_ERROR([inet_ntop not found],1)))

case `uname` in
	Darwin*) SOFLAGS="$SOFLAGS -dynamiclib" ;;
	*)       SOFLAGS="$SOFLAGS -shared" ;;
esac

AC_SUBST(CFLAGS)
AC_SUBST(LIBS)
AC_SUBST(SOFLAGS)

AC_OUTPUT([bgpdump.spec Makefile])
