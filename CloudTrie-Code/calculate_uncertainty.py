from collections import defaultdict
from IPTrie import IPTrie, default_source_dict, default_list, default_peer_dict, TrieNode
import numpy as np
def generate_t_s_cloud_drops(ex, en, he, num_drops=1000):
    """生成时间-空间二维正态云滴"""
    drops = []
    for _ in range(num_drops):
        # 生成非负熵值
        en_time = abs(np.random.normal(en[0], he[0]))  # 取绝对值保证非负
        en_space = abs(np.random.normal(en[1], he[1]))
        
        # 生成云滴坐标
        x = np.random.normal(ex[0], max(en_time, 1e-6))  # 最小标准差保护
        y = np.random.normal(ex[1], max(en_space, 1e-6))
        
        # 计算隶属度（加入平滑处理）
        mu = 1-np.exp(-(
            (x - ex[0])**2 / (2 * (en_time**2 + 1e-6)) + 
            (y - ex[1])**2 / (2 * (en_space**2 + 1e-6))
        ))
        mu = np.clip(mu, 0, 1)  # 限制在[0,1]范围
        drops.append((x, y, mu))
    return np.array(drops)

def generate_t_m_cloud_drops(ex, en, he, num_drops=1000):
    """生成时间-数据源二维正态云滴"""
    drops = []
    for _ in range(num_drops):
        # 生成非负熵值
        en_time = abs(np.random.normal(en[0], he[0]))  # 取绝对值保证非负
        en_space = abs(np.random.normal(en[2], he[2]))
        
        # 生成云滴坐标
        x = np.random.normal(ex[0], max(en_time, 1e-6))  # 最小标准差保护
        y = np.random.normal(ex[2], max(en_space, 1e-6))
        
        # 计算隶属度（加入平滑处理）
        mu = 1-np.exp(-(
            (x - ex[0])**2 / (2 * (en_time**2 + 1e-6)) + 
            (y - ex[2])**2 / (2 * (en_space**2 + 1e-6))
        ))
        mu = np.clip(mu, 0, 1)  # 限制在[0,1]范围
        drops.append((x, y, mu))
    return np.array(drops)

def generate_s_m_cloud_drops(ex, en, he, num_drops=1000):
    """生成空间-数据源二维正态云滴"""
    drops = []
    for _ in range(num_drops):
        # 生成非负熵值
        en_time = abs(np.random.normal(en[1], he[1]))  # 取绝对值保证非负
        en_space = abs(np.random.normal(en[2], he[2]))
        
        # 生成云滴坐标
        x = np.random.normal(ex[1], max(en_time, 1e-6))  # 最小标准差保护
        y = np.random.normal(ex[2], max(en_space, 1e-6))
        
        # 计算隶属度（加入平滑处理）
        mu = 1-np.exp(-(
            (x - ex[1])**2 / (2 * (en_time**2 + 1e-6)) + 
            (y - ex[2])**2 / (2 * (en_space**2 + 1e-6))
        ))
        mu = np.clip(mu, 0, 1)  # 限制在[0,1]范围
        drops.append((x, y, mu))
    return np.array(drops)
def collect_po_pairs(node, current_prefix=""):
    results = []
    if node.is_end_of_prefix:
        # 提取P/O对信息
        for source_as in node.sources:
            for source_type in node.sources[source_as]:
                # 时间持续性数据：5天周期的宣告状态
                time_data = node.sources[source_as][source_type]
                # 空间一致性数据：观测点（Peer AS）列表
                peers = list(node.peers[source_as].keys())
                results.append({
                    "prefix": current_prefix,
                    "source_as": source_as,
                    "source_type": source_type,
                    "time_announced": time_data,
                    "peers": peers
                })
    for bit in ['0', '1']:
        child = node.children[bit]
        if child:
            results += collect_po_pairs(child, current_prefix + bit)
    return results
def calculate_time_persistence(time_announced):
    weights = [1.0,0.8, 0.6, 0.4, 0.2]  # 5天周期的线性递减权重
    weighted_sum = sum(h * w for h, w in zip(time_announced, weights))
    total_h = sum(time_announced)
    return weighted_sum / total_h if total_h != 0 else 0
def calculate_space_consistency(peers):
    # 假设每个Peer AS的权重为1
    return len(peers)
def calculate_membership(source_type):
    # 可信度分配
    weights = {'RPKI': 0.9, 'IRR': 0.7, 'RIB': 0.6}
    return weights.get(source_type, 0.5)

def bootstrap_he(scores, n_bootstrap=1000):
    # 生成Bootstrap样本并计算En的分布
    en_samples = []
    n = len(scores)
    for _ in range(n_bootstrap):
        sample = np.random.choice(scores, size=n, replace=True)
        en = np.std(sample)
        en_samples.append(en)
    # He是En样本的标准差
    return np.std(en_samples)
def calculate_prefix_uncertainty(trie, target_prefix, num_drops=1000):
    """根据前缀计算不确定度"""
    current_node = trie.root
    for bit in target_prefix:
        if bit not in ['0', '1']:
            raise ValueError(f"Invalid prefix character: {bit}")
        if not current_node.children[bit]:
            continue
        current_node = current_node.children[bit]
    
    po_pairs = []
    if current_node.is_end_of_prefix:
        for source_as in current_node.sources:
            for source_type in current_node.sources[source_as]:
                time_data = current_node.sources[source_as][source_type]
                peers = list(current_node.peers[source_as].keys())
                po_pairs.append({
                    "time_announced": time_data,
                    "peers": peers,
                    "source_type": source_type
                })

    if not po_pairs:
        return 0.0
    
    # 计算各维度指标
    time_scores = [calculate_time_persistence(po["time_announced"]) for po in po_pairs]
    space_scores = [calculate_space_consistency(po["peers"]) for po in po_pairs]
    member_scores = [calculate_membership(po["source_type"]) for po in po_pairs]
    
    # 计算云模型参数
    ex = (np.mean(time_scores), np.mean(space_scores), np.mean(member_scores))
    en = (np.std(time_scores), np.std(space_scores), np.std(member_scores))
    he = (bootstrap_he(time_scores), bootstrap_he(space_scores), bootstrap_he(member_scores))

    t_s_drops = generate_t_s_cloud_drops(ex, en, he, num_drops)
    t_m_drops = generate_t_m_cloud_drops(ex, en, he, num_drops)
    s_m_drops = generate_s_m_cloud_drops(ex, en, he, num_drops)

    avg_uncertainty = np.mean([
        t_s_drops[:,2].mean(),
        t_m_drops[:,2].mean(),
        s_m_drops[:,2].mean()
    ])
    
    return round(avg_uncertainty, 4)

# 使用示例
# trie = IPTrie.load_from_file('/IPTrie/trie_12_1.dat')
# prefixes = ['************/24']  # 示例前缀

# for prefix in prefixes:
#     uncertainty = calculate_prefix_uncertainty(trie, prefix)
#     print(f"Prefix: {prefix} => Uncertainty: {uncertainty}")