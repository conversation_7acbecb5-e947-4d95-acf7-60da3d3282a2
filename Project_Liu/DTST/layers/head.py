import torch
from torch import nn


class ClassificationHead(nn.Module):
    """
    Classification head for BGP anomaly detection
    """
    def __init__(self, n_vars, nf, num_classes, head_dropout=0):
        super().__init__()
        
        self.n_vars = n_vars
        self.num_classes = num_classes
        
        # Global average pooling to aggregate temporal information
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # Classification layers
        self.classifier = nn.Sequential(
            nn.Flatten(),
            nn.Linear(nf, 256),
            nn.ReLU(),
            nn.Dropout(head_dropout),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(head_dropout),
            nn.Linear(128, num_classes)
        )

    def forward(self, x):
        # x: (B, n_vars, D, N) where D is feature dim, N is sequence length
        B, n_vars, D, N = x.shape
        
        # Reshape to (B*n_vars, D, N)
        x = x.view(B * n_vars, D, N)
        
        # Global pooling: (B*n_vars, D, N) -> (B*n_vars, D, 1)
        x = self.global_pool(x)
        
        # Reshape back: (B*n_vars, D, 1) -> (B, n_vars*D)
        x = x.view(B, n_vars * D)
        
        # Classification
        x = self.classifier(x)
        
        return x


class Flatten_Head(nn.Module):
    """
    Original flatten head from DeformableTST, adapted for classification
    """
    def __init__(self, individual, n_vars, nf, target_window, head_dropout=0, num_classes=2):
        super().__init__()

        self.individual = individual
        self.n_vars = n_vars
        self.num_classes = num_classes

        if self.individual:
            self.linears = nn.ModuleList()
            self.dropouts = nn.ModuleList()
            self.flattens = nn.ModuleList()
            for i in range(self.n_vars):
                self.flattens.append(nn.Flatten(start_dim=-2))
                self.linears.append(nn.Linear(nf, num_classes))
                self.dropouts.append(nn.Dropout(head_dropout))
        else:
            self.flatten = nn.Flatten(start_dim=-2)
            self.linear = nn.Linear(nf, num_classes)
            self.dropout = nn.Dropout(head_dropout)

    def forward(self, x):
        if self.individual:
            x_out = []
            for i in range(self.n_vars):
                z = self.flattens[i](x[:, i, :, :])
                z = self.linears[i](z)
                z = self.dropouts[i](z)
                x_out.append(z)
            x = torch.stack(x_out, dim=1)
            # Average predictions across variables
            x = x.mean(dim=1)
        else:
            x = self.flatten(x)
            x = self.linear(x)
            x = self.dropout(x)
        return x
