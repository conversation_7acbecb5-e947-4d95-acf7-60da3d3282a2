import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import einops
from timm.models.layers import to_2tuple, trunc_normal_
try:
    from .Transformer_Module import LayerNormProxy
except ImportError:
    from Transformer_Module import LayerNormProxy


class DAttentionBaseline(nn.Module):
    """
    Deformable Attention mechanism for time series data
    """
    def __init__(
            self, q_size, kv_size, n_heads, n_head_channels, n_groups,
            attn_drop, proj_drop, stride,
            offset_range_factor, use_pe, dwc_pe,
            no_off, fixed_pe, ksize, log_cpb
    ):
        super().__init__()
        
        self.n_head_channels = n_head_channels
        self.scale = self.n_head_channels ** -0.5
        self.n_heads = n_heads
        
        self.q_size = q_size
        self.kv_size = self.q_size // stride
        self.nc = n_head_channels * n_heads
        
        # Position encoding parameters
        self.use_pe = use_pe
        self.fixed_pe = fixed_pe
        self.log_cpb = log_cpb
        self.dwc_pe = dwc_pe
        
        # Deformable attention parameters
        self.no_off = no_off
        self.offset_range_factor = offset_range_factor
        
        self.n_groups = n_groups
        self.n_group_channels = self.nc // self.n_groups
        self.n_group_heads = self.n_heads // self.n_groups
        
        self.stride = stride
        self.ksize = ksize
        kk = self.ksize
        pad_size = kk // 2 if kk != stride else 0
        
        # Offset learning network
        self.conv_offset = nn.Sequential(
            nn.Conv1d(self.n_group_channels, self.n_group_channels, kk, stride, pad_size, 
                     groups=self.n_group_channels),
            LayerNormProxy(self.n_group_channels),
            nn.GELU(),
            nn.Conv1d(self.n_group_channels, 1, 1, 1, 0, bias=False)
        )
        if self.no_off:
            for m in self.conv_offset.parameters():
                m.requires_grad_(False)
        
        # Query, Key, Value projections
        self.proj_q = nn.Conv1d(self.nc, self.nc, kernel_size=1, stride=1, padding=0)
        self.proj_k = nn.Conv1d(self.nc, self.nc, kernel_size=1, stride=1, padding=0)
        self.proj_v = nn.Conv1d(self.nc, self.nc, kernel_size=1, stride=1, padding=0)
        self.proj_out = nn.Conv1d(self.nc, self.nc, kernel_size=1, stride=1, padding=0)
        
        # Dropout layers
        self.proj_drop = nn.Dropout(proj_drop, inplace=False)
        self.attn_drop = nn.Dropout(attn_drop, inplace=False)
        
        # Relative position encoding
        if self.use_pe and not self.no_off:
            if self.dwc_pe:
                self.rpe_table = nn.Conv1d(
                    self.nc, self.nc, kernel_size=3, stride=1, padding=1, groups=self.nc)
            elif self.fixed_pe:
                self.rpe_table = nn.Parameter(
                    torch.zeros(self.n_heads, self.q_size, self.kv_size)
                )
                trunc_normal_(self.rpe_table, std=0.01)
            elif self.log_cpb:
                self.rpe_table = nn.Sequential(
                    nn.Linear(1, 32, bias=True),
                    nn.ReLU(inplace=False),
                    nn.Linear(32, self.n_group_heads, bias=False)
                )
            else:
                self.rpe_table = nn.Parameter(
                    torch.zeros(self.n_heads, self.q_size * 2 - 1, 1)
                )
                trunc_normal_(self.rpe_table, std=0.01)
        else:
            self.rpe_table = None

    @torch.no_grad()
    def _get_ref_points(self, L_key, B, dtype, device):
        """Generate reference points for deformable attention"""
        ref = torch.linspace(0.5, L_key - 0.5, L_key, dtype=dtype, device=device)
        ref = ref / (L_key - 1.0) * 2.0 - 1.0  # Avoid inplace operations
        ref = ref[None, :, None, None].expand(B * self.n_groups, -1, -1, -1)
        return ref

    @torch.no_grad()
    def _get_q_grid(self, L, B, dtype, device):
        """Generate query grid for position encoding"""
        ref = torch.arange(0, L, dtype=dtype, device=device)
        ref = ref / (L - 1.0) * 2.0 - 1.0  # Avoid inplace operations
        ref = ref[None, :, None, None].expand(B * self.n_groups, -1, -1, -1)
        return ref

    def forward(self, x):
        B, C, L = x.size()
        dtype, device = x.dtype, x.device
        
        # Generate Q, K, V
        q = self.proj_q(x)
        k = self.proj_k(x)
        v = self.proj_v(x)
        
        # Learn deformable offsets
        q_off = einops.rearrange(q, 'b (g c) n -> (b g) c n', g=self.n_groups, c=self.n_group_channels)
        offset = self.conv_offset(q_off).contiguous()
        Lk = offset.size(2)
        n_sample = Lk
        
        # Apply offset range factor
        if self.offset_range_factor >= 0 and not self.no_off:
            # Avoid division by zero when Lk = 1
            if Lk > 1:
                offset_range = torch.tensor([1.0 / (Lk - 1.0)], device=device).reshape(1, 1, 1)
            else:
                offset_range = torch.tensor([1.0], device=device).reshape(1, 1, 1)
            offset = offset.tanh().mul(offset_range).mul(self.offset_range_factor)
        
        offset = einops.rearrange(offset, 'b p n -> b n p')
        reference = self._get_ref_points(Lk, B, dtype, device)
        offset = offset.unsqueeze(-2)
        
        if self.no_off:
            offset = offset.fill_(0.0)
        
        if self.offset_range_factor >= 0:
            pos = offset + reference
        else:
            pos = (offset + reference).clamp(-1., +1.)
        
        # Sample features using deformable positions
        if self.no_off:
            x_sampled = F.avg_pool1d(x, kernel_size=self.stride, stride=self.stride)
            assert x_sampled.size(2) == Lk, f"Size is {x_sampled.size()}"
        else:
            pos_2 = pos.repeat(1,1,1,2)
            pos_2[... , 1] = 0
            x_sampled = F.grid_sample(
                input=x.reshape(B * self.n_groups, self.n_group_channels, L, 1),
                grid=pos_2,
                mode='bilinear', align_corners=True)
            x_sampled = x_sampled.reshape(B, C, n_sample)
        
        # Reshape for multi-head attention
        q = q.reshape(B * self.n_heads, self.n_head_channels, L)
        k = x_sampled.reshape(B * self.n_heads, self.n_head_channels, n_sample)
        v = x_sampled.reshape(B * self.n_heads, self.n_head_channels, n_sample)
        
        # Compute attention scores with numerical stability
        attn = torch.einsum('b c m, b c n -> b m n', q, k).mul(self.scale)

        # Add relative position encoding if enabled
        if self.use_pe and not self.no_off:
            if self.dwc_pe:
                residual_lepe = self.rpe_table(q.reshape(B, C, L)).reshape(B * self.n_heads, self.n_head_channels, L)
            # Add other position encoding variants here if needed

        # Clamp attention scores to prevent overflow
        attn = torch.clamp(attn, min=-10.0, max=10.0)

        # Apply softmax and dropout
        attn = F.softmax(attn, dim=2)
        attn = self.attn_drop(attn)
        
        # Compute output
        out = torch.einsum('b m n, b c n -> b c m', attn, v)
        
        # Add position encoding to output if enabled
        if self.use_pe and self.dwc_pe:
            out = out + residual_lepe
        out = out.reshape(B, C, L)
        
        # Final projection
        y = self.proj_drop(self.proj_out(out))
        
        return y, pos.reshape(B, self.n_groups, Lk, 1), reference.reshape(B, self.n_groups, Lk, 1)
