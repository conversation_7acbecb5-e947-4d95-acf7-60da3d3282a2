import torch
import torch.nn as nn
import torch.nn.functional as F
import einops


class PatchEmbedding(nn.Module):
    """
    Patch embedding layer for time series data
    
    This layer performs two main operations:
    1. Division: Split the input time series into non-overlapping patches
    2. Embedding: Map each patch to a high-dimensional vector
    
    Args:
        n_vars (int): Number of variables/features in the time series
        patch_size (int): Size of each patch (P)
        embed_dim (int): Embedding dimension (D_0)
        stride (int, optional): Stride for patching. If None, uses patch_size (non-overlapping)
        norm_layer (nn.Module, optional): Normalization layer to apply after embedding
    """
    
    def __init__(self, n_vars, patch_size, embed_dim, stride=None, norm_layer=None):
        super().__init__()
        
        self.n_vars = n_vars
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        self.stride = stride if stride is not None else patch_size
        
        # Patch embedding using 1D convolution
        # This efficiently implements both patching and embedding in one step
        self.patch_embed = nn.Conv1d(
            in_channels=n_vars,
            out_channels=embed_dim,
            kernel_size=patch_size,
            stride=self.stride,
            padding=0,
            bias=True
        )
        
        # Optional normalization
        self.norm = norm_layer(embed_dim) if norm_layer is not None else nn.Identity()
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize the weights of the patch embedding layer"""
        nn.init.xavier_uniform_(self.patch_embed.weight)
        if self.patch_embed.bias is not None:
            nn.init.zeros_(self.patch_embed.bias)
    
    def forward(self, x):
        """
        Forward pass of patch embedding

        Args:
            x (torch.Tensor): Input time series of shape (B, L, M)
                             where B=batch_size, L=sequence_length, M=n_vars

        Returns:
            torch.Tensor: Patch embeddings of shape (B, N_0, D_0)
                         where N_0=number_of_patches, D_0=embed_dim
        """
        B, L, M = x.shape

        # Handle incomplete patches by padding
        n_complete_patches = L // self.patch_size
        remainder = L % self.patch_size

        if remainder > 0:
            # Pad the sequence to make the last patch complete
            pad_length = self.patch_size - remainder
            padding = torch.zeros(B, pad_length, M, device=x.device, dtype=x.dtype)
            x = torch.cat([x, padding], dim=1)  # (B, L + pad_length, M)
            n_patches = n_complete_patches + 1
        else:
            n_patches = n_complete_patches

        # Transpose for 1D convolution: (B, effective_length, M) -> (B, M, effective_length)
        x = x.transpose(1, 2)  # (B, M, effective_length)

        # Apply patch embedding (convolution)
        # This simultaneously performs patching and embedding
        x = self.patch_embed(x)  # (B, embed_dim, N_0)

        # Apply normalization
        x = self.norm(x)

        # Transpose back: (B, embed_dim, N_0) -> (B, N_0, embed_dim)
        x = x.transpose(1, 2)  # (B, N_0, embed_dim)

        return x
    
    def get_num_patches(self, seq_len):
        """
        Calculate the number of patches for a given sequence length (including incomplete patches)

        Args:
            seq_len (int): Length of the input sequence

        Returns:
            int: Number of patches
        """
        import math
        return math.ceil(seq_len / self.patch_size)  # Include incomplete patches


class PositionalEncoding(nn.Module):
    """
    Positional encoding for patch sequences
    """
    
    def __init__(self, embed_dim, max_patches=1000, dropout=0.1):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.dropout = nn.Dropout(dropout)
        
        # Create positional encoding matrix
        pe = torch.zeros(max_patches, embed_dim)
        position = torch.arange(0, max_patches, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, embed_dim, 2).float() * 
                           (-torch.log(torch.tensor(10000.0)) / embed_dim))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        # Register as buffer (not a parameter, but part of the model state)
        self.register_buffer('pe', pe.unsqueeze(0))  # (1, max_patches, embed_dim)
    
    def forward(self, x):
        """
        Add positional encoding to patch embeddings
        
        Args:
            x (torch.Tensor): Patch embeddings of shape (B, N_0, embed_dim)
            
        Returns:
            torch.Tensor: Patch embeddings with positional encoding
        """
        B, N_0, _ = x.shape
        
        # Add positional encoding
        x = x + self.pe[:, :N_0, :]
        
        return self.dropout(x)


class PatchingModule(nn.Module):
    """
    Complete patching module that combines patch embedding and positional encoding
    """
    
    def __init__(self, n_vars, patch_size, embed_dim, stride=None, 
                 use_pos_encoding=True, pos_dropout=0.1, norm_layer=None):
        super().__init__()
        
        self.n_vars = n_vars
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        self.use_pos_encoding = use_pos_encoding
        
        # Patch embedding
        self.patch_embed = PatchEmbedding(
            n_vars=n_vars,
            patch_size=patch_size,
            embed_dim=embed_dim,
            stride=stride,
            norm_layer=norm_layer
        )
        
        # Positional encoding
        if use_pos_encoding:
            self.pos_encoding = PositionalEncoding(
                embed_dim=embed_dim,
                dropout=pos_dropout
            )
        else:
            self.pos_encoding = nn.Identity()
    
    def forward(self, x):
        """
        Forward pass of the complete patching module
        
        Args:
            x (torch.Tensor): Input time series of shape (B, L, M)
        
        Returns:
            torch.Tensor: Patch embeddings with positional encoding of shape (B, N_0, embed_dim)
        """
        # Apply patch embedding
        x = self.patch_embed(x)
        
        # Apply positional encoding
        x = self.pos_encoding(x)
        
        return x
    
    def get_num_patches(self, seq_len):
        """Get number of patches for a given sequence length"""
        return self.patch_embed.get_num_patches(seq_len)


if __name__ == "__main__":
    # Test the patching module
    print("🧪 Testing Patching Module")
    
    # Parameters
    batch_size = 4
    seq_len = 500
    n_vars = 26
    patch_size = 32  # Updated patch size
    embed_dim = 64
    
    # Create dummy input
    x = torch.randn(batch_size, seq_len, n_vars)
    print(f"Input shape: {x.shape}")
    
    # Create patching module
    patching = PatchingModule(
        n_vars=n_vars,
        patch_size=patch_size,
        embed_dim=embed_dim,
        use_pos_encoding=True
    )
    
    # Forward pass
    output = patching(x)
    print(f"Output shape: {output.shape}")
    
    # Calculate expected number of patches
    expected_patches = patching.get_num_patches(seq_len)
    print(f"Expected patches: {expected_patches}")
    print(f"Actual patches: {output.shape[1]}")
    
    print("✅ Patching module test completed!")
