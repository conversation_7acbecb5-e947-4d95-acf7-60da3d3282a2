"""
真正的事件级DTST模型
将38个事件作为patching，形成短序列进行注意力计算
"""

import torch
import torch.nn as nn
import pytorch_lightning as pl
from typing import List, Dict, Optional
import numpy as np

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class EventEmbedding(nn.Module):
    """
    事件嵌入层
    将单个事件的时间序列压缩成固定维度的向量
    """

    def __init__(self, n_features, embed_dim):
        super().__init__()

        self.n_features = n_features
        self.embed_dim = embed_dim

        # 使用1D卷积进行事件级压缩
        # 这里使用全局平均池化的思想，但通过卷积实现
        self.conv_layers = nn.Sequential(
            nn.Conv1d(n_features, embed_dim // 2, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv1d(embed_dim // 2, embed_dim, kernel_size=3, padding=1),
            nn.ReLU(),
        )

        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)

        # 最终的线性层
        self.final_layer = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(embed_dim, embed_dim)
        )

    def forward(self, x):
        """
        前向传播

        Args:
            x: (batch_size, seq_len, n_features)

        Returns:
            embedding: (batch_size, embed_dim)
        """
        # 转换为卷积格式: (batch_size, n_features, seq_len)
        x = x.transpose(1, 2)

        # 卷积特征提取
        x = self.conv_layers(x)  # (batch_size, embed_dim, seq_len)

        # 全局平均池化
        x = self.global_pool(x)  # (batch_size, embed_dim, 1)
        x = x.squeeze(-1)  # (batch_size, embed_dim)

        # 最终变换
        x = self.final_layer(x)  # (batch_size, embed_dim)

        return x


class EventSequenceTransformer(nn.Module):
    """
    事件序列Transformer
    处理38个事件组成的序列
    """
    
    def __init__(self, embed_dim, num_heads=8, num_layers=4, dropout=0.1):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.num_layers = num_layers
        
        # Transformer编码器层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim,
            nhead=num_heads,
            dim_feedforward=embed_dim * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        
        self.transformer = nn.TransformerEncoder(
            encoder_layer,
            num_layers=num_layers
        )
        
        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(1, 50, embed_dim) * 0.02)  # 最多50个事件
        
    def forward(self, event_embeddings, mask=None):
        """
        前向传播
        
        Args:
            event_embeddings: (batch_size, num_events, embed_dim)
            mask: (batch_size, num_events) 可选的掩码
            
        Returns:
            attended_embeddings: (batch_size, num_events, embed_dim)
        """
        batch_size, num_events, embed_dim = event_embeddings.shape
        
        # 添加位置编码
        pos_emb = self.pos_encoding[:, :num_events, :]
        x = event_embeddings + pos_emb
        
        # Transformer编码
        attended = self.transformer(x, src_key_padding_mask=mask)
        
        return attended


class TrueEventDTST(pl.LightningModule):
    """
    真正的事件级DTST模型
    38个事件 → 事件嵌入 → 短序列 → 注意力 → 时间点预测
    """
    
    def __init__(self, config):
        super().__init__()
        
        self.config = config
        self.n_features = config.n_features
        self.embed_dim = config.embed_dim
        self.num_classes = config.num_classes
        self.learning_rate = config.learning_rate
        self.weight_decay = config.weight_decay
        
        # 事件嵌入器 - 将每个事件压缩成固定维度
        self.event_embedding = EventEmbedding(
            n_features=self.n_features,
            embed_dim=self.embed_dim
        )
        
        # 事件序列Transformer - 处理38个事件的序列
        self.event_transformer = EventSequenceTransformer(
            embed_dim=self.embed_dim,
            num_heads=config.heads[0] if hasattr(config, 'heads') else 8,
            num_layers=config.depths[0] if hasattr(config, 'depths') else 4,
            dropout=config.drop if hasattr(config, 'drop') else 0.1
        )
        
        # 时间点预测头 - 从事件嵌入预测该事件内的所有时间点
        self.time_predictor = nn.Sequential(
            nn.LayerNorm(self.embed_dim),
            nn.Dropout(config.head_dropout if hasattr(config, 'head_dropout') else 0.1),
            nn.Linear(self.embed_dim, 256),
            nn.GELU(),
            nn.Dropout(config.head_dropout if hasattr(config, 'head_dropout') else 0.1),
            nn.Linear(256, 128),
            nn.GELU(),
            nn.Dropout(config.head_dropout if hasattr(config, 'head_dropout') else 0.1),
            nn.Linear(128, 1)  # 输出单个时间点的异常概率
        )
        
        # 加权损失函数 - 寻找检测率和误报率的平衡点
        class_weights = torch.tensor([1.0, 20.0])  # [正常, 异常] - 使用中间值20倍
        self.criterion = nn.CrossEntropyLoss(weight=class_weights)
        
        # 用于存储验证结果
        self.validation_step_outputs = []
        
    def forward(self, all_events_data):
        """
        前向传播
        
        Args:
            all_events_data: List[Tensor] 所有事件的数据，长度为38
            
        Returns:
            all_predictions: List[Tensor] 每个事件的时间点预测
        """
        # 步骤1: 为每个事件生成嵌入
        event_embeddings = []
        for event in all_events_data:
            event = event.to(self.device)
            event_batch = event.unsqueeze(0)  # (1, seq_len, n_features)
            event_emb = self.event_embedding(event_batch)  # (1, embed_dim)
            event_embeddings.append(event_emb.squeeze(0))  # (embed_dim,)
        
        # 步骤2: 堆叠成事件序列
        event_sequence = torch.stack(event_embeddings, dim=0)  # (num_events, embed_dim)
        event_sequence = event_sequence.unsqueeze(0)  # (1, num_events, embed_dim)
        
        # 步骤3: 通过事件序列Transformer
        attended_events = self.event_transformer(event_sequence)  # (1, num_events, embed_dim)
        attended_events = attended_events.squeeze(0)  # (num_events, embed_dim)
        
        # 步骤4: 为每个事件预测其内部的时间点
        all_predictions = []
        for i, (event, event_emb) in enumerate(zip(all_events_data, attended_events)):
            seq_len = event.shape[0]
            
            # 将事件嵌入扩展到每个时间点
            time_embeddings = event_emb.unsqueeze(0).repeat(seq_len, 1)  # (seq_len, embed_dim)
            
            # 预测每个时间点
            time_logits = self.time_predictor(time_embeddings)  # (seq_len, 1)
            
            # 转换为二分类logits
            time_probs = torch.sigmoid(time_logits).squeeze(-1)  # (seq_len,)
            time_logits_2class = torch.stack([1 - time_probs, time_probs], dim=1)  # (seq_len, 2)
            
            all_predictions.append(time_logits_2class)
        
        return all_predictions
    
    def training_step(self, batch, batch_idx):
        """训练步骤"""
        all_events_data = batch['all_events_features']
        all_events_labels = batch['all_events_labels']
        
        # 前向传播
        all_predictions = self(all_events_data)
        
        # 计算损失
        total_loss = 0.0
        total_correct = 0
        total_points = 0
        
        for predictions, labels in zip(all_predictions, all_events_labels):
            if len(labels) > 0:
                labels = labels.to(self.device)
                min_len = min(len(predictions), len(labels))
                
                pred_trimmed = predictions[:min_len]
                labels_trimmed = labels[:min_len]
                
                loss = self.criterion(pred_trimmed, labels_trimmed)
                total_loss += loss
                
                pred_classes = torch.argmax(pred_trimmed, dim=1)
                correct = (pred_classes == labels_trimmed).sum().item()
                total_correct += correct
                total_points += len(labels_trimmed)
        
        avg_loss = total_loss / len(all_predictions) if len(all_predictions) > 0 else 0.0
        avg_acc = total_correct / total_points if total_points > 0 else 0.0
        
        self.log('train_loss', avg_loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log('train_acc', avg_acc, on_step=False, on_epoch=True, prog_bar=True)
        
        return avg_loss
    
    def validation_step(self, batch, batch_idx):
        """验证步骤"""
        all_events_data = batch['all_events_features']
        all_events_labels = batch['all_events_labels']
        
        # 前向传播
        all_predictions = self(all_events_data)
        
        # 计算损失
        total_loss = 0.0
        total_correct = 0
        total_points = 0
        
        for predictions, labels in zip(all_predictions, all_events_labels):
            if len(labels) > 0:
                labels = labels.to(self.device)
                min_len = min(len(predictions), len(labels))
                
                pred_trimmed = predictions[:min_len]
                labels_trimmed = labels[:min_len]
                
                loss = self.criterion(pred_trimmed, labels_trimmed)
                total_loss += loss
                
                pred_classes = torch.argmax(pred_trimmed, dim=1)
                correct = (pred_classes == labels_trimmed).sum().item()
                total_correct += correct
                total_points += len(labels_trimmed)
        
        avg_loss = total_loss / len(all_predictions) if len(all_predictions) > 0 else 0.0
        avg_acc = total_correct / total_points if total_points > 0 else 0.0
        
        self.log('val_loss', avg_loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log('val_acc', avg_acc, on_step=False, on_epoch=True, prog_bar=True)
        
        return avg_loss
    
    def on_validation_epoch_end(self):
        """验证轮次结束"""
        self.validation_step_outputs.clear()
    
    def configure_optimizers(self):
        """配置优化器"""
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay
        )
        
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=10
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss'
            }
        }


if __name__ == "__main__":
    print("🧪 Testing True Event DTST Model")
    
    # 创建模拟配置
    class MockConfig:
        n_features = 26
        embed_dim = 128
        num_classes = 2
        learning_rate = 1e-4
        weight_decay = 1e-5
        heads = [8]
        depths = [4]
        drop = 0.1
        head_dropout = 0.1
    
    config = MockConfig()
    model = TrueEventDTST(config)
    
    # 模拟38个事件
    all_events = [torch.randn(np.random.randint(500, 2000), 26) for _ in range(38)]
    
    with torch.no_grad():
        predictions = model(all_events)
        print(f"Input: 38 events with varying lengths")
        print(f"Output: {len(predictions)} event predictions")
        for i, pred in enumerate(predictions[:3]):
            print(f"  Event {i+1}: {pred.shape}")
    
    print("✅ True Event DTST model test completed!")
