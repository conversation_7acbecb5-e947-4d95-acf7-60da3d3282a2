import torch
from torch import nn
import torch.nn.functional as F
import numpy as np
import pytorch_lightning as pl
from timm.models.layers import DropPath

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from layers.RevIN import RevIN
from layers.Global_Attn import DAttentionBaseline
from layers.Transformer_Module import TransformerMLPWithConv, TransformerMLP, LayerNormProxy
from layers.head import ClassificationHead, Flatten_Head
from layers.Patching import PatchingModule


class LayerScale(nn.Module):
    """Layer scaling for better training stability"""
    def __init__(self, dim: int, inplace: bool = False, init_values: float = 1e-5):
        super().__init__()
        self.inplace = False  # Always disable inplace to avoid gradient issues
        self.weight = nn.Parameter(torch.ones(dim) * init_values)

    def forward(self, x):
        # Always use non-inplace operation to avoid gradient computation issues
        return x * self.weight.view(-1, 1)


class ResidualConnection(nn.Module):
    """Smart residual connection that handles dimension mismatches"""
    def __init__(self, input_dim, output_dim):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim

        # Projection layer if dimensions don't match
        if input_dim != output_dim:
            self.projection = nn.Conv1d(input_dim, output_dim, 1, bias=False)
        else:
            self.projection = nn.Identity()

    def forward(self, x, residual):
        # x: output, residual: input
        # Both should be (B, C, N) format
        if x.shape != residual.shape:
            # Project residual to match output dimensions
            residual = self.projection(residual)

            # Handle sequence length mismatch by interpolation
            if x.shape[2] != residual.shape[2]:
                residual = torch.nn.functional.interpolate(
                    residual, size=x.shape[2], mode='linear', align_corners=False
                )

        return x + residual


class Stage(nn.Module):
    """
    Single stage of the DeformableTST model
    """
    def __init__(self, fmap_size, dim_embed, depths, stage_spec, n_heads, n_head_channels, 
                 n_groups, attn_drop, proj_drop, drop_path_rate, layer_scale_value,
                 use_pe, dwc_pe, no_off, fixed_pe, ksize, log_cpb, stride, 
                 offset_range_factor, use_lpu, local_kernel_size, expansion, 
                 drop, use_dwc_mlp):
        super().__init__()
        
        self.depths = depths
        self.stage_spec = stage_spec
        self.use_lpu = use_lpu
        
        # Layer normalization
        self.layer_norms = nn.ModuleList([
            LayerNormProxy(dim_embed) for _ in range(2 * depths)
        ])
        
        # Layer scaling
        self.layer_scales = nn.ModuleList([
            LayerScale(dim_embed, init_values=layer_scale_value) for _ in range(2 * depths)
        ])
        
        # Local perception units (optional)
        self.local_perception_units = nn.ModuleList([
            nn.Conv1d(dim_embed, dim_embed, kernel_size=local_kernel_size, stride=1, 
                     padding=local_kernel_size//2, groups=dim_embed) if use_lpu else nn.Identity()
            for _ in range(depths)
        ])
        
        # MLP layers
        self.mlps = nn.ModuleList([
            TransformerMLPWithConv(dim_embed, expansion, drop, local_kernel_size) if use_dwc_mlp
            else TransformerMLP(dim_embed, expansion, drop)
            for _ in range(depths)
        ])
        
        # Attention layers
        self.attns = nn.ModuleList()
        self.drop_path = nn.ModuleList()
        
        for i in range(depths):
            if stage_spec[i] == 'D':
                self.attns.append(
                    DAttentionBaseline(fmap_size, fmap_size, n_heads,
                                     n_head_channels, n_groups, attn_drop, proj_drop,
                                     stride, offset_range_factor, use_pe, dwc_pe,
                                     no_off, fixed_pe, ksize, log_cpb)
                )
            else:
                raise NotImplementedError(f'Spec: {stage_spec[i]} is not supported.')
            
            self.drop_path.append(DropPath(drop_path_rate[i]) if drop_path_rate[i] > 0.0 else nn.Identity())

    def forward(self, x):
        for d in range(self.depths):
            # Local perception unit
            if self.use_lpu:
                x0 = x
                x = self.local_perception_units[d](x.contiguous())
                x = x + x0
            
            # Deformable attention
            if self.stage_spec[d] == 'D':
                x0 = x
                x, pos, ref = self.attns[d](self.layer_norms[2 * d](x))
                x = self.layer_scales[2 * d](x)
                x = self.drop_path[d](x) + x0
                
                # MLP
                x0 = x
                x = self.mlps[d](self.layer_norms[2 * d + 1](x))
                x = self.layer_scales[2 * d + 1](x)
                x = self.drop_path[d](x) + x0
        
        return x


class DeformableTST_BGP(pl.LightningModule):
    """
    DeformableTST model adapted for BGP anomaly detection
    """
    def __init__(self, config):
        super(DeformableTST_BGP, self).__init__()
        self.save_hyperparameters()
        self.config = config
        
        # Model parameters
        params = config.get_model_params()
        self.n_vars = params['n_vars']
        self.seq_len = params['seq_len']  # This is now n_patches
        self.num_classes = params['num_classes']

        # Patching parameters
        self.patch_size = config.patch_size
        self.embed_dim = config.embed_dim
        self.max_seq_length = config.max_seq_length

        # RevIN normalization (applied before patching)
        self.rev = params['rev']
        if self.rev:
            self.revin = RevIN(self.n_vars, affine=params['revin_affine'],
                              subtract_last=params['revin_subtract_last'])

        # Patching module
        self.patching = PatchingModule(
            n_vars=self.n_vars,
            patch_size=self.patch_size,
            embed_dim=self.embed_dim,
            use_pos_encoding=True,
            pos_dropout=0.1,
            norm_layer=None  # Don't use normalization in patching for now
        )
        
        # Multi-stage architecture (now working with patch embeddings)
        self.num_stage = len(params['dims'])
        self.stem_ratio = params['stem_ratio']
        self.down_ratio = params['down_ratio']

        # Downsample layers (modified for sequence labeling - no spatial downsampling)
        self.downsample_layers = nn.ModuleList()
        # Stem layer: from patch embeddings to first stage (no stride to preserve sequence length)
        self.downsample_layers.append(
            nn.Conv1d(self.embed_dim, params['dims'][0], kernel_size=7, stride=1, padding=3)
        )
        # Intermediate downsample layers (no stride to preserve sequence length)
        for i in range(self.num_stage - 1):
            self.downsample_layers.append(
                nn.Conv1d(params['dims'][i], params['dims'][i+1],
                         kernel_size=3, stride=1, padding=1)
            )
        
        # Stages
        self.stages = nn.ModuleList()
        for i in range(self.num_stage):
            stage = Stage(
                fmap_size=params['fmap_size'][i],
                dim_embed=params['dims'][i],
                depths=params['depths'][i],
                stage_spec=params['stage_spec'][i],
                n_heads=params['heads'][i],
                n_head_channels=params['dims'][i] // params['heads'][i],
                n_groups=params['n_groups'],
                attn_drop=params['attn_drop'],
                proj_drop=params['proj_drop'],
                drop_path_rate=[params['drop_path_rate']] * params['depths'][i],
                layer_scale_value=params['layer_scale_value'],
                use_pe=params['use_pe'],
                dwc_pe=params['dwc_pe'],
                no_off=params['no_off'],
                fixed_pe=params['fixed_pe'],
                ksize=params['ksize'],
                log_cpb=params['log_cpb'],
                stride=params['stride'],
                offset_range_factor=params['offset_range_factor'],
                use_lpu=params['use_lpu'],
                local_kernel_size=params['local_kernel_size'],
                expansion=params['expansion'],
                drop=params['drop'],
                use_dwc_mlp=params['use_dwc_mlp']
            )
            self.stages.append(stage)

        # Residual connections for stages
        self.stage_residuals = nn.ModuleList()
        for i in range(self.num_stage):
            if i == 0:
                # First stage: from initial embedding to first stage output
                input_dim = self.embed_dim
            else:
                # Subsequent stages: from previous stage output to current stage output
                input_dim = params['dims'][i-1]
            output_dim = params['dims'][i]

            self.stage_residuals.append(
                ResidualConnection(input_dim, output_dim)
            )

        # Classification head - use adaptive pooling to handle variable dimensions
        self.use_head_norm = params['use_head_norm']
        if self.use_head_norm:
            self.head_norm = LayerNormProxy(params['dims'][self.num_stage - 1])

        # Sequence labeling head (no pooling, keep sequence dimension)
        # After patching, we work with embedded features, so input dim is just the final stage dim
        classifier_input_dim = params['dims'][self.num_stage - 1]
        self.sequence_classifier = nn.Sequential(
            nn.Linear(classifier_input_dim, 256),
            nn.ReLU(),
            nn.Dropout(params['head_dropout']),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(params['head_dropout']),
            nn.Linear(128, self.num_classes)
        )
        
        # Loss function with class weights support
        self.criterion = nn.CrossEntropyLoss()
        
        # Metrics storage
        self.validation_step_outputs = []
        self.train_step_outputs = []

        # Rolling window training parameters
        self.use_rolling_window_training = params.get('use_rolling_window_training', False)
        self.rolling_window_size = params.get('rolling_window_size', 32)

    def forward(self, x):
        # x: (B, L, M) where L is sequence length, M is number of variables
        B, L, M = x.shape

        # Apply RevIN normalization before patching
        if self.rev:
            x = self.revin(x, 'norm')

        # Apply patching: (B, L, M) -> (B, N_patches, embed_dim)
        x = self.patching(x)  # (B, N_patches, embed_dim)

        # Transpose for 1D convolution: (B, N_patches, embed_dim) -> (B, embed_dim, N_patches)
        x = x.transpose(1, 2)  # (B, embed_dim, N_patches)

        # Apply downsample layers and stages with smart residual connections
        x = self.downsample_layers[0](x)

        # Store initial features for global residual connection
        initial_features = x

        for i in range(self.num_stage):
            # Store input for stage-level residual connection
            stage_input = x
            x = self.stages[i](x)

            # Apply smart residual connection
            x = self.stage_residuals[i](x, stage_input)

            if i < (self.num_stage - 1):
                x = self.downsample_layers[i + 1](x)

        # Prepare for sequence labeling head
        _, D, N_patches = x.shape

        if self.use_head_norm:
            x = self.head_norm(x)

        # Transpose for sequence labeling: (B, D, N_patches) -> (B, N_patches, D)
        x = x.transpose(1, 2)  # (B, N_patches, D)

        # Apply sequence classifier to each patch
        x = self.sequence_classifier(x)  # (B, N_patches, num_classes)

        # Upsample from patches back to original sequence length
        # We need to map from N_patches back to original sequence length
        B, N_patches, num_classes = x.shape
        patch_size = self.patch_size

        # Repeat each patch prediction patch_size times
        x = x.unsqueeze(2).repeat(1, 1, patch_size, 1)  # (B, N_patches, patch_size, num_classes)
        x = x.reshape(B, N_patches * patch_size, num_classes)  # (B, reconstructed_seq_len, num_classes)

        # Note: The reconstructed sequence might be longer than the original due to padding
        # This will be handled in the evaluation script by truncating to original length
        
        if self.rev:
            # For classification, we don't need to denormalize
            pass
        
        return x

    def _rolling_window_training_step(self, x, y):
        """
        滚动窗口训练：为每个时间步创建历史窗口进行训练

        Args:
            x: (B, seq_len, features) 完整序列特征
            y: (B, seq_len) 完整序列标签

        Returns:
            loss: 平均损失
        """
        B, seq_len, features = x.shape
        window_size = getattr(self, 'rolling_window_size', 32)

        total_loss = 0.0
        total_samples = 0

        # 为每个时间步创建滚动窗口
        for i in range(seq_len):
            # 创建历史窗口
            if i < window_size:
                # 对于前window_size个时间步，使用填充策略
                available_steps = i + 1
                if available_steps == 1:
                    # 只有一个时间步，重复它来填充整个窗口
                    window_x = x[:, 0:1, :].repeat(1, window_size, 1)  # (B, window_size, features)
                else:
                    # 有多个时间步，先使用可用的，然后重复第一个时间步填充
                    padding_needed = window_size - available_steps
                    padding = x[:, 0:1, :].repeat(1, padding_needed, 1)  # (B, padding_needed, features)
                    available_x = x[:, :available_steps, :]  # (B, available_steps, features)
                    window_x = torch.cat([padding, available_x], dim=1)  # (B, window_size, features)
            else:
                # 正常情况：使用前window_size个时间步
                window_x = x[:, i-window_size:i, :]  # (B, window_size, features)

            # 当前时间步的标签
            current_y = y[:, i]  # (B,)

            # 模型预测
            window_pred = self(window_x)  # (B, window_size, num_classes)

            # 取最后一个时间步的预测（对应当前时间点）
            current_pred = window_pred[:, -1, :]  # (B, num_classes)

            # 计算损失
            step_loss = self.criterion(current_pred, current_y)
            total_loss += step_loss
            total_samples += 1

        # 返回平均损失
        return total_loss / total_samples

    def training_step(self, batch, batch_idx):
        x, y = batch  # x: (B, seq_len, features), y: (B, seq_len)
        y = y.long()

        # 检查是否使用滚动窗口训练
        if hasattr(self, 'use_rolling_window_training') and self.use_rolling_window_training:
            loss = self._rolling_window_training_step(x, y)
        else:
            # 原有的完整序列训练
            y_hat = self(x)  # (B, reconstructed_seq_len, num_classes)

            # Truncate model output to match target length
            B, target_seq_len = y.shape
            y_hat = y_hat[:, :target_seq_len, :]  # (B, target_seq_len, num_classes)

            # Flatten for loss calculation
            y_hat_flat = y_hat.contiguous().view(-1, self.num_classes)  # (B*target_seq_len, num_classes)
            y_flat = y.contiguous().view(-1)  # (B*target_seq_len,)

            loss = self.criterion(y_hat_flat, y_flat)

        # Calculate accuracy (only on non-padded positions)
        pred = y_hat.argmax(dim=-1)  # (B, target_seq_len)

        # Create mask for non-padded positions (padding value is 0)
        mask = (y != 0) | (torch.arange(target_seq_len, device=y.device)[None, :] == 0)  # Include first position even if label is 0
        if mask.sum() > 0:
            acc = (pred[mask] == y[mask]).float().mean()
        else:
            acc = torch.tensor(0.0, device=self.device)

        self.log('train_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log('train_acc', acc, on_step=False, on_epoch=True, prog_bar=True)

        return loss

    def validation_step(self, batch, batch_idx):
        x, y = batch  # x: (B, seq_len, features), y: (B, seq_len)
        y = y.long()

        y_hat = self(x)  # (B, reconstructed_seq_len, num_classes)

        # Truncate model output to match target length
        B, target_seq_len = y.shape
        y_hat = y_hat[:, :target_seq_len, :]  # (B, target_seq_len, num_classes)

        # Flatten for loss calculation
        y_hat_flat = y_hat.contiguous().view(-1, self.num_classes)  # (B*target_seq_len, num_classes)
        y_flat = y.contiguous().view(-1)  # (B*target_seq_len,)

        loss = self.criterion(y_hat_flat, y_flat)

        # Calculate accuracy (only on non-padded positions)
        pred = y_hat.argmax(dim=-1)  # (B, target_seq_len)

        # Create mask for non-padded positions (padding value is 0)
        mask = (y != 0) | (torch.arange(target_seq_len, device=y.device)[None, :] == 0)  # Include first position even if label is 0
        if mask.sum() > 0:
            acc = (pred[mask] == y[mask]).float().mean()
        else:
            acc = torch.tensor(0.0, device=self.device)

        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log('val_acc', acc, on_step=False, on_epoch=True, prog_bar=True)

        self.validation_step_outputs.append({'loss': loss, 'acc': acc, 'pred': pred, 'target': y})

        return loss

    def on_validation_epoch_end(self):
        self.validation_step_outputs.clear()

    def configure_optimizers(self):
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.config.learning_rate,
            weight_decay=self.config.weight_decay
        )
        
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=10
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'monitor': 'val_loss'
            }
        }
