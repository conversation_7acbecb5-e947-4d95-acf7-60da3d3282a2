#!/usr/bin/env python3
"""
真正的事件级DTST训练脚本
38个事件作为patching，形成短序列进行训练
"""

import os
import sys
import argparse
import torch
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
from torch.utils.data import DataLoader

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import DTSTConfig
from core.true_event_data_manager import TrueEventDataManager, collate_all_events
from models.TrueEventDTST import TrueEventDTST


def train_true_event_dtst(config, event_type='hijack', max_epochs=100):
    """
    训练真正的事件级DTST模型
    
    Args:
        config: 配置对象
        event_type: 事件类型
        max_epochs: 最大训练轮次
    """
    print(f"🚀 Starting TRUE Event-Level DTST Training for {event_type.upper()} events")
    print("="*80)
    print("📋 Architecture: 38 Events → Event Embeddings → Short Sequence → Attention → Time Point Predictions")
    print("="*80)
    
    # 1. 数据准备
    print("📊 Preparing TRUE event-level data with train/test split...")

    data_manager = TrueEventDataManager(
        data_path=config.data_path,
        target_event_types=['hijack', 'leak', 'outage']  # 加载所有类型
    )

    # 创建训练和测试数据集
    train_dataset, test_dataset = data_manager.create_train_test_split(normalize=True)

    print(f"✅ Created train/test split with proper event separation")
    
    # 创建数据加载器 - 使用真正的训练/验证分离
    train_loader = DataLoader(
        train_dataset,
        batch_size=1,
        shuffle=False,  # 只有一个样本，不需要shuffle
        collate_fn=collate_all_events,
        num_workers=0
    )

    val_loader = DataLoader(
        test_dataset,  # 使用测试集作为验证集
        batch_size=1,
        shuffle=False,
        collate_fn=collate_all_events,
        num_workers=0
    )

    print(f"✅ Created data loaders: Train samples=1, Val samples=1 (with proper event separation)")
    
    # 2. 模型创建
    print("🏗️  Creating TRUE Event-Level DTST model...")
    
    model = TrueEventDTST(config)
    
    print(f"✅ Model created with {sum(p.numel() for p in model.parameters())} parameters")
    print(f"   - Event Embedding: {sum(p.numel() for p in model.event_embedding.parameters())} params")
    print(f"   - Event Transformer: {sum(p.numel() for p in model.event_transformer.parameters())} params")
    print(f"   - Time Predictor: {sum(p.numel() for p in model.time_predictor.parameters())} params")
    
    # 3. 训练配置
    print("⚙️  Setting up training configuration...")
    
    # 模型保存目录
    model_dir = f"./models_{event_type}_true_event_level"
    os.makedirs(model_dir, exist_ok=True)
    
    # 回调函数
    checkpoint_callback = ModelCheckpoint(
        dirpath=model_dir,
        filename=f'{event_type}-true-event-dtst-' + 'epoch={epoch:02d}-val_loss={val_loss:.3f}',
        monitor='val_loss',
        mode='min',
        save_top_k=3,
        verbose=True
    )
    
    # 移除早停机制，让模型完整训练300个epoch
    # early_stopping = EarlyStopping(
    #     monitor='val_loss',
    #     patience=config.patience,
    #     mode='min',
    #     verbose=True
    # )

    # 训练器
    trainer = pl.Trainer(
        max_epochs=max_epochs,
        callbacks=[checkpoint_callback],  # 只保留检查点回调
        accelerator="auto",
        devices="auto",
        enable_progress_bar=True,
        log_every_n_steps=1,  # 因为只有一个batch，每步都记录
        check_val_every_n_epoch=1
    )
    
    print(f"✅ Trainer configured: max_epochs={max_epochs}, patience={config.patience}")
    
    # 4. 开始训练
    print("🎯 Starting TRUE event-level training...")
    print("-"*50)
    print("📝 Note: This approach treats all 38 events as patches in a short sequence")
    print("📝 Each epoch processes all events together through attention mechanism")
    print("-"*50)
    
    try:
        trainer.fit(model, train_loader, val_loader)
        
        print("🎉 Training completed successfully!")
        print(f"📁 Best model saved in: {model_dir}")
        print(f"🏆 Best model: {checkpoint_callback.best_model_path}")
        
        return checkpoint_callback.best_model_path
        
    except Exception as e:
        print(f"❌ Training failed: {str(e)}")
        return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Train TRUE Event-Level DTST model")
    parser.add_argument('--event_type', type=str, default='hijack',
                       choices=['hijack', 'leak', 'outage'],
                       help="Type of BGP event to train on")
    parser.add_argument('--learning_rate', type=float, default=1e-4,
                       help="Learning rate")
    parser.add_argument('--max_epochs', type=int, default=50,
                       help="Maximum number of epochs")
    parser.add_argument('--embed_dim', type=int, default=128,
                       help="Embedding dimension")
    parser.add_argument('--num_heads', type=int, default=8,
                       help="Number of attention heads")
    parser.add_argument('--num_layers', type=int, default=4,
                       help="Number of transformer layers")
    
    args = parser.parse_args()
    
    # 创建配置
    config = DTSTConfig()
    
    # 更新配置
    config.learning_rate = args.learning_rate
    config.max_epochs = args.max_epochs
    config.embed_dim = args.embed_dim
    
    # 真正的事件级特定配置
    config.heads = [args.num_heads]
    config.depths = [args.num_layers]
    config.use_true_event_level_patching = True
    
    print("🔧 Configuration:")
    print(f"  Event Type: {args.event_type}")
    print(f"  Learning Rate: {config.learning_rate}")
    print(f"  Max Epochs: {args.max_epochs}")
    print(f"  Embed Dim: {config.embed_dim}")
    print(f"  Attention Heads: {args.num_heads}")
    print(f"  Transformer Layers: {args.num_layers}")
    print(f"  Data Path: {config.data_path}")
    print()
    print("🎯 Key Innovation:")
    print("  - 38 events as patches (not fixed-size patches)")
    print("  - Event-level 1D convolution for compression")
    print("  - Short sequence (38) with attention mechanism")
    print("  - Time-point level predictions from event embeddings")
    print()
    
    # 开始训练
    best_model_path = train_true_event_dtst(
        config=config,
        event_type=args.event_type,
        max_epochs=args.max_epochs
    )
    
    if best_model_path:
        print(f"✅ Training completed successfully!")
        print(f"🏆 Best model: {best_model_path}")
        print()
        print("🔍 Next steps:")
        print("  1. Evaluate the model on time-point level metrics")
        print("  2. Compare with traditional sliding window approaches")
        print("  3. Analyze attention patterns across events")
    else:
        print("❌ Training failed!")
        sys.exit(1)


if __name__ == "__main__":
    # 设置随机种子
    torch.manual_seed(42)
    
    main()
