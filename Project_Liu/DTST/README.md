# DTST for BGP Anomaly Detection

This project implements a Deformable Time Series Transformer (DTST) model for BGP anomaly detection, adapted from the original DeformableTST architecture for time series forecasting.

## Overview

The DTST model uses deformable attention mechanisms to adaptively focus on important time steps in BGP feature sequences, making it particularly suitable for detecting anomalous patterns in network routing data.

### Key Features

- **Deformable Attention**: Learns dynamic sampling positions for more flexible attention patterns
- **Multi-stage Architecture**: Hierarchical feature extraction with downsampling
- **RevIN Normalization**: Reversible instance normalization for better time series modeling
- **Classification Head**: Adapted for binary anomaly detection (normal vs anomaly)
- **Class Imbalance Handling**: Supports class weights and early stopping

## Project Structure

```
DTST/
├── config.py              # Configuration settings
├── data_loader.py          # Data loading and preprocessing
├── train.py               # Training script
├── evaluate.py            # Evaluation script
├── requirements.txt       # Dependencies
├── models/
│   ├── __init__.py
│   └── DeformableTST_BGP.py  # Main model implementation
└── layers/
    ├── __init__.py
    ├── Global_Attn.py      # Deformable attention layer
    ├── Transformer_Module.py  # Transformer components
    ├── RevIN.py            # Reversible normalization
    └── head.py             # Classification heads
```

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure you have access to the BGP anomaly event data in the expected format.

## Usage

### Training

Train a model for a specific event type:

```bash
# Train for hijack events
python train.py --event_type hijack --batch_size 32 --learning_rate 1e-4 --max_epochs 100

# Train for leak events  
python train.py --event_type leak --batch_size 32 --learning_rate 1e-4 --max_epochs 100

# Train for outage events
python train.py --event_type outage --batch_size 32 --learning_rate 1e-4 --max_epochs 100
```

### Evaluation

Evaluate a trained model:

```bash
# Evaluate hijack model
python evaluate.py --event_type hijack --model_path ./models_hijack/hijack-dtst-epoch=XX-val_loss=X.XXX.ckpt

# Evaluate leak model
python evaluate.py --event_type leak --model_path ./models_leak/leak-dtst-epoch=XX-val_loss=X.XXX.ckpt

# Evaluate outage model  
python evaluate.py --event_type outage --model_path ./models_outage/outage-dtst-epoch=XX-val_loss=X.XXX.ckpt
```

## Model Architecture

### Deformable Attention

The core innovation is the deformable attention mechanism that:
- Learns offset positions for each query
- Uses grid sampling to gather features from deformed positions
- Enables adaptive receptive fields for different time series patterns

### Multi-stage Processing

1. **Stem Layer**: Initial feature extraction with 1D convolution
2. **Downsampling Stages**: Hierarchical feature learning with attention
3. **Classification Head**: Global pooling + MLP for final prediction

### Key Components

- **RevIN**: Reversible instance normalization for stable training
- **Layer Scaling**: Improves training stability in deep networks
- **Local Perception Units**: Optional 1D convolutions for local modeling
- **Drop Path**: Stochastic depth for regularization

## Configuration

Key parameters in `config.py`:

- `window_size`: Sliding window size (default: 10)
- `n_features`: Number of BGP features (auto-detected: 26)
- `dims`: Feature dimensions per stage [64, 128]
- `depths`: Number of layers per stage [2, 2]
- `heads`: Number of attention heads [4, 8]
- `offset_range_factor`: Controls deformation range (default: 2.0)

## Data Format

The model expects BGP event data in JSON format with:
- Features: 26-dimensional BGP routing features
- Labels: Binary (0=normal, 1=anomaly)
- Structure: Time series with sliding window processing

## Results

The model outputs detailed evaluation metrics:
- Accuracy, Precision, Recall, F1-Score
- False Positive Rate (FPR) and False Negative Rate (FNR)
- Confusion matrices
- Per-event detailed results

## Comparison with SA-LSTM

Key advantages over the baseline SA-LSTM:
1. **Adaptive Attention**: Deformable attention vs fixed self-attention
2. **Multi-scale Processing**: Hierarchical stages vs single LSTM
3. **Better Parallelization**: Attention-based vs sequential LSTM
4. **Flexible Receptive Fields**: Dynamic sampling vs fixed windows

## Notes

- The model is specifically adapted for BGP anomaly detection from the original DeformableTST forecasting model
- Early stopping with patience=20 is used to prevent overfitting
- Class weights are automatically calculated to handle imbalanced datasets
- GPU training is recommended for faster convergence
