#!/usr/bin/env python3
"""
重构后的DTST训练脚本
使用统一的核心组件进行训练
"""

import os
import sys
import argparse
import pytorch_lightning as pl

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import DTSTConfig
from core.data_manager import DataManager
from core.trainer import train_dtst_model


def main():
    """主训练函数"""
    parser = argparse.ArgumentParser(description="Train DTST model for BGP anomaly detection")
    parser.add_argument('--event_type', type=str, required=True,
                       choices=['hijack', 'leak', 'outage'],
                       help="Type of BGP event to train on")
    parser.add_argument('--batch_size', type=int, default=None,
                       help="Batch size for training")
    parser.add_argument('--learning_rate', type=float, default=None,
                       help="Learning rate")
    parser.add_argument('--max_epochs', type=int, default=None,
                       help="Maximum number of epochs")
    parser.add_argument('--patience', type=int, default=None,
                       help="Early stopping patience")
    parser.add_argument('--force_recreate', action='store_true',
                       help="Force recreate datasets even if they exist")
    parser.add_argument('--train_events', type=int, default=None,
                       help="Number of events to use for training (use first N events)")
    
    args = parser.parse_args()
    
    # Initialize configuration
    config = DTSTConfig()
    
    # Override config with command line arguments
    if args.batch_size is not None:
        config.batch_size = args.batch_size
    if args.learning_rate is not None:
        config.learning_rate = args.learning_rate
    if args.max_epochs is not None:
        config.max_epochs = args.max_epochs
    if args.patience is not None:
        config.patience = args.patience
    
    print(f"🚀 DTST BGP Anomaly Detection Training")
    print(f"🎯 Event Type: {args.event_type}")
    print(f"⚙️ Batch Size: {config.batch_size}")
    print(f"⚙️ Learning Rate: {config.learning_rate}")
    print(f"⚙️ Max Epochs: {config.max_epochs}")
    print(f"⚙️ Patience: {config.patience}")
    print(f"📁 Data Path: {config.data_path}")
    
    # Set random seed for reproducibility
    pl.seed_everything(42)
    
    # Create data manager
    data_manager = DataManager(config)
    
    # Train model
    try:
        trainer, success = train_dtst_model(args.event_type, config, data_manager, args.train_events)
        
        if success and trainer:
            print(f"\n✅ Training completed successfully!")
            
            # Print dataset info
            dataset_info = data_manager.get_dataset_info()
            print(f"\n📊 Dataset Information:")
            for key, value in dataset_info.items():
                print(f"   {key}: {value}")
                
            # Print model summary
            model_summary = trainer.get_model_summary()
            if model_summary:
                print(f"\n📋 Model Summary:")
                print(f"   Total Parameters: {model_summary['total_parameters']:,}")
                print(f"   Trainable Parameters: {model_summary['trainable_parameters']:,}")
                print(f"   Model Size: {model_summary['model_size_mb']:.2f} MB")
                
                if model_summary['best_model_path']:
                    print(f"   Best Model: {model_summary['best_model_path']}")
                
        else:
            print("❌ Training failed!")
            
    except Exception as e:
        print(f"❌ Error during training: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
