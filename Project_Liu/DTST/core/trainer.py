"""
统一的训练器
处理模型训练逻辑
"""

import os
import torch
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
from pytorch_lightning.loggers import TensorBoardLogger
from typing import Tuple, Optional

from models.DeformableTST_BGP import DeformableTST_BGP


class ModelTrainer:
    """统一的模型训练器"""
    
    def __init__(self, config):
        """
        Args:
            config: 训练配置
        """
        self.config = config
        self.model = None
        self.trainer = None
        self.best_model_path = None
    
    def setup_model(self, feature_dim: int, class_weights: torch.Tensor):
        """
        设置模型
        
        Args:
            feature_dim: 特征维度
            class_weights: 类别权重
        """
        # 更新配置
        self.config.n_features = feature_dim
        self.config.n_vars = feature_dim
        
        # 创建模型
        self.model = DeformableTST_BGP(self.config)
        
        # 设置类别权重
        if hasattr(self.model, 'set_class_weights'):
            self.model.set_class_weights(class_weights)
        
        print(f"Model created with {feature_dim} features")
        print(f"Class weights: {class_weights}")
    
    def setup_trainer(self, event_type: str):
        """
        设置训练器
        
        Args:
            event_type: 事件类型
        """
        # 设置目录
        model_dir = f'./models_{event_type}'
        log_dir = f'./logs_{event_type}'
        os.makedirs(model_dir, exist_ok=True)
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置回调
        checkpoint_callback = ModelCheckpoint(
            dirpath=model_dir,
            filename=f'{event_type}-dtst-{{epoch:02d}}-{{val_loss:.3f}}',
            monitor='val_loss',
            mode='min',
            save_top_k=3,
            save_last=True,
            verbose=True
        )
        
        # 禁用早停机制，让模型充分训练
        # early_stopping_callback = EarlyStopping(
        #     monitor='val_loss',
        #     patience=self.config.patience,
        #     mode='min',
        #     verbose=True
        # )
        
        # 设置日志记录器
        logger = TensorBoardLogger(
            save_dir=log_dir,
            name=f'{event_type}_dtst_model',
            version=None
        )
        
        # 设置训练器
        trainer_kwargs = {
            'max_epochs': self.config.max_epochs,
            'callbacks': [checkpoint_callback],  # 只保留checkpoint，移除早停
            'logger': logger,
            'enable_progress_bar': True,
            'log_every_n_steps': 10,
            'val_check_interval': 1.0,
            'gradient_clip_val': 0.5,
            'gradient_clip_algorithm': 'norm',
            'deterministic': True
        }
        
        # 根据设备类型设置加速器
        if torch.cuda.is_available():
            trainer_kwargs.update({
                'accelerator': 'gpu',
                'devices': 1
            })
        else:
            trainer_kwargs.update({
                'accelerator': 'cpu',
                'devices': 1
            })
        
        self.trainer = pl.Trainer(**trainer_kwargs)
        self.checkpoint_callback = checkpoint_callback
        
        print(f"Trainer setup complete")
        print(f"Model dir: {model_dir}")
        print(f"Log dir: {log_dir}")
    
    def train(self, train_loader, val_loader) -> Tuple[bool, str]:
        """
        训练模型
        
        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            
        Returns:
            (success, best_model_path)
        """
        if self.model is None or self.trainer is None:
            raise ValueError("Model and trainer must be setup before training")
        
        try:
            print("\n🚀 Starting training...")
            
            # 开始训练
            self.trainer.fit(self.model, train_loader, val_loader)
            
            # 获取最佳模型路径
            self.best_model_path = self.checkpoint_callback.best_model_path
            
            print(f"\n✅ Training completed!")
            print(f"📁 Best model: {self.best_model_path}")
            
            return True, self.best_model_path
            
        except Exception as e:
            print(f"❌ Training failed: {e}")
            import traceback
            traceback.print_exc()
            return False, None
    
    def get_model_summary(self) -> dict:
        """
        获取模型摘要信息
        
        Returns:
            模型摘要字典
        """
        if self.model is None:
            return {}
        
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024),  # 假设float32
            'best_model_path': self.best_model_path
        }
    
    def print_summary(self):
        """打印训练摘要"""
        summary = self.get_model_summary()
        
        if summary:
            print(f"\n📋 Model Summary:")
            print(f"   Total Parameters: {summary['total_parameters']:,}")
            print(f"   Trainable Parameters: {summary['trainable_parameters']:,}")
            print(f"   Model Size: {summary['model_size_mb']:.2f} MB")
            
            if summary['best_model_path']:
                print(f"   Best Model: {summary['best_model_path']}")


def train_dtst_model(event_type: str, config, data_manager, train_events: int = None) -> Tuple[Optional[ModelTrainer], bool]:
    """
    训练DTST模型的主函数
    
    Args:
        event_type: 事件类型
        config: 配置对象
        data_manager: 数据管理器
        
    Returns:
        (trainer, success)
    """
    print(f"\n🎯 Training DTST model for '{event_type}' events")
    
    try:
        # 创建数据集
        if not data_manager.create_datasets(event_type, train_events=train_events):
            print("❌ Failed to create datasets")
            return None, False
        
        # 创建数据加载器
        train_loader, val_loader, test_loader = data_manager.create_data_loaders(
            batch_size=config.batch_size,
            val_split=0.2,
            shuffle=True
        )
        
        # 获取类别权重
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        class_weights = data_manager.get_class_weights(device)
        
        # 创建训练器
        trainer = ModelTrainer(config)
        
        # 设置模型
        feature_dim = data_manager.get_feature_dim()
        trainer.setup_model(feature_dim, class_weights)
        
        # 设置训练器
        trainer.setup_trainer(event_type)
        
        # 开始训练
        success, best_model_path = trainer.train(train_loader, val_loader)
        
        if success:
            trainer.print_summary()
            return trainer, True
        else:
            return None, False
            
    except Exception as e:
        print(f"❌ Error during training: {e}")
        import traceback
        traceback.print_exc()
        return None, False
