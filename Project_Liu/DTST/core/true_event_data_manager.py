"""
真正的事件级数据管理器
将所有38个事件作为一个训练样本
"""

import os
import json
import torch
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
import joblib


class AllEventsDataset(Dataset):
    """
    所有事件数据集
    将所有38个事件作为一个样本
    """
    
    def __init__(self, all_events_data: List[torch.Tensor], 
                 all_events_labels: List[torch.Tensor],
                 all_events_info: List[Dict]):
        """
        Args:
            all_events_data: 所有事件的特征数据列表 (38个事件)
            all_events_labels: 所有事件的标签数据列表 (38个事件)
            all_events_info: 所有事件的元信息列表 (38个事件)
        """
        self.all_events_data = all_events_data
        self.all_events_labels = all_events_labels
        self.all_events_info = all_events_info
        
        # 统计信息
        self._print_dataset_stats()
    
    def _print_dataset_stats(self):
        """打印数据集统计信息"""
        n_events = len(self.all_events_data)
        seq_lengths = [len(event) for event in self.all_events_data]
        
        # 统计事件类型
        event_types = [info['event_type'] for info in self.all_events_info]
        type_counts = pd.Series(event_types).value_counts()
        
        # 统计标签分布
        total_normal = sum(torch.sum(labels == 0).item() for labels in self.all_events_labels)
        total_anomaly = sum(torch.sum(labels == 1).item() for labels in self.all_events_labels)
        
        print(f"📊 All Events Dataset Statistics:")
        print(f"  Total events: {n_events}")
        print(f"  Sequence lengths: min={min(seq_lengths)}, max={max(seq_lengths)}, avg={np.mean(seq_lengths):.1f}")
        print(f"  Event types: {dict(type_counts)}")
        print(f"  Label distribution: Normal={total_normal}, Anomaly={total_anomaly}")
        print(f"  Anomaly ratio: {total_anomaly/(total_normal+total_anomaly)*100:.2f}%")
    
    def __len__(self):
        # 只有一个样本：所有38个事件
        return 1
    
    def __getitem__(self, idx):
        # 总是返回所有事件
        return {
            'all_events_features': self.all_events_data,
            'all_events_labels': self.all_events_labels,
            'all_events_info': self.all_events_info
        }


class TrueEventDataManager:
    """
    真正的事件级数据管理器
    支持训练/测试划分的事件级处理
    """

    def __init__(self, data_path: str, target_event_types: List[str] = None):
        """
        Args:
            data_path: BGP数据根目录
            target_event_types: 目标事件类型列表，如['hijack', 'leak', 'outage']
        """
        self.data_path = data_path
        self.target_event_types = target_event_types or ['hijack', 'leak', 'outage']
        self.scaler = StandardScaler()

        # 定义测试集事件（按时间顺序的最后几个）
        self.test_events = {
            'hijack': [
                'hijack-20171212-DV_LINK_AS39523',
                'hijack-20180629-Bitcanal_Jingdong',
                'hijack-20220215-Nigeria_JSC_Ukraine',
                'hijack-20220228-Russia_Ukraine'
            ],
            'leak': [
                'leak-20200721-Commercial_Conecte_leak',
                'leak-20210211-Cablevision_Mexico_leak',
                'leak-20241030-Worldstream_leak'
            ],
            'outage': [
                'outage-20220313-RU_BGP_outage',
                'outage-20220621-Cloudflare_outage'
            ]
        }
        
    def load_event_data(self, event_dir: str) -> Tuple[torch.Tensor, torch.Tensor, Dict]:
        """
        加载单个事件的数据
        """
        feature_file = os.path.join(event_dir, "features", "fea.json")
        
        if not os.path.exists(feature_file):
            raise FileNotFoundError(f"Feature file not found: {feature_file}")
        
        # 加载JSON数据
        with open(feature_file, 'r') as f:
            data = json.load(f)
        
        # 检查数据格式并提取特征
        features_list = []
        labels_list = []
        
        if isinstance(data, list) and len(data) > 0:
            first_entry = data[0]
            
            if 'features' in first_entry and 'label' in first_entry:
                # 格式1: 每个条目有'features'和'label'字段
                for entry in data:
                    features_list.append(entry['features'])
                    labels_list.append(entry['label'])
            else:
                # 格式2: 每个条目直接是特征字典
                label_file = os.path.join(event_dir, "minute_labels.csv")
                
                # 提取特征（排除非数值字段）
                feature_keys = [k for k, v in first_entry.items() 
                              if isinstance(v, (int, float)) and k != 'label']
                
                for entry in data:
                    features = [entry.get(key, 0.0) for key in feature_keys]
                    features_list.append(features)
                
                # 加载标签
                if os.path.exists(label_file):
                    import pandas as pd
                    labels_df = pd.read_csv(label_file)
                    if 'label' in labels_df.columns:
                        labels_list = labels_df['label'].tolist()
                    else:
                        labels_list = [0] * len(features_list)
                else:
                    labels_list = [0] * len(features_list)
        
        if not features_list:
            raise ValueError(f"No valid data found in {feature_file}")
        
        # 转换为张量
        features = torch.tensor(features_list, dtype=torch.float32)
        labels = torch.tensor(labels_list, dtype=torch.long)
        
        # 事件信息
        event_name = os.path.basename(event_dir)
        event_type = self._extract_event_type(event_name)
        
        event_info = {
            'event_name': event_name,
            'event_type': event_type,
            'event_dir': event_dir,
            'sequence_length': len(features),
            'anomaly_count': torch.sum(labels == 1).item(),
            'normal_count': torch.sum(labels == 0).item()
        }
        
        return features, labels, event_info
    
    def _extract_event_type(self, event_name: str) -> str:
        """从事件名称提取事件类型"""
        event_name_lower = event_name.lower()
        for event_type in self.target_event_types:
            if event_type in event_name_lower:
                return event_type
        return 'unknown'

    def _is_test_event(self, event_name: str) -> bool:
        """判断事件是否属于测试集"""
        for event_type, test_event_list in self.test_events.items():
            if event_name in test_event_list:
                return True
        return False
    
    def load_all_events(self, normalize: bool = True, split: str = 'all') -> AllEventsDataset:
        """
        加载目标事件数据集

        Args:
            normalize: 是否进行特征标准化
            split: 数据集划分 ('all', 'train', 'test')
        """
        print(f"🔄 Loading {split.upper()} BGP events from {self.data_path}")

        all_events_data = []
        all_events_labels = []
        all_events_info = []
        all_features_for_scaling = []
        
        # 获取所有事件目录并按split过滤
        event_dirs = []
        for item in os.listdir(self.data_path):
            item_path = os.path.join(self.data_path, item)
            if os.path.isdir(item_path):
                event_type = self._extract_event_type(item)
                if event_type in self.target_event_types:
                    # 根据split参数过滤事件
                    is_test = self._is_test_event(item)
                    if split == 'all':
                        event_dirs.append(item_path)
                    elif split == 'train' and not is_test:
                        event_dirs.append(item_path)
                    elif split == 'test' and is_test:
                        event_dirs.append(item_path)

        print(f"Found {len(event_dirs)} {split} events")
        
        # 第一遍：收集特征用于标准化（只在训练集上拟合）
        if normalize and split in ['all', 'train']:
            print("📊 Collecting features for normalization...")
            for event_dir in event_dirs:
                try:
                    features, _, _ = self.load_event_data(event_dir)
                    all_features_for_scaling.append(features.numpy())
                except Exception as e:
                    print(f"⚠️  Warning: Failed to load {event_dir}: {e}")
                    continue

            # 拟合标准化器（只在训练数据上）
            if all_features_for_scaling:
                combined_features = np.vstack(all_features_for_scaling)
                self.scaler.fit(combined_features)
                print(f"✅ Fitted scaler on {combined_features.shape[0]} samples from {split} set")
        
        # 第二遍：加载和处理数据
        print(f"📥 Loading and processing {split.upper()} events...")
        for event_dir in event_dirs:
            try:
                features, labels, event_info = self.load_event_data(event_dir)
                
                # 标准化特征
                if normalize:
                    features_np = features.numpy()
                    features_scaled = self.scaler.transform(features_np)
                    features = torch.tensor(features_scaled, dtype=torch.float32)
                
                all_events_data.append(features)
                all_events_labels.append(labels)
                all_events_info.append(event_info)
                
                print(f"  ✅ {event_info['event_name']}: {event_info['sequence_length']} timesteps, "
                      f"{event_info['anomaly_count']} anomalies")
                
            except Exception as e:
                print(f"  ❌ Failed to load {os.path.basename(event_dir)}: {e}")
                continue
        
        if not all_events_data:
            raise ValueError("No valid events loaded!")
        
        return AllEventsDataset(all_events_data, all_events_labels, all_events_info)

    def create_train_test_split(self, normalize: bool = True) -> Tuple[AllEventsDataset, AllEventsDataset]:
        """
        创建训练和测试数据集

        Args:
            normalize: 是否进行特征标准化

        Returns:
            (train_dataset, test_dataset)
        """
        print("🔄 Creating train/test split...")

        # 首先加载训练集并拟合标准化器
        train_dataset = self.load_all_events(normalize=normalize, split='train')

        # 然后加载测试集（使用训练集的标准化器）
        test_dataset = self.load_all_events(normalize=normalize, split='test')

        # 打印划分统计
        print(f"\n📊 Train/Test Split Summary:")
        print(f"  Training events: {len(train_dataset.all_events_data)}")
        print(f"  Test events: {len(test_dataset.all_events_data)}")

        # 统计各类型事件的分布
        train_types = [info['event_type'] for info in train_dataset.all_events_info]
        test_types = [info['event_type'] for info in test_dataset.all_events_info]

        train_type_counts = pd.Series(train_types).value_counts()
        test_type_counts = pd.Series(test_types).value_counts()

        print(f"  Training set: {dict(train_type_counts)}")
        print(f"  Test set: {dict(test_type_counts)}")

        # 验证测试集事件
        print(f"\n🔍 Test Events:")
        for info in test_dataset.all_events_info:
            print(f"  - {info['event_name']} ({info['event_type']})")

        return train_dataset, test_dataset

    def save_scaler(self, path: str):
        """保存标准化器"""
        joblib.dump(self.scaler, path)
        print(f"💾 Scaler saved to {path}")
    
    def load_scaler(self, path: str):
        """加载标准化器"""
        self.scaler = joblib.load(path)
        print(f"📂 Scaler loaded from {path}")


def collate_all_events(batch):
    """
    批处理函数 - 直接返回所有事件
    """
    # batch只有一个元素，因为数据集长度为1
    return batch[0]


if __name__ == "__main__":
    # 测试真正的事件级数据管理器
    print("🧪 Testing True Event Data Manager")
    
    data_path = "/data/data/anomaly-event-routedata"
    target_types = ['hijack']
    
    try:
        # 创建数据管理器
        manager = TrueEventDataManager(data_path, target_types)
        
        # 加载所有事件数据
        dataset = manager.load_all_events(normalize=True)
        
        print(f"✅ Created dataset with {len(dataset)} sample (all events)")
        
        # 创建数据加载器
        dataloader = DataLoader(
            dataset,
            batch_size=1,
            shuffle=False,
            collate_fn=collate_all_events
        )
        
        # 测试数据加载
        for batch in dataloader:
            print(f"✅ Loaded batch with {len(batch['all_events_features'])} events")
            break
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("This is expected if running outside the data environment")
