"""
统一的数据管理器
处理所有数据加载、划分和预处理逻辑
"""

import os
import torch
import numpy as np
import joblib
from sklearn.preprocessing import StandardScaler
from torch.utils.data import Dataset, DataLoader, SubsetRandomSampler
from torch.nn.utils.rnn import pad_sequence
from collections import Counter
from typing import Tuple, List, Optional, Dict

from utils.data_utils import (
    load_event_data, get_events_by_type, intelligent_temporal_split,
    calculate_class_weights, print_dataset_summary
)


class BGPSequenceDataset(Dataset):
    """BGP序列数据集"""
    
    def __init__(self, sequences: List[torch.Tensor], labels: List[torch.Tensor]):
        """
        Args:
            sequences: 特征序列列表
            labels: 标签序列列表
        """
        self.sequences = sequences
        self.labels = labels
        
        # 验证数据
        assert len(sequences) == len(labels), "Sequences and labels must have same length"
        
        # 计算序列长度统计
        seq_lengths = [len(seq) for seq in sequences]
        print(f"Dataset created with {len(sequences)} sequences")
        print(f"Sequence lengths: min={min(seq_lengths)}, max={max(seq_lengths)}, avg={np.mean(seq_lengths):.1f}")
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return self.sequences[idx], self.labels[idx]


def collate_fn(batch):
    """自定义批处理函数，处理变长序列"""
    sequences, labels = zip(*batch)
    
    # 填充序列到相同长度
    sequences_padded = pad_sequence(sequences, batch_first=True, padding_value=0.0)
    labels_padded = pad_sequence(labels, batch_first=True, padding_value=0)
    
    return sequences_padded, labels_padded


class DataManager:
    """统一的数据管理器"""
    
    def __init__(self, config):
        self.config = config
        self.scaler = None
        self.train_dataset = None
        self.test_dataset = None
        self.label_counts = None
        
    def create_datasets(self, event_type: str, force_recreate: bool = False,
                       split_mode: str = 'temporal', train_events: int = None) -> bool:
        """
        创建训练和测试数据集
        
        Args:
            event_type: 事件类型
            force_recreate: 是否强制重新创建
            
        Returns:
            是否成功创建
        """
        output_dir = f'./output_{event_type}'
        train_path = os.path.join(output_dir, 'train_dataset.pt')
        test_path = os.path.join(output_dir, 'test_dataset.pt')
        scaler_path = os.path.join(output_dir, 'scaler.pkl')
        
        # 检查是否已存在
        if not force_recreate and all(os.path.exists(p) for p in [train_path, test_path, scaler_path]):
            print(f"Loading existing datasets from {output_dir}")
            return self._load_existing_datasets(output_dir)
        
        print(f"Creating new datasets for '{event_type}' events...")
        return self._create_new_datasets(event_type, output_dir, split_mode, train_events)
    
    def _load_existing_datasets(self, output_dir: str) -> bool:
        """加载已存在的数据集"""
        try:
            self.train_dataset = torch.load(os.path.join(output_dir, 'train_dataset.pt'), weights_only=False)
            self.test_dataset = torch.load(os.path.join(output_dir, 'test_dataset.pt'), weights_only=False)
            self.scaler = joblib.load(os.path.join(output_dir, 'scaler.pkl'))
            
            # 加载标签计数
            metadata_path = os.path.join(output_dir, 'metadata.pt')
            if os.path.exists(metadata_path):
                metadata = torch.load(metadata_path, weights_only=False)
                self.label_counts = metadata['label_counts']
            else:
                # 重新计算标签计数
                self._calculate_label_counts()
            
            print(f"✓ Loaded datasets: {len(self.train_dataset)} train, {len(self.test_dataset)} test")
            return True
            
        except Exception as e:
            print(f"❌ Error loading datasets: {e}")
            return False
    
    def _create_new_datasets(self, event_type: str, output_dir: str,
                            split_mode: str = 'temporal', train_events: int = None) -> bool:
        """创建新的数据集"""
        try:
            # 获取所有事件
            events = get_events_by_type(self.config.data_path, event_type)
            if not events:
                print(f"No {event_type} events found in {self.config.data_path}")
                return False

            # 如果指定了训练事件数量，只使用前N个事件
            if train_events is not None and train_events < len(events):
                events = events[:train_events]
                print(f"Using first {train_events} {event_type} events for training")

            print(f"Found {len(events)} {event_type} events")
            
            # 处理每个事件
            train_sequences = []
            train_labels = []
            test_sequences = []
            test_labels = []
            all_train_features = []
            
            for event_name in events:
                print(f"\nProcessing {event_name}...")
                
                # 加载事件数据
                features, labels = load_event_data(event_name, self.config.data_path)
                if features is None or labels is None:
                    continue
                
                # 智能时间划分
                train_feat, train_lab, test_feat, test_lab = intelligent_temporal_split(
                    features, labels, train_ratio=0.8
                )
                
                # 转换为张量并添加到列表
                if len(train_feat) > 0:
                    train_sequences.append(torch.tensor(train_feat, dtype=torch.float32))
                    train_labels.append(torch.tensor(train_lab, dtype=torch.long))
                    all_train_features.append(train_feat)
                
                if len(test_feat) > 0:
                    test_sequences.append(torch.tensor(test_feat, dtype=torch.float32))
                    test_labels.append(torch.tensor(test_lab, dtype=torch.long))
            
            if not train_sequences:
                print("No valid training data found")
                return False
            
            # 创建并拟合标准化器（仅使用训练数据）
            print("\nFitting scaler on training data...")
            combined_train_features = np.vstack(all_train_features)
            self.scaler = StandardScaler()
            self.scaler.fit(combined_train_features)
            
            # 标准化所有序列
            print("Applying standardization...")
            for i in range(len(train_sequences)):
                scaled_features = self.scaler.transform(train_sequences[i].numpy())
                train_sequences[i] = torch.tensor(scaled_features, dtype=torch.float32)
            
            for i in range(len(test_sequences)):
                scaled_features = self.scaler.transform(test_sequences[i].numpy())
                test_sequences[i] = torch.tensor(scaled_features, dtype=torch.float32)
            
            # 创建数据集
            self.train_dataset = BGPSequenceDataset(train_sequences, train_labels)
            self.test_dataset = BGPSequenceDataset(test_sequences, test_labels)
            
            # 计算标签计数
            self._calculate_label_counts()
            
            # 保存数据集和元数据
            os.makedirs(output_dir, exist_ok=True)
            
            torch.save(self.train_dataset, os.path.join(output_dir, 'train_dataset.pt'))
            torch.save(self.test_dataset, os.path.join(output_dir, 'test_dataset.pt'))
            joblib.dump(self.scaler, os.path.join(output_dir, 'scaler.pkl'))
            
            # 保存元数据
            metadata = {
                'label_counts': self.label_counts,
                'event_type': event_type,
                'n_features': combined_train_features.shape[1]
            }
            torch.save(metadata, os.path.join(output_dir, 'metadata.pt'))
            
            print(f"\n✅ Datasets created and saved to {output_dir}")
            print_dataset_summary(
                [(seq.numpy(), lab.numpy()) for seq, lab in zip(train_sequences, train_labels)],
                [(seq.numpy(), lab.numpy()) for seq, lab in zip(test_sequences, test_labels)],
                self.label_counts
            )
            
            return True
            
        except Exception as e:
            print(f"❌ Error creating datasets: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _calculate_label_counts(self):
        """计算标签分布"""
        all_labels = []
        for _, labels in self.train_dataset:
            all_labels.extend(labels.numpy().tolist())
        self.label_counts = Counter(all_labels)
    
    def create_data_loaders(self, batch_size: int, val_split: float = 0.2, 
                          shuffle: bool = True) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """
        创建数据加载器
        
        Args:
            batch_size: 批大小
            val_split: 验证集比例
            shuffle: 是否打乱
            
        Returns:
            train_loader, val_loader, test_loader
        """
        if self.train_dataset is None or self.test_dataset is None:
            raise ValueError("Datasets not created. Call create_datasets first.")
        
        # 创建训练和验证加载器
        dataset_size = len(self.train_dataset)
        indices = list(range(dataset_size))
        
        if val_split > 0:
            val_size = max(1, int(dataset_size * val_split))
            if shuffle:
                np.random.shuffle(indices)

            train_indices = indices[val_size:]
            val_indices = indices[:val_size]
            
            train_sampler = SubsetRandomSampler(train_indices)
            val_sampler = SubsetRandomSampler(val_indices)
            
            train_loader = DataLoader(
                self.train_dataset, batch_size=batch_size, 
                sampler=train_sampler, collate_fn=collate_fn
            )
            val_loader = DataLoader(
                self.train_dataset, batch_size=batch_size,
                sampler=val_sampler, collate_fn=collate_fn
            )
            
            print(f"Data loaders created: {len(train_indices)} train, {len(val_indices)} val")
        else:
            train_loader = DataLoader(
                self.train_dataset, batch_size=batch_size,
                shuffle=shuffle, collate_fn=collate_fn
            )
            val_loader = None
            print(f"Data loader created: {len(self.train_dataset)} train")
        
        # 创建测试加载器
        test_loader = DataLoader(
            self.test_dataset, batch_size=batch_size,
            shuffle=False, collate_fn=collate_fn
        )
        
        return train_loader, val_loader, test_loader
    
    def get_class_weights(self, device: str = 'cpu', anomaly_boost: float = 3.0) -> torch.Tensor:
        """获取类别权重，给异常类别额外权重"""
        if self.label_counts is None:
            raise ValueError("Label counts not available. Create datasets first.")

        return calculate_class_weights(self.label_counts, device, anomaly_boost)

    def get_feature_dim(self) -> int:
        """获取特征维度"""
        if self.train_dataset is None:
            raise ValueError("Dataset not created")

        sample_seq, _ = self.train_dataset[0]
        return sample_seq.shape[1]

    def get_dataset_info(self) -> Dict:
        """获取数据集信息"""
        if self.train_dataset is None or self.test_dataset is None:
            return {}

        return {
            'train_events': len(self.train_dataset),
            'test_events': len(self.test_dataset),
            'feature_dim': self.get_feature_dim(),
            'label_counts': dict(self.label_counts) if self.label_counts else {},
            'scaler_fitted': self.scaler is not None
        }
