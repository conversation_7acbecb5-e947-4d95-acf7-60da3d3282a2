"""
统一的评估器
处理所有模型评估逻辑
"""

import os
import torch
import numpy as np
from typing import Dict, List, Optional, Tuple
import joblib

from utils.metrics import (
    find_optimal_threshold, calculate_classification_metrics,
    calculate_detection_metrics, format_metrics_for_display,
    aggregate_metrics, print_summary_metrics, get_threshold_by_event_type
)


class ModelEvaluator:
    """统一的模型评估器"""
    
    def __init__(self, model, scaler, device='cpu'):
        """
        Args:
            model: 训练好的模型
            scaler: 数据标准化器
            device: 计算设备
        """
        self.model = model
        self.scaler = scaler
        self.device = device
        
        # 设置模型为评估模式
        self.model.eval()
        self.model.to(device)
    
    @classmethod
    def from_checkpoint(cls, model_path: str, scaler_path: str, config, device='cpu'):
        """
        从检查点加载评估器
        
        Args:
            model_path: 模型检查点路径
            scaler_path: 标准化器路径
            config: 模型配置
            device: 计算设备
            
        Returns:
            ModelEvaluator实例
        """
        from models.DeformableTST_BGP import DeformableTST_BGP
        
        # 加载模型
        model = DeformableTST_BGP.load_from_checkpoint(
            model_path, config=config, map_location=device
        )
        
        # 加载标准化器
        scaler = joblib.load(scaler_path)
        
        return cls(model, scaler, device)
    
    def predict_sequence(self, features: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        对单个序列进行预测
        
        Args:
            features: 特征序列 (timesteps, features)
            
        Returns:
            probabilities: 异常概率 (timesteps,)
            predictions: 预测标签 (timesteps,)
        """
        # 标准化特征
        features_scaled = self.scaler.transform(features)
        
        # 转换为张量
        input_tensor = torch.tensor(features_scaled, dtype=torch.float32).unsqueeze(0).to(self.device)
        
        # 模型预测
        with torch.no_grad():
            outputs = self.model(input_tensor)  # (1, seq_len, num_classes)
            probabilities = torch.softmax(outputs, dim=-1)[0, :, 1]  # 异常类别概率
        
        # 截断到原始长度
        original_length = len(features)
        probabilities = probabilities[:original_length].cpu().numpy()
        
        return probabilities, None  # 预测标签将在阈值优化后确定
    
    def evaluate_sequence(self, features: np.ndarray, labels: np.ndarray, 
                         event_name: str = "", event_type: str = "hijack") -> Dict:
        """
        评估单个序列
        
        Args:
            features: 特征序列
            labels: 真实标签序列
            event_name: 事件名称
            event_type: 事件类型
            
        Returns:
            评估结果字典
        """
        # 检查是否有异常样本
        anomaly_count = np.sum(labels == 1)
        if anomaly_count == 0:
            print(f"  ⚠️ No anomaly samples in {event_name}, skipping")
            return None
        
        print(f"  📊 {event_name}: {len(features)} timesteps, {anomaly_count} anomalies")
        
        # 获取预测概率
        probabilities, _ = self.predict_sequence(features)
        
        # 获取事件类型的阈值范围
        min_threshold, max_threshold = get_threshold_by_event_type(event_type)
        
        # 寻找最优阈值
        best_threshold, _, best_metrics = find_optimal_threshold(
            probabilities, labels, min_threshold, max_threshold
        )
        
        if best_metrics is None:
            print(f"  ❌ No valid metrics found for {event_name}")
            return None
        
        # 计算检测相关指标
        detection_metrics = calculate_detection_metrics(best_metrics)
        
        # 添加序列信息
        result = {
            'event_name': event_name,
            'timesteps': len(features),
            'anomalies': anomaly_count,
            'threshold': best_threshold,
            **detection_metrics
        }
        
        print(f"  🎯 Optimal threshold: {best_threshold:.4f}")
        print(f"  📈 F1: {best_metrics['f1']:.4f}, Precision: {best_metrics['precision']:.4f}, Recall: {best_metrics['recall']:.4f}")
        
        return result
    
    def evaluate_dataset(self, dataset, event_type: str = "hijack") -> List[Dict]:
        """
        评估整个数据集
        
        Args:
            dataset: 数据集
            event_type: 事件类型
            
        Returns:
            评估结果列表
        """
        results = []
        
        print(f"\n🔍 Evaluating {len(dataset)} events...")
        
        for i in range(len(dataset)):
            if len(dataset[i]) == 3:
                # 新格式: (features, labels, event_name)
                features, labels, event_name = dataset[i]
            else:
                # 旧格式: (features, labels)
                features, labels = dataset[i]
                event_name = f"event_{i+1}"
            
            # 转换为numpy数组
            if isinstance(features, torch.Tensor):
                features = features.numpy()
            if isinstance(labels, torch.Tensor):
                labels = labels.numpy()
            
            # 评估单个序列
            result = self.evaluate_sequence(features, labels, event_name, event_type)
            if result is not None:
                results.append(result)
        
        return results
    
    def print_evaluation_results(self, results: List[Dict], event_type: str, validation_mode: str = "full_sequence"):
        """
        打印评估结果

        Args:
            results: 评估结果列表
            event_type: 事件类型
            validation_mode: 验证模式
        """
        if not results:
            print(f"❌ No valid results for {event_type} events")
            return
        
        print(f"\n{'='*60}")
        if validation_mode == "rolling_window":
            print(f"DTST {event_type.upper()} Evaluation Results - Rolling Window Validation:")
        else:
            print(f"DTST {event_type.upper()} Evaluation Results - Full Sequence Validation:")
        print('='*60)
        
        # 打印每个事件的结果
        for result in results:
            metrics_dict = {k: v for k, v in result.items() 
                          if k not in ['event_name', 'timesteps', 'anomalies']}
            print(format_metrics_for_display(metrics_dict, result['event_name']))
            print()
        
        # 计算和打印汇总统计
        aggregated = aggregate_metrics(results)
        print_summary_metrics(aggregated, len(results))
    
    def save_results(self, results: List[Dict], event_type: str, output_file: str = None,
                    validation_mode: str = "full_sequence", window_size: int = None):
        """
        保存评估结果
        
        Args:
            results: 评估结果列表
            event_type: 事件类型
            output_file: 输出文件路径
        """
        if not results:
            return
        
        if output_file is None:
            output_file = f"dtst_{event_type}_evaluation_results.txt"
        
        with open(output_file, 'w') as f:
            # 写入标题，包含验证模式信息
            if validation_mode == "rolling_window":
                f.write(f"DTST {event_type.upper()} Evaluation Results - Rolling Window Validation\n")
                f.write(f"Window Size: {window_size}\n")
            else:
                f.write(f"DTST {event_type.upper()} Evaluation Results - Full Sequence Validation\n")
            f.write('='*60 + '\n\n')
            
            for result in results:
                f.write(f"{result['event_name']}:\n")
                f.write(f"  F1-Score:     {result['f1']:.4f}\n")
                f.write(f"  Precision:    {result['precision']:.4f}\n")
                f.write(f"  Recall:       {result['recall']:.4f}\n")
                f.write(f"  Accuracy:     {result['accuracy']:.4f}\n")
                f.write(f"  Alarms:       {result['alarms']}\n")
                f.write(f"  False Alarms: {result['false_alarms']}\n")
                f.write(f"  Threshold:    {result['threshold']:.4f}\n")
                f.write(f"  Timesteps:    {result['timesteps']}\n")
                f.write(f"  Anomalies:    {result['anomalies']}\n")

                # 滚动窗口特有信息
                if validation_mode == "rolling_window":
                    f.write(f"  Window Size:  {result.get('window_size', window_size)}\n")
                    f.write(f"  Original Timesteps: {result.get('original_timesteps', 'N/A')}\n")
                    f.write(f"  Total Anomalies: {result.get('total_anomalies', 'N/A')}\n")

                f.write("\n")
            
            # 添加汇总统计
            aggregated = aggregate_metrics(results)
            f.write("\nSummary Statistics:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Total Events: {len(results)}\n")
            f.write(f"Average F1: {aggregated.get('avg_f1', 0):.4f}\n")
            f.write(f"Average Precision: {aggregated.get('avg_precision', 0):.4f}\n")
            f.write(f"Average Recall: {aggregated.get('avg_recall', 0):.4f}\n")
            f.write(f"Overall F1: {aggregated.get('overall_f1', 0):.4f}\n")
            f.write(f"Overall Precision: {aggregated.get('overall_precision', 0):.4f}\n")
            f.write(f"Overall Recall: {aggregated.get('overall_recall', 0):.4f}\n")
        
        print(f"\n💾 Results saved to: {output_file}")
    
    def evaluate_and_save(self, dataset, event_type: str, output_file: str = None) -> List[Dict]:
        """
        评估数据集并保存结果
        
        Args:
            dataset: 数据集
            event_type: 事件类型
            output_file: 输出文件路径
            
        Returns:
            评估结果列表
        """
        # 评估数据集
        results = self.evaluate_dataset(dataset, event_type)
        
        # 打印结果
        self.print_evaluation_results(results, event_type)
        
        # 保存结果
        self.save_results(results, event_type, output_file)
        
        return results

    def evaluate_rolling_window(self, dataset: List[Tuple[np.ndarray, np.ndarray, str]],
                               event_type: str = "hijack", window_size: int = 32,
                               output_file: str = None) -> List[Dict]:
        """
        使用滚动窗口验证模式评估数据集

        Args:
            dataset: 数据集列表，每个元素为(features, labels, event_name)
            event_type: 事件类型
            window_size: 滚动窗口大小
            output_file: 输出文件路径

        Returns:
            评估结果列表
        """
        print(f"\n🪟 Rolling Window Validation (window_size={window_size})")
        print("="*60)

        results = []

        for features, labels, event_name in dataset:
            print(f"\n📊 Evaluating {event_name}...")

            # 使用滚动窗口评估单个序列
            result = self.evaluate_sequence_rolling_window(
                features, labels, event_name, event_type, window_size
            )

            if result is not None:
                results.append(result)

        # 打印评估结果
        self.print_evaluation_results(results, event_type, validation_mode="rolling_window")

        # 保存结果
        if output_file:
            self.save_results(results, event_type, output_file, validation_mode="rolling_window", window_size=window_size)

        return results

    def evaluate_sequence_rolling_window(self, features: np.ndarray, labels: np.ndarray,
                                       event_name: str = "", event_type: str = "hijack",
                                       window_size: int = 32) -> Dict:
        """
        使用滚动窗口评估单个序列

        Args:
            features: 特征序列
            labels: 真实标签序列
            event_name: 事件名称
            event_type: 事件类型
            window_size: 滚动窗口大小

        Returns:
            评估结果字典
        """
        # 检查序列长度
        if len(features) < window_size:
            print(f"  ⚠️ Sequence too short ({len(features)} < {window_size}), skipping {event_name}")
            return None

        # 检查是否有异常样本
        anomaly_count = np.sum(labels == 1)
        if anomaly_count == 0:
            print(f"  ⚠️ No anomaly samples in {event_name}, skipping")
            return None

        print(f"  📊 {event_name}: {len(features)} timesteps, {anomaly_count} anomalies")
        print(f"  🪟 Using rolling window of size {window_size}")

        # 滚动窗口预测 - 修复版本：从第0步开始预测
        prediction_probs = []
        true_labels = []

        # 为每个时间点创建历史窗口（包括前window_size个时间步）
        for i in range(len(features)):
            true_label = labels[i]

            if i < window_size:
                # 对于前window_size个时间步，使用填充策略
                # 策略：重复第一个时间步来填充不足的历史
                available_steps = i + 1
                if available_steps == 1:
                    # 只有一个时间步，重复它来填充整个窗口
                    window_features = np.tile(features[0:1, :], (window_size, 1))
                else:
                    # 有多个时间步，先使用可用的，然后重复第一个时间步填充
                    padding_needed = window_size - available_steps
                    padding = np.tile(features[0:1, :], (padding_needed, 1))
                    window_features = np.vstack([padding, features[:available_steps, :]])
            else:
                # 正常情况：使用前window_size个时间步
                window_features = features[i-window_size:i, :]

            # 预测当前时间步
            prob = self.predict_single_timestep(window_features)

            prediction_probs.append(prob)
            true_labels.append(true_label)

        # 转换为numpy数组
        prediction_probs = np.array(prediction_probs)
        true_labels = np.array(true_labels)

        print(f"  📈 Generated {len(prediction_probs)} rolling window predictions (covering all timesteps)")

        # 获取事件类型的阈值范围
        min_threshold, max_threshold = get_threshold_by_event_type(event_type)

        # 寻找最优阈值
        best_threshold, _, best_metrics = find_optimal_threshold(
            prediction_probs, true_labels, min_threshold, max_threshold
        )

        if best_metrics is None:
            print(f"  ❌ No valid metrics found for {event_name}")
            return None

        # 计算检测相关指标
        detection_metrics = calculate_detection_metrics(best_metrics)

        # 添加序列信息
        result = {
            'event_name': event_name,
            'timesteps': len(prediction_probs),  # 实际预测的时间步数
            'original_timesteps': len(features),  # 原始序列长度
            'anomalies': np.sum(true_labels == 1),  # 窗口范围内的异常数
            'total_anomalies': anomaly_count,  # 原始序列中的总异常数
            'window_size': window_size,
            'threshold': best_threshold,
            **detection_metrics
        }

        print(f"  🎯 Optimal threshold: {best_threshold:.4f}")
        print(f"  📈 F1: {best_metrics['f1']:.4f}, Precision: {best_metrics['precision']:.4f}, Recall: {best_metrics['recall']:.4f}")

        return result

    def predict_single_timestep(self, window_features: np.ndarray) -> float:
        """
        使用历史窗口预测单个时间步的异常概率

        Args:
            window_features: 历史窗口特征 (window_size, n_features)

        Returns:
            异常概率
        """
        # 标准化特征
        window_scaled = self.scaler.transform(window_features)

        # 转换为张量
        input_tensor = torch.tensor(window_scaled, dtype=torch.float32).unsqueeze(0).to(self.device)

        # 模型预测
        self.model.eval()
        with torch.no_grad():
            outputs = self.model(input_tensor)
            probs = torch.softmax(outputs, dim=-1)

            # 取最后一个时间步的预测（对应当前时间点）
            final_probs = probs[0, -1, :]  # (2,)
            anomaly_prob = final_probs[1].item()

            return anomaly_prob
