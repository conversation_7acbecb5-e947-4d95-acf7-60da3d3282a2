#!/usr/bin/env python3
"""
使用滚动窗口训练DTST模型
确保训练和推理的一致性
"""

import os
import sys
import argparse
import torch
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
from pytorch_lightning.loggers import TensorBoardLogger

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import DTSTConfig
from core.data_manager import DataManager
from core.trainer import DTSTTrainer


def main():
    """主训练函数"""
    parser = argparse.ArgumentParser(description="Train DTST model with rolling window strategy")
    parser.add_argument('--event_type', type=str, required=True,
                       choices=['hijack', 'leak', 'outage'],
                       help="Type of BGP event to train on")
    parser.add_argument('--window_size', type=int, default=32,
                       help="Rolling window size for training")
    parser.add_argument('--max_epochs', type=int, default=100,
                       help="Maximum number of training epochs")
    parser.add_argument('--batch_size', type=int, default=4,
                       help="Batch size (smaller for rolling window training)")
    parser.add_argument('--learning_rate', type=float, default=1e-4,
                       help="Learning rate")
    parser.add_argument('--output_dir', type=str, default=None,
                       help="Output directory for models and logs")
    
    args = parser.parse_args()
    
    # Initialize configuration with rolling window training
    config = DTSTConfig()
    config.use_rolling_window_training = True
    config.rolling_window_size = args.window_size
    config.max_epochs = args.max_epochs
    config.batch_size = args.batch_size
    config.learning_rate = args.learning_rate
    
    # Set output directory
    if args.output_dir is None:
        args.output_dir = f'./models_{args.event_type}_rolling_window'
    
    print(f"🚀 DTST Rolling Window Training")
    print(f"🎯 Event Type: {args.event_type}")
    print(f"🪟 Window Size: {args.window_size}")
    print(f"📊 Batch Size: {args.batch_size}")
    print(f"📈 Learning Rate: {args.learning_rate}")
    print(f"🔄 Max Epochs: {args.max_epochs}")
    print(f"📁 Output Dir: {args.output_dir}")
    
    try:
        # Create data manager and load datasets
        data_manager = DataManager(config)
        
        if not data_manager.create_datasets(args.event_type):
            print("❌ Failed to load datasets")
            return
        
        print(f"✅ Datasets loaded: {len(data_manager.train_dataset)} train, {len(data_manager.test_dataset)} test")
        
        # Create trainer
        trainer = DTSTTrainer(config, data_manager)
        
        # Setup PyTorch Lightning trainer
        os.makedirs(args.output_dir, exist_ok=True)
        
        # Callbacks
        checkpoint_callback = ModelCheckpoint(
            dirpath=args.output_dir,
            filename=f'{args.event_type}-rolling-window-{{epoch:02d}}-{{val_loss:.3f}}',
            monitor='val_loss',
            mode='min',
            save_top_k=3,
            save_last=True
        )
        
        early_stopping = EarlyStopping(
            monitor='val_loss',
            patience=config.patience,
            mode='min',
            verbose=True
        )
        
        # Logger
        logger = TensorBoardLogger(
            save_dir=args.output_dir,
            name=f'{args.event_type}_rolling_window_logs'
        )
        
        # PyTorch Lightning trainer
        pl_trainer = pl.Trainer(
            max_epochs=config.max_epochs,
            callbacks=[checkpoint_callback, early_stopping],
            logger=logger,
            accelerator='auto',
            devices='auto',
            precision=32,
            gradient_clip_val=1.0,
            log_every_n_steps=10,
            check_val_every_n_epoch=1
        )
        
        print(f"\n🏋️ Starting rolling window training...")
        print(f"⚠️ Note: Rolling window training is computationally intensive!")
        print(f"   Each epoch processes {args.window_size}x more forward passes than normal training.")
        
        # Start training
        pl_trainer.fit(trainer.model, trainer.train_dataloader, trainer.val_dataloader)
        
        print(f"\n✅ Training completed!")
        print(f"📁 Best model saved in: {args.output_dir}")
        print(f"📊 TensorBoard logs: {logger.log_dir}")
        
        # Test the best model
        best_model_path = checkpoint_callback.best_model_path
        if best_model_path:
            print(f"\n🧪 Testing best model: {best_model_path}")
            pl_trainer.test(ckpt_path=best_model_path, dataloaders=trainer.test_dataloader)
        
    except Exception as e:
        print(f"❌ Error during training: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
