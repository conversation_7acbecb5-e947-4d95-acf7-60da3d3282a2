"""
简化的数据加载器
主要功能已迁移到core/data_manager.py
保留基础数据集类以兼容现有代码
"""

import torch
from torch.utils.data import Dataset
from torch.nn.utils.rnn import pad_sequence


def variable_length_collate_fn(batch):
    """
    Custom collate function for variable length sequences
    """
    sequences, labels = zip(*batch)

    # Pad sequences to the same length within the batch
    sequences_padded = pad_sequence(sequences, batch_first=True, padding_value=0.0)
    labels_padded = pad_sequence(labels, batch_first=True, padding_value=0)  # Pad labels with 0 (normal)

    return sequences_padded, labels_padded


class BGPFullSequenceDataset(Dataset):
    """
    Full sequence dataset for BGP anomaly detection (no sliding window)
    Time-step level classification - supports variable length sequences
    
    Note: This class is kept for backward compatibility.
    New code should use core.data_manager.BGPSequenceDataset
    """
    def __init__(self, sequences, sequence_labels):
        super(BGPFullSequenceDataset, self).__init__()
        self.sequences = sequences        # List of sequences, each with shape (seq_len, n_features)
        self.sequence_labels = sequence_labels  # List of label sequences, each with shape (seq_len,)

        print(f"Dataset created with {len(sequences)} sequences")
        
        # Calculate sequence length statistics
        if sequences:
            seq_lengths = [len(seq) for seq in sequences]
            print(f"Sequence lengths range: {min(seq_lengths)} - {max(seq_lengths)}")

    def __len__(self):
        return len(self.sequences)

    def __getitem__(self, idx):
        return self.sequences[idx], self.sequence_labels[idx]


# 为了向后兼容，保留一些基础函数的存根
def load_and_process_event_data(event_name, base_data_dir):
    """
    向后兼容的函数存根
    实际功能已迁移到utils.data_utils.load_event_data
    """
    from utils.data_utils import load_event_data
    return load_event_data(event_name, base_data_dir)


def get_class_weights(label_counts, device='cpu'):
    """
    向后兼容的函数存根
    实际功能已迁移到utils.data_utils.calculate_class_weights
    """
    from utils.data_utils import calculate_class_weights
    return calculate_class_weights(label_counts, device)


if __name__ == "__main__":
    print("⚠️ This module has been refactored.")
    print("📦 Main functionality moved to:")
    print("   - core.data_manager.DataManager")
    print("   - utils.data_utils")
    print("   - utils.metrics")
    print("🚀 Use the new train.py and evaluate.py scripts for training and evaluation.")
