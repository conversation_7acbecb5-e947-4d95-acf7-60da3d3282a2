"""
评估指标计算工具
"""

import numpy as np
from sklearn.metrics import confusion_matrix, precision_recall_fscore_support
from typing import Tu<PERSON>, Dict, Optional


def find_optimal_threshold(probabilities: np.ndarray, true_labels: np.n<PERSON>ray, 
                          min_threshold: float = 0.05, max_threshold: float = 0.95,
                          step: float = 0.05) -> Tuple[float, float, Dict]:
    """
    寻找最优分类阈值
    
    Args:
        probabilities: 预测概率
        true_labels: 真实标签
        min_threshold: 最小阈值
        max_threshold: 最大阈值
        step: 步长
        
    Returns:
        best_threshold: 最优阈值
        best_f1: 最优F1分数
        best_metrics: 最优指标字典
    """
    thresholds = np.arange(min_threshold, max_threshold + step, step)
    best_f1 = -1
    best_threshold = 0.5
    best_metrics = None
    
    for threshold in thresholds:
        predictions = (probabilities >= threshold).astype(int)
        metrics = calculate_classification_metrics(true_labels, predictions)
        
        if metrics['f1'] > best_f1:
            best_f1 = metrics['f1']
            best_threshold = threshold
            best_metrics = metrics.copy()
            best_metrics['threshold'] = threshold
    
    return best_threshold, best_f1, best_metrics


def calculate_classification_metrics(true_labels: np.ndarray, predictions: np.ndarray) -> Dict:
    """
    计算分类指标
    
    Args:
        true_labels: 真实标签
        predictions: 预测标签
        
    Returns:
        指标字典
    """
    # 处理边界情况
    if len(np.unique(true_labels)) == 1 and len(np.unique(predictions)) == 1:
        if true_labels[0] == predictions[0]:
            return {
                'precision': 1.0, 'recall': 1.0, 'f1': 1.0,
                'tp': len(true_labels) if true_labels[0] == 1 else 0,
                'fp': 0, 'tn': len(true_labels) if true_labels[0] == 0 else 0, 'fn': 0,
                'accuracy': 1.0
            }
        else:
            return {
                'precision': 0.0, 'recall': 0.0, 'f1': 0.0,
                'tp': 0, 'fp': len(predictions) if predictions[0] == 1 else 0,
                'tn': 0, 'fn': len(true_labels) if true_labels[0] == 1 else 0,
                'accuracy': 0.0
            }
    
    try:
        # 计算混淆矩阵
        cm = confusion_matrix(true_labels, predictions, labels=[0, 1])
        
        if cm.shape == (2, 2):
            tn, fp, fn, tp = cm.ravel()
        elif cm.shape == (1, 1):
            # 只有一个类别
            if np.unique(true_labels)[0] == 0:
                tn, fp, fn, tp = cm[0, 0], 0, 0, 0
            else:
                tn, fp, fn, tp = 0, 0, 0, cm[0, 0]
        else:
            # 异常情况
            tn = fp = fn = tp = 0
        
        # 计算基础指标
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        accuracy = (tp + tn) / (tp + tn + fp + fn) if (tp + tn + fp + fn) > 0 else 0.0
        
        return {
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'accuracy': accuracy,
            'tp': int(tp),
            'fp': int(fp),
            'tn': int(tn),
            'fn': int(fn)
        }
        
    except Exception as e:
        print(f"Error calculating metrics: {e}")
        return {
            'precision': 0.0, 'recall': 0.0, 'f1': 0.0, 'accuracy': 0.0,
            'tp': 0, 'fp': 0, 'tn': 0, 'fn': 0
        }


def calculate_detection_metrics(metrics: Dict) -> Dict:
    """
    计算检测相关指标
    
    Args:
        metrics: 基础分类指标
        
    Returns:
        扩展的指标字典
    """
    result = metrics.copy()
    
    # 报警相关指标
    result['alarms'] = metrics['tp'] + metrics['fp']  # 总报警数
    result['false_alarms'] = metrics['fp']  # 误报数
    
    # 检测率
    result['detection_rate'] = metrics['recall']  # 检测率 = 召回率
    
    # 误报率
    total_normal = metrics['tn'] + metrics['fp']
    result['false_positive_rate'] = metrics['fp'] / total_normal if total_normal > 0 else 0.0
    
    # 漏报率
    total_anomaly = metrics['tp'] + metrics['fn']
    result['false_negative_rate'] = metrics['fn'] / total_anomaly if total_anomaly > 0 else 0.0
    
    return result


def format_metrics_for_display(metrics: Dict, event_name: str = "") -> str:
    """
    格式化指标用于显示
    
    Args:
        metrics: 指标字典
        event_name: 事件名称
        
    Returns:
        格式化的字符串
    """
    lines = []
    
    if event_name:
        lines.append(f"📊 {event_name}:")
    
    lines.extend([
        f"  F1-Score:     {metrics['f1']:.4f}",
        f"  Precision:    {metrics['precision']:.4f}",
        f"  Recall:       {metrics['recall']:.4f}",
        f"  Accuracy:     {metrics['accuracy']:.4f}",
    ])
    
    if 'alarms' in metrics:
        lines.extend([
            f"  Alarms:       {metrics['alarms']}",
            f"  False Alarms: {metrics['false_alarms']}",
        ])
    
    if 'threshold' in metrics:
        lines.append(f"  Threshold:    {metrics['threshold']:.4f}")
    
    # 混淆矩阵
    lines.extend([
        f"  Confusion Matrix:",
        f"    TP: {metrics['tp']}, FP: {metrics['fp']}",
        f"    FN: {metrics['fn']}, TN: {metrics['tn']}"
    ])
    
    return "\n".join(lines)


def aggregate_metrics(metrics_list: list) -> Dict:
    """
    聚合多个指标
    
    Args:
        metrics_list: 指标字典列表
        
    Returns:
        聚合后的指标字典
    """
    if not metrics_list:
        return {}
    
    # 计算平均值
    avg_metrics = {}
    for key in ['f1', 'precision', 'recall', 'accuracy']:
        values = [m[key] for m in metrics_list if key in m]
        avg_metrics[f'avg_{key}'] = np.mean(values) if values else 0.0
        avg_metrics[f'std_{key}'] = np.std(values) if values else 0.0
    
    # 计算总和
    for key in ['tp', 'fp', 'tn', 'fn', 'alarms', 'false_alarms']:
        values = [m[key] for m in metrics_list if key in m]
        avg_metrics[f'total_{key}'] = sum(values) if values else 0
    
    # 计算总体指标
    total_tp = avg_metrics.get('total_tp', 0)
    total_fp = avg_metrics.get('total_fp', 0)
    total_tn = avg_metrics.get('total_tn', 0)
    total_fn = avg_metrics.get('total_fn', 0)
    
    if total_tp + total_fp > 0:
        avg_metrics['overall_precision'] = total_tp / (total_tp + total_fp)
    else:
        avg_metrics['overall_precision'] = 0.0
    
    if total_tp + total_fn > 0:
        avg_metrics['overall_recall'] = total_tp / (total_tp + total_fn)
    else:
        avg_metrics['overall_recall'] = 0.0
    
    if avg_metrics['overall_precision'] + avg_metrics['overall_recall'] > 0:
        avg_metrics['overall_f1'] = (2 * avg_metrics['overall_precision'] * avg_metrics['overall_recall'] / 
                                    (avg_metrics['overall_precision'] + avg_metrics['overall_recall']))
    else:
        avg_metrics['overall_f1'] = 0.0
    
    if total_tp + total_tn + total_fp + total_fn > 0:
        avg_metrics['overall_accuracy'] = (total_tp + total_tn) / (total_tp + total_tn + total_fp + total_fn)
    else:
        avg_metrics['overall_accuracy'] = 0.0
    
    return avg_metrics


def print_summary_metrics(aggregated_metrics: Dict, total_events: int):
    """
    打印汇总指标
    
    Args:
        aggregated_metrics: 聚合指标
        total_events: 总事件数
    """
    print(f"\n📈 Summary Statistics ({total_events} events):")
    print("="*50)
    
    print(f"Average Metrics:")
    print(f"  F1-Score:  {aggregated_metrics.get('avg_f1', 0):.4f} ± {aggregated_metrics.get('std_f1', 0):.4f}")
    print(f"  Precision: {aggregated_metrics.get('avg_precision', 0):.4f} ± {aggregated_metrics.get('std_precision', 0):.4f}")
    print(f"  Recall:    {aggregated_metrics.get('avg_recall', 0):.4f} ± {aggregated_metrics.get('std_recall', 0):.4f}")
    print(f"  Accuracy:  {aggregated_metrics.get('avg_accuracy', 0):.4f} ± {aggregated_metrics.get('std_accuracy', 0):.4f}")
    
    print(f"\nOverall Metrics:")
    print(f"  F1-Score:  {aggregated_metrics.get('overall_f1', 0):.4f}")
    print(f"  Precision: {aggregated_metrics.get('overall_precision', 0):.4f}")
    print(f"  Recall:    {aggregated_metrics.get('overall_recall', 0):.4f}")
    print(f"  Accuracy:  {aggregated_metrics.get('overall_accuracy', 0):.4f}")
    
    print(f"\nDetection Summary:")
    print(f"  Total Alarms: {aggregated_metrics.get('total_alarms', 0)}")
    print(f"  False Alarms: {aggregated_metrics.get('total_false_alarms', 0)}")
    
    if aggregated_metrics.get('total_alarms', 0) > 0:
        false_alarm_rate = aggregated_metrics.get('total_false_alarms', 0) / aggregated_metrics.get('total_alarms', 1)
        print(f"  False Alarm Rate: {false_alarm_rate:.4f} ({false_alarm_rate*100:.2f}%)")


def get_threshold_by_event_type(event_type: str) -> Tuple[float, float]:
    """
    根据事件类型获取推荐的阈值范围
    
    Args:
        event_type: 事件类型
        
    Returns:
        (min_threshold, max_threshold)
    """
    thresholds = {
        'hijack': (0.001, 0.95),  # 降低最小阈值以适应模型输出
        'leak': (0.001, 0.95),    # 同样调整leak和outage
        'outage': (0.001, 0.95)
    }
    
    return thresholds.get(event_type, (0.05, 0.95))
