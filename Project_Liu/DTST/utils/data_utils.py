"""
数据处理工具函数
"""

import os
import pandas as pd
import numpy as np
import torch
from typing import Tuple, List, Optional, Dict
from collections import Counter


def load_event_data(event_name: str, base_data_dir: str) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
    """
    加载单个事件的原始数据 (仅支持JSON格式)

    Args:
        event_name: 事件名称
        base_data_dir: 数据基础目录

    Returns:
        features: 特征数据 (timesteps, features)
        labels: 标签数据 (timesteps,)
    """
    # 加载原始JSON格式数据
    features_file = os.path.join(base_data_dir, event_name, 'features', 'fea.json')
    if not os.path.exists(features_file):
        print(f"Features file not found: {features_file}")
        return None, None

    try:
        features_df = pd.read_json(features_file)

        # 最后一列是标签，其他是特征
        labels = features_df.iloc[:, -1].values.astype(int)
        features = features_df.iloc[:, :-1].values.astype(np.float32)

        print(f"Loaded {event_name}: Features {features.shape}, Labels {labels.shape}")
        return features, labels

    except Exception as e:
        print(f"Error loading {event_name}: {e}")
        return None, None


def analyze_anomaly_distribution(labels: np.ndarray) -> Dict:
    """
    分析异常分布
    
    Args:
        labels: 标签序列
        
    Returns:
        分析结果字典
    """
    anomaly_indices = np.where(labels == 1)[0]
    total_timesteps = len(labels)
    
    if len(anomaly_indices) == 0:
        return {
            'total_timesteps': total_timesteps,
            'anomaly_count': 0,
            'anomaly_ratio': 0.0,
            'first_anomaly': None,
            'last_anomaly': None,
            'anomaly_span': 0,
            'has_anomalies': False
        }
    
    first_anomaly = anomaly_indices[0]
    last_anomaly = anomaly_indices[-1]
    anomaly_span = last_anomaly - first_anomaly + 1
    
    return {
        'total_timesteps': total_timesteps,
        'anomaly_count': len(anomaly_indices),
        'anomaly_ratio': len(anomaly_indices) / total_timesteps,
        'first_anomaly': first_anomaly,
        'last_anomaly': last_anomaly,
        'anomaly_span': anomaly_span,
        'anomaly_indices': anomaly_indices,
        'has_anomalies': True
    }


def intelligent_temporal_split(features: np.ndarray, labels: np.ndarray, 
                             train_ratio: float = 0.8, 
                             min_anomaly_ratio: float = 0.1) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    智能时间划分：确保训练集和测试集都包含异常样本
    
    Args:
        features: 特征数据
        labels: 标签数据
        train_ratio: 训练集比例
        min_anomaly_ratio: 测试集最小异常比例
        
    Returns:
        train_features, train_labels, test_features, test_labels
    """
    analysis = analyze_anomaly_distribution(labels)
    
    if not analysis['has_anomalies']:
        # 没有异常，使用简单时间划分
        split_point = int(len(features) * train_ratio)
        return (features[:split_point], labels[:split_point], 
                features[split_point:], labels[split_point:])
    
    anomaly_indices = analysis['anomaly_indices']
    total_anomalies = len(anomaly_indices)
    
    # 计算目标异常分布
    target_train_anomalies = max(1, int(total_anomalies * train_ratio))
    target_test_anomalies = total_anomalies - target_train_anomalies
    
    if target_test_anomalies == 0:
        # 如果只有一个异常，确保测试集至少有一个
        target_train_anomalies = total_anomalies - 1
        target_test_anomalies = 1
    
    # 策略1：尝试在异常分布中找到合适的分割点
    best_split = None
    best_score = float('inf')
    
    # 在异常区间内寻找分割点
    first_anomaly = analysis['first_anomaly']
    last_anomaly = analysis['last_anomaly']
    
    # 扩展搜索范围到异常区间前后
    search_start = max(0, first_anomaly - 50)
    search_end = min(len(features), last_anomaly + 50)
    
    for split_point in range(search_start, search_end):
        if split_point <= 0 or split_point >= len(features):
            continue
            
        train_labels_temp = labels[:split_point]
        test_labels_temp = labels[split_point:]
        
        train_anomalies = np.sum(train_labels_temp == 1)
        test_anomalies = np.sum(test_labels_temp == 1)
        
        # 确保两个集合都有异常
        if train_anomalies == 0 or test_anomalies == 0:
            continue
            
        # 计算与目标分布的偏差
        train_ratio_actual = split_point / len(features)
        anomaly_ratio_deviation = abs(train_anomalies - target_train_anomalies)
        time_ratio_deviation = abs(train_ratio_actual - train_ratio)
        
        # 综合评分（异常分布权重更高）
        score = anomaly_ratio_deviation * 2 + time_ratio_deviation * len(features) * 0.01
        
        if score < best_score:
            best_score = score
            best_split = split_point
    
    # 如果没找到合适的分割点，使用备选策略
    if best_split is None:
        print("Warning: Could not find optimal split, using fallback strategy")
        
        # 备选策略：确保测试集包含最后几个异常
        if total_anomalies >= 2:
            # 将最后25%的异常分配给测试集
            test_anomaly_start_idx = int(total_anomalies * 0.75)
            split_point = anomaly_indices[test_anomaly_start_idx]
        else:
            # 只有一个异常，在异常位置附近分割
            split_point = min(anomaly_indices[0] + 10, int(len(features) * 0.9))
        
        best_split = split_point
    
    # 执行分割
    train_features = features[:best_split]
    train_labels = labels[:best_split]
    test_features = features[best_split:]
    test_labels = labels[best_split:]
    
    # 验证分割结果
    train_anomalies = np.sum(train_labels == 1)
    test_anomalies = np.sum(test_labels == 1)
    
    print(f"  Split at timestep {best_split} ({best_split/len(features)*100:.1f}%)")
    print(f"  Train: {len(train_labels)} timesteps, {train_anomalies} anomalies")
    print(f"  Test:  {len(test_labels)} timesteps, {test_anomalies} anomalies")
    
    return train_features, train_labels, test_features, test_labels


def get_events_by_type(base_data_dir: str, event_type: str) -> List[str]:
    """
    获取指定类型的所有事件
    
    Args:
        base_data_dir: 数据基础目录
        event_type: 事件类型 (hijack, leak, outage)
        
    Returns:
        事件名称列表
    """
    if not os.path.exists(base_data_dir):
        print(f"Data directory not found: {base_data_dir}")
        return []
    
    all_events = [d for d in os.listdir(base_data_dir) 
                  if os.path.isdir(os.path.join(base_data_dir, d))]
    
    events = [e for e in all_events if e.startswith(f'{event_type}-')]
    events.sort()  # 确保顺序一致
    
    return events


def calculate_class_weights(label_counts: Counter, device: str = 'cpu', anomaly_boost: float = 2.0) -> torch.Tensor:
    """
    计算类别权重以处理不平衡数据，给异常类别额外权重

    Args:
        label_counts: 标签计数
        device: 设备类型
        anomaly_boost: 异常类别权重提升倍数

    Returns:
        类别权重张量
    """
    total_samples = sum(label_counts.values())
    n_classes = len(label_counts)

    weights = []
    for class_id in sorted(label_counts.keys()):
        class_count = label_counts[class_id]
        if class_count > 0:
            # 标准平衡权重
            weight = total_samples / (n_classes * class_count)
            # 如果是异常类别（标签1），给予额外权重
            if class_id == 1:
                weight *= anomaly_boost
            weights.append(weight)
        else:
            weights.append(0)

    return torch.tensor(weights, dtype=torch.float32, device=device)


def print_dataset_summary(train_data: List, test_data: List, label_counts: Counter):
    """
    打印数据集摘要信息
    
    Args:
        train_data: 训练数据列表
        test_data: 测试数据列表  
        label_counts: 标签计数
    """
    print(f"\n📊 Dataset Summary:")
    print(f"   Training events: {len(train_data)}")
    print(f"   Test events: {len(test_data)}")
    
    # 计算总时间步
    total_train_timesteps = sum(len(labels) for _, labels in train_data)
    total_test_timesteps = sum(len(labels) for _, labels in test_data)
    
    print(f"   Training timesteps: {total_train_timesteps:,}")
    print(f"   Test timesteps: {total_test_timesteps:,}")
    print(f"   Label distribution: {dict(label_counts)}")
    
    # 计算异常比例
    if 1 in label_counts:
        anomaly_ratio = label_counts[1] / sum(label_counts.values())
        print(f"   Anomaly ratio: {anomaly_ratio:.4f} ({anomaly_ratio*100:.2f}%)")
