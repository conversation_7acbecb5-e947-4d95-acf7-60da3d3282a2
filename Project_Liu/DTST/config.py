"""
Configuration for DTST BGP Anomaly Detection
"""

class DTSTConfig:
    def __init__(self):
        # Data parameters
        self.n_features = 26       # Number of BGP features (excluding label)
        self.num_classes = 2       # Binary classification (normal vs anomaly)

        # Patching parameters
        self.patch_size = 32       # Size of each patch (P)
        self.patch_stride = None   # Stride for patching (None = non-overlapping)
        self.embed_dim = 64        # Embedding dimension for patches (D_0)

        # Dynamic sequence length handling
        if self.patch_stride is None:
            self.patch_stride = self.patch_size  # Non-overlapping

        # These will be set dynamically based on actual sequence length
        self.max_seq_length = None  # No fixed limit
        self.n_patches = None       # Calculated dynamically
        self.effective_seq_length = None  # Calculated dynamically

        # Model architecture parameters
        self.n_vars = self.n_features
        self.seq_len = self.n_patches  # Use patch sequence length instead of raw sequence
        self.pred_len = 1  # For classification, we predict 1 class
        
        # DeformableTST specific parameters
        self.rev = True  # Use RevIN normalization
        self.revin_affine = True
        self.revin_subtract_last = False
        
        # Multi-stage architecture (will be calculated dynamically)
        self.stem_ratio = 2
        self.down_ratio = 4
        self.fmap_size = None  # Will be calculated based on actual patch count
        self.dims = [self.embed_dim, self.embed_dim * 2]  # Feature dimensions for each stage
        self.depths = [2, 2]   # Number of layers in each stage
        
        # Attention parameters
        self.heads = [4, 8]
        self.attn_drop = 0.1
        self.proj_drop = 0.1
        self.stage_spec = [['D', 'D'], ['D', 'D']]  # All layers use deformable attention
        
        # Deformable attention specific
        self.window_size_attn = 7
        self.nat_ksize = 7
        self.ksize = 3
        self.stride = 1
        self.n_groups = 2
        self.offset_range_factor = 2.0
        self.no_off = False
        self.dwc_pe = True
        self.fixed_pe = False
        self.log_cpb = False
        self.use_pe = True
        
        # MLP parameters
        self.expansion = 4
        self.drop = 0.1
        self.use_dwc_mlp = True
        self.local_kernel_size = 3
        self.use_lpu = True
        
        # Training parameters
        self.drop_path_rate = 0.1
        self.layer_scale_value = 1e-6
        
        # Head parameters
        self.head_dropout = 0.1
        self.head_type = 'Classification'  # Changed from 'Flatten' to 'Classification'
        self.use_head_norm = True
        
        # Training hyperparameters
        self.batch_size = 8   # Smaller batch size for full sequences
        self.learning_rate = 1e-4
        self.weight_decay = 1e-5
        self.max_epochs = 100
        self.patience = 20  # Early stopping patience

        # Rolling window training parameters
        self.use_rolling_window_training = False  # 是否使用滚动窗口训练
        self.rolling_window_size = 32             # 滚动窗口大小
        
        # Data augmentation for BGP anomaly detection
        self.augmentation_factors = {
            'hijack': 20,  # 20x augmentation for hijack events
            'leak': 10,    # 10x augmentation for leak events  
            'outage': 5    # 5x augmentation for outage events
        }
        
        # Paths - use original anomaly event route data (fea.json files)
        self.data_path = '/data/data/anomaly-event-routedata'
        self.output_dir = './output'
        self.model_dir = './models'
        self.log_dir = './logs'
        
    def get_model_params(self):
        """Get parameters for model initialization"""
        return {
            'n_vars': self.n_vars,
            'rev': self.rev,
            'revin_affine': self.revin_affine,
            'revin_subtract_last': self.revin_subtract_last,
            'stem_ratio': self.stem_ratio,
            'down_ratio': self.down_ratio,
            'fmap_size': [16, 4] if self.fmap_size is None else self.fmap_size,  # Default values
            'dims': self.dims,
            'depths': self.depths,
            'drop_path_rate': self.drop_path_rate,
            'layer_scale_value': self.layer_scale_value,
            'use_pe': self.use_pe,
            'use_lpu': self.use_lpu,
            'local_kernel_size': self.local_kernel_size,
            'expansion': self.expansion,
            'drop': self.drop,
            'use_dwc_mlp': self.use_dwc_mlp,
            'heads': self.heads,
            'attn_drop': self.attn_drop,
            'proj_drop': self.proj_drop,
            'stage_spec': self.stage_spec,
            'window_size': self.window_size_attn,
            'nat_ksize': self.nat_ksize,
            'ksize': self.ksize,
            'stride': self.stride,
            'n_groups': self.n_groups,
            'offset_range_factor': self.offset_range_factor,
            'no_off': self.no_off,
            'dwc_pe': self.dwc_pe,
            'fixed_pe': self.fixed_pe,
            'log_cpb': self.log_cpb,
            'seq_len': 32 if self.seq_len is None else self.seq_len,  # Default patch count
            'pred_len': self.pred_len,
            'head_dropout': self.head_dropout,
            'head_type': self.head_type,
            'use_head_norm': self.use_head_norm,
            'num_classes': self.num_classes,
            'patch_size': self.patch_size,
            'embed_dim': self.embed_dim,
            'n_patches': 32 if self.n_patches is None else self.n_patches,  # Default patch count

            # Rolling window training parameters
            'use_rolling_window_training': self.use_rolling_window_training,
            'rolling_window_size': self.rolling_window_size,
        }
