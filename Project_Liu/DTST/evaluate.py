#!/usr/bin/env python3
"""
统一的DTST评估脚本
使用重构后的核心组件进行评估
"""

import os
import sys
import argparse

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import DTSTConfig
from core.data_manager import DataManager
from core.evaluator import ModelEvaluator
import numpy as np


def compare_validation_results(full_results, rolling_results, event_type):
    """对比完整序列验证和滚动窗口验证的结果"""

    print(f"\n📊 Validation Results Comparison for {event_type.upper()} Events")
    print("="*80)

    if not full_results or not rolling_results:
        print("❌ Cannot compare: one or both result sets are empty")
        return

    # 计算平均性能指标
    def calculate_avg_metrics(results):
        if not results:
            return {}

        metrics = ['f1', 'precision', 'recall', 'accuracy']
        avg_metrics = {}

        for metric in metrics:
            values = [r[metric] for r in results if metric in r]
            avg_metrics[metric] = np.mean(values) if values else 0

        return avg_metrics

    full_avg = calculate_avg_metrics(full_results)
    rolling_avg = calculate_avg_metrics(rolling_results)

    print(f"📈 Performance Comparison:")
    print(f"{'Metric':<12} {'Full Sequence':<15} {'Rolling Window':<15} {'Difference':<12}")
    print("-" * 60)

    for metric in ['f1', 'precision', 'recall', 'accuracy']:
        full_val = full_avg.get(metric, 0)
        rolling_val = rolling_avg.get(metric, 0)
        diff = full_val - rolling_val

        print(f"{metric.capitalize():<12} {full_val:<15.4f} {rolling_val:<15.4f} {diff:+.4f}")

    print(f"\n💡 Analysis:")

    # 性能差异分析
    f1_diff = full_avg.get('f1', 0) - rolling_avg.get('f1', 0)
    if f1_diff > 0.05:
        print(f"  🔍 Rolling window validation shows significantly lower F1 ({f1_diff:.4f} drop)")
        print(f"     This is expected as rolling window doesn't use future information")
    elif f1_diff > 0.02:
        print(f"  📊 Rolling window validation shows moderate F1 drop ({f1_diff:.4f})")
        print(f"     Performance is still reasonable for real-time applications")
    else:
        print(f"  ✅ Rolling window validation maintains similar performance ({f1_diff:.4f} drop)")
        print(f"     Excellent for real-time deployment")

    # 实际应用建议
    rolling_f1 = rolling_avg.get('f1', 0)
    if rolling_f1 > 0.6:
        print(f"  🎯 Rolling window F1 ({rolling_f1:.4f}) is excellent for real-time detection")
    elif rolling_f1 > 0.4:
        print(f"  👍 Rolling window F1 ({rolling_f1:.4f}) is good for real-time detection")
    else:
        print(f"  ⚠️ Rolling window F1 ({rolling_f1:.4f}) may need model optimization")


def main():
    """主评估函数"""
    parser = argparse.ArgumentParser(description="Evaluate DTST model for BGP anomaly detection")
    parser.add_argument('--event_type', type=str, required=True,
                       choices=['hijack', 'leak', 'outage'],
                       help="Type of BGP event to evaluate")
    parser.add_argument('--model_path', type=str, required=True,
                       help="Path to the trained model checkpoint")
    parser.add_argument('--scaler_path', type=str, default=None,
                       help="Path to the scaler file (auto-detect if not provided)")
    parser.add_argument('--output_file', type=str, default=None,
                       help="Output file for results (auto-generate if not provided)")
    parser.add_argument('--dataset_type', type=str, default='test',
                       choices=['train', 'test'],
                       help="Which dataset to evaluate on")
    parser.add_argument('--validation_mode', type=str, default='full_sequence',
                       choices=['full_sequence', 'rolling_window', 'both'],
                       help="Validation mode: full_sequence, rolling_window, or both")
    parser.add_argument('--window_size', type=int, default=32,
                       help="Window size for rolling window validation")

    args = parser.parse_args()
    
    # Initialize configuration
    config = DTSTConfig()
    
    # Auto-detect scaler path if not provided
    if args.scaler_path is None:
        model_dir = os.path.dirname(args.model_path)
        output_dir = model_dir.replace('models_', 'output_')
        args.scaler_path = os.path.join(output_dir, 'scaler.pkl')
    
    print(f"🚀 DTST BGP Anomaly Detection Evaluation")
    print(f"🎯 Event Type: {args.event_type}")
    print(f"📁 Model: {args.model_path}")
    print(f"📁 Scaler: {args.scaler_path}")
    print(f"📊 Dataset: {args.dataset_type}")
    print(f"🔄 Validation Mode: {args.validation_mode}")
    if args.validation_mode in ['rolling_window', 'both']:
        print(f"🪟 Window Size: {args.window_size}")
    
    try:
        # Create data manager and load datasets
        data_manager = DataManager(config)
        
        if not data_manager.create_datasets(args.event_type):
            print("❌ Failed to load datasets")
            return
        
        # Get the appropriate dataset
        if args.dataset_type == 'test':
            bgp_dataset = data_manager.test_dataset
            print(f"📊 Evaluating on test set: {len(bgp_dataset)} events")
        else:
            bgp_dataset = data_manager.train_dataset
            print(f"📊 Evaluating on training set: {len(bgp_dataset)} events")

        # Convert BGPSequenceDataset to list format expected by evaluator
        dataset = []
        for i in range(len(bgp_dataset)):
            features_tensor, labels_tensor = bgp_dataset[i]
            features = features_tensor.numpy()
            labels = labels_tensor.numpy()
            event_name = f"test_sequence_{i}"
            dataset.append((features, labels, event_name))

        print(f"✅ Converted to evaluator format: {len(dataset)} sequences")
        
        # Create evaluator
        import torch
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"🖥️ Using device: {device}")

        evaluator = ModelEvaluator.from_checkpoint(
            args.model_path, args.scaler_path, config, device
        )
        
        print(f"✅ Model and scaler loaded successfully")
        
        # Evaluate and save results
        if args.validation_mode == 'full_sequence':
            # 原有的完整序列验证
            results = evaluator.evaluate_dataset(dataset, args.event_type)
            if results:
                evaluator.save_results(results, args.event_type, args.output_file)
        elif args.validation_mode == 'rolling_window':
            # 滚动窗口验证
            results = evaluator.evaluate_rolling_window(
                dataset, args.event_type, args.window_size, args.output_file
            )
        else:  # both
            # 两种验证方式都执行
            print(f"\n🔄 Full Sequence Validation:")
            full_results = evaluator.evaluate_dataset(dataset, args.event_type)
            if full_results:
                evaluator.save_results(full_results, args.event_type,
                                     args.output_file.replace('.txt', '_full_sequence.txt'))

            print(f"\n🪟 Rolling Window Validation:")
            rolling_results = evaluator.evaluate_rolling_window(
                dataset, args.event_type, args.window_size,
                args.output_file.replace('.txt', '_rolling_window.txt')
            )

            # 对比分析
            if full_results and rolling_results:
                compare_validation_results(full_results, rolling_results, args.event_type)
            results = {'full_sequence': full_results, 'rolling_window': rolling_results}
        
        if results:
            print(f"\n✅ Evaluation completed successfully!")
            print(f"📊 Evaluated {len(results)} events with anomalies")
        else:
            print(f"\n⚠️ No events with anomalies found for evaluation")
            
    except Exception as e:
        print(f"❌ Error during evaluation: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
