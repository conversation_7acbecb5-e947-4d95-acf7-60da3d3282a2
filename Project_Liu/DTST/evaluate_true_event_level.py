#!/usr/bin/env python3
"""
真正的事件级DTST评估脚本
评估38个事件作为patching的模型性能
"""

import os
import sys
import argparse
import torch
import numpy as np
import pandas as pd
from sklearn.metrics import classification_report, confusion_matrix, f1_score, precision_score, recall_score

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import DTSTConfig
from core.true_event_data_manager import TrueEventDataManager, collate_all_events
from models.TrueEventDTST import TrueEventDTST
from torch.utils.data import DataLoader


def evaluate_true_event_dtst(model_path, test_split='test'):
    """
    评估真正的事件级DTST模型

    Args:
        model_path: 模型路径
        test_split: 测试数据集类型 ('test' 或 'all')
    """
    print(f"🔍 Evaluating TRUE Event-Level DTST Model on {test_split.upper()} set")
    print("="*80)
    print("📋 Architecture: Multi-Event → Event Embeddings → Attention → Time Point Predictions")
    print("📋 Train/Test Split: 30 training events, 9 test events")
    print("="*80)
    
    # 1. 加载配置和数据
    config = DTSTConfig()
    config.embed_dim = 128  # 与训练时保持一致
    config.heads = [8]  # 与训练时保持一致
    config.depths = [4]  # 与训练时保持一致
    
    data_manager = TrueEventDataManager(
        data_path=config.data_path,
        target_event_types=['hijack', 'leak', 'outage']  # 加载所有类型
    )

    # 加载测试数据集
    if test_split == 'test':
        # 先创建训练集以拟合标准化器，然后获取测试集
        _, test_dataset = data_manager.create_train_test_split(normalize=True)
    else:
        # 加载所有事件
        test_dataset = data_manager.load_all_events(normalize=True, split='all')

    print(f"✅ Loaded {test_split.upper()} dataset for evaluation")
    
    # 2. 加载模型
    print(f"📂 Loading model from: {model_path}")
    model = TrueEventDTST.load_from_checkpoint(model_path, config=config)
    model.eval()
    
    # 移动到GPU（如果可用）
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    print(f"🔧 Model loaded on device: {device}")
    
    # 3. 创建数据加载器
    dataloader = DataLoader(
        test_dataset,
        batch_size=1,
        shuffle=False,
        collate_fn=collate_all_events
    )
    
    # 4. 评估模型
    print("🎯 Starting evaluation...")
    
    all_predictions = []
    all_true_labels = []
    all_probabilities = []
    event_results = []
    
    with torch.no_grad():
        for batch in dataloader:
            all_events_data = batch['all_events_features']
            all_events_labels = batch['all_events_labels']
            all_events_info = batch['all_events_info']
            
            # 前向传播 - 获取所有事件的时间点预测
            all_event_predictions = model(all_events_data)  # List[Tensor(seq_len, 2)]
            
            # 处理每个事件的预测结果
            for i, (predictions, labels, info) in enumerate(zip(all_event_predictions, all_events_labels, all_events_info)):
                # predictions: (seq_len, 2)
                # labels: (seq_len,)
                
                if len(labels) > 0:
                    # 确保长度匹配
                    min_len = min(len(predictions), len(labels))
                    pred_trimmed = predictions[:min_len]
                    labels_trimmed = labels[:min_len]
                    
                    # 获取异常概率和预测类别
                    probs = torch.softmax(pred_trimmed, dim=1)  # (seq_len, 2)
                    anomaly_probs = probs[:, 1].cpu().numpy()  # 异常概率
                    true_classes = labels_trimmed.cpu().numpy()  # 真实类别

                    # 为当前事件寻找最佳阈值
                    best_f1 = 0
                    best_threshold = 0.5
                    best_pred_classes = (anomaly_probs > 0.5).astype(int)

                    if np.sum(true_classes == 1) > 0:  # 只有当存在异常时才寻找阈值
                        for thresh in np.arange(0.05, 0.95, 0.05):
                            thresh_pred = (anomaly_probs > thresh).astype(int)
                            if len(np.unique(thresh_pred)) > 1 or np.sum(thresh_pred == 1) > 0:
                                thresh_f1 = f1_score(true_classes, thresh_pred, zero_division=0)
                                if thresh_f1 > best_f1:
                                    best_f1 = thresh_f1
                                    best_threshold = thresh
                                    best_pred_classes = thresh_pred

                    pred_classes = best_pred_classes
                    
                    # 添加到总体结果
                    all_predictions.extend(pred_classes)
                    all_true_labels.extend(true_classes)
                    all_probabilities.extend(anomaly_probs)
                    
                    # 计算事件级指标
                    event_accuracy = np.mean(pred_classes == true_classes)
                    event_anomaly_count = np.sum(true_classes == 1)
                    event_detected_count = np.sum((pred_classes == 1) & (true_classes == 1))
                    event_false_alarms = np.sum((pred_classes == 1) & (true_classes == 0))
                    
                    # 事件级F1分数
                    if event_anomaly_count > 0:
                        event_recall = event_detected_count / event_anomaly_count
                        if np.sum(pred_classes == 1) > 0:
                            event_precision = event_detected_count / np.sum(pred_classes == 1)
                            event_f1 = 2 * (event_precision * event_recall) / (event_precision + event_recall) if (event_precision + event_recall) > 0 else 0
                        else:
                            event_precision = 0
                            event_f1 = 0
                    else:
                        event_recall = 0
                        event_precision = 1 if np.sum(pred_classes == 1) == 0 else 0
                        event_f1 = 0
                    
                    # 记录详细结果
                    event_results.append({
                        'event_name': info['event_name'],
                        'event_type': info['event_type'],
                        'sequence_length': len(labels_trimmed),
                        'anomaly_count': event_anomaly_count,
                        'detected_count': event_detected_count,
                        'false_alarms': event_false_alarms,
                        'accuracy': event_accuracy,
                        'precision': event_precision,
                        'recall': event_recall,
                        'f1_score': event_f1,
                        'best_threshold': best_threshold,
                        'avg_anomaly_prob': np.mean(anomaly_probs),
                        'max_anomaly_prob': np.max(anomaly_probs),
                        'min_anomaly_prob': np.min(anomaly_probs)
                    })
                    
                    print(f"  Event {i+1:2d}: {info['event_name'][:45]:<45}")
                    print(f"           Length: {len(labels_trimmed):4d} | Anomalies: {event_anomaly_count:3d} | "
                          f"Detected: {event_detected_count:3d} | F1: {event_f1:.3f} | Threshold: {best_threshold:.2f}")
    
    # 5. 计算总体评估指标
    print("\n📊 Overall Evaluation Results:")
    print("-"*60)
    
    # 时间点级别指标
    accuracy = np.mean(np.array(all_predictions) == np.array(all_true_labels))
    precision = precision_score(all_true_labels, all_predictions, zero_division=0)
    recall = recall_score(all_true_labels, all_predictions, zero_division=0)
    f1 = f1_score(all_true_labels, all_predictions, zero_division=0)
    
    print(f"Time-Point Level Metrics:")
    print(f"  Accuracy:  {accuracy:.4f}")
    print(f"  Precision: {precision:.4f}")
    print(f"  Recall:    {recall:.4f}")
    print(f"  F1-Score:  {f1:.4f}")
    
    # 混淆矩阵
    cm = confusion_matrix(all_true_labels, all_predictions)
    print(f"\nConfusion Matrix:")
    print(f"                Predicted")
    print(f"                Normal  Anomaly")
    print(f"Actual Normal   {cm[0,0]:6d}  {cm[0,1]:7d}")
    print(f"Actual Anomaly  {cm[1,0]:6d}  {cm[1,1]:7d}")
    
    # 事件级别指标
    results_df = pd.DataFrame(event_results)
    
    print(f"\nEvent-Level Metrics:")
    print(f"  Total events: {len(results_df)}")
    print(f"  Events with anomalies: {len(results_df[results_df['anomaly_count'] > 0])}")
    print(f"  Events with detections: {len(results_df[results_df['detected_count'] > 0])}")
    print(f"  Average event F1: {results_df['f1_score'].mean():.4f}")
    print(f"  Average event accuracy: {results_df['accuracy'].mean():.4f}")

    # 阈值分布统计
    print(f"\n🎯 Optimal Threshold Analysis:")
    print("-"*40)
    thresholds = results_df['best_threshold'].values
    print(f"  Threshold range: {np.min(thresholds):.2f} - {np.max(thresholds):.2f}")
    print(f"  Average threshold: {np.mean(thresholds):.3f}")
    print(f"  Median threshold: {np.median(thresholds):.3f}")

    # 按事件类型分组的阈值统计
    for event_type in results_df['event_type'].unique():
        type_df = results_df[results_df['event_type'] == event_type]
        type_thresholds = type_df['best_threshold'].values
        print(f"  {event_type.upper()} events: avg={np.mean(type_thresholds):.3f}, "
              f"range=[{np.min(type_thresholds):.2f}, {np.max(type_thresholds):.2f}]")
    
    # 6. 阈值分析
    print(f"\n🔍 Threshold Analysis:")
    print("-"*40)
    
    best_f1 = 0
    best_threshold = 0.5
    
    for thresh in np.arange(0.1, 0.9, 0.05):
        thresh_predictions = (np.array(all_probabilities) > thresh).astype(int)
        thresh_f1 = f1_score(all_true_labels, thresh_predictions, zero_division=0)
        
        if thresh_f1 > best_f1:
            best_f1 = thresh_f1
            best_threshold = thresh
        
        print(f"Threshold {thresh:.2f}: F1 = {thresh_f1:.4f}")
    
    print(f"\n🏆 Best threshold: {best_threshold:.2f} (F1 = {best_f1:.4f})")
    
    # 7. 保存详细结果
    results_file = f'true_event_level_{test_split}_evaluation_results.csv'
    results_df.to_csv(results_file, index=False)
    print(f"\n💾 Detailed results saved to: {results_file}")
    
    # 8. 异常检测分析
    print(f"\n📈 Anomaly Detection Analysis:")
    print("-"*35)
    
    total_anomalies = sum(results_df['anomaly_count'])
    total_detected = sum(results_df['detected_count'])
    total_false_alarms = sum(results_df['false_alarms'])
    
    print(f"Total time points: {len(all_true_labels)}")
    print(f"Total anomalies: {total_anomalies}")
    print(f"Total detected: {total_detected}")
    print(f"Total false alarms: {total_false_alarms}")
    print(f"Detection rate: {total_detected/total_anomalies*100:.1f}%")
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'best_threshold': best_threshold,
        'best_f1': best_f1,
        'confusion_matrix': cm,
        'event_results': event_results
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Evaluate TRUE Event-Level DTST model")
    parser.add_argument('--model_path', type=str, required=True,
                       help="Path to the trained model checkpoint")
    parser.add_argument('--test_split', type=str, default='test',
                       choices=['test', 'all'],
                       help="Which dataset to evaluate on")

    args = parser.parse_args()

    if not os.path.exists(args.model_path):
        print(f"❌ Model file not found: {args.model_path}")
        sys.exit(1)

    print("🔧 Configuration:")
    print(f"  Model Path: {args.model_path}")
    print(f"  Test Split: {args.test_split}")
    print()

    # 开始评估
    results = evaluate_true_event_dtst(
        model_path=args.model_path,
        test_split=args.test_split
    )
    
    print(f"\n✅ Evaluation completed!")
    print(f"🏆 Final F1-Score: {results['f1_score']:.4f}")
    print(f"🎯 Best F1-Score: {results['best_f1']:.4f} (threshold: {results['best_threshold']:.2f})")
    print(f"📊 Detection Rate: {results['recall']*100:.1f}%")


if __name__ == "__main__":
    main()
