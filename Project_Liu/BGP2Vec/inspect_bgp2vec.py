# ==============================================================================
#  BGP2Vec - 知识库探索脚本
# ==============================================================================
#
#  功能：
#  1. 加载已训练的BGP2Vec模型。
#  2. 演示如何查询特定AS的向量。
#  3. 演示如何查询与某个AS最相似的其他AS。
#  4. 演示如何获取为未知AS准备的[UNK]向量。
#

from gensim.models import Word2Vec
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- 配置 ---
MODEL_PATH = 'Project_Liu/BGP2Vec/bgp2vec.model'
UNK_TOKEN = '[UNK]'

def main():
    """主执行函数"""
    
    # --- 1. 加载BGP2Vec模型 ---
    logging.info(f"正在加载BGP2Vec模型从: {MODEL_PATH}...")
    try:
        model = Word2Vec.load(MODEL_PATH)
    except FileNotFoundError:
        logging.error(f"错误: 模型文件 {MODEL_PATH} 不存在。")
        logging.error("请先成功运行 'Project_Liu/BGP2Vec/train_bgp2vec.py'。")
        return
    logging.info("模型加载成功。")
    logging.info(f"词汇表大小 (包含'{UNK_TOKEN}'): {len(model.wv.key_to_index)}")

    # --- 2. 演示如何查询向量 ---
    print("\n" + "="*20 + " 向量查询演示 " + "="*20)
    
    # 查询一个常见的、肯定存在的AS
    target_as = '701'
    try:
        vector = model.wv[target_as]
        print(f"ASN '{target_as}' 的向量 (前5维): {vector[:5]}")
        print(f"向量维度: {len(vector)}")
    except KeyError:
        print(f"ASN '{target_as}' 在词汇表中未找到。")
        
    # 查询我们为未知AS准备的[UNK]向量
    try:
        unk_vector = model.wv[UNK_TOKEN]
        print(f"特殊标记 '{UNK_TOKEN}' 的向量 (前5维): {unk_vector[:5]}")
    except KeyError:
        print(f"特殊标记 '{UNK_TOKEN}' 在词汇表中未找到。")

    # --- 3. 演示如何查找相似AS ---
    print("\n" + "="*20 + " 相似性查询演示 " + "="*20)
    
    # 查找与'701'最相似的5个AS
    try:
        similar_asns = model.wv.most_similar(target_as, topn=5)
        print(f"与ASN '{target_as}' 最相似的5个AS是:")
        for asn, similarity in similar_asns:
            print(f"  - ASN: {asn:<10} | 相似度: {similarity:.4f}")
    except KeyError:
        print(f"无法为 '{target_as}' 查找相似项，因为它不在词汇表中。")

if __name__ == '__main__':
    main() 