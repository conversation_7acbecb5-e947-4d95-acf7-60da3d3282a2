# ==============================================================================
#  BGP-BERT - 模型架构
# ==============================================================================
#  定义了BGP-BERT模型的结构，这是一个基于Transformer的编码器，
#  用于学习BGP路径的上下文表示。
#

import torch
import torch.nn as nn
import math
from gensim.models import Word2Vec # 导入Word2Vec以加载预训练AS嵌入
import numpy as np # 用于处理numpy数组

# 导入项目配置
import config

class PositionalEncoding(nn.Module):
    """
    为序列注入位置信息。
    改编自PyTorch官方教程 "SEQUENCE-TO-SEQUENCE MODELING WITH NN.TRANSFORMER AND TORCHTEXT"。
    """
    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)

        position = torch.arange(max_len).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2) * (-math.log(10000.0) / d_model))
        pe = torch.zeros(max_len, 1, d_model)
        pe[:, 0, 0::2] = torch.sin(position * div_term)
        pe[:, 0, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: Tensor, shape [seq_len, batch_size, embedding_dim]
        """
        x = x + self.pe[:x.size(0)]
        return self.dropout(x)

class BGPBERT(nn.Module):
    """
    BGP-BERT 模型。
    它接收一批经过掩码的AS ID序列，并预测出原始的AS嵌入。
    """
    def __init__(self, vocab_size: int, embed_dim: int, transformer_dim: int, 
                 n_heads: int, n_layers: int, dim_feedforward: int, dropout: float,
                 bgp2vec_model: Word2Vec, mask_token_id: int):
        super().__init__()
        self.model_type = 'Transformer'
        self.embed_dim = embed_dim
        self.transformer_dim = transformer_dim
        self.mask_token_id = mask_token_id

        # 1. AS 嵌入层 (BGP2Vec集成)
        self.embedding_layer = nn.Embedding(vocab_size, embed_dim)
        # 加载预训练的BGP2Vec权重并冻结 (或不冻结，实现联合训练)
        # 这里我们实现联合训练，所以不冻结
        if bgp2vec_model:
            # 确保Word2Vec模型中所有AS都对应到vocab_size
            # Word2Vec的vocab是一个dict，键是AS字符串，值是Vocab对象，其中index是其ID
            # embedding_layer的weight是(num_embeddings, embedding_dim)
            # 我们需要将bgp2vec_model.wv.vectors (numpy array) 复制到embedding_layer的weight
            
            # 获取bgp2vec的词汇表大小和嵌入维度
            bgp2vec_vocab_size = len(bgp2vec_model.wv)
            bgp2vec_embed_dim = bgp2vec_model.wv.vector_size
            
            if bgp2vec_embed_dim != embed_dim:
                raise ValueError(f"BGP2Vec嵌入维度 ({bgp2vec_embed_dim}) 与模型定义的嵌入维度 ({embed_dim}) 不匹配。")
            
            # 创建一个与embedding_layer相同大小的权重矩阵，并用bgp2vec的权重填充
            initial_weights = torch.randn(vocab_size, embed_dim) * 0.01 # 随机初始化所有AS

            # 填充来自bgp2vec的已知AS权重
            for word, vocab_obj in bgp2vec_model.wv.key_to_index.items():
                if vocab_obj < vocab_size: # 确保ID在我们的词汇表范围内
                    initial_weights[vocab_obj] = torch.tensor(bgp2vec_model.wv[word], dtype=torch.float32)
            
            self.embedding_layer.weight.data = initial_weights
            # 可以选择在这里冻结嵌入层：self.embedding_layer.weight.requires_grad = False
            # 但为了联合训练，我们不冻结
        else:
            self.embedding_layer.weight.data.uniform_(-0.1, 0.1) # 没有预训练模型时随机初始化

        # 输入投影层: 将嵌入维度投影到Transformer的工作维度
        self.input_projection = nn.Linear(embed_dim, transformer_dim)

        # 2. 位置编码
        self.pos_encoder = PositionalEncoding(transformer_dim, dropout)

        # 3. Transformer 编码器核心
        encoder_layers = nn.TransformerEncoderLayer(
            d_model=transformer_dim, 
            nhead=n_heads, 
            dim_feedforward=dim_feedforward, 
            dropout=dropout,
            batch_first=True # 重要：我们的数据是 [batch, seq, feature] 格式
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layers, num_layers=n_layers)
        
        # 输出投影层: 将Transformer的输出维度投影回原始嵌入维度，以便计算损失
        self.output_projection = nn.Linear(transformer_dim, embed_dim)

        self.init_weights()

    def init_weights(self) -> None:
        """
        初始化非嵌入层的权重。
        嵌入层已在__init__中加载或初始化。
        """
        initrange = 0.1
        self.input_projection.weight.data.uniform_(-initrange, initrange)
        self.input_projection.bias.data.zero_()
        self.output_projection.weight.data.uniform_(-initrange, initrange)
        self.output_projection.bias.data.zero_()

    def forward(self, src_ids: torch.Tensor, src_padding_mask: torch.Tensor = None) -> torch.Tensor:
        """
        BGP-BERT的正向传播。
        
        Args:
            src_ids: 输入AS ID张量, shape [batch_size, seq_len]
            src_padding_mask: 填充掩码, shape [batch_size, seq_len]
                              值为True的位置是填充，在注意力计算中将被忽略。

        Returns:
            输出张量, shape [batch_size, seq_len, embed_dim] (预测的AS嵌入)
        """
        
        # 1. 通过嵌入层获取AS嵌入
        # input_embeds shape: [batch_size, seq_len, embed_dim]
        input_embeds = self.embedding_layer(src_ids)

        # 2. 识别并替换 [MASK] 占位符
        # 数据加载器会将被掩码的AS ID设置为 mask_token_id
        is_mask_token = (src_ids == self.mask_token_id).unsqueeze(-1).expand_as(input_embeds)
        
        # 将所有被掩码的位置替换为可学习的 [MASK] 嵌入
        # 我们直接使用 embedding_layer.weight[mask_token_id] 作为 MASK 嵌入
        # 因为 mask_token_id 会在 embedding_layer 中有自己的行
        # 但是，为了让 MASK 真正可学习，且不被普通的 AS 嵌入影响，我们单独定义一个参数。
        # 这里的 `self.embedding_layer.weight[self.mask_token_id]` 会被优化。
        # if mask_token_id is also part of vocab, then it will be trained.
        # Let's adjust to pass mask_token_embedding from outside if not using ID from vocab.
        # Or, we can ensure MASK_TOKEN has a dedicated ID in the vocab that maps to its specific embedding.

        # Best practice: MASK_TOKEN is just another token in the vocabulary with a specific ID.
        # Its embedding will be learned as part of self.embedding_layer.
        
        # No need for src_clone if mask_token_id is handled by embedding layer directly
        # The embedding_layer(src_ids) will already give the MASK_TOKEN's embedding at masked positions.
        
        # 3. 输入投影 (如果 embed_dim != transformer_dim)
        projected_src = self.input_projection(input_embeds)
        
        # 4. 位置编码
        # PyTorch TransformerEncoder 需要 [seq_len, batch_size, features] 格式 (如果batch_first=False)
        # 或 [batch_size, seq_len, features] (如果batch_first=True)
        # 我们的 PositionalEncoding 默认使用 [seq_len, batch_size, features]
        # 为了保持一致，我们调整一下数据流
        projected_src = self.pos_encoder(projected_src.permute(1, 0, 2)).permute(1, 0, 2)
        
        # 5. 通过Transformer编码器
        memory = self.transformer_encoder(projected_src, src_key_padding_mask=src_padding_mask)
        
        # 6. 输出投影
        output = self.output_projection(memory)
        
        return output

if __name__ == '__main__':
    # --- 用于测试模型架构的示例代码 ---
    # 注意：这里的测试代码需要更新以匹配新的BGPBERT初始化和forward签名
    # 在实际train.py中，这些参数将从bgp2vec模型和config中获取
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    logging.info("开始测试BGP-BERT模型架构...")
    
    # 模拟 bgp2vec_model
    class DummyWordVectors:
        def __init__(self, vocab_size, embed_dim):
            self.vectors = np.random.rand(vocab_size, embed_dim).astype(np.float32)
            self.key_to_index = {str(i): i for i in range(vocab_size)}
            self.vector_size = embed_dim
        def __len__(self):
            return len(self.vectors)
        def __contains__(self, word):
            return word in self.key_to_index
        def __getitem__(self, word):
            return self.vectors[self.key_to_index[word]]

    dummy_vocab_size = 50000 # 模拟词汇表大小
    dummy_embed_dim = 64     # 模拟嵌入维度
    dummy_bgp2vec = Word2Vec()
    dummy_bgp2vec.wv = DummyWordVectors(dummy_vocab_size, dummy_embed_dim)

    # 模拟 config 中的特殊 token ID
    # 假设这些ID会添加到真实的BGP2Vec vocab中，或者单独管理
    dummy_mask_token_id = dummy_vocab_size # MASK token ID
    dummy_pad_token_id = dummy_vocab_size + 1 # PAD token ID
    # 真实情况下，这些ID会在data_loader中与bgp2vec的vocab合并管理

    # 使用config中的参数实例化模型
    model = BGPBERT(
        vocab_size=dummy_vocab_size + 2, # +2 for MASK and PAD
        embed_dim=dummy_embed_dim,
        transformer_dim=config.TRANSFORMER_DIM,
        n_heads=config.N_HEADS,
        n_layers=config.N_LAYERS,
        dim_feedforward=config.DIM_FEEDFORWARD,
        dropout=config.DROPOUT,
        bgp2vec_model=dummy_bgp2vec,
        mask_token_id=dummy_mask_token_id
    ).to(config.DEVICE)

    model.eval()

    # 创建一个模拟的输入批次 (现在是AS ID序列)
    batch_size = 4
    seq_len = 10
    
    # 模拟来自DataLoader的输入：AS ID序列
    # 随机生成AS ID，并模拟MASK和PAD
    dummy_input_ids = torch.randint(0, dummy_vocab_size, (batch_size, seq_len)).to(config.DEVICE)
    dummy_input_ids[:, 2] = dummy_mask_token_id  # 模拟掩码位置
    dummy_input_ids[:, 5] = dummy_mask_token_id

    # 模拟填充：假设最后一个token是填充
    dummy_padding_mask = torch.zeros(batch_size, seq_len, dtype=torch.bool).to(config.DEVICE)
    dummy_padding_mask[:, -1] = True # 假设最后一位是填充
    dummy_input_ids[dummy_padding_mask] = dummy_pad_token_id # 填充位置设置为PAD ID

    logging.info(f"创建模拟输入AS ID，维度: {dummy_input_ids.shape}")
    
    # 执行前向传播
    with torch.no_grad():
        output = model(dummy_input_ids, src_padding_mask=dummy_padding_mask)

    logging.info(f"成功执行前向传播。")
    logging.info(f"输出维度: {output.shape}")

    # 验证输出维度是否正确
    assert output.shape == (batch_size, seq_len, dummy_embed_dim)
    logging.info("输出维度正确。")
    
    logging.info("模型架构测试通过！") 