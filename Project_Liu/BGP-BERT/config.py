# ==============================================================================
#  BGP-BERT - 配置文件
# ==============================================================================
#  集中管理项目的所有可配置参数，包括文件路径、模型超参数和训练设置。
#

import torch
import logging # 导入logging模块

# --- 路径配置 (Paths) ---
# 预训练的BGP2Vec模型，作为我们的"AS字母表"
BGP2VEC_MODEL_PATH = 'Project_Liu/BGP2Vec/bgp2vec.model'
# BGP原始数据目录。我们假设此目录包含多个事件子文件夹，
# 每个子文件夹中都有一个bgp_updates.csv文件。
BGP_DATA_DIR = '/data/data/anomaly-event-routedata/leak-20041224-TTNet_in_Turkey_leak'
# 用于划分训练集和测试集的文件名 (例如 rrc00_updates.20041224.0920.txt)
SPLIT_FILE_NAME = "rrc00_updates.20041224.0920.txt"
# 训练好的BGP-BERT模型的保存路径
BGP_BERT_MODEL_PATH = 'Project_Liu/BGP-BERT/bgp_bert.pth'

# --- 数据处理配置 (Data Processing) ---
# 定义在处理AS路径时使用的特殊标记
START_TOKEN = '[START]' # 路径开始标记，用于构建第一个三元组
END_TOKEN = '[END]'     # 路径结束标记，用于构建最后一个三元组
PAD_TOKEN = '[PAD]'     # 填充标记，用于将不同长度的序列对齐
MASK_TOKEN = '[MASK]'   # 掩码标记，用于MLM自监督学习任务
UNK_TOKEN = '[UNK]'     # 未知AS标记，继承自BGP2Vec模型

# --- 模型超参数 (Model Hyperparameters) ---
# BGP2Vec模型的嵌入维度将在脚本中从模型文件动态加载
# 组合式三元组的嵌入维度将根据BGP2Vec维度动态计算 (BGP2VEC_EMBED_DIM * 3)
# Transformer模型内部的工作维度 (d_model)
TRANSFORMER_DIM = 768
# Transformer中的多头注意力头数
N_HEADS = 8
# Transformer编码器的层数
N_LAYERS = 6
# Transformer中前馈网络的维度
DIM_FEEDFORWARD = 2048
# Dropout比例，用于正则化
DROPOUT = 0.1

# --- 训练超参数 (Training Hyperparameters) ---
# 自动选择在CUDA或CPU上运行
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
# 训练时的批次大小
BATCH_SIZE = 16
# 训练轮数
EPOCHS = 15
# 学习率
LEARNING_RATE = 1e-5
# MLM任务中，遮盖一个序列中标记的概率
MASK_PROB = 0.15
# 用于数据加载的工作进程数 (通常是CPU核心数)
NUM_WORKERS = 4
# 用于复现实验结果的随机种子
RANDOM_SEED = 42
# 为了快速原型验证，限制加载的最大路径数量 (None表示加载全部)
MAX_PATHS_FOR_TRAINING = None
# 用于测试集评估的路径数量 (None表示评估全部)
NUM_TEST_PATHS_TO_EVALUATE = 100 # 例如，评估1000条测试路径

# 日志级别
LOG_LEVEL = logging.INFO # 设置为INFO以减少调试输出，或DEBUG/WARNING/ERROR

# 用于复现实验结果的随机种子
RANDOM_SEED = 42
# 为了快速原型验证，限制加载的最大路径数量 (None表示加载全部)
MAX_PATHS_FOR_TRAINING = None 