# ==============================================================================
#  BGP-BERT - 异常检测器
# ==============================================================================
#  该脚本使用训练好的BGP-BERT模型来检测和定位BGP路径中的异常。
#  它通过对路径中的每个AS进行"完形填空"测试并计算重构误差来实现。
#

import torch
import torch.nn.functional as F
from gensim.models import Word2Vec
import numpy as np
import logging
from typing import List, Tuple, Dict

# 导入项目模块
import config
from model import BGPBERT
from data_loader import build_vocab # 导入build_vocab函数

# 设置日志
logging.basicConfig(level=config.LOG_LEVEL, format='%(asctime)s - %(levelname)s - %(message)s')

class AnomalyDetector:
    """
    使用BGP-BERT模型来分析和评分BGP路径的异常程度。
    """
    def __init__(self, model_path: str, bgp2vec_path: str, device: torch.device,
                 word_to_idx: Dict[str, int], idx_to_word: Dict[int, str],
                 unk_id: int, mask_id: int, pad_id: int):
        self.device = device
        self.word_to_idx = word_to_idx
        self.idx_to_word = idx_to_word
        self.unk_id = unk_id
        self.mask_id = mask_id
        self.pad_id = pad_id
        
        # 1. 加载BGP2Vec模型 (我们的 "AS字母表")
        logging.info("加载BGP2Vec模型...")
        try:
            self.bgp2vec_model = Word2Vec.load(bgp2vec_path)
            self.embed_dim = self.bgp2vec_model.wv.vector_size
        except FileNotFoundError:
            logging.error(f"BGP2Vec模型文件未找到: {bgp2vec_path}")
            raise

        # 2. 加载训练好的BGP-BERT模型
        logging.info("加载BGP-BERT模型...")
        try:
            self.model = BGPBERT(
                vocab_size=len(self.word_to_idx),
                embed_dim=self.embed_dim,
                transformer_dim=config.TRANSFORMER_DIM,
                n_heads=config.N_HEADS,
                n_layers=config.N_LAYERS,
                dim_feedforward=config.DIM_FEEDFORWARD,
                dropout=config.DROPOUT,
                bgp2vec_model=self.bgp2vec_model, # 传递Word2Vec模型实例
                mask_token_id=self.mask_id
            ).to(self.device)
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            self.model.eval()  # 设置为评估模式
        except FileNotFoundError:
            logging.error(f"BGP-BERT模型文件未找到: {model_path}")
            logging.error("请先成功运行 'train.py' 脚本。")
            raise
        except Exception as e:
            logging.error(f"加载或初始化BGP-BERT模型时出错: {e}", exc_info=True)
            raise

    def score_path(self, as_path_ids: List[int]) -> Tuple[float, List[Tuple[int, float]]]:
        """
        对给定的AS ID路径进行评分，为每个可被掩码的AS计算一个异常分数（重构误差）。
        
        Args:
            as_path_ids: 一个AS的整数ID列表。

        Returns:
            一个元组 (path_score, individual_as_errors)，其中：
            - path_score: 整条路径的异常分数 (所有单个AS重构误差的最大值)。
            - individual_as_errors: 一个列表，每个元素是 (AS_ID, 误差) 的元组。
                                    这里的AS_ID是原始的AS ID，而不是三元组。
        """
        if not as_path_ids or len(as_path_ids) < 2: # 至少需要2个AS形成上下文
            return 0.0, []

        original_ids_tensor = torch.tensor(as_path_ids, dtype=torch.long).unsqueeze(0).to(self.device) # Add batch dim
        seq_len = original_ids_tensor.shape[1]
        reconstruction_errors = []
        individual_as_errors = []

        with torch.no_grad():
            # 获取整个序列的原始AS嵌入，用于计算真实目标
            true_embeddings_seq = self.model.embedding_layer(original_ids_tensor)

            # 创建一个用于Transformer的padding mask
            # 值为True的位置表示填充，将在注意力计算中被忽略
            padding_mask = (original_ids_tensor == self.pad_id).squeeze(0) # Remove batch dim for transformer
            # src_padding_mask: [batch_size, seq_len] -> [seq_len]

            # 遍历路径中的每个AS，对其进行掩码预测
            # 排除第一个和最后一个AS，因为它们通常作为上下文，而不是被预测的目标
            for i in range(seq_len):
                # 对于MLM任务，通常不掩码[CLS]/[SEP]或边界token。
                # 在AS路径中，第一个和最后一个AS可以被认为是边界。
                # 假设我们只预测中间的AS。
                # 如果路径很短 (如只有2个AS), 则没有中间AS，不进行掩码。
                if seq_len > 2 and i > 0 and i < seq_len - 1:
                    masked_input_ids = original_ids_tensor.clone() # 复制原始AS ID序列
                    masked_input_ids[0, i] = self.mask_id # 将当前AS替换为MASK ID

                    # 前向传播，获取预测的嵌入
                    predicted_embeddings_seq = self.model(masked_input_ids, src_padding_mask=padding_mask.unsqueeze(0)) # Add back batch dim
                    predicted_embedding_i = predicted_embeddings_seq[0, i, :]

                    # 获取当前AS的真实嵌入
                    original_embedding_i = true_embeddings_seq[0, i, :]
                    
                    # 计算重构误差 (余弦距离)
                    cos_sim = F.cosine_similarity(original_embedding_i, predicted_embedding_i, dim=0)
                    error = 1.0 - cos_sim.item() # 余弦距离
                    
                    individual_as_errors.append((as_path_ids[i], error))
                    reconstruction_errors.append(error)
                elif seq_len <= 2: # 对于极短路径，没有可掩码的中间AS，默认错误为0，或根据需要设置
                    # 可以选择跳过，或者给一个默认值
                    pass # 当前策略是跳过
        
        path_score = max(reconstruction_errors) if reconstruction_errors else 0.0
        
        # 按误差从高到低排序，便于分析
        individual_as_errors.sort(key=lambda x: x[1], reverse=True)

        return path_score, individual_as_errors

if __name__ == '__main__':
    logging.info("===== BGP-BERT Anomaly Detector Demo =====")
    
    # 1. 加载实际的BGP2Vec模型并构建词汇表
    try:
        actual_bgp2vec_model = Word2Vec.load(config.BGP2VEC_MODEL_PATH)
        word_to_idx, idx_to_word, unk_id, mask_id, pad_id = build_vocab(actual_bgp2vec_model)
        logging.info(f"Demo: 词汇表大小: {len(word_to_idx)}")
    except FileNotFoundError:
        logging.error(f"BGP2Vec模型文件未找到: {config.BGP2VEC_MODEL_PATH}。无法运行演示。")
        exit() # 退出，因为没有bgp2vec模型就无法继续
    except Exception as e:
        logging.error(f"Demo: 加载BGP2Vec模型或构建词汇表时出错: {e}", exc_info=True)
        exit() # 退出

    try:
        # --- 初始化检测器 ---
        detector = AnomalyDetector(
            model_path=config.BGP_BERT_MODEL_PATH,
            bgp2vec_path=config.BGP2VEC_MODEL_PATH, # 实际模型路径
            device=config.DEVICE,
            word_to_idx=word_to_idx,
            idx_to_word=idx_to_word,
            unk_id=unk_id,
            mask_id=mask_id,
            pad_id=pad_id
        )

        # --- 定义待检测的路径 (现在是AS ID列表) ---
        # 将字符串AS转换为ID，使用实际构建的词汇表
        def path_str_to_ids(path_str):
            return [word_to_idx.get(asn, unk_id) for asn in path_str.split()]

        normal_path_ids = path_str_to_ids("701 1239 3356 1299")
        # 假设 7018 2914 174 是比较不寻常的序列或AS
        suspicious_path_ids = path_str_to_ids("701 1299 7018 2914 174")

        paths_to_test = {
            "Normal Path": normal_path_ids,
            "Suspicious Path": suspicious_path_ids
        }

        # --- 执行检测并打印结果 ---
        for name, path_ids in paths_to_test.items():
            logging.info("\n" + f"--- Analyzing: {name} ---")
            path_str = " ".join([idx_to_word.get(idx, config.UNK_TOKEN) for idx in path_ids])
            logging.info(f"Path (IDs): {path_ids}")
            logging.info(f"Path (ASNs): {path_str}")
            
            path_score, individual_as_errors = detector.score_path(path_ids)
            
            logging.info(f"Path Anomaly Score (Max Error): {path_score:.4f}")
            logging.info("Individual AS Scores (Reconstruction Error):")
            
            # 寻找并高亮显示异常分数最高的AS
            max_error = -1
            most_anomalous_as = None
            if individual_as_errors:
                most_anomalous_as = max(individual_as_errors, key=lambda item: item[1])
                max_error = most_anomalous_as[1]

            for as_id, error in individual_as_errors:
                highlight = " ***** ANOMALY HOTSPOT *****" if error == max_error else ""
                as_name = idx_to_word.get(as_id, config.UNK_TOKEN)
                logging.info(f"  - AS: {as_name:>7} | Error: {error:.4f}{highlight}")

    except (FileNotFoundError, RuntimeError) as e:
        logging.error(f"无法运行演示: {e}", exc_info=True)
        logging.error("请确保您已经成功运行了 'train.py' 来生成 'bgp_bert.pth' 模型文件。")
    except Exception as e:
        logging.error(f"运行时发生未知错误: {e}", exc_info=True) 