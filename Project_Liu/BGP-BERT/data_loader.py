# ==============================================================================
#  BGP-BERT - 数据加载器
# ==============================================================================
#  负责加载、预处理BGP数据，并为Transformer的MLM任务准备批次数据。
#  主要流程：
#  1. 从CSV文件中读取AS_PATH。
#  2. 将每个AS_PATH转换为三元组序列。
#  3. 使用BGP2Vec模型将三元组转换为组合式向量嵌入。
#  4. 在打包批次时，进行填充（Padding）和随机掩码（Masking）。
#

import torch
from torch.utils.data import Dataset, DataLoader
from gensim.models import Word2Vec
import pandas as pd
import numpy as np
from pathlib import Path
import logging
import random
from typing import List, Dict, Tuple
from tqdm import tqdm

# 导入项目配置
import config

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def build_vocab(bgp2vec_model: Word2Vec) -> Tuple[Dict[str, int], Dict[int, str], int, int, int]:
    """
    构建一个统一的词汇表，包括BGP2Vec中的所有AS和特殊标记。
    返回：word_to_idx, idx_to_word, unk_id, mask_id, pad_id
    """
    word_to_idx = {}
    idx_to_word = {}
    current_idx = 0

    # 1. 添加BGP2Vec中的所有AS
    for word in bgp2vec_model.wv.key_to_index:
        word_to_idx[word] = current_idx
        idx_to_word[current_idx] = word
        current_idx += 1
    
    logging.info(f"从BGP2Vec模型加载了 {current_idx} 个AS到词汇表。")

    # 2. 添加特殊标记
    # UNK_TOKEN: 如果BGP2Vec中没有，则添加
    unk_id = word_to_idx.get(config.UNK_TOKEN)
    if unk_id is None:
        unk_id = current_idx
        word_to_idx[config.UNK_TOKEN] = unk_id
        idx_to_word[current_idx] = config.UNK_TOKEN
        current_idx += 1
    
    # MASK_TOKEN
    mask_id = current_idx
    word_to_idx[config.MASK_TOKEN] = mask_id
    idx_to_word[current_idx] = config.MASK_TOKEN
    current_idx += 1

    # PAD_TOKEN
    pad_id = current_idx
    word_to_idx[config.PAD_TOKEN] = pad_id
    idx_to_word[current_idx] = config.PAD_TOKEN
    current_idx += 1

    logging.info(f"构建完成词汇表，总大小: {len(word_to_idx)}")
    return word_to_idx, idx_to_word, unk_id, mask_id, pad_id

def load_and_split_paths(data_dir: str, split_file_name: str, max_paths: int = None, random_seed: int = 42) \
                            -> Tuple[List[List[int]], List[List[int]], Dict[str, int], int, int, int]:
    """
    扫描目录，加载所有AS路径，并将其划分为训练集和测试集。
    根据bgp2vec模型构建统一词汇表，并将AS路径转换为ID序列。
    返回：train_paths_ids, test_paths_ids, word_to_idx, unk_id, mask_id, pad_id
    """
    logging.info(f"步骤 1/6: 开始加载并划分数据从 {data_dir}...")
    
    try:
        bgp2vec_model = Word2Vec.load(config.BGP2VEC_MODEL_PATH)
        logging.info(f"BGP2Vec模型加载成功: {config.BGP2VEC_MODEL_PATH}")
    except FileNotFoundError:
        logging.error(f"BGP2Vec模型文件未找到: {config.BGP2VEC_MODEL_PATH}")
        logging.error("请先成功运行 'Project_Liu/BGP2Vec/train_bgp2vec.py'。")
        return [], [], {}, -1, -1, -1

    word_to_idx, idx_to_word, unk_id, mask_id, pad_id = build_vocab(bgp2vec_model)
    logging.info(f"AS词汇表构建完成，总词汇量: {len(word_to_idx)}")

    path_obj = Path(data_dir)
    all_update_files = []

    if path_obj.is_file() and (path_obj.name.startswith('rrc') and path_obj.name.endswith('.txt')):
        all_update_files = [path_obj]
    elif path_obj.is_dir():
        all_update_files = sorted(list(path_obj.rglob('rrc*_updates*.txt')))
    else:
        logging.warning(f"指定路径 {data_dir} 既不是文件也不是目录，或不符合预期的文件名模式。")
        return [], [], word_to_idx, unk_id, mask_id, pad_id
    
    if not all_update_files:
        logging.warning(f"在 {data_dir} 中未找到任何符合BGP更新模式的数据文件 (rrc*_updates*.txt)。")
        return [], [], word_to_idx, unk_id, mask_id, pad_id

    split_index = -1
    for i, file_path in enumerate(all_update_files):
        if file_path.name == split_file_name:
            split_index = i
            break

    if split_index == -1:
        logging.error(f"未在指定目录中找到切分点文件: {split_file_name}。请确认文件存在。")
        return [], [], word_to_idx, unk_id, mask_id, pad_id

    train_files = all_update_files[:split_index + 1]
    test_files = all_update_files[split_index + 1:]

    logging.info(f"找到 {len(train_files)} 个训练数据文件和 {len(test_files)} 个测试数据文件。")
        
    all_train_paths_str = []
    for path_file in tqdm(train_files, desc="正在读取训练BGP数据文件"):
        try:
            with open(path_file, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    parts = line.strip().split('|')
                    if len(parts) > 6 and parts[6].strip():
                        as_path_str_raw = parts[6].strip()
                        if not as_path_str_raw or not all(asn.isdigit() for asn in as_path_str_raw.split()):
                            continue
                        as_list = as_path_str_raw.split() 
                        if not as_list:
                            continue
                        cleaned_as_list = [as_list[0]]
                        for i in range(1, len(as_list)):
                            if as_list[i] != as_list[i-1]:
                                cleaned_as_list.append(as_list[i])
                        # 单AS序列长度至少为1即可，因为我们现在是单AS预测，无需三元组。
                        # 但是考虑到Transformer需要上下文，我们要求路径至少有2个AS才值得训练
                        if len(cleaned_as_list) >= 2:
                            all_train_paths_str.append(" ".join(cleaned_as_list))
            
        except Exception as e:
            logging.error(f"读取或处理训练文件 {path_file} 时出错: {e}", exc_info=True)
    
    logging.info(f"加载完成。初步找到 {len(all_train_paths_str)} 条训练路径字符串记录。")
    unique_train_paths_str = list(set(all_train_paths_str))

    if max_paths and max_paths < len(unique_train_paths_str):
        logging.info(f"训练数据量限制：从总训练路径中随机抽取 {max_paths} 条进行处理。")
        random.seed(random_seed)
        train_paths_str_final = random.sample(unique_train_paths_str, max_paths)
    else:
        logging.info(f"使用全部 {len(unique_train_paths_str)} 条独立训练路径进行处理。")
        train_paths_str_final = unique_train_paths_str
    
    # 将训练路径字符串转换为ID序列
    train_paths_ids = []
    for path_str in tqdm(train_paths_str_final, desc="将训练路径转换为ID"):
        train_paths_ids.append([word_to_idx.get(asn, unk_id) for asn in path_str.split()])

    logging.info(f"训练路径ID转换完成。最终训练集包含 {len(train_paths_ids)} 条路径ID序列。")

    # 现在处理测试文件，测试路径不进行max_paths限制
    all_test_paths_str = []
    for path_file in tqdm(test_files, desc="正在读取测试BGP数据文件"):
        try:
            with open(path_file, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    parts = line.strip().split('|')
                    if len(parts) > 6 and parts[6].strip():
                        as_path_str_raw = parts[6].strip()
                        if not as_path_str_raw or not all(asn.isdigit() for asn in as_path_str_raw.split()):
                            continue
                        as_list = as_path_str_raw.split() 
                        if not as_list:
                            continue
                        cleaned_as_list = [as_list[0]]
                        for i in range(1, len(as_list)):
                            if as_list[i] != as_list[i-1]:
                                cleaned_as_list.append(as_list[i])
                        # 单AS序列长度至少为2才能用于测试（需要上下文）
                        if len(cleaned_as_list) >= 2:
                            all_test_paths_str.append(" ".join(cleaned_as_list))
        except Exception as e:
            logging.error(f"读取或处理测试文件 {path_file} 时出错: {e}", exc_info=True)
    
    logging.info(f"加载完成。初步找到 {len(all_test_paths_str)} 条测试路径字符串记录。")
    unique_test_paths_str = list(set(all_test_paths_str))
    logging.info(f"测试路径字符串去重完成。最终测试集包含 {len(unique_test_paths_str)} 条独立路径字符串。")

    # 将测试路径字符串转换为ID序列
    test_paths_ids = []
    for path_str in tqdm(unique_test_paths_str, desc="将测试路径转换为ID"):
        test_paths_ids.append([word_to_idx.get(asn, unk_id) for asn in path_str.split()])
    
    logging.info(f"测试路径ID转换完成。最终测试集包含 {len(test_paths_ids)} 条路径ID序列。")

    logging.info(f"数据划分完成：训练集 {len(train_paths_ids)} 条，测试集 {len(test_paths_ids)} 条。")
    
    return train_paths_ids, test_paths_ids, word_to_idx, unk_id, mask_id, pad_id

class BGPPathDataset(Dataset):
    """
    PyTorch数据集，用于处理BGP AS ID序列。
    """
    def __init__(self, paths_ids: List[List[int]]):
        self.paths_ids = paths_ids
        
    def __len__(self):
        return len(self.paths_ids)

    def __getitem__(self, idx):
        return torch.tensor(self.paths_ids[idx], dtype=torch.long)

class BGPDataCollator:
    """
    自定义的Collate函数，用于处理BGP AS ID序列批次数据。
    功能：
    - 填充序列以匹配批次中的最大长度。
    - 为MLM任务执行随机掩码。
    """
    def __init__(self, mask_prob: float, mask_token_id: int, pad_token_id: int):
        self.mask_prob = mask_prob
        self.mask_token_id = mask_token_id
        self.pad_token_id = pad_token_id

    def __call__(self, batch: List[torch.Tensor]) -> Dict[str, torch.Tensor]:
        # batch 是一个列表，每个元素是一个[seq_len]的Tensor (AS ID序列)
        
        # 1. 准备输入和目标序列
        # target_sequences 是原始的AS ID序列
        target_sequences = [item for item in batch]
        # input_sequences 是将被掩码的序列副本
        input_sequences = [item.clone() for item in target_sequences]
        
        # 2. 执行随机掩码
        # loss_mask 用于标记哪些位置被掩码了，只在这些位置计算损失
        loss_masks = []
        for seq in input_sequences:
            # torch.rand返回[0, 1)之间的均匀分布随机数
            prob_mask = torch.rand(seq.shape[0]) < self.mask_prob
            # 确保至少有一个token被mask，以避免空的训练样本 (仅当序列非空时)
            if not prob_mask.any() and seq.shape[0] > 0:
                # 确保不掩码PAD token，所以选择非PAD位置进行掩码
                non_pad_indices = (seq != self.pad_token_id).nonzero(as_tuple=True)[0]
                if len(non_pad_indices) > 0:
                    idx_to_mask = random.choice(non_pad_indices.tolist())
                    prob_mask[idx_to_mask] = True
                # else: # 如果全是PAD，则无法掩码，跳过
                    # logging.debug("Skipping mask for an all-PAD sequence.")

            # 将被选中的位置替换为[MASK] ID
            seq[prob_mask] = self.mask_token_id
            loss_masks.append(prob_mask)

        # 3. 填充序列
        # 使用torch的内置函数进行填充，效率更高
        # padding_value=self.pad_token_id 对应PAD_TOKEN的ID
        padded_inputs = torch.nn.utils.rnn.pad_sequence(input_sequences, batch_first=True, padding_value=self.pad_token_id)
        padded_targets = torch.nn.utils.rnn.pad_sequence(target_sequences, batch_first=True, padding_value=self.pad_token_id)
        padded_loss_masks = torch.nn.utils.rnn.pad_sequence(loss_masks, batch_first=True, padding_value=False) # False表示不计算损失
        
        return {
            "inputs": padded_inputs, # 包含 MASK ID 和 PAD ID 的 AS 序列
            "targets": padded_targets, # 原始 AS ID 序列，用于计算损失
            "loss_mask": padded_loss_masks # 指示哪些位置被掩码了，只在这些位置计算损失
        }

if __name__ == '__main__':
    # --- 用于测试数据加载器的示例代码 ---
    logging.info("开始测试数据加载器 (新版本)...")
    
    # 模拟一个简化的bgp2vec模型和数据目录
    class DummyWordVectors:
        def __init__(self, words, embed_dim):
            self.words = words # list of strings
            self.key_to_index = {word: i for i, word in enumerate(words)}
            self.index_to_key = {i: word for i, word in enumerate(words)}
            self.vectors = np.random.rand(len(words), embed_dim).astype(np.float32)
            self.vector_size = embed_dim
        def __len__(self):
            return len(self.words)
        def __contains__(self, word):
            return word in self.key_to_index
        def __getitem__(self, word_or_idx):
            if isinstance(word_or_idx, str):
                return self.vectors[self.key_to_index[word_or_idx]]
            return self.vectors[word_or_idx]

    # 模拟BGP2Vec模型
    dummy_bgp2vec_words = [str(i) for i in range(100)] + [config.UNK_TOKEN]
    dummy_bgp2vec = Word2Vec()
    dummy_bgp2vec.wv = DummyWordVectors(dummy_bgp2vec_words, 64)
    
    # 模拟config中的特殊标记
    config.UNK_TOKEN = '[UNK]'
    config.MASK_TOKEN = '[MASK]'
    config.PAD_TOKEN = '[PAD]'
    
    # 模拟数据目录
    # 为了方便测试，我们直接构造一些路径字符串，不读取文件
    dummy_train_paths_str = [
        "1 2 3 4 5",
        "10 20 30",
        "5 4 3 2 1 0",
        "1 1 2 3 3 3 4", # 包含重复AS
        "100 200", # 长度为2，应该被保留
        "999", # 长度为1，应该被过滤
        "" # 空路径
    ]

    # 手动模拟load_and_split_paths的输出，因为我们没有真实文件
    # 直接用dummy_bgp2vec生成vocab
    word_to_idx, idx_to_word, unk_id, mask_id, pad_id = build_vocab(dummy_bgp2vec)
    
    # 转换模拟路径为ID
    mock_train_paths_ids = []
    for path_str in dummy_train_paths_str:
        as_list = path_str.split()
        cleaned_as_list = []
        if as_list:
            cleaned_as_list.append(as_list[0])
            for i in range(1, len(as_list)):
                if as_list[i] != as_list[i-1]:
                    cleaned_as_list.append(as_list[i])
        if len(cleaned_as_list) >= 2: # 至少2个AS才保留
            mock_train_paths_ids.append([word_to_idx.get(asn, unk_id) for asn in cleaned_as_list])
    
    logging.info(f"模拟训练路径ID序列: {mock_train_paths_ids}")

    # 1. 创建数据集
    train_dataset = BGPPathDataset(mock_train_paths_ids)
    
    # 2. 创建Collate函数实例
    collator = BGPDataCollator(mask_prob=config.MASK_PROB, mask_token_id=mask_id, pad_token_id=pad_id)

    # 3. 创建DataLoader
    dataloader = DataLoader(train_dataset, batch_size=2, collate_fn=collator, shuffle=True)

    # 4. 取一个批次并检查其结构和维度
    try:
        first_batch = next(iter(dataloader))
        
        logging.info("成功获取一个批次的数据。")
        inputs = first_batch['inputs']
        targets = first_batch['targets']
        loss_mask = first_batch['loss_mask']
        
        logging.info(f"批次输入维度 (AS IDs): {inputs.shape}")
        logging.info(f"批次目标维度 (AS IDs): {targets.shape}")
        logging.info(f"批次损失掩码维度: {loss_mask.shape}")
        
        # 验证掩码和填充是否正确应用
        num_masked = loss_mask.sum()
        logging.info(f"此批次中被掩码的AS总数: {num_masked}")

        # 打印部分内容以供人工检查
        logging.info(f"输入IDs (masked):\n{inputs}")
        logging.info(f"目标IDs (original):\n{targets}")
        logging.info(f"损失掩码:\n{loss_mask}")
        
        # 检查 masked 位置是否包含 mask_token_id
        masked_positions_in_inputs = (inputs == mask_id) & loss_mask
        assert masked_positions_in_inputs.all(), "掩码位置的输入应该都是MASK ID"
        logging.info("掩码位置的输入ID验证通过。")

        # 检查非 masked 位置的输入和目标是否一致
        non_masked_and_non_pad_positions = (~loss_mask) & (targets != pad_id)
        assert torch.equal(inputs[non_masked_and_non_pad_positions], targets[non_masked_and_non_pad_positions]), "非掩码非填充位置的输入和目标应该一致"
        logging.info("非掩码非填充位置的输入ID和目标ID验证通过。")

        logging.info("数据加载器测试通过！")

    except StopIteration:
        logging.error("数据加载器为空，无法获取批次。请检查数据路径和文件。")
    except Exception as e:
        logging.error(f"数据加载器测试过程中发生错误: {e}", exc_info=True) 