# ==============================================================================
#  BGP-BERT - 训练脚本
# ==============================================================================
#  该脚本整合了数据加载、模型定义，并执行BGP-BERT模型的自监督训练流程。
#  训练完成后，它会将训练好的模型权重保存到文件中。
#

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from gensim.models import Word2Vec
import numpy as np
import logging
import random
from tqdm import tqdm

# 导入项目模块
import config
from data_loader import BGPPathDataset, BGPDataCollator, load_and_split_paths
from model import BGPBERT
from detector import AnomalyDetector

# --- 初始化 ---

# 设置日志
logging.basicConfig(level=config.LOG_LEVEL, format='%(asctime)s - %(levelname)s - %(message)s')

# 设置随机种子以保证可复现性
torch.manual_seed(config.RANDOM_SEED)
np.random.seed(config.RANDOM_SEED)
random.seed(config.RANDOM_SEED)
if torch.cuda.is_available():
    torch.cuda.manual_seed_all(config.RANDOM_SEED)

def main():
    """主训练函数"""
    
    # 确保词汇表和特殊标记ID在任何情况下都已定义
    word_to_idx = {}
    idx_to_word = {}
    unk_id = -1
    mask_id = -1
    pad_id = -1

    # --- 1. 加载并划分数据 ---
    logging.info("步骤 1/6: 开始加载并划分数据...")
    
    # 加载并划分数据，并获取词汇表信息
    train_paths_ids, test_paths_ids, word_to_idx, unk_id, mask_id, pad_id = load_and_split_paths(
        data_dir=config.BGP_DATA_DIR,
        split_file_name=config.SPLIT_FILE_NAME,
        max_paths=config.MAX_PATHS_FOR_TRAINING,
        random_seed=config.RANDOM_SEED
    )

    if not train_paths_ids:
        logging.error("训练路径集为空，训练中止。")
        return

    # --- 动态获取维度 ---
    # 从bgp2vec模型动态加载嵌入维度
    try:
        # 再次加载bgp2vec模型以获取其维度，因为data_loader中加载的模型实例可能已被销毁
        bgp2vec_model_for_dim = Word2Vec.load(config.BGP2VEC_MODEL_PATH)
        bgp2vec_embed_dim = bgp2vec_model_for_dim.wv.vector_size
        del bgp2vec_model_for_dim # 及时释放内存
    except FileNotFoundError:
        logging.error(f"BGP2Vec模型文件未找到: {config.BGP2VEC_MODEL_PATH}。无法获取嵌入维度。")
        return
    
    logging.info(f"从BGP2Vec模型动态加载维度: AS Embed Dim = {bgp2vec_embed_dim}")

    # --- 步骤 2: 创建PyTorch数据集和数据加载器 ---
    logging.info("步骤 2/6: 创建PyTorch数据集...") # 步骤编号调整
    
    # 训练集和测试集
    train_dataset = BGPPathDataset(train_paths_ids)
    test_dataset = BGPPathDataset(test_paths_ids)

    collator = BGPDataCollator(mask_prob=config.MASK_PROB, mask_token_id=mask_id, pad_token_id=pad_id)

    logging.info("创建数据加载器 (DataLoaders)...")
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.BATCH_SIZE,
        collate_fn=collator,
        shuffle=True,
        num_workers=config.NUM_WORKERS, # 从config获取
        pin_memory=True # 如果使用GPU，可以加速数据转移
    )
    test_loader = DataLoader(
        test_dataset,
        batch_size=config.BATCH_SIZE,
        collate_fn=collator,
        shuffle=False,
        num_workers=config.NUM_WORKERS, # 从config获取
        pin_memory=True
    )

    # --- 3. 初始化模型、损失函数和优化器 ---
    logging.info("步骤 3/6: 初始化模型、损失函数和优化器...") # 步骤编号调整

    # 加载BGP2Vec模型，将其传递给BGPBERT模型以便初始化其嵌入层
    try:
        bgp2vec_model_for_bert = Word2Vec.load(config.BGP2VEC_MODEL_PATH)
    except FileNotFoundError:
        logging.error(f"BGP2Vec模型文件未找到: {config.BGP2VEC_MODEL_PATH}。BERT模型无法初始化。")
        return

    model = BGPBERT(
        vocab_size=len(word_to_idx),
        embed_dim=bgp2vec_embed_dim,
        transformer_dim=config.TRANSFORMER_DIM,
        n_heads=config.N_HEADS,
        n_layers=config.N_LAYERS,
        dim_feedforward=config.DIM_FEEDFORWARD,
        dropout=config.DROPOUT,
        bgp2vec_model=bgp2vec_model_for_bert, # 传递Word2Vec模型实例
        mask_token_id=mask_id
    ).to(config.DEVICE)
    logging.info(f"模型已移动到设备: {config.DEVICE}")

    # 使用均方误差损失来比较预测向量和真实向量
    criterion = nn.MSELoss()
    optimizer = torch.optim.AdamW(model.parameters(), lr=config.LEARNING_RATE)

    # --- 4. 执行训练 ---
    logging.info(f"步骤 4/6: 开始在 {len(train_dataset)} 条路径上进行 {config.EPOCHS} 轮训练...") # 步骤编号调整
    model.train() # 设置为训练模式

    for epoch in range(config.EPOCHS):
        total_loss = 0
        
        # 使用tqdm显示进度条
        pbar = tqdm(train_loader, desc=f"Epoch {epoch + 1}/{config.EPOCHS}")
        
        for batch in pbar:
            # 将数据移动到指定设备
            inputs = batch['inputs'].to(config.DEVICE) # AS ID序列
            targets = batch['targets'].to(config.DEVICE) # 原始 AS ID序列
            loss_mask = batch['loss_mask'].to(config.DEVICE) # 指示哪些AS被掩码了
            
            # 生成padding mask (TransformerEncoder需要)
            # PAD_TOKEN_ID 在这里被用来识别填充位置
            padding_mask = (inputs == pad_id)

            # --- 前向传播 ---
            optimizer.zero_grad()
            # predictions 是模型预测的 AS 嵌入，shape: [batch_size, seq_len, embed_dim]
            predictions = model(inputs, src_padding_mask=padding_mask)

            # --- 计算损失 ---
            # 获取原始 AS ID 对应的真实嵌入
            with torch.no_grad(): # 确保不计算这部分的梯度
                true_embeddings = model.embedding_layer(targets).detach() # 获取真实嵌入

            # 关键：只在被掩码的位置计算损失
            # loss_mask 已经指示了被掩码的 AS ID 位置
            # 将loss_mask扩展以匹配嵌入维度
            loss_mask_expanded = loss_mask.unsqueeze(-1).expand_as(predictions)
            
            # 从predictions和true_embeddings中只选择被掩码的元素
            masked_predictions = torch.masked_select(predictions, loss_mask_expanded)
            masked_targets = torch.masked_select(true_embeddings, loss_mask_expanded)
            
            if masked_predictions.numel() == 0: # 如果当前批次没有被掩码的token，跳过损失计算
                # logging.debug("Current batch has no masked tokens, skipping loss calculation.")
                continue

            loss = criterion(masked_predictions, masked_targets)

            # --- 反向传播和优化 ---
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            pbar.set_postfix({'loss': f'{loss.item():.4f}'})

        avg_loss = total_loss / len(train_loader)
        logging.info(f"Epoch {epoch + 1} 完成 | 平均损失: {avg_loss:.6f}")

    # --- 5. 保存模型 ---
    logging.info("步骤 5/6: 训练完成，正在保存模型...") # 步骤编号调整
    try:
        # 保存模型时只保存state_dict
        torch.save(model.state_dict(), config.BGP_BERT_MODEL_PATH)
        logging.info(f"模型已成功保存到: {config.BGP_BERT_MODEL_PATH}")
    except Exception as e:
        logging.error(f"保存模型时出错: {e}")

    # --- 6. 在测试集上评估 ---
    logging.info("步骤 6/6: 在测试集上评估模型...") # 步骤编号调整
    if not test_paths_ids:
        logging.warning("测试集为空，跳过评估步骤。")
        return

    try:
        detector = AnomalyDetector(
            model_path=config.BGP_BERT_MODEL_PATH,
            bgp2vec_path=config.BGP2VEC_MODEL_PATH,
            device=config.DEVICE,
            word_to_idx=word_to_idx, # 传递词汇表
            idx_to_word=idx_to_word,
            unk_id=unk_id,
            mask_id=mask_id,
            pad_id=pad_id
        )
        
        all_test_scores = []
        logging.info(f"正在分析 {len(test_paths_ids)} 条测试路径...")
        
        # 为了演示，我们只分析一部分测试路径，避免输出过多
        paths_to_evaluate = test_paths_ids[:config.NUM_TEST_PATHS_TO_EVALUATE] # 从config获取评估数量
        
        for path_ids in tqdm(paths_to_evaluate, desc="评估测试路径"):
            # detector.score_path 现在接收 AS ID 序列
            path_score, _ = detector.score_path(path_ids)
            all_test_scores.append((path_ids, path_score))
        
        # 按异常分数从高到低排序
        all_test_scores.sort(key=lambda x: x[1], reverse=True)

        logging.info("\n==============================")
        logging.info("      测试集异常检测结果      ")
        logging.info("==============================")
        logging.info(f"在 {len(paths_to_evaluate)} 条前所未见的路径中，模型认为最可疑的{min(5, len(paths_to_evaluate))}条是：")

        for i, (path_ids, score) in enumerate(all_test_scores[:min(5, len(paths_to_evaluate))]):
            path_str = " ".join([idx_to_word.get(idx, config.UNK_TOKEN) for idx in path_ids])
            logging.info(f"--- Top {i+1} 异常路径 (得分: {score:.4f}) ---")
            logging.info(f"路径: {path_str}")
            
            # 为了简化，我们不再在训练阶段打印每个三元组的误差
            # 完整的细粒度分析将在detector.py中实现
            # if detailed_errors:
            #     for (up_as, current_as, down_as), error in detailed_errors:
            #         logging.info(f"  - Triplet: ({up_as:6}, {current_as:6}, {down_as:6}) | Error: {error:.4f}")
            # logging.info(f"  - 异常热点AS: {detector.idx_to_word.get(anomaly_hotspot_idx, config.UNK_TOKEN)} | Error: {anomaly_hotspot_error:.4f}")


    except Exception as e:
        logging.error(f"评估测试集时出错: {e}", exc_info=True)

if __name__ == '__main__':
    main() 