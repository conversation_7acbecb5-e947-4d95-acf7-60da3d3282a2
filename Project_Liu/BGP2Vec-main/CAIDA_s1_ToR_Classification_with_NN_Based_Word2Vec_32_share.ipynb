{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Import python packages"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:13.250412Z", "start_time": "2020-01-02T12:25:11.453838Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Using TensorFlow backend.\n"]}], "source": ["import pickle\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "\n", "from keras.models import Sequential\n", "from keras.layers import Dense, Reshape, Flatten\n", "from keras.layers import LSTM\n", "from keras.layers.convolutional import Convolution1D\n", "from keras.layers.convolutional import MaxPooling1D\n", "from keras.layers.embeddings import Embedding\n", "from keras.preprocessing import sequence\n", "from keras.callbacks import TensorBoard,ModelCheckpoint\n", "from keras.utils import to_categorical\n", "\n", "SEED = 7\n", "np.random.seed(SEED)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Define parameters and load dataset"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:14.273080Z", "start_time": "2020-01-02T12:25:14.152968Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(580762, 2) (580762,)\n", "(113378, 2) (113378,)\n"]}], "source": ["MODEL_NAME = \"CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32\"\n", "\n", "if experiment is not None:\n", "    experiment.set_name(MODEL_NAME)\n", "    experiment.set_filename(MODEL_NAME +'.ipynb')\n", "\n", "Word2Vec_MODEL_NAME = \"Word2Vec_No_Indexed_3_iters_5_negative_1_window\"  #################\n", "\n", "TEST_SIZE = 0.2\n", "TOR_LABELS_DICT = {'P2P':0, 'C2P': 1,'P2C': 2}\n", "class_names = ['P2P', 'C2P', 'P2C']\n", "num_classes = len(class_names)\n", "DATA_PATH = '../../Data/'\n", "MODELS_PATH = '../../Models/'\n", "RESULTS_PATH = '../../Results/'\n", "\n", "DATA = \"caida_s1_tor\"\n", "dataset = np.load(DATA_PATH + DATA + \"_dataset.npy\")\n", "labels = np.load(DATA_PATH + DATA + \"_labels.npy\")\n", "\n", "print(dataset.shape, labels.shape)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:15.558004Z", "start_time": "2020-01-02T12:25:15.552656Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['1' '11537']\n"]}], "source": ["print(dataset[0])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:19.932521Z", "start_time": "2020-01-02T12:25:19.163791Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["58679\n"]}], "source": ["ASN_set = set()\n", "for tor in dataset:\n", "    ASN_set.add(tor[0])\n", "    ASN_set.add(tor[1])\n", "\n", "print(len(ASN_set))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:23.877660Z", "start_time": "2020-01-02T12:25:23.743144Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Counter({0: 343952, 2: 118405, 1: 118405})\n"]}], "source": ["from collections import Counter\n", "c_caida = Counter(labels)\n", "print(c_caida)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load Word2Vec embedding"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:26.965573Z", "start_time": "2020-01-02T12:25:26.183569Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["62525 32\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/lib/python3.7/site-packages/ipykernel_launcher.py:3: DeprecationWarning: Call to deprecated `syn0` (Attribute will be removed in 4.0.0, use self.vectors instead).\n", "  This is separate from the ipykernel package so we can avoid doing imports until\n"]}], "source": ["from gensim.models import Word2Vec\n", "word2vec_model = Word2Vec.load(MODELS_PATH + Word2Vec_MODEL_NAME + \".word2vec\")\n", "emdeddings = word2vec_model.wv.syn0\n", "total_ASNs, embedding_vecor_length = emdeddings.shape\n", "print(total_ASNs, embedding_vecor_length)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate indexed data with fixed length"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:30.826938Z", "start_time": "2020-01-02T12:25:29.501780Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(580762, 2)\n"]}], "source": ["def asn2idx(asn):\n", "    return word2vec_model.wv.vocab[asn].index\n", "def idx2asn(idx):\n", "    return word2vec_model.wv.index2word[idx]\n", "\n", "def dataset_asn2idx(dataset):\n", "    dataset_idx = np.zeros([len(dataset), 2], dtype=np.int32)\n", "\n", "    for i, pair in enumerate(dataset):\n", "            dataset_idx[i, 0] = asn2idx(pair[0])\n", "            dataset_idx[i, 1] = asn2idx(pair[1])\n", "    return dataset_idx\n", "\n", "def dataset_idx2asn(dataset_idx):\n", "    dataset_asn = np.zeros([len(dataset_idx), 2], dtype = np.dtype('U6'))\n", "\n", "    for i, pair in enumerate(dataset_idx):\n", "            dataset_asn[i, 0] = idx2asn(pair[0])\n", "            dataset_asn[i, 1] = idx2asn(pair[1])\n", "    return dataset_asn\n", "        \n", "dataset = dataset_asn2idx(dataset)\n", "print(dataset.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate training and test sets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Shuffle dataset"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:32.633541Z", "start_time": "2020-01-02T12:25:32.562707Z"}}, "outputs": [], "source": ["from sklearn.utils import shuffle\n", "indices = np.arange(len(dataset))\n", "np.random.shuffle(indices)\n", "dataset = dataset[indices]\n", "labels = labels[indices]\n", "# dataset, labels = shuffle(dataset, labels, random_state=7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train-Test split"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:34.342222Z", "start_time": "2020-01-02T12:25:34.284519Z"}}, "outputs": [], "source": ["indices = np.arange(len(dataset))\n", "x_training, x_test, indices_training, indices_test = train_test_split(dataset, indices, test_size=TEST_SIZE)\n", "y_training = labels[indices_training]\n", "y_test = labels[indices_test]\n", "\n", "del dataset, labels"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:35.668403Z", "start_time": "2020-01-02T12:25:35.521257Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(464609, 2) (464609,)\n", "(116153, 2) (116153,)\n", "0.7999989668745545\n", "Counter({0: 275086, 1: 94895, 2: 94628}) Counter({0: 68866, 2: 23777, 1: 23510})\n"]}], "source": ["print(x_training.shape, y_training.shape)\n", "print(x_test.shape, y_test.shape)\n", "\n", "print(1.0*len(x_training)/(len(x_test)+len(x_training)))\n", "\n", "from collections import Counter\n", "training_c = Counter(y_training)\n", "test_c = Counter(y_test)\n", "print(training_c, test_c)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Define and run NN model\n", "## Define model"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:38.154757Z", "start_time": "2020-01-02T12:25:37.913806Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:From /home/<USER>/anaconda3/lib/python3.7/site-packages/tensorflow/python/framework/op_def_library.py:263: colocate_with (from tensorflow.python.framework.ops) is deprecated and will be removed in a future version.\n", "Instructions for updating:\n", "Colocations handled automatically by placer.\n", "_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "embedding_1 (Embedding)      (None, 2, 32)             2000800   \n", "_________________________________________________________________\n", "conv1d_1 (Conv1D)            (None, 2, 32)             3104      \n", "_________________________________________________________________\n", "reshape_1 (Reshape)          (None, 32, 2)             0         \n", "_________________________________________________________________\n", "max_pooling1d_1 (MaxPooling1 (None, 16, 2)             0         \n", "_________________________________________________________________\n", "conv1d_2 (Conv1D)            (None, 16, 32)            224       \n", "_________________________________________________________________\n", "reshape_2 (Reshape)          (None, 32, 16)            0         \n", "_________________________________________________________________\n", "max_pooling1d_2 (MaxPooling1 (None, 16, 16)            0         \n", "_________________________________________________________________\n", "flatten_1 (<PERSON>ten)          (None, 256)               0         \n", "_________________________________________________________________\n", "dense_1 (<PERSON><PERSON>)              (None, 100)               25700     \n", "_________________________________________________________________\n", "dense_2 (<PERSON><PERSON>)              (None, 3)                 303       \n", "=================================================================\n", "Total params: 2,030,131\n", "Trainable params: 29,331\n", "Non-trainable params: 2,000,800\n", "_________________________________________________________________\n", "None\n"]}], "source": ["embedding_trainable = False\n", "if experiment is not None:\n", "    experiment.log_parameter(\"embedding_trainable\", embedding_trainable)\n", "input_length = 2\n", "\n", "model = Sequential()\n", "model.add(Embedding(total_ASNs, embedding_vecor_length, input_length=input_length,\n", "                    weights=[emdeddings], trainable=embedding_trainable))\n", "model.add(Convolution1D(filters=32, kernel_size=3, padding='same', activation='relu'))\n", "model.add(Reshape((model.output_shape[2],model.output_shape[1])))\n", "model.add(MaxPooling1D(pool_size=2))\n", "model.add(Convolution1D(filters=32, kernel_size=3, padding='same', activation='relu'))\n", "model.add(Reshape((model.output_shape[2],model.output_shape[1])))\n", "model.add(MaxPooling1D(pool_size=2))\n", "model.add(<PERSON><PERSON>())\n", "model.add(Dense(100, activation='relu'))\n", "model.add(Dense(num_classes, activation='softmax'))\n", "model.compile(loss='categorical_crossentropy', optimizer='adam', metrics=['accuracy'])\n", "print(model.summary())"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T14:38:09.430642Z", "start_time": "2019-12-28T14:38:09.242446Z"}}, "outputs": [], "source": ["from keras.utils import plot_model\n", "plot_model(model, show_shapes=True,to_file=RESULTS_PATH + MODEL_NAME + \"_\" + 'model.png')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## convert class vectors to binary class matrices"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:40.578213Z", "start_time": "2020-01-02T12:25:40.562943Z"}}, "outputs": [], "source": ["y_training_vector = to_categorical(y_training, num_classes)\n", "y_test_vector = to_categorical(y_test, num_classes)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use class_weight to deal with unbalanced dataset"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:43.201016Z", "start_time": "2020-01-02T12:25:43.194561Z"}}, "outputs": [], "source": ["from sklearn.utils import class_weight\n", "\n", "# class_weights = class_weight.compute_class_weight('balanced', list(range(num_classes)), y_training)\n", "# print(class_weights)\n", "class_weights = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fit model"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T14:38:13.710028Z", "start_time": "2019-12-28T14:38:13.704026Z"}}, "outputs": [], "source": ["checkpointer_loss = ModelCheckpoint(filepath= MODELS_PATH + MODEL_NAME + '_loss.hdf5', verbose=1, save_best_only=True, save_weights_only=True)\n", "checkpointer_acc = ModelCheckpoint(monitor='val_acc', filepath= MODELS_PATH + MODEL_NAME + '_acc.hdf5', verbose=1, save_best_only=True, save_weights_only=True)\n", "\n", "callbacks = [checkpointer_loss,checkpointer_acc] #tensorboard"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:46.948296Z", "start_time": "2020-01-02T12:25:46.934589Z"}}, "outputs": [], "source": ["import math\n", "\n", "epochs = 40 #################################   3  ##########\n", "if experiment is not None:\n", "    experiment.log_parameter(\"epochs\", epochs)\n", "\n", "batch_size = 64\n", "if experiment is not None:\n", "    experiment.log_parameter(\"batch_size\", batch_size)\n", "\n", "steps_per_epoch = math.ceil(len(x_training)/batch_size)\n", "if experiment is not None:\n", "    experiment.log_parameter(\"steps_per_epoch\", steps_per_epoch)\n", "\n", "val_batch_size = 1024\n", "if experiment is not None:\n", "    experiment.log_parameter(\"val_batch_size\", val_batch_size)\n", "\n", "validation_steps = math.ceil(len(x_test)/val_batch_size)\n", "if experiment is not None:\n", "    experiment.log_parameter(\"validation_steps\", validation_steps)\n", "\n", "def val_generator(features, labels, val_batch_size):\n", "    index = 0\n", "    while True:\n", "        index += val_batch_size\n", "        batch_features, batch_labels = features[index-val_batch_size:index], labels[index-val_batch_size:index]\n", "        if index >= len(features):\n", "            index = 0\n", "        yield batch_features, batch_labels\n", "        \n", "def generator(features, labels, batch_size):\n", "    index = 0\n", "    while True:\n", "        index += batch_size\n", "        if index >= len(features):\n", "            batch_features = np.append(features[index-batch_size:len(features)], features[0:index-len(features)], axis=0)\n", "            batch_labels = np.append(labels[index-batch_size:len(features)], labels[0:index-len(features)], axis=0)\n", "            index -= len(features)\n", "            yield batch_features, batch_labels\n", "        else:\n", "            yield features[index-batch_size:index], labels[index-batch_size:index]"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T14:59:22.762572Z", "start_time": "2019-12-28T14:38:17.581642Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:From /home/<USER>/anaconda3/lib/python3.7/site-packages/tensorflow/python/ops/math_ops.py:3066: to_int32 (from tensorflow.python.ops.math_ops) is deprecated and will be removed in a future version.\n", "Instructions for updating:\n", "Use tf.cast instead.\n", "Epoch 1/40\n", "7260/7260 [==============================] - 37s 5ms/step - loss: 0.2786 - acc: 0.8976 - val_loss: 0.2332 - val_acc: 0.9177\n", "\n", "Epoch 00001: val_loss improved from inf to 0.23322, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00001: val_acc improved from -inf to 0.91774, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 2/40\n", "7260/7260 [==============================] - 37s 5ms/step - loss: 0.2127 - acc: 0.9244 - val_loss: 0.2050 - val_acc: 0.9284\n", "\n", "Epoch 00002: val_loss improved from 0.23322 to 0.20505, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00002: val_acc improved from 0.91774 to 0.92843, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 3/40\n", "7260/7260 [==============================] - 37s 5ms/step - loss: 0.1946 - acc: 0.9319 - val_loss: 0.1941 - val_acc: 0.9322\n", "\n", "Epoch 00003: val_loss improved from 0.20505 to 0.19412, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00003: val_acc improved from 0.92843 to 0.93219, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 4/40\n", "7260/7260 [==============================] - 37s 5ms/step - loss: 0.1837 - acc: 0.9360 - val_loss: 0.1872 - val_acc: 0.9350\n", "\n", "Epoch 00004: val_loss improved from 0.19412 to 0.18723, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00004: val_acc improved from 0.93219 to 0.93504, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 5/40\n", "7260/7260 [==============================] - 37s 5ms/step - loss: 0.1764 - acc: 0.9386 - val_loss: 0.1819 - val_acc: 0.9370\n", "\n", "Epoch 00005: val_loss improved from 0.18723 to 0.18190, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00005: val_acc improved from 0.93504 to 0.93705, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 6/40\n", "7260/7260 [==============================] - 36s 5ms/step - loss: 0.1705 - acc: 0.9409 - val_loss: 0.1808 - val_acc: 0.9376\n", "\n", "Epoch 00006: val_loss improved from 0.18190 to 0.18083, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00006: val_acc improved from 0.93705 to 0.93759, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 7/40\n", "7260/7260 [==============================] - 37s 5ms/step - loss: 0.1660 - acc: 0.9423 - val_loss: 0.1780 - val_acc: 0.9389\n", "\n", "Epoch 00007: val_loss improved from 0.18083 to 0.17795, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00007: val_acc improved from 0.93759 to 0.93890, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 8/40\n", "7260/7260 [==============================] - 37s 5ms/step - loss: 0.1620 - acc: 0.9435 - val_loss: 0.1759 - val_acc: 0.9391\n", "\n", "Epoch 00008: val_loss improved from 0.17795 to 0.17589, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00008: val_acc improved from 0.93890 to 0.93913, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 9/40\n", "7260/7260 [==============================] - 37s 5ms/step - loss: 0.1588 - acc: 0.9447 - val_loss: 0.1747 - val_acc: 0.9396\n", "\n", "Epoch 00009: val_loss improved from 0.17589 to 0.17466, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00009: val_acc improved from 0.93913 to 0.93961, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 10/40\n", "7260/7260 [==============================] - 37s 5ms/step - loss: 0.1558 - acc: 0.9457 - val_loss: 0.1733 - val_acc: 0.9405\n", "\n", "Epoch 00010: val_loss improved from 0.17466 to 0.17328, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00010: val_acc improved from 0.93961 to 0.94051, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 11/40\n", "7260/7260 [==============================] - 37s 5ms/step - loss: 0.1533 - acc: 0.9465 - val_loss: 0.1732 - val_acc: 0.9410\n", "\n", "Epoch 00011: val_loss improved from 0.17328 to 0.17321, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00011: val_acc improved from 0.94051 to 0.94102, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 12/40\n", "7260/7260 [==============================] - 37s 5ms/step - loss: 0.1511 - acc: 0.9475 - val_loss: 0.1724 - val_acc: 0.9411\n", "\n", "Epoch 00012: val_loss improved from 0.17321 to 0.17244, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00012: val_acc improved from 0.94102 to 0.94112, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 13/40\n", "7260/7260 [==============================] - 36s 5ms/step - loss: 0.1492 - acc: 0.9481 - val_loss: 0.1729 - val_acc: 0.9409\n", "\n", "Epoch 00013: val_loss did not improve from 0.17244\n", "\n", "Epoch 00013: val_acc did not improve from 0.94112\n", "Epoch 14/40\n", "7260/7260 [==============================] - 36s 5ms/step - loss: 0.1472 - acc: 0.9487 - val_loss: 0.1736 - val_acc: 0.9406\n", "\n", "Epoch 00014: val_loss did not improve from 0.17244\n", "\n", "Epoch 00014: val_acc did not improve from 0.94112\n", "Epoch 15/40\n", "7260/7260 [==============================] - 37s 5ms/step - loss: 0.1455 - acc: 0.9493 - val_loss: 0.1745 - val_acc: 0.9403\n", "\n", "Epoch 00015: val_loss did not improve from 0.17244\n", "\n", "Epoch 00015: val_acc did not improve from 0.94112\n", "Epoch 16/40\n", "7260/7260 [==============================] - 36s 5ms/step - loss: 0.1441 - acc: 0.9497 - val_loss: 0.1735 - val_acc: 0.9407\n", "\n", "Epoch 00016: val_loss did not improve from 0.17244\n", "\n", "Epoch 00016: val_acc did not improve from 0.94112\n", "Epoch 17/40\n", "7260/7260 [==============================] - 36s 5ms/step - loss: 0.1426 - acc: 0.9502 - val_loss: 0.1740 - val_acc: 0.9407\n", "\n", "Epoch 00017: val_loss did not improve from 0.17244\n", "\n", "Epoch 00017: val_acc did not improve from 0.94112\n", "Epoch 18/40\n", "7260/7260 [==============================] - 36s 5ms/step - loss: 0.1413 - acc: 0.9507 - val_loss: 0.1742 - val_acc: 0.9406\n", "\n", "Epoch 00018: val_loss did not improve from 0.17244\n", "\n", "Epoch 00018: val_acc did not improve from 0.94112\n", "Epoch 19/40\n", "7260/7260 [==============================] - 37s 5ms/step - loss: 0.1398 - acc: 0.9512 - val_loss: 0.1743 - val_acc: 0.9414\n", "\n", "Epoch 00019: val_loss did not improve from 0.17244\n", "\n", "Epoch 00019: val_acc improved from 0.94112 to 0.94140, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 20/40\n", "7260/7260 [==============================] - 36s 5ms/step - loss: 0.1386 - acc: 0.9515 - val_loss: 0.1740 - val_acc: 0.9416\n", "\n", "Epoch 00020: val_loss did not improve from 0.17244\n", "\n", "Epoch 00020: val_acc improved from 0.94140 to 0.94165, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 21/40\n", "7260/7260 [==============================] - 26s 4ms/step - loss: 0.1374 - acc: 0.9521 - val_loss: 0.1759 - val_acc: 0.9413\n", "\n", "Epoch 00021: val_loss did not improve from 0.17244\n", "\n", "Epoch 00021: val_acc did not improve from 0.94165\n", "Epoch 22/40\n", "7260/7260 [==============================] - 25s 3ms/step - loss: 0.1365 - acc: 0.9524 - val_loss: 0.1760 - val_acc: 0.9414\n", "\n", "Epoch 00022: val_loss did not improve from 0.17244\n", "\n", "Epoch 00022: val_acc did not improve from 0.94165\n", "Epoch 23/40\n", "7260/7260 [==============================] - 25s 3ms/step - loss: 0.1354 - acc: 0.9526 - val_loss: 0.1771 - val_acc: 0.9410\n", "\n", "Epoch 00023: val_loss did not improve from 0.17244\n", "\n", "Epoch 00023: val_acc did not improve from 0.94165\n", "Epoch 24/40\n"]}, {"name": "stdout", "output_type": "stream", "text": ["7260/7260 [==============================] - 26s 4ms/step - loss: 0.1343 - acc: 0.9532 - val_loss: 0.1773 - val_acc: 0.9415\n", "\n", "Epoch 00024: val_loss did not improve from 0.17244\n", "\n", "Epoch 00024: val_acc did not improve from 0.94165\n", "Epoch 25/40\n", "7260/7260 [==============================] - 25s 3ms/step - loss: 0.1335 - acc: 0.9533 - val_loss: 0.1776 - val_acc: 0.9417\n", "\n", "Epoch 00025: val_loss did not improve from 0.17244\n", "\n", "Epoch 00025: val_acc improved from 0.94165 to 0.94172, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 26/40\n", "7260/7260 [==============================] - 27s 4ms/step - loss: 0.1326 - acc: 0.9537 - val_loss: 0.1780 - val_acc: 0.9416\n", "\n", "Epoch 00026: val_loss did not improve from 0.17244\n", "\n", "Epoch 00026: val_acc did not improve from 0.94172\n", "Epoch 27/40\n", "7260/7260 [==============================] - 33s 5ms/step - loss: 0.1318 - acc: 0.9539 - val_loss: 0.1791 - val_acc: 0.9413\n", "\n", "Epoch 00027: val_loss did not improve from 0.17244\n", "\n", "Epoch 00027: val_acc did not improve from 0.94172\n", "Epoch 28/40\n", "7260/7260 [==============================] - 34s 5ms/step - loss: 0.1308 - acc: 0.9545 - val_loss: 0.1784 - val_acc: 0.9416\n", "\n", "Epoch 00028: val_loss did not improve from 0.17244\n", "\n", "Epoch 00028: val_acc did not improve from 0.94172\n", "Epoch 29/40\n", "7260/7260 [==============================] - 26s 4ms/step - loss: 0.1302 - acc: 0.9547 - val_loss: 0.1787 - val_acc: 0.9415\n", "\n", "Epoch 00029: val_loss did not improve from 0.17244\n", "\n", "Epoch 00029: val_acc did not improve from 0.94172\n", "Epoch 30/40\n", "7260/7260 [==============================] - 27s 4ms/step - loss: 0.1294 - acc: 0.9551 - val_loss: 0.1793 - val_acc: 0.9411\n", "\n", "Epoch 00030: val_loss did not improve from 0.17244\n", "\n", "Epoch 00030: val_acc did not improve from 0.94172\n", "Epoch 31/40\n", "7260/7260 [==============================] - 25s 3ms/step - loss: 0.1287 - acc: 0.9552 - val_loss: 0.1793 - val_acc: 0.9419\n", "\n", "Epoch 00031: val_loss did not improve from 0.17244\n", "\n", "Epoch 00031: val_acc improved from 0.94172 to 0.94186, saving model to ../../Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 32/40\n", "7260/7260 [==============================] - 25s 3ms/step - loss: 0.1280 - acc: 0.9554 - val_loss: 0.1804 - val_acc: 0.9412\n", "\n", "Epoch 00032: val_loss did not improve from 0.17244\n", "\n", "Epoch 00032: val_acc did not improve from 0.94186\n", "Epoch 33/40\n", "7260/7260 [==============================] - 25s 3ms/step - loss: 0.1274 - acc: 0.9555 - val_loss: 0.1804 - val_acc: 0.9414\n", "\n", "Epoch 00033: val_loss did not improve from 0.17244\n", "\n", "Epoch 00033: val_acc did not improve from 0.94186\n", "Epoch 34/40\n", "7260/7260 [==============================] - 25s 3ms/step - loss: 0.1266 - acc: 0.9560 - val_loss: 0.1812 - val_acc: 0.9411\n", "\n", "Epoch 00034: val_loss did not improve from 0.17244\n", "\n", "Epoch 00034: val_acc did not improve from 0.94186\n", "Epoch 35/40\n", "7260/7260 [==============================] - 25s 3ms/step - loss: 0.1261 - acc: 0.9562 - val_loss: 0.1811 - val_acc: 0.9411\n", "\n", "Epoch 00035: val_loss did not improve from 0.17244\n", "\n", "Epoch 00035: val_acc did not improve from 0.94186\n", "Epoch 36/40\n", "7260/7260 [==============================] - 25s 3ms/step - loss: 0.1255 - acc: 0.9563 - val_loss: 0.1813 - val_acc: 0.9413\n", "\n", "Epoch 00036: val_loss did not improve from 0.17244\n", "\n", "Epoch 00036: val_acc did not improve from 0.94186\n", "Epoch 37/40\n", "7260/7260 [==============================] - 25s 3ms/step - loss: 0.1249 - acc: 0.9563 - val_loss: 0.1823 - val_acc: 0.9412\n", "\n", "Epoch 00037: val_loss did not improve from 0.17244\n", "\n", "Epoch 00037: val_acc did not improve from 0.94186\n", "Epoch 38/40\n", "7260/7260 [==============================] - 25s 3ms/step - loss: 0.1245 - acc: 0.9567 - val_loss: 0.1822 - val_acc: 0.9410\n", "\n", "Epoch 00038: val_loss did not improve from 0.17244\n", "\n", "Epoch 00038: val_acc did not improve from 0.94186\n", "Epoch 39/40\n", "7260/7260 [==============================] - 25s 3ms/step - loss: 0.1238 - acc: 0.9568 - val_loss: 0.1833 - val_acc: 0.9415\n", "\n", "Epoch 00039: val_loss did not improve from 0.17244\n", "\n", "Epoch 00039: val_acc did not improve from 0.94186\n", "Epoch 40/40\n", "7260/7260 [==============================] - 25s 4ms/step - loss: 0.1232 - acc: 0.9573 - val_loss: 0.1846 - val_acc: 0.9408\n", "\n", "Epoch 00040: val_loss did not improve from 0.17244\n", "\n", "Epoch 00040: val_acc did not improve from 0.94186\n"]}], "source": ["if experiment is not None:\n", "    with experiment.train():\n", "        history = model.fit_generator(val_generator(x_training, y_training_vector, batch_size), steps_per_epoch=steps_per_epoch,\n", "                        epochs=epochs, callbacks=callbacks, class_weight=class_weights,\n", "                        validation_data=val_generator(x_test, y_test_vector,val_batch_size), \n", "                        validation_steps=validation_steps)\n", "else:\n", "    history = model.fit_generator(val_generator(x_training, y_training_vector, batch_size), steps_per_epoch=steps_per_epoch,\n", "                        epochs=epochs, callbacks=callbacks, class_weight=class_weights,\n", "                        validation_data=val_generator(x_test, y_test_vector,val_batch_size), \n", "                        validation_steps=validation_steps)\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Plot history accuracy"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T15:03:07.020940Z", "start_time": "2019-12-28T15:03:06.430813Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['val_loss', 'val_acc', 'loss', 'acc'])\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import pickle\n", "%matplotlib inline\n", "\n", "def smooth(y, box_pts):\n", "    box = np.ones(box_pts)/box_pts\n", "    y_smooth = np.convolve(y, box, mode='same')\n", "    return y_smooth\n", "\n", "\n", "with open(RESULTS_PATH + MODEL_NAME +  \"_accuracy.pkl\", 'wb') as output:\n", "    pickle.dump(history.history, output, pickle.HIGHEST_PROTOCOL)\n", "    if experiment is not None:\n", "        experiment.log_asset(RESULTS_PATH + MODEL_NAME +  \"_accuracy.pkl\")\n", "    \n", "# list all data in history\n", "print(history.history.keys())\n", "x = np.asarray(range(1,epochs + 1))\n", "# summarize history for accuracy\n", "plt.figure()\n", "plt.plot(x, smooth([y*100 for y in history.history['acc']],2))\n", "# plt.plot(x, [y*100 for y in history_history['val_acc']])\n", "plt.plot(x, smooth([y*100 for y in history.history['val_acc']],2))\n", "plt.ylabel('Accuracy (%)')\n", "plt.xlabel('Epochs')\n", "plt.ylim(70,100) ###########################\n", "plt.legend(['Training', 'Test'], loc='lower right')\n", "plt.grid()\n", "plt.savefig(RESULTS_PATH + MODEL_NAME +  \" accuracy history\", bbox_inches='tight')\n", "if experiment is not None:\n", "    experiment.log_image(RESULTS_PATH + MODEL_NAME + \" accuracy history.png\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2018-10-20T11:24:40.580036Z", "start_time": "2018-10-20T11:24:33.351927Z"}}, "source": ["# Final evaluation of the model\n", "## Evaluate accuracy over the test set"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:51.170733Z", "start_time": "2020-01-02T12:25:51.063227Z"}}, "outputs": [], "source": ["model.load_weights(MODELS_PATH + MODEL_NAME + '_acc.hdf5')"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:09:54.116273Z", "start_time": "2019-12-28T19:09:52.384951Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["116153/116153 [==============================] - 2s 15us/step\n", "Accuracy: 94.19%\n"]}], "source": ["test_scores = model.evaluate(x_test, y_test_vector, batch_size=val_batch_size, verbose=1)\n", "    \n", "    \n", "print(\"Accuracy: %.2f%%\" % (test_scores[1]*100))"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:25:55.291973Z", "start_time": "2020-01-02T12:25:54.533215Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["116153/116153 [==============================] - 1s 6us/step\n"]}], "source": ["y_test_prediction = model.predict_classes(x_test, batch_size=val_batch_size, verbose=1)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:10:02.654305Z", "start_time": "2019-12-28T19:09:57.530148Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["464609/464609 [==============================] - 5s 11us/step\n"]}], "source": ["y_training_prediction = model.predict_classes(x_training, batch_size=val_batch_size, verbose=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test if by learning (asn1, asn2) -> p2c then (asn2, asn1) -> c2p and vice versa"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"ExecuteTime": {"end_time": "2019-09-13T16:11:27.120529Z", "start_time": "2019-09-13T16:11:26.778878Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(65736, 2) (65736, 2) (65736, 3)\n"]}], "source": ["p2c = 2\n", "c2p = 1\n", "\n", "p2c_training = np.asarray([np.asarray(x_training[i]) for i in range(len(x_training)) if y_training[i] == p2c])\n", "p2c_training_oposite = np.asarray([np.asarray([pair[1], pair[0]]) for pair in p2c_training])\n", "\n", "p2c_training_label_vector = to_categorical([p2c]*len(p2c_training), num_classes)\n", "p2c_training_oposite_label_vector = to_categorical([c2p]*len(p2c_training_oposite), num_classes)\n", "\n", "print(p2c_training.shape, p2c_training_oposite.shape, p2c_training_oposite_label_vector.shape)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {"ExecuteTime": {"end_time": "2019-09-13T16:11:36.207091Z", "start_time": "2019-09-13T16:11:32.349371Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["65736/65736 [==============================] - 2s 30us/step\n", "Accuracy: 99.08%\n", "65736/65736 [==============================] - 2s 29us/step\n", "Accuracy: 95.81%\n"]}], "source": ["p2c_training_scores = model.evaluate(p2c_training, p2c_training_label_vector, verbose=1)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_scores[1]*100))\n", "\n", "p2c_training_oposite_scores = model.evaluate(p2c_training_oposite, p2c_training_oposite_label_vector, verbose=1)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_oposite_scores[1]*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test if by learning (asn1, asn2) -> p2p then (asn2, asn1) -> p2p and vice versa"]}, {"cell_type": "code", "execution_count": 47, "metadata": {"ExecuteTime": {"end_time": "2019-09-13T16:11:40.613392Z", "start_time": "2019-09-13T16:11:40.512947Z"}}, "outputs": [], "source": ["p2p = 0\n", "\n", "p2p_training = np.asarray([np.asarray(x_training[i]) for i in range(len(x_training)) if y_training[i] == p2p])\n", "p2p_training_oposite = np.asarray([np.asarray([pair[1], pair[0]]) for pair in p2p_training])\n", "\n", "p2p_training_label_vector = to_categorical([p2p]*len(p2p_training), num_classes)\n", "p2p_training_oposite_label_vector = to_categorical([p2p]*len(p2p_training_oposite), num_classes)"]}, {"cell_type": "code", "execution_count": 48, "metadata": {"ExecuteTime": {"end_time": "2019-09-13T16:11:42.561946Z", "start_time": "2019-09-13T16:11:41.737183Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["14229/14229 [==============================] - 0s 29us/step\n", "Accuracy: 92.09%\n", "14229/14229 [==============================] - 0s 28us/step\n", "Accuracy: 87.49%\n"]}], "source": ["p2p_training_scores = model.evaluate(p2p_training, p2p_training_label_vector, verbose=1)\n", "print(\"Accuracy: %.2f%%\" % (p2p_training_scores[1]*100))\n", "\n", "p2p_training_oposite_scores = model.evaluate(p2p_training_oposite, p2p_training_oposite_label_vector, verbose=1)\n", "print(\"Accuracy: %.2f%%\" % (p2p_training_oposite_scores[1]*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot and save a confusion matrix for results over the test set"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define a function"]}, {"cell_type": "code", "execution_count": 49, "metadata": {"ExecuteTime": {"end_time": "2019-09-13T16:12:02.999821Z", "start_time": "2019-09-13T16:12:02.929676Z"}}, "outputs": [], "source": ["%matplotlib inline\n", "\n", "import matplotlib\n", "import pylab as pl\n", "from sklearn.metrics import confusion_matrix\n", "import itertools\n", "import matplotlib.pyplot as plt\n", "import matplotlib.cm as cm\n", "\n", "def plot_confusion_matrix(cm, classes,\n", "                          normalize=False,\n", "                          title='Confusion matrix',\n", "                          fname='Confusion matrix',\n", "                          cmap=plt.cm.Blues):\n", "    \"\"\"\n", "    This function prints and plots the confusion matrix.\n", "    Normalization can be applied by setting `normalize=True`.\n", "    \"\"\"\n", "    if normalize:\n", "        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "        print(\"Normalized confusion matrix\")\n", "        plt.imshow([[100*j for j in i] for i in cm], interpolation='nearest', cmap=cmap)\n", "        cbar = plt.colorbar()\n", "        cbar.ax.set_yticklabels(['0%','20%','40%','60%','80%','100%'])\n", "    else:\n", "        print('Confusion matrix, without normalization')\n", "        plt.imshow(cm, interpolation='nearest', cmap=cmap)\n", "        plt.colorbar()\n", "    \n", "    tick_marks = np.arange(len(classes))\n", "    plt.xticks(tick_marks, classes, rotation=45)\n", "    plt.yticks(tick_marks, classes)\n", "\n", "    fmt = '.1f' if normalize else 'd'\n", "    thresh = cm.max() / 2.\n", "    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):\n", "        if normalize:\n", "            plt.text(j, i, format(cm[i, j]*100, fmt) + '%',\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")\n", "        else:\n", "            plt.text(j, i, format(cm[i, j], fmt),\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")    \n", "        \n", "    plt.tight_layout()\n", "    plt.ylabel('True Label')\n", "    plt.xlabel('Predicted Label')\n", "    plt.savefig(fname, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 50, "metadata": {"ExecuteTime": {"end_time": "2019-09-13T16:12:39.756540Z", "start_time": "2019-09-13T16:12:38.908132Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Confusion matrix, without normalization\n", "Normalized confusion matrix\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Compute confusion matrix\n", "cnf_matrix = confusion_matrix(y_test, y_test_prediction)\n", "np.set_printoptions(precision=2)\n", "\n", "# Plot non-normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names,\n", "                      title='Confusion matrix, without normalization',\n", "                      fname=RESULTS_PATH + MODEL_NAME + \"_\" + 'Confusion_matrix_without_normalization')\n", "if experiment is not None:\n", "    experiment.log_image(RESULTS_PATH + MODEL_NAME + \"_\" + 'Confusion_matrix_without_normalization.png', \n", "                         name='Confusion_matrix_without_normalization.png')\n", "\n", "# Plot normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names, normalize=True,\n", "                      title='Normalized Confusion Msatrix',\n", "                      fname=RESULTS_PATH +MODEL_NAME + \"_\" + 'Normalized_confusion_matrix')\n", "\n", "if experiment is not None:\n", "    experiment.log_image(RESULTS_PATH + MODEL_NAME + \"_\" + 'Normalized_confusion_matrix.png',\n", "                         name='Normalized_confusion_matrix.png')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot cm for training set"]}, {"cell_type": "code", "execution_count": 51, "metadata": {"ExecuteTime": {"end_time": "2019-09-13T16:13:13.383658Z", "start_time": "2019-09-13T16:13:12.591168Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Confusion matrix, without normalization\n", "Normalized confusion matrix\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Compute confusion matrix\n", "cnf_matrix = confusion_matrix(y_training, y_training_prediction)\n", "np.set_printoptions(precision=2)\n", "\n", "# Plot non-normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names,\n", "                      title='Confusion matrix, without normalization',\n", "                      fname=RESULTS_PATH + MODEL_NAME + \"_\" + 'training_Confusion_matrix_without_normalization')\n", "if experiment is not None:\n", "    experiment.log_image(RESULTS_PATH + MODEL_NAME + \"_\" + 'training_Confusion_matrix_without_normalization.png')\n", "\n", "# Plot normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names, normalize=True,\n", "                      title='Normalized Confusion Matrix',\n", "                      fname=RESULTS_PATH +MODEL_NAME + \"_\" + 'training_Normalized_confusion_matrix')\n", "if experiment is not None:\n", "    experiment.log_image(RESULTS_PATH + MODEL_NAME + \"_\" + 'training_Normalized_confusion_matrix.png')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Export the model to a file"]}, {"cell_type": "code", "execution_count": 52, "metadata": {"ExecuteTime": {"end_time": "2019-09-13T16:13:35.680589Z", "start_time": "2019-09-13T16:13:35.655242Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Save Model\n"]}], "source": ["model_json = model.to_json()\n", "with open(MODELS_PATH + MODEL_NAME + '.json', \"w\") as json_file:\n", "    json_file.write(model_json)\n", "model.save_weights(MODELS_PATH + MODEL_NAME + '.h5')\n", "print(\"Save Model\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Export results to a csv file (with original ASNs)\n", "## Define functions"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:27:52.420379Z", "start_time": "2020-01-02T12:27:52.393487Z"}}, "outputs": [], "source": ["def dataset_idx2asn(dataset, indexes=None):\n", "    dataset_asn = []\n", "    if indexes is not None:\n", "        dataset = np.take(dataset, indexes, axis=0)\n", "\n", "    for i, route in enumerate(dataset):\n", "        route_asn = []\n", "        for idx in route:\n", "            route_asn.append(idx2asn(idx))\n", "        dataset_asn.append(route_asn) \n", "    return dataset_asn\n", "\n", "def dataset_idx2asn_labels(dataset, labels, class_names=class_names, indexes=None):\n", "    dataset_asn = []\n", "    if indexes is not None:\n", "        dataset = np.take(dataset, indexes, axis=0)\n", "        labels = np.take(labels, indexes, axis=0)\n", "    \n", "    for i, route in enumerate(dataset):\n", "        route_asn = []\n", "        for idx in route:\n", "            route_asn.append(idx2asn(idx))\n", "        route_asn.append(class_names[labels[i]])\n", "        dataset_asn.append(route_asn) \n", "    return dataset_asn\n", "\n", "\n", "def dataset_idx2asn_labels_predictions(dataset, labels, predictions, class_names=class_names, indexes=None):\n", "    dataset_asn = []\n", "    if indexes is not None:\n", "        dataset = np.take(dataset, indexes, axis=0)\n", "        labels = np.take(labels, indexes, axis=0)\n", "        predictions = np.take(predictions, indexes, axis=0)\n", "    \n", "    for i, route in enumerate(dataset):\n", "        route_asn = []\n", "        for idx in route:\n", "            route_asn.append(idx2asn(idx))\n", "        route_asn.append(class_names[labels[i]])\n", "        route_asn.append(class_names[predictions[i]])\n", "        dataset_asn.append(route_asn) \n", "    return dataset_asn\n", "\n", "def dataset_idx2asn_labels_predictions_prob(dataset, labels, predictions, prob, class_names=class_names, indexes=None):\n", "    dataset_asn = []\n", "    if indexes is not None:\n", "        dataset = np.take(dataset, indexes, axis=0)\n", "        labels = np.take(labels, indexes, axis=0)\n", "        predictions = np.take(predictions, indexes, axis=0)\n", "        prob = np.take(prob, indexes, axis=0)\n", "    \n", "    for i, route in enumerate(dataset):\n", "        route_asn = []\n", "        for idx in route:\n", "            route_asn.append(idx2asn(idx))\n", "        route_asn.append(class_names[labels[i]])\n", "        route_asn.append(class_names[predictions[i]])\n", "        for p in prob[i]:\n", "            route_asn.append(round(p,4))\n", "        \n", "        dataset_asn.append(route_asn) \n", "    return dataset_asn\n", "\n", "import csv\n", "def export_csv(dataset, csv_name, header_type='with_gt'):\n", "    header_types = {\n", "        'reg': [\"AS1\", \"AS2\", \"Label\", \"Prediction\", \"P2P_Prob\", \"C2P_Prob\", \"P2C_Prob\"],\n", "        'with_gt': [\"AS1\", \"AS2\", \"Label\", 'GT', \"Prediction\", \"P2P_Prob\", \"C2P_Prob\", \"P2C_Prob\"]\n", "    }\n", "    \n", "    with open(csv_name + '.csv', 'w') as csv_file:\n", "        csv_writer = csv.writer(csv_file)\n", "        if header_type is not None:\n", "            csv_writer.writerow(header_types[header_type])\n", "        for row in dataset:\n", "            csv_writer.writerow(row)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load a relevant dataset {all, misclassified, decided, undecided} and get model predictions"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:28:15.287278Z", "start_time": "2020-01-02T12:28:14.512854Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["116153/116153 [==============================] - 1s 7us/step\n"]}], "source": ["y_test_prob = model.predict_proba(x_test, batch_size=val_batch_size, verbose=1)\n", "# y_training_prob = model.predict_proba(x_training, batch_size=val_batch_size, verbose=1)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:28:25.482915Z", "start_time": "2020-01-02T12:28:25.093115Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6753 6753 6753 6753\n", "(6753, 3)\n"]}], "source": ["# # Create test misclassified dataset\n", "x_test_misclassified = np.asarray([route for i,route in enumerate(x_test) if y_test[i] != y_test_prediction[i]])\n", "y_test_misclassified_prediction = np.asarray([label for i,label in enumerate(y_test_prediction) if y_test[i] != y_test_prediction[i]])\n", "y_test_misclassified = np.asarray([label for i,label in enumerate(y_test) if y_test[i] != y_test_prediction[i]])\n", "y_test_misclassified_prob = np.asarray([prob for i,prob in enumerate(y_test_prob) if y_test[i] != y_test_prediction[i]])\n", "\n", "print(len(x_test_misclassified), len(y_test_misclassified_prediction), len(y_test_misclassified))\n", "print(y_test_misclassified_prob.shape)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:28:40.895288Z", "start_time": "2020-01-02T12:28:40.682670Z"}}, "outputs": [], "source": ["dataset_test_misclassified = dataset_idx2asn_labels_predictions_prob(x_test_misclassified, y_test_misclassified,\n", "                                        y_test_misclassified_prediction, y_test_misclassified_prob)\n", "export_csv(dataset_test_misclassified, RESULTS_PATH + MODEL_NAME + \"_test_misclassified\")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:28:50.624154Z", "start_time": "2020-01-02T12:28:50.612628Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.96811545 0.56680095 0.8645542  0.9803387  0.7388056  0.9407794\n", " 0.9536408  0.9981211  0.9132586  0.5935503 ]\n"]}], "source": ["y_test_misclassified_prob_max = np.array([y_test_misclassified_prob[i][pred] for i, pred in enumerate(y_test_misclassified_prediction)])\n", "print(y_test_misclassified_prob_max[:10])"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"ExecuteTime": {"end_time": "2020-01-02T12:29:00.460559Z", "start_time": "2020-01-02T12:29:00.443226Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[4085, 3980, 678, 1566, 6738, 5789, 2122, 72, 679, 1927, 1261, 1043, 5839, 30, 2664, 4697, 4711, 3824, 561, 1362, 3325, 5737, 5792, 2048, 2549, 5591, 323, 2706, 3973, 2506, 4700, 2961, 5761, 6244, 5950, 947, 4891, 4096, 1737, 3749, 6101, 4034, 700, 1148, 5064, 2071, 2585, 6191, 5199, 2559, 5444, 1973, 1135, 5710, 3573, 3462, 3127, 5061, 1801, 1145, 424, 5363, 2954, 5132, 2708, 2187, 2422, 3601, 6265, 1307, 4008, 456, 1946, 3773, 5095, 2243, 2483, 1680, 6060, 6435, 3247, 3258, 3892, 6653, 673, 404, 5873, 3531, 2785, 5949, 711, 1414, 3602, 2305, 1110, 6392, 6143, 2118, 2864, 3431]\n", "0.999998\n"]}], "source": ["indexes = np.array(list(range(len(x_test_misclassified))))\n", "indexes = [x for _, x in sorted(zip(y_test_misclassified_prob_max,indexes), reverse=True)]\n", "print(indexes[:100])\n", "print(y_test_misclassified_prob_max[indexes[0]])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2019-05-04T15:44:11.090069Z", "start_time": "2019-05-04T15:44:11.008017Z"}}, "outputs": [], "source": ["dataset_misclassified = dataset_idx2asn_and_labels(x_test_misclassified, y_test_misclassified_prediction)\n", "export_csv(dataset_misclassified, RESULTS_PATH + MODEL_NAME + \"_misclassified\")"]}], "metadata": {"hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 2}