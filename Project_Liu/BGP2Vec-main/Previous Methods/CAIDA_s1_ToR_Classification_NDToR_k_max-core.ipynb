{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Import python packages"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:47:21.366054Z", "start_time": "2020-01-01T19:47:20.425201Z"}}, "outputs": [], "source": ["from collections import defaultdict\n", "import pickle\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from collections import Counter\n", "import random\n", "\n", "np.random.seed(7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Define parameters and load bgp_routes and ToR datasets"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:47:30.571166Z", "start_time": "2020-01-01T19:47:22.742782Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3669655,) (3669655,)\n", "(580762, 2) (580762,)\n"]}], "source": ["ToR_MODEL_NAME = \"CAIDA_s1_ToR_Classification_NDToR_k_max-core\"\n", "\n", "TEST_SIZE = 0.2\n", "TOR_LABELS_DICT = {'P2P':0, 'C2P': 1,'P2C': 2}\n", "class_names = ['P2P', 'C2P', 'P2C']\n", "DATA_PATH = '../../Data/'\n", "MODELS_PATH = '../../Models/'\n", "RESULTS_PATH = '../../Results/'\n", "\n", "\n", "bgp_routes = np.load(DATA_PATH + \"bgp_routes_dataset.npy\")\n", "bgp_routes_labels = np.load(DATA_PATH + \"bgp_routes_labels.npy\")\n", "print(bgp_routes.shape, bgp_routes_labels.shape)\n", "\n", "DATA = \"caida_s1_tor\"\n", "tor_dataset = np.load(DATA_PATH + DATA + \"_dataset.npy\")\n", "tor_labels = np.load(DATA_PATH + DATA + \"_labels.npy\")\n", "\n", "print(tor_dataset.shape, tor_labels.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate training and test sets\n", "## Shauffle dataset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:47:30.706997Z", "start_time": "2020-01-01T19:47:30.616843Z"}}, "outputs": [], "source": ["from sklearn.utils import shuffle\n", "dataset, labels = shuffle(tor_dataset, tor_labels, random_state=7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate a balanced dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T13:08:27.960627Z", "start_time": "2018-11-17T13:08:27.890678Z"}}, "outputs": [], "source": ["# def generate_balanced_dataset(dataset, labels, labels_set):\n", "#     sets_dict = dict()\n", "#     for label in labels_set:\n", "#         sets_dict[label] = np.asarray([np.asarray(dataset[i]) for i in range(len(dataset)) if labels[i] == label])\n", "    \n", "#     min_set_len = min([len(label_set) for label_set in sets_dict.values()])\n", "    \n", "#     for label, label_set in sets_dict.items():\n", "#         sets_dict[label] = label_set[np.random.choice(label_set.shape[0], min_set_len, replace=False)]\n", "    \n", "#     dataset = np.concatenate((sets_dict.values()))\n", "#     labels = []\n", "#     for label, label_set in sets_dict.items():\n", "#         labels += [label]*len(label_set)\n", "#         print label, len(label_set)\n", "#     labels = np.asarray(labels)\n", "#     return shuffle(dataset, labels, random_state=7)\n", "\n", "# dataset, labels = generate_balanced_dataset(dataset, labels, (0,1,3))\n", "# print dataset.shape, labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train Test Split"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:47:30.843276Z", "start_time": "2020-01-01T19:47:30.755268Z"}}, "outputs": [], "source": ["x_training, x_test, y_training, y_test = train_test_split(dataset, labels, test_size=TEST_SIZE)\n", "\n", "del dataset, labels"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:47:31.047932Z", "start_time": "2020-01-01T19:47:30.891003Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(464609, 2) (464609,)\n", "(116153, 2) (116153,)\n", "0.7999989668745545\n", "Counter({0: 275382, 1: 94716, 2: 94511}) Counter({0: 68570, 2: 23894, 1: 23689})\n"]}], "source": ["print(x_training.shape, y_training.shape)\n", "print(x_test.shape, y_test.shape)\n", "\n", "print(1.0*len(x_training)/(len(x_test)+len(x_training)))\n", "\n", "from collections import Counter\n", "training_c = Counter(y_training)\n", "test_c = Counter(y_test)\n", "print(training_c, test_c)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:47:31.193470Z", "start_time": "2020-01-01T19:47:31.182667Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 20.386174180870366\n", "0 59.27177476114324\n", "2 20.342051057986392\n", "0 59.03420488493625\n", "2 20.571143233493753\n", "1 20.39465188157\n"]}], "source": ["for k,v in training_c.items():\n", "    print(k, 100.0*v/len(x_training))\n", "print\n", "for k,v in test_c.items():\n", "    print(k, 100.0*v/len(x_test))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Run ND-ToR Algo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load k_shell"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T07:26:34.356426Z", "start_time": "2020-01-01T07:26:34.332298Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["62523\n"]}], "source": ["with open(MODELS_PATH + 's1_k_shell.pickle', 'rb') as handle:\n", "    k_shell = pickle.load(handle)\n", "print(len(k_shell))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load CAIDA results and create CP-Core with NetworkX"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T07:26:38.917214Z", "start_time": "2020-01-01T07:26:37.882770Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["343952\n"]}], "source": ["caida_p2p = [tor for i, tor in enumerate(tor_dataset) if tor_labels[i] == 0]\n", "\n", "TIER1 = [\"174\", \"209\", \"286\", \"701\", \"1239\", \"1299\", \"2828\", \"2914\", \"3257\", \"3320\", \"3356\",\n", "         \"3491\", \"5511\", \"6453\", \"6461\", \"6762\", \"6830\", \"7018\", \"12956\"]\n", "# with open(caida_path, \"r\") as f:\n", "#     for line in f:\n", "#         as0, as1, label = [int(part) for part in line.split()[0].split('|')]\n", "#         if label == 0 and as0 in ASN_index_map and as1 in ASN_index_map:\n", "#             caida_p2p.append((ASN_index_map[as0], ASN_index_map[as1]))\n", "caida_p2p = [tor for i, tor in enumerate(tor_dataset) if tor_labels[i] == 0]\n", "print(len(caida_p2p))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:09:54.311120Z", "start_time": "2019-12-28T16:09:41.690522Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["12254\n", "12254 12254\n"]}], "source": ["import networkx as nx\n", "\n", "verteces = set()\n", "for pair in caida_p2p:\n", "    verteces.add(pair[0])\n", "    verteces.add(pair[1])\n", "print(len(verteces))\n", "\n", "vertex2ind = dict()\n", "ind2vertex = dict()\n", "for i, vertex in enumerate(verteces):\n", "    vertex2ind[vertex] = i\n", "    ind2vertex[i] = vertex\n", "\n", "print(len(vertex2ind), len(ind2vertex))\n", "\n", "g = nx.DiGraph()\n", "g.add_edges_from([(vertex2ind[pair[0]], vertex2ind[pair[1]]) for pair in caida_p2p])\n", "g.add_edges_from([(vertex2ind[pair[1]], vertex2ind[pair[0]]) for pair in caida_p2p])\n", "\n", "SCCs = [c for c in sorted(nx.strongly_connected_components(g),key=len, reverse=True)]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:10:33.810535Z", "start_time": "2019-12-28T16:10:33.771861Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["114\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "11889\n", "11889\n"]}], "source": ["print(len(SCCs))\n", "for i, scc in enumerate(SCCs):\n", "    for as_tier1 in TIER1:\n", "        if vertex2ind[as_tier1] not in scc:\n", "            break\n", "        print(i)\n", "print(len(SCCs[0]))\n", "scc = SCCs[0]\n", "scc = set([ind2vertex[ind] for ind in scc])\n", "print(len(scc))"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:12:51.275044Z", "start_time": "2019-12-28T16:12:46.031280Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["343446\n", "numbr of edges 171723.0\n"]}], "source": ["cp_core = set()\n", "for pair in caida_p2p:\n", "    if pair[0] in scc and pair[1] in scc:\n", "        cp_core.add(tuple(pair))\n", "        cp_core.add(tuple((pair[1], pair[0])))\n", "print(len(cp_core))\n", "print(\"numbr of edges \" + str(len(cp_core)/2))"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["## Load CAIDA results and create CP-Core"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:25:50.178920Z", "start_time": "2019-01-05T10:25:49.237305Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["192088\n"]}], "source": ["caida_path = 'CAIDA_20181001.as-rel_cleaned.txt'\n", "caida_p2p = list()\n", "\n", "TIER1 = [7018, 209, 3356, 3549, 4323, 3320, 3257, 286, 6830, 2914, 5511, 3491, 1239, 6453, 6762, 12956, 701, 702, 703, 2828, 6461]\n", "TIER1 = [ASN_index_map[asn] for asn in TIER1]\n", "\n", "with open(caida_path, \"r\") as f:\n", "    for line in f:\n", "        as0, as1, label = [int(part) for part in line.split()[0].split('|')]\n", "        if label == 0 and as0 in ASN_index_map and as1 in ASN_index_map:\n", "            caida_p2p.append((ASN_index_map[as0], ASN_index_map[as1]))\n", "print(len(caida_p2p))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:32:15.437000Z", "start_time": "2019-01-05T10:32:15.379704Z"}, "code_folding": [3], "hidden": true}, "outputs": [], "source": ["#This class represents a directed graph using adjacency list representation \n", "class Graph: \n", "   \n", "    def __init__(self,vertices): \n", "        self.V = vertices #No. of vertices \n", "        self.graph = defaultdict(list) # default dictionary to store graph\n", "        self.scc_list = []\n", "   \n", "    # function to add an edge to graph \n", "    def addEdge(self,u,v): \n", "        self.graph[u].append(v) \n", "   \n", "    # A function used by DFS \n", "    def DFSUtil(self,v,visited): \n", "        # Mark the current node as visited and print it \n", "        visited[v]= True\n", "#         print v,\n", "        self.scc_list[-1].append(v)\n", "        #Recur for all the vertices adjacent to this vertex \n", "        for i in self.graph[v]: \n", "            if visited[i]==False: \n", "                self.D<PERSON><PERSON><PERSON>(i,visited) \n", "  \n", "  \n", "    def fillOrder(self,v,visited, stack): \n", "        # Mark the current node as visited  \n", "        visited[v]= True\n", "        #Recur for all the vertices adjacent to this vertex \n", "        for i in self.graph[v]: \n", "            if visited[i]==False: \n", "                self.fillOrder(i, visited, stack) \n", "        stack = stack.append(v) \n", "      \n", "  \n", "    # Function that returns reverse (or transpose) of this graph \n", "    def get<PERSON><PERSON><PERSON><PERSON>(self): \n", "        g = Graph(self.V) \n", "  \n", "        # Recur for all the vertices adjacent to this vertex \n", "        for i in self.graph: \n", "            for j in self.graph[i]: \n", "                g.add<PERSON>dge(j,i) \n", "        return g \n", "  \n", "   \n", "   \n", "    # The main function that finds and prints all strongly \n", "    # connected components \n", "    def printSCCs(self): \n", "          \n", "        stack = [] \n", "        # Mark all the vertices as not visited (For first DFS) \n", "        visited =[False]*(self.V) \n", "        # Fill vertices in stack according to their finishing \n", "        # times \n", "        for i in range(self.V): \n", "            if visited[i]==False: \n", "                self.fillOrder(i, visited, stack) \n", "  \n", "        # Create a reversed graph \n", "        gr = self.getTranspose() \n", "          \n", "        # Mark all the vertices as not visited (For second DFS) \n", "        visited =[False]*(self.V) \n", "  \n", "        # Now process all vertices in order defined by <PERSON><PERSON> \n", "        while stack: \n", "            i = stack.pop() \n", "            if visited[i]==False:\n", "                gr.scc_list.append([])\n", "                gr.<PERSON><PERSON><PERSON><PERSON>(i, visited) \n", "#                 print\"\"\n", "        \n", "        scc = gr.scc_list\n", "        return scc\n", "\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:45:11.810632Z", "start_time": "2019-01-05T10:45:11.347684Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["13083\n", "(13083, 13083)\n"]}], "source": ["verteces = set()\n", "for pair in caida_p2p:\n", "    verteces.add(pair[0])\n", "    verteces.add(pair[1])\n", "print(len(verteces))\n", "\n", "vertex2ind = dict()\n", "ind2vertex = dict()\n", "for i, vertex in enumerate(verteces):\n", "    vertex2ind[vertex] = i\n", "    ind2vertex[i] = vertex\n", "\n", "print(len(vertex2ind), len(ind2vertex))\n", "\n", "g = Graph(len(verteces))\n", "for pair in caida_p2p:\n", "    g.addEdge(vertex2ind[pair[0]], vertex2ind[pair[1]])\n", "    g.addEdge(vertex2ind[pair[1]], vertex2ind[pair[0]])\n", "\n", "SCCs = g.printSCCs()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:48:02.516359Z", "start_time": "2019-01-05T10:48:02.492207Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["135\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "12618\n", "12618\n"]}], "source": ["print(len(SCCs))\n", "for i, scc in enumerate(SCCs):\n", "    for as_tier1 in TIER1:\n", "        if vertex2ind[as_tier1] not in scc:\n", "            break\n", "        print(i)\n", "print(len(SCCs[134]))\n", "scc = SCCs[134]\n", "scc = set([ind2vertex[ind] for ind in scc])\n", "print(len(scc))"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:48:39.959504Z", "start_time": "2019-01-05T10:48:39.759351Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["383512\n", "numbr of edges 191756\n"]}], "source": ["cp_core = set()\n", "for pair in caida_p2p:\n", "    if pair[0] in scc and pair[1] in scc:\n", "        cp_core.add(pair)\n", "        cp_core.add((pair[1], pair[0]))\n", "print(len(cp_core))\n", "print(\"numbr of edges \" + str(len(cp_core)/2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create k_max-core"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:22:27.788461Z", "start_time": "2019-12-28T17:22:27.760421Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["21\n"]}], "source": ["k_max = max(k_shell.values())\n", "k_max_core = [vertex for vertex, k in k_shell.items() if k == k_max]\n", "print(len(k_max_core))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:22:45.563401Z", "start_time": "2019-12-28T17:22:33.935024Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["420\n"]}], "source": ["k_max_edges = set()\n", "for i in range(len(k_max_core)):\n", "    for j in range(i):\n", "        if (k_max_core[i], k_max_core[j]) in x_training or (k_max_core[i], k_max_core[j]) in x_test:\n", "            k_max_edges.add((k_max_core[i], k_max_core[j]))\n", "        if (k_max_core[j], k_max_core[i]) in x_training or (k_max_core[j], k_max_core[i]) in x_test:\n", "            k_max_edges.add((k_max_core[j], k_max_core[i]))\n", "            \n", "print(len(k_max_edges))"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["## Create x_training_core"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"ExecuteTime": {"end_time": "2019-01-11T19:36:20.486675Z", "start_time": "2019-01-11T19:36:20.351637Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(90388, 47794)\n"]}], "source": ["x_training_edges = set()\n", "x_training_vertecs = set()\n", "for pair in x_training:\n", "    x_training_edges.add((pair[0], pair[1]))\n", "    x_training_vertecs.add(pair[0])\n", "    x_training_vertecs.add(pair[1])\n", "print(len(x_training_edges), len(x_training_vertecs))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run NDTOR_CP"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:22:53.569765Z", "start_time": "2019-12-28T17:22:53.401927Z"}, "code_folding": [23, 29, 105, 127, 161]}, "outputs": [], "source": ["class NDTOR_CP:\n", "    def __init__(self, core_verteces, core_edges, routing_tables, core_labels=None, is_core=True, threshold=0.85, k_shell=None):\n", "        if is_core:\n", "            self.tor_dict = self.__intialize_tor_dict_with_core(core_edges)\n", "            print('Finished __intialize_tor_dict_with_core')\n", "            self.pahse2_routes = self.__split_path_through_core(routing_tables, core_verteces)\n", "        else:\n", "            self.tor_dict = self.__intialize_tor_dict_with_training_set(core_edges, core_labels)\n", "            print('Finished __intialize_tor_dict_with_training_set')\n", "            self.pahse2_routes = routing_tables\n", "        print('Finished __split_path_through_core with ' + str(len(self.pahse2_routes)) + ' remaining for phase 2')\n", "        self.unclassified_pairs = self.__pahse2(threshold)\n", "        print('Finished __pahse2 with ' + str(len(self.unclassified_pairs)) + \" unclassified pairs\")\n", "        self.__pahse3()\n", "        print('Finished __pahse3 with ' + str(len(self.unclassified_pairs)) + \" unclassified pairs\")\n", "        \n", "        if len(self.unclassified_pairs) > 0:\n", "            if k_shell is None:\n", "                self.k_shell = self.__compute_k_shells(routing_tables)\n", "            else:\n", "                self.k_shell = k_shell\n", "            print(\"Finished __compute_k_shells\")\n", "            self.__compare_k_shells()\n", "            print(\"Finished __compare_k_shells\")\n", "    \n", "    \n", "    def __intialize_tor_dict_with_training_set(self, core_edges, core_labels):\n", "        tor_dict = dict()\n", "        for i, edge in enumerate(core_edges):\n", "            tor_dict[edge] = core_labels[i]\n", "        return tor_dict\n", "    \n", "\n", "    def __intialize_tor_dict_with_core(self, core_edges):\n", "        tor_dict = dict()\n", "        for edge in core_edges:\n", "            tor_dict[edge] = 0 # 0 - p2p\n", "        return tor_dict\n", "        \n", "    def __split_path_through_core(self, routes, core_verteces):\n", "        pahse2_routes = list()\n", "        for path in routes:\n", "            core_inds = list()\n", "            for j, vertex in enumerate(path):\n", "                if vertex in core_verteces:\n", "                    core_inds.append(j)\n", "            if len(core_inds) == 0:\n", "                pahse2_routes.append(path)\n", "            else:\n", "                for i in range(core_inds[0]):\n", "                    if (path[i], path[i+1]) not in self.tor_dict:\n", "                        self.tor_dict[(path[i], path[i+1])] = 1 # 1 - c2p\n", "                        self.tor_dict[(path[i+1], path[i])] = 3\n", "                for i in range(core_inds[0], core_inds[-1]):\n", "                    if (path[i], path[i+1]) not in self.tor_dict:\n", "                        self.tor_dict[(path[i], path[i+1])] = 0 # 0 - p2p\n", "                        self.tor_dict[(path[i+1], path[i])] = 0 \n", "                for i in range(core_inds[-1], len(path)-1):\n", "                    if (path[i], path[i+1]) not in self.tor_dict:\n", "                        self.tor_dict[(path[i], path[i+1])] = 3 # 0 - p2c\n", "                        self.tor_dict[(path[i+1], path[i])] = 1\n", "        return pahse2_routes\n", "    \n", "    \n", "    def __pahse2(self, threshold):\n", "        votings_p2c = defaultdict(lambda:0)\n", "        votings_c2p = defaultdict(lambda:0)\n", "        voting_pairs = set()\n", "        \n", "        for path in self.pahse2_routes:\n", "            pairs = list(zip(path[:-1], path[1:]))\n", "            pairs_tor = []\n", "            for i, pair in enumerate(pairs):\n", "                if pair in self.tor_dict:\n", "                    pairs_tor.append(self.tor_dict[pair])\n", "                else:\n", "                    pairs_tor.append(-1)\n", "                    voting_pairs.add(pair)\n", "            for i in range(len(pairs)):\n", "                if pairs_tor[i] == -1:\n", "                    if i > 1 and pairs_tor[i-1] == 3: #p2c\n", "                        pairs_tor[i] = 3\n", "                        votings_p2c[pairs[i]] += 1\n", "                    if i + 1 < len(pairs) and pairs_tor[i+1] == 1: #c2p\n", "                        pairs_tor[i] = 1\n", "                        votings_c2p[pairs[i]] += 1\n", "        \n", "        unclassified_pairs = set()\n", "        for pair in voting_pairs:\n", "            if (votings_p2c[pair] + votings_c2p[pair]) > 0:\n", "                rank = (votings_p2c[pair]*1.0)/(votings_p2c[pair] + votings_c2p[pair])\n", "                if rank >= threshold:\n", "                    self.tor_dict[pair] = 3\n", "                elif rank <= (1 - threshold):\n", "                    self.tor_dict[pair] = 1\n", "                else:\n", "                    unclassified_pairs.add(pair)\n", "            else:\n", "                unclassified_pairs.add(pair)\n", "        return unclassified_pairs\n", "    \n", "    def __pahse3(self):\n", "        for path in self.pahse2_routes:\n", "            pairs = list(zip(path[:-1], path[1:]))\n", "            if len(pairs) > 2: \n", "                for i in range(1,len(pairs)-1):\n", "                    if pairs[i] not in self.tor_dict and pairs[i-1] in self.tor_dict and pairs[i+1] in self.tor_dict:\n", "                        if self.tor_dict[pairs[i-1]] == 1 and self.tor_dict[pairs[i+1]] == 3:\n", "                            self.tor_dict[pairs[i]] = 0\n", "                            self.unclassified_pairs.remove(pairs[i])\n", "                        elif self.tor_dict[pairs[i-1]] == 3 and self.tor_dict[pairs[i+1]] == 1:\n", "                            self.tor_dict[pairs[i]] = 0\n", "                            self.unclassified_pairs.remove(pairs[i])\n", "    \n", "    \n", "    def __get_k_shell(self, k, edges, k_shell):\n", "        neighbors = defaultdict(set)\n", "        k_shell_verteces = set()\n", "        for edge in edges:\n", "            neighbors[edge[0]].add(edge)\n", "            neighbors[edge[1]].add(edge)\n", "        for asn, asn_edges in neighbors.items():\n", "            if len(asn_edges) <= k:\n", "                k_shell[asn] = k\n", "                k_shell_verteces.add(asn)\n", "                \n", "        return neighbors, k_shell_verteces\n", "    \n", "    \n", "    def __get_graph_for_routes(self, P):\n", "        edges = []\n", "        for route in P:\n", "            for edge in zip(route[:-1], route[1:]):\n", "                edges.append(edge)\n", "        return set(edges)\n", "                \n", "    \n", "    def __remove_k_shell_edges(self, edges, k_shell_verteces, neighbors):\n", "        for vertex in k_shell_verteces:\n", "            edges = edges - neighbors[vertex]\n", "        return edges\n", "                \n", "                \n", "    def __compute_k_shells(self, routes):\n", "        k_shell = dict()\n", "        k = 1\n", "        edges = self.__get_graph_for_routes(routes)\n", "        \n", "        while len(edges) > 0:\n", "            print(\"K: \" + str(k) + \" Start Iteration on \" + str(len(edges)) + \" edges\")\n", "            neighbors, k_shell_verteces = self.__get_k_shell(k, edges, k_shell)\n", "            k += 1\n", "            edges = self.__remove_k_shell_edges(edges, k_shell_verteces, neighbors)\n", "            print(\"Number of remaining edges: \" + str(len(edges)))\n", "            print()\n", "            \n", "        return k_shell\n", "        \n", "        \n", "    def __compare_k_shells(self):\n", "        for pair in self.unclassified_pairs:\n", "            try:\n", "                as0_k = self.k_shell[pair[0]]\n", "            except:\n", "                as0_k = 0\n", "            try:\n", "                as1_k = self.k_shell[pair[1]]\n", "            except:\n", "                as0_k = 0\n", "            if as0_k == as1_k:\n", "                self.tor_dict[pair] = 0 # p2p\n", "            elif as0_k > as1_k:\n", "                self.tor_dict[pair] = 3 # p2c\n", "            else:\n", "                self.tor_dict[pair] = 1 # c2p\n", "    \n", "                \n", "    def tor_dict2dataset(self):\n", "        dataset = []\n", "        labels = []\n", "        for pair, label in self.tor_dict.items():\n", "            dataset.append(np.asarray(pair))\n", "            labels.append(label)\n", "        print(\"Finished __tor_dict2dataset\")\n", "        return np.asarray(dataset), np.asarray(labels)\n", "\n", "\n", "    def generate_labels_for_set(self, pairs):\n", "        labels = []\n", "        for pair in pairs:\n", "            if (pair[0], pair[1]) in self.tor_dict:\n", "                labels.append(self.tor_dict[(pair[0], pair[1])])\n", "            elif (pair[1], pair[0]) in self.tor_dict:\n", "                if self.tor_dict[(pair[1], pair[0])] == 0 or self.tor_dict[(pair[1], pair[0])] == 2:\n", "                    labels.append(self.tor_dict[(pair[1], pair[0])])\n", "                else:\n", "                    labels.append((self.tor_dict[(pair[1], pair[0])] + 2)%4)\n", "            else:\n", "                labels.append(-1)\n", "        return np.asarray(labels)\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:03:11.797765Z", "start_time": "2019-12-28T16:31:09.928989Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished __intialize_tor_dict_with_core\n", "Finished __split_path_through_core with 276 remaining for phase 2\n", "Finished __pahse2 with 142 unclassified pairs\n", "Finished __pahse3 with 142 unclassified pairs\n", "K: 1 Start Iteration on 147667 edges\n", "Number of remaining edges: 123786\n", "\n", "K: 2 Start Iteration on 123786 edges\n", "Number of remaining edges: 80747\n", "\n", "K: 3 Start Iteration on 80747 edges\n", "Number of remaining edges: 59956\n", "\n", "K: 4 Start Iteration on 59956 edges\n", "Number of remaining edges: 47881\n", "\n", "K: 5 Start Iteration on 47881 edges\n", "Number of remaining edges: 39960\n", "\n", "K: 6 Start Iteration on 39960 edges\n", "Number of remaining edges: 34330\n", "\n", "K: 7 Start Iteration on 34330 edges\n", "Number of remaining edges: 30048\n", "\n", "K: 8 Start Iteration on 30048 edges\n", "Number of remaining edges: 26555\n", "\n", "K: 9 Start Iteration on 26555 edges\n", "Number of remaining edges: 23538\n", "\n", "K: 10 Start Iteration on 23538 edges\n", "Number of remaining edges: 20722\n", "\n", "K: 11 Start Iteration on 20722 edges\n", "Number of remaining edges: 18171\n", "\n", "K: 12 Start Iteration on 18171 edges\n", "Number of remaining edges: 16102\n", "\n", "K: 13 Start Iteration on 16102 edges\n", "Number of remaining edges: 14291\n", "\n", "K: 14 Start Iteration on 14291 edges\n", "Number of remaining edges: 12517\n", "\n", "K: 15 Start Iteration on 12517 edges\n", "Number of remaining edges: 10872\n", "\n", "K: 16 Start Iteration on 10872 edges\n", "Number of remaining edges: 9525\n", "\n", "K: 17 Start Iteration on 9525 edges\n", "Number of remaining edges: 8561\n", "\n", "K: 18 Start Iteration on 8561 edges\n", "Number of remaining edges: 7541\n", "\n", "K: 19 Start Iteration on 7541 edges\n", "Number of remaining edges: 6955\n", "\n", "K: 20 Start Iteration on 6955 edges\n", "Number of remaining edges: 6518\n", "\n", "K: 21 Start Iteration on 6518 edges\n", "Number of remaining edges: 6108\n", "\n", "K: 22 Start Iteration on 6108 edges\n", "Number of remaining edges: 5780\n", "\n", "K: 23 Start Iteration on 5780 edges\n", "Number of remaining edges: 5393\n", "\n", "K: 24 Start Iteration on 5393 edges\n", "Number of remaining edges: 4987\n", "\n", "K: 25 Start Iteration on 4987 edges\n", "Number of remaining edges: 4614\n", "\n", "K: 26 Start Iteration on 4614 edges\n", "Number of remaining edges: 4356\n", "\n", "K: 27 Start Iteration on 4356 edges\n", "Number of remaining edges: 4142\n", "\n", "K: 28 Start Iteration on 4142 edges\n", "Number of remaining edges: 3835\n", "\n", "K: 29 Start Iteration on 3835 edges\n", "Number of remaining edges: 3662\n", "\n", "K: 30 Start Iteration on 3662 edges\n", "Number of remaining edges: 3485\n", "\n", "K: 31 Start Iteration on 3485 edges\n", "Number of remaining edges: 3213\n", "\n", "K: 32 Start Iteration on 3213 edges\n", "Number of remaining edges: 2938\n", "\n", "K: 33 Start Iteration on 2938 edges\n", "Number of remaining edges: 2648\n", "\n", "K: 34 Start Iteration on 2648 edges\n", "Number of remaining edges: 2327\n", "\n", "K: 35 Start Iteration on 2327 edges\n", "Number of remaining edges: 1926\n", "\n", "K: 36 Start Iteration on 1926 edges\n", "Number of remaining edges: 1664\n", "\n", "K: 37 Start Iteration on 1664 edges\n", "Number of remaining edges: 1262\n", "\n", "K: 38 Start Iteration on 1262 edges\n", "Number of remaining edges: 812\n", "\n", "K: 39 Start Iteration on 812 edges\n", "Number of remaining edges: 362\n", "\n", "K: 40 Start Iteration on 362 edges\n", "Number of remaining edges: 0\n", "\n", "Finished __compute_k_shells\n", "Finished __compare_k_shells\n"]}], "source": ["ndtor_cp = NDTOR_CP(scc, cp_core, bgp_routes,k_shell=k_shell)   # CP - Core"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:23:57.124142Z", "start_time": "2019-12-28T17:23:03.552400Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished __intialize_tor_dict_with_core\n", "Finished __split_path_through_core with 372339 remaining for phase 2\n", "Finished __pahse2 with 17250 unclassified pairs\n", "Finished __pahse3 with 14158 unclassified pairs\n", "Finished __compute_k_shells\n", "Finished __compare_k_shells\n"]}], "source": ["ndtor_k_max_core = NDTOR_CP(k_max_core, k_max_edges, bgp_routes, k_shell=k_shell)   # k_max_core - Core"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"ExecuteTime": {"end_time": "2019-01-11T19:45:29.890441Z", "start_time": "2019-01-11T19:45:07.572546Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished __intialize_tor_dict_with_training_set\n", "Finished __split_path_through_core with 3669655 remaining for phase 2\n", "Finished __pahse2 with 19960 unclassified pairs\n", "Finished __pahse3 with 17062 unclassified pairs\n", "Finished __compute_k_shells\n", "Finished __compare_k_shells\n"]}], "source": ["### Reverse to original labels\n", "core_labels = list()\n", "for label in y_training:\n", "    if label == 2:\n", "        core_labels.append(3)\n", "    else:\n", "        core_labels.append(label)\n", "\n", "ndtor_x_training_core = NDTOR_CP(x_training_vertecs, x_training_edges, bgp_routes, core_labels=core_labels, is_core=False, k_shell=k_shell)\n"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"ExecuteTime": {"end_time": "2019-01-11T19:45:58.174139Z", "start_time": "2019-01-11T19:45:55.731185Z"}}, "outputs": [], "source": ["ToR_MODEL_NAME = \"Cleaned_Orig_3_ToR_Classification_NDToR_x_training_core\"\n", "\n", "with open(MODELS_PATH + ToR_MODEL_NAME + '_tor_dict.pickle', 'wb') as handle:\n", "    pickle.dump(ndtor_k_max_core.tor_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)\n", "\n", "# with open(MODELS_PATH + 'k_shell.pickle', 'wb') as handle:\n", "#     pickle.dump(ndtor_cp.k_shell, handle, protocol=pickle.HIGHEST_PROTOCOL)"]}, {"cell_type": "code", "execution_count": 48, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T13:45:11.237303Z", "start_time": "2019-01-05T13:45:11.213810Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["21\n"]}], "source": ["k_max = max(ndtor_cp.k_shell.values())\n", "k_max_core = [vertex for vertex, k in ndtor_cp.k_shell.items() if k == k_max]\n", "print(len(k_max_core))"]}, {"cell_type": "code", "execution_count": 49, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T13:46:40.776218Z", "start_time": "2019-01-05T13:46:40.758274Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3356,)\n", "(6939,)\n", "(1299,)\n", "(174,)\n", "(3257,)\n", "(2914,)\n", "(6453,)\n", "(209,)\n", "(1239,)\n", "(6762,)\n", "(9002,)\n", "(701,)\n", "(6461,)\n", "(4637,)\n", "(3491,)\n", "(286,)\n", "(37100,)\n", "(2497,)\n", "(3303,)\n", "(2516,)\n", "(1273,)\n"]}], "source": ["index_ASN_map = {index: ASN for ASN, index in ASN_index_map.items()}\n", "\n", "for ind in k_max_core:\n", "    print(index_ASN_map[ind],)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save kshell"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:26:22.688694Z", "start_time": "2019-12-28T17:26:22.168255Z"}}, "outputs": [], "source": ["with open(MODELS_PATH + ToR_MODEL_NAME + '_tor_dict.pickle', 'wb') as handle:\n", "    pickle.dump(ndtor_k_max_core.tor_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)\n", "\n", "# with open(MODELS_PATH + ToR_MODEL_NAME + 's1_k_shell.pickle', 'wb') as handle:\n", "#     pickle.dump(ndtor_cp.k_shell, handle, protocol=pickle.HIGHEST_PROTOCOL)\n", "\n", "# with open(MODELS_PATH + 's1_k_shell.pickle', 'rb') as handle:\n", "#     k_shell = pickle.load(handle)\n", "# print(len(k_shell))"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2018-10-20T11:24:40.580036Z", "start_time": "2018-10-20T11:24:33.351927Z"}}, "source": ["# Final evaluation of the model\n", "## Evaluate accuracy over the test set"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:48:22.842666Z", "start_time": "2020-01-01T19:48:22.630614Z"}}, "outputs": [], "source": ["with open(MODELS_PATH + ToR_MODEL_NAME + '_tor_dict.pickle', 'rb') as handle:\n", "    k_max_tor_dict = pickle.load(handle)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:48:32.674310Z", "start_time": "2020-01-01T19:48:32.662137Z"}}, "outputs": [], "source": ["def generate_labels_for_set(tor_dict, pairs):\n", "    labels = []\n", "    for pair in pairs:\n", "        if (pair[0], pair[1]) in tor_dict:\n", "            labels.append(tor_dict[(pair[0], pair[1])])\n", "        elif (pair[1], pair[0]) in tor_dict:\n", "            if tor_dict[(pair[1], pair[0])] == 0 or tor_dict[(pair[1], pair[0])] == 2:\n", "                labels.append(tor_dict[(pair[1], pair[0])])\n", "            else:\n", "                labels.append((tor_dict[(pair[1], pair[0])] + 2)%4)\n", "        else:\n", "            labels.append(-1)\n", "    return np.asarray(labels)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:49:55.058820Z", "start_time": "2020-01-01T19:49:54.549135Z"}}, "outputs": [], "source": ["y_test_prediction = generate_labels_for_set(k_max_tor_dict, x_test)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:26:27.813750Z", "start_time": "2019-12-28T17:26:27.117329Z"}}, "outputs": [], "source": ["# y_test_prediction = ndtor_k_max_core.generate_labels_for_set(x_test)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:50:00.455138Z", "start_time": "2020-01-01T19:50:00.409173Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0, 1, 3, -1}\n"]}], "source": ["print(set(y_test_prediction))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:50:03.155899Z", "start_time": "2020-01-01T19:50:02.789035Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["116153\n", "116153\n"]}], "source": ["print(len(y_test_prediction))\n", "y_test_prediction_new = []\n", "for i in range(len(y_test_prediction)):\n", "    if y_test_prediction[i] %2 == 0:\n", "        y_test_prediction_new.append(0)\n", "    elif y_test_prediction[i] == 3:\n", "        y_test_prediction_new.append(2)\n", "    elif y_test_prediction[i] == 1:\n", "        y_test_prediction_new.append(1)\n", "    else:\n", "        y_test_prediction_new.append(-1)\n", "\n", "y_test_prediction_new = np.asarray(y_test_prediction_new)\n", "print(len(y_test_prediction_new))\n", "y_test_prediction = y_test_prediction_new"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:50:05.166915Z", "start_time": "2020-01-01T19:50:05.119816Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0, 1, 2, -1}\n"]}], "source": ["print(set(y_test_prediction))"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:50:13.184372Z", "start_time": "2020-01-01T19:50:12.991741Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0, 1, 2}\n", "49719 49719\n"]}], "source": ["y_test = [y_test[i] for i, label in enumerate(y_test_prediction) if label!=-1]\n", "y_test_prediction = [label for i, label in enumerate(y_test_prediction) if label!=-1]\n", "print(set(y_test_prediction))\n", "print(len(y_test), len(y_test_prediction))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:50:16.277248Z", "start_time": "2020-01-01T19:50:16.218135Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 84.57%\n"]}], "source": ["from sklearn.metrics import accuracy_score\n", "test_scores = accuracy_score(y_test, y_test_prediction)\n", "print(\"Accuracy: %.2f%%\" % (test_scores*100))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:13.913361Z", "start_time": "2018-11-17T15:27:13.845207Z"}}, "outputs": [], "source": ["# x_test_cleaned = np.asarray([np.asarray(x_test[i]) for i in range(len(x_test)) if y_test_prediction[i] != -1])\n", "# y_test_cleaned = np.asarray([y_test[i] for i in range(len(y_test)) if y_test_prediction[i] != -1])\n", "# y_test_prediction_cleaned = np.asarray([y_test_prediction[i] for i in range(len(y_test_prediction)) if y_test_prediction[i] != -1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:15.867226Z", "start_time": "2018-11-17T15:27:15.861071Z"}}, "outputs": [], "source": ["# print(len(x_test_cleaned), len(y_test_cleaned), len(y_test_prediction_cleaned))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:17.061659Z", "start_time": "2018-11-17T15:27:17.052861Z"}}, "outputs": [], "source": ["# from sklearn.metrics import accuracy_score\n", "# test_scores = accuracy_score(y_test_cleaned, y_test_prediction_cleaned)\n", "# print(\"Accuracy: %.2f%%\" % (test_scores*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test if by learning (asn1, asn2) -> p2c then (asn2, asn1) -> c2p and vice versa"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2019-01-04T17:50:32.317308Z", "start_time": "2019-01-04T17:50:32.017509Z"}}, "outputs": [], "source": ["p2c = TOR_ORIG_LABELS_DICT['P2C']\n", "c2p = TOR_ORIG_LABELS_DICT['C2P']\n", "\n", "p2c_training = np.asarray([np.asarray(x_training[i]) for i in range(len(x_training)) if y_training[i] == p2c])\n", "p2c_training_oposite = np.asarray([np.asarray([pair[1], pair[0]]) for pair in p2c_training])\n", "p2c_training_labels = [p2c]*len(p2c_training)\n", "p2c_training_oposite_labels = [c2p]*len(p2c_training_oposite)\n", "print(p2c_training.shape, p2c_training_oposite.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2019-01-04T17:51:02.400638Z", "start_time": "2019-01-04T17:51:02.106675Z"}}, "outputs": [], "source": ["p2c_training_labels_prediction = generate_labels_for_set(caida_tor_dict, p2c_training)\n", "p2c_training_scores = accuracy_score(p2c_training_labels, p2c_training_labels_prediction)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_scores*100))\n", "\n", "p2c_training_oposite_labels_prediction = generate_labels_for_set(caida_tor_dict, p2c_training_oposite)\n", "p2c_training_oposite_scores = accuracy_score(p2c_training_oposite_labels, p2c_training_oposite_labels_prediction)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_oposite_scores*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot and save a confusion matrix for results over the test set"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define a function"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:50:21.277051Z", "start_time": "2020-01-01T19:50:20.949560Z"}}, "outputs": [], "source": ["%matplotlib inline\n", "\n", "import matplotlib\n", "import pylab as pl\n", "from sklearn.metrics import confusion_matrix\n", "import itertools\n", "import matplotlib.pyplot as plt\n", "import matplotlib.cm as cm\n", "\n", "def plot_confusion_matrix(cm, classes,\n", "                          normalize=False,\n", "                          title='Confusion matrix',\n", "                          fname='Confusion matrix',\n", "                          cmap=plt.cm.Blues):\n", "    \"\"\"\n", "    This function prints and plots the confusion matrix.\n", "    Normalization can be applied by setting `normalize=True`.\n", "    \"\"\"\n", "    if normalize:\n", "        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "        print(\"Normalized confusion matrix\")\n", "    else:\n", "        print('Confusion matrix, without normalization')\n", "\n", "#     print(cm)\n", "\n", "    plt.imshow(cm, interpolation='nearest', cmap=cmap)\n", "#     plt.title(title)\n", "    plt.colorbar()\n", "    tick_marks = np.arange(len(classes))\n", "    plt.xticks(tick_marks, classes, rotation=45)\n", "    plt.yticks(tick_marks, classes)\n", "\n", "    fmt = '.1f' if normalize else 'd'\n", "    thresh = cm.max() / 2.\n", "    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):\n", "        if normalize:\n", "            plt.text(j, i, format(cm[i, j]*100, fmt) + '%',\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")\n", "        else:\n", "            plt.text(j, i, format(cm[i, j], fmt),\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")    \n", "        \n", "    plt.tight_layout()\n", "    plt.ylabel('True Label')\n", "    plt.xlabel('Predicted Label')\n", "    plt.savefig(fname, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:50:24.595743Z", "start_time": "2020-01-01T19:50:23.107433Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Confusion matrix, without normalization\n", "Normalized confusion matrix\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Compute confusion matrix\n", "cnf_matrix = confusion_matrix(y_test, y_test_prediction)\n", "np.set_printoptions(precision=2)\n", "\n", "# Plot non-normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names,\n", "                      title='Confusion matrix, without normalization',\n", "                      fname=RESULTS_PATH + ToR_MODEL_NAME + \"_\" + 'Confusion_matrix_without_normalization')\n", "\n", "# Plot normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names, normalize=True,\n", "                      title='Normalized Confusion Msatrix',\n", "                      fname=RESULTS_PATH +ToR_MODEL_NAME + \"_\" + 'Normalized_confusion_matrix')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["# Export the model to a file"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-16T12:50:04.937659Z", "start_time": "2018-11-16T12:50:04.873865Z"}, "hidden": true}, "outputs": [], "source": ["model_json = pairs_model.to_json()\n", "with open(MODELS_PATH + ToR_MODEL_NAME + '.json', \"w\") as json_file:\n", "    json_file.write(model_json)\n", "pairs_model.save_weights(MODELS_PATH + ToR_MODEL_NAME + '.h5')\n", "print(\"Save Model\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Export results to a csv file (with original ASNs)\n", "## Define functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:53:31.858073Z", "start_time": "2018-10-20T15:53:31.831311Z"}}, "outputs": [], "source": ["def index2ASN(dataset_indexed, ASN_index_map):\n", "    dataset = []\n", "    index_ASN_map = {index: ASN for ASN, index in ASN_index_map.items()}\n", "    for row_indexed in dataset_indexed:\n", "        row = []\n", "        for index in row_indexed:\n", "            if index != 0:\n", "                row += [index_ASN_map[index]]\n", "        dataset.append(row)\n", "    \n", "    return dataset\n", "\n", "def index2ASN_labeled(dataset_indexed, labels_indexed, ASN_index_map):\n", "    dataset = []\n", "    index_ASN_map = {index: ASN for ASN, index in ASN_index_map.items()}\n", "    labels_colors_map = {0:'GREEN', 1:'RED'}\n", "    \n", "    for i, row_indexed in enumerate(dataset_indexed):\n", "        row = []\n", "        for index in row_indexed:\n", "            if index != 0:\n", "                row += [index_ASN_map[index]]\n", "        row += [labels_colors_map[labels_indexed[i]]]\n", "        dataset.append(row)\n", "    \n", "    return dataset\n", "\n", "import csv\n", "def export_csv(dataset, csv_name):\n", "    with open(csv_name + '.csv', 'wb') as csv_file:\n", "        csv_writer = csv.writer(csv_file)\n", "        for row in dataset:\n", "            csv_writer.writerow(row)"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["## Load a relevant dataset {all, misclassified, decided, undecided} and get model predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:35:46.412152Z", "start_time": "2018-10-20T15:30:04.317489Z"}, "hidden": true}, "outputs": [], "source": ["### misclassified from the entire dataset ###\n", "\n", "dataset = np.load(DATA_PATH + \"bgp_routes_indexed_dataset.npy\")\n", "labels = np.load(DATA_PATH + \"bgp_routes_labels.npy\")\n", "\n", "# remove UNDECIDED\n", "dataset = np.asarray([np.asarray(dataset[i]) for i in range(len(dataset)) if labels[i] != 2])\n", "labels = np.asarray([labels[i] for i in range(len(labels)) if labels[i] != 2])\n", "\n", "# pad sequences\n", "dataset = sequence.pad_sequences(dataset, maxlen=max_len)\n", "# Get Model Predictions\n", "predictions = model.predict_classes(dataset, verbose=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:54:32.435559Z", "start_time": "2018-10-20T15:54:28.652217Z"}, "hidden": true}, "outputs": [], "source": ["# Create misclassified dataset\n", "x_misclassified = np.asarray([route for i,route in enumerate(dataset) if labels[i] != predictions[i]])\n", "y_misclassified_prediction = np.asarray([label for i,label in enumerate(predictions) if labels[i] != predictions[i]])\n", "print len(x_misclassified), len(y_misclassified_prediction)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:54:39.339072Z", "start_time": "2018-10-20T15:54:39.169037Z"}}, "outputs": [], "source": ["dataset_misclassified = index2ASN_labeled(x_misclassified, y_misclassified_prediction, ASN_index_map)\n", "export_csv(dataset_misclassified, RESULTS_PATH + MODEL_NAME + \"_misclassified\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 2}