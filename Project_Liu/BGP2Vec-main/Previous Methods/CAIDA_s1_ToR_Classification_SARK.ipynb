{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Import python packages"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2019-12-30T06:46:11.401994Z", "start_time": "2019-12-30T06:46:10.771571Z"}}, "outputs": [], "source": ["from run_sark import SARK\n", "\n", "import pickle\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from collections import Counter\n", "import random\n", "\n", "np.random.seed(7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Define parameters and load bgp_routes and ToR datasets"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2019-12-30T06:46:22.532525Z", "start_time": "2019-12-30T06:46:15.456574Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3669655,) (3669655,)\n", "(580762, 2) (580762,)\n"]}], "source": ["ToR_MODEL_NAME = \"CAIDA_s1_ToR_Classification_SARK\"\n", "\n", "TEST_SIZE = 0.2\n", "TOR_LABELS_DICT = {'P2P':0, 'C2P': 1,'P2C': 2}\n", "class_names = ['P2P', 'C2P', 'P2C']\n", "DATA_PATH = '../../Data/'\n", "MODELS_PATH = '../../Models/'\n", "RESULTS_PATH = '../../Results/'\n", "\n", "\n", "bgp_routes = np.load(DATA_PATH + \"bgp_routes_dataset.npy\")\n", "bgp_routes_labels = np.load(DATA_PATH + \"bgp_routes_labels.npy\")\n", "print(bgp_routes.shape, bgp_routes_labels.shape)\n", "\n", "DATA = \"caida_s1_tor\"\n", "tor_dataset = np.load(DATA_PATH + DATA + \"_dataset.npy\")\n", "tor_labels = np.load(DATA_PATH + DATA + \"_labels.npy\")\n", "\n", "print(tor_dataset.shape, tor_labels.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate training and test sets\n", "## Shauffle dataset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2019-12-30T06:46:22.621829Z", "start_time": "2019-12-30T06:46:22.545589Z"}}, "outputs": [], "source": ["from sklearn.utils import shuffle\n", "dataset, labels = shuffle(tor_dataset, tor_labels, random_state=7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate a balanced dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T13:08:27.960627Z", "start_time": "2018-11-17T13:08:27.890678Z"}}, "outputs": [], "source": ["# def generate_balanced_dataset(dataset, labels, labels_set):\n", "#     sets_dict = dict()\n", "#     for label in labels_set:\n", "#         sets_dict[label] = np.asarray([np.asarray(dataset[i]) for i in range(len(dataset)) if labels[i] == label])\n", "    \n", "#     min_set_len = min([len(label_set) for label_set in sets_dict.values()])\n", "    \n", "#     for label, label_set in sets_dict.iteritems():\n", "#         sets_dict[label] = label_set[np.random.choice(label_set.shape[0], min_set_len, replace=False)]\n", "    \n", "#     dataset = np.concatenate((sets_dict.values()))\n", "#     labels = []\n", "#     for label, label_set in sets_dict.iteritems():\n", "#         labels += [label]*len(label_set)\n", "#         print label, len(label_set)\n", "#     labels = np.asarray(labels)\n", "#     return shuffle(dataset, labels, random_state=7)\n", "\n", "# dataset, labels = generate_balanced_dataset(dataset, labels, (0,1,3))\n", "# print dataset.shape, labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train Test Split"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2019-12-30T06:46:22.710349Z", "start_time": "2019-12-30T06:46:22.632948Z"}}, "outputs": [], "source": ["x_training, x_test, y_training, y_test = train_test_split(dataset, labels, test_size=TEST_SIZE)\n", "\n", "del dataset, labels"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2019-12-30T06:46:22.727083Z", "start_time": "2019-12-30T06:46:22.720904Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(464609, 2) (464609,)\n", "(116153, 2) (116153,)\n"]}], "source": ["print(x_training.shape, y_training.shape)\n", "print(x_test.shape, y_test.shape)\n", "\n", "# print 1.0*len(x_training)/(len(x_test)+len(x_training))\n", "\n", "# from collections import Counter\n", "# training_c = Counter(y_training)\n", "# test_c = Counter(y_test)\n", "# print(training_c, test_c)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:31:00.949800Z", "start_time": "2019-12-28T17:31:00.946345Z"}}, "outputs": [], "source": ["# for k,v in training_c.iteritems():\n", "#     print k, 100.0*v/len(x_training)\n", "# print\n", "# for k,v in test_c.iteritems():\n", "#     print k, 100.0*v/len(x_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Run SARK Algorithm"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"start_time": "2019-12-29T07:00:06.881Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["46\n", "Working on vantage number 0\n", "Start Iteration on 53344 Leaves\n", "Number of remaining edges: 17498\n", "Number of leaves: 5470\n", "\n", "Start Iteration on 5470 Leaves\n", "Number of remaining edges: 7825\n", "Number of leaves: 1393\n", "\n", "Start Iteration on 1393 Leaves\n", "Number of remaining edges: 4848\n", "Number of leaves: 439\n", "\n", "Start Iteration on 439 Leaves\n", "Number of remaining edges: 3843\n", "Number of leaves: 150\n", "\n", "Start Iteration on 150 Leaves\n", "Number of remaining edges: 3491\n", "Number of leaves: 35\n", "\n", "Start Iteration on 35 Leaves\n", "Number of remaining edges: 3397\n", "Number of leaves: 14\n", "\n", "Start Iteration on 14 Leaves\n", "Number of remaining edges: 3362\n", "Number of leaves: 5\n", "\n", "Start Iteration on 5 Leaves\n", "Number of remaining edges: 3356\n", "Number of leaves: 1\n", "\n", "Start Iteration on 1 Leaves\n", "Number of remaining edges: 3345\n", "Number of leaves: 1\n", "\n", "Start Iteration on 1 Leaves\n", "Number of remaining edges: 3341\n", "Number of leaves: 0\n", "\n", "Finished __get_ranking_for_vantage_garaph number 0\n", "Working on vantage number 1\n", "Start Iteration on 53579 Leaves\n", "Number of remaining edges: 17040\n", "Number of leaves: 5478\n", "\n", "Start Iteration on 5478 Leaves\n", "Number of remaining edges: 7299\n", "Number of leaves: 1325\n", "\n", "Start Iteration on 1325 Leaves\n", "Number of remaining edges: 4448\n", "Number of leaves: 401\n", "\n", "Start Iteration on 401 Leaves\n", "Number of remaining edges: 3500\n", "Number of leaves: 142\n", "\n", "Start Iteration on 142 Leaves\n", "Number of remaining edges: 3118\n", "Number of leaves: 38\n", "\n", "Start Iteration on 38 Leaves\n", "Number of remaining edges: 3025\n", "Number of leaves: 14\n", "\n", "Start Iteration on 14 Leaves\n", "Number of remaining edges: 2981\n", "Number of leaves: 6\n", "\n", "Start Iteration on 6 Leaves\n", "Number of remaining edges: 2972\n", "Number of leaves: 2\n", "\n", "Start Iteration on 2 Leaves\n", "Number of remaining edges: 2958\n", "Number of leaves: 2\n", "\n", "Start Iteration on 2 Leaves\n", "Number of remaining edges: 2954\n", "Number of leaves: 0\n", "\n", "Finished __get_ranking_for_vantage_garaph number 1\n", "Working on vantage number 2\n", "Start Iteration on 53454 Leaves\n", "Number of remaining edges: 17686\n", "Number of leaves: 5512\n", "\n", "Start Iteration on 5512 Leaves\n", "Number of remaining edges: 7695\n", "Number of leaves: 1353\n", "\n", "Start Iteration on 1353 Leaves\n", "Number of remaining edges: 4842\n", "Number of leaves: 408\n", "\n", "Start Iteration on 408 Leaves\n", "Number of remaining edges: 3914\n", "Number of leaves: 134\n", "\n", "Start Iteration on 134 Leaves\n", "Number of remaining edges: 3581\n", "Number of leaves: 37\n", "\n", "Start Iteration on 37 Leaves\n", "Number of remaining edges: 3490\n", "Number of leaves: 13\n", "\n", "Start Iteration on 13 Leaves\n", "Number of remaining edges: 3450\n", "Number of leaves: 8\n", "\n", "Start Iteration on 8 Leaves\n", "Number of remaining edges: 3438\n", "Number of leaves: 3\n", "\n", "Start Iteration on 3 Leaves\n", "Number of remaining edges: 3426\n", "Number of leaves: 1\n", "\n", "Start Iteration on 1 Leaves\n", "Number of remaining edges: 3422\n", "Number of leaves: 1\n", "\n", "Start Iteration on 1 Leaves\n", "Number of remaining edges: 3421\n", "Number of leaves: 0\n", "\n", "Finished __get_ranking_for_vantage_garaph number 2\n", "Working on vantage number 3\n", "Start Iteration on 53330 Leaves\n", "Number of remaining edges: 17669\n", "Number of leaves: 5449\n", "\n", "Start Iteration on 5449 Leaves\n", "Number of remaining edges: 8089\n", "Number of leaves: 1317\n", "\n", "Start Iteration on 1317 Leaves\n", "Number of remaining edges: 5335\n", "Number of leaves: 437\n", "\n", "Start Iteration on 437 Leaves\n", "Number of remaining edges: 4348\n", "Number of leaves: 154\n", "\n", "Start Iteration on 154 Leaves\n", "Number of remaining edges: 3970\n", "Number of leaves: 52\n", "\n", "Start Iteration on 52 Leaves\n", "Number of remaining edges: 3849\n", "Number of leaves: 22\n", "\n", "Start Iteration on 22 Leaves\n", "Number of remaining edges: 3775\n", "Number of leaves: 9\n", "\n", "Start Iteration on 9 Leaves\n", "Number of remaining edges: 3761\n", "Number of leaves: 3\n", "\n", "Start Iteration on 3 Leaves\n", "Number of remaining edges: 3744\n", "Number of leaves: 1\n", "\n", "Start Iteration on 1 Leaves\n", "Number of remaining edges: 3740\n", "Number of leaves: 0\n", "\n", "Finished __get_ranking_for_vantage_garaph number 3\n", "Working on vantage number 4\n", "Start Iteration on 53308 Leaves\n", "Number of remaining edges: 18032\n", "Number of leaves: 5503\n", "\n", "Start Iteration on 5503 Leaves\n", "Number of remaining edges: 8215\n", "Number of leaves: 1332\n", "\n", "Start Iteration on 1332 Leaves\n", "Number of remaining edges: 5422\n", "Number of leaves: 406\n", "\n", "Start Iteration on 406 Leaves\n", "Number of remaining edges: 4534\n", "Number of leaves: 126\n", "\n", "Start Iteration on 126 Leaves\n", "Number of remaining edges: 4238\n", "Number of leaves: 36\n", "\n", "Start Iteration on 36 Leaves\n", "Number of remaining edges: 4140\n", "Number of leaves: 21\n", "\n", "Start Iteration on 21 Leaves\n", "Number of remaining edges: 4066\n", "Number of leaves: 13\n", "\n", "Start Iteration on 13 Leaves\n", "Number of remaining edges: 4044\n", "Number of leaves: 4\n", "\n", "Start Iteration on 4 Leaves\n", "Number of remaining edges: 4025\n", "Number of leaves: 2\n", "\n", "Start Iteration on 2 Leaves\n", "Number of remaining edges: 4016\n", "Number of leaves: 0\n", "\n", "Finished __get_ranking_for_vantage_garaph number 4\n", "Working on vantage number 5\n", "Start Iteration on 53556 Leaves\n", "Number of remaining edges: 16910\n", "Number of leaves: 5497\n", "\n", "Start Iteration on 5497 Leaves\n", "Number of remaining edges: 7128\n", "Number of leaves: 1334\n", "\n", "Start Iteration on 1334 Leaves\n", "Number of remaining edges: 4307\n", "Number of leaves: 422\n", "\n", "Start Iteration on 422 Leaves\n", "Number of remaining edges: 3314\n", "Number of leaves: 146\n", "\n", "Start Iteration on 146 Leaves\n", "Number of remaining edges: 2933\n", "Number of leaves: 43\n", "\n", "Start Iteration on 43 Leaves\n", "Number of remaining edges: 2812\n", "Number of leaves: 22\n", "\n", "Start Iteration on 22 Leaves\n", "Number of remaining edges: 2749\n", "Number of leaves: 9\n", "\n", "Start Iteration on 9 Leaves\n", "Number of remaining edges: 2736\n", "Number of leaves: 4\n", "\n", "Start Iteration on 4 Leaves\n", "Number of remaining edges: 2719\n", "Number of leaves: 2\n", "\n", "Start Iteration on 2 Leaves\n", "Number of remaining edges: 2715\n", "Number of leaves: 0\n", "\n", "Finished __get_ranking_for_vantage_garaph number 5\n", "Working on vantage number 6\n", "Start Iteration on 53420 Leaves\n", "Number of remaining edges: 17492\n", "Number of leaves: 5483\n", "\n", "Start Iteration on 5483 Leaves\n", "Number of remaining edges: 7796\n", "Number of leaves: 1354\n", "\n", "Start Iteration on 1354 Leaves\n", "Number of remaining edges: 5003\n", "Number of leaves: 419\n", "\n", "Start Iteration on 419 Leaves\n", "Number of remaining edges: 4041\n", "Number of leaves: 152\n", "\n", "Start Iteration on 152 Leaves\n", "Number of remaining edges: 3673\n", "Number of leaves: 47\n", "\n", "Start Iteration on 47 Leaves\n", "Number of remaining edges: 3561\n", "Number of leaves: 18\n", "\n", "Start Iteration on 18 Leaves\n", "Number of remaining edges: 3509\n", "Number of leaves: 7\n", "\n", "Start Iteration on 7 Leaves\n", "Number of remaining edges: 3499\n", "Number of leaves: 3\n", "\n", "Start Iteration on 3 Leaves\n", "Number of remaining edges: 3483\n", "Number of leaves: 1\n", "\n", "Start Iteration on 1 Leaves\n", "Number of remaining edges: 3479\n", "Number of leaves: 0\n", "\n", "Finished __get_ranking_for_vantage_garaph number 6\n", "Working on vantage number 7\n", "Start Iteration on 53359 Leaves\n", "Number of remaining edges: 17696\n", "Number of leaves: 5493\n", "\n", "Start Iteration on 5493 Leaves\n", "Number of remaining edges: 8007\n", "Number of leaves: 1337\n", "\n", "Start Iteration on 1337 Leaves\n", "Number of remaining edges: 5144\n", "Number of leaves: 411\n", "\n", "Start Iteration on 411 Leaves\n", "Number of remaining edges: 4198\n", "Number of leaves: 148\n", "\n", "Start Iteration on 148 Leaves\n", "Number of remaining edges: 3832\n", "Number of leaves: 43\n", "\n", "Start Iteration on 43 Leaves\n", "Number of remaining edges: 3723\n", "Number of leaves: 19\n", "\n", "Start Iteration on 19 Leaves\n", "Number of remaining edges: 3665\n", "Number of leaves: 7\n", "\n", "Start Iteration on 7 Leaves\n", "Number of remaining edges: 3655\n", "Number of leaves: 3\n", "\n", "Start Iteration on 3 Leaves\n", "Number of remaining edges: 3641\n", "Number of leaves: 1\n", "\n", "Start Iteration on 1 Leaves\n", "Number of remaining edges: 3637\n", "Number of leaves: 0\n", "\n", "Finished __get_ranking_for_vantage_garaph number 7\n", "Working on vantage number 8\n", "Start Iteration on 53449 Leaves\n", "Number of remaining edges: 18723\n", "Number of leaves: 5484\n", "\n", "Start Iteration on 5484 Leaves\n", "Number of remaining edges: 8644\n", "Number of leaves: 1317\n", "\n", "Start Iteration on 1317 Leaves\n", "Number of remaining edges: 5755\n", "Number of leaves: 396\n", "\n", "Start Iteration on 396 Leaves\n", "Number of remaining edges: 4759\n", "Number of leaves: 128\n", "\n", "Start Iteration on 128 Leaves\n", "Number of remaining edges: 4419\n", "Number of leaves: 36\n", "\n", "Start Iteration on 36 Leaves\n", "Number of remaining edges: 4331\n", "Number of leaves: 10\n", "\n", "Start Iteration on 10 Leaves\n", "Number of remaining edges: 4299\n", "Number of leaves: 6\n", "\n", "Start Iteration on 6 Leaves\n", "Number of remaining edges: 4286\n", "Number of leaves: 0\n", "\n", "Finished __get_ranking_for_vantage_garaph number 8\n", "Working on vantage number 9\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Start Iteration on 53315 Leaves\n", "Number of remaining edges: 17990\n", "Number of leaves: 5453\n", "\n", "Start Iteration on 5453 Leaves\n", "Number of remaining edges: 8315\n", "Number of leaves: 1358\n", "\n", "Start Iteration on 1358 Leaves\n", "Number of remaining edges: 5464\n", "Number of leaves: 434\n", "\n", "Start Iteration on 434 Leaves\n", "Number of remaining edges: 4480\n", "Number of leaves: 134\n", "\n", "Start Iteration on 134 Leaves\n", "Number of remaining edges: 4133\n", "Number of leaves: 31\n", "\n", "Start Iteration on 31 Leaves\n", "Number of remaining edges: 4050\n", "Number of leaves: 17\n", "\n", "Start Iteration on 17 Leaves\n", "Number of remaining edges: 4000\n", "Number of leaves: 7\n", "\n", "Start Iteration on 7 Leaves\n", "Number of remaining edges: 3989\n", "Number of leaves: 3\n", "\n", "Start Iteration on 3 Leaves\n", "Number of remaining edges: 3975\n", "Number of leaves: 0\n", "\n", "Finished __get_ranking_for_vantage_garaph number 9\n", "Working on vantage number 10\n", "Start Iteration on 53448 Leaves\n", "Number of remaining edges: 17850\n", "Number of leaves: 5482\n", "\n", "Start Iteration on 5482 Leaves\n", "Number of remaining edges: 8010\n", "Number of leaves: 1372\n", "\n", "Start Iteration on 1372 Leaves\n", "Number of remaining edges: 5059\n", "Number of leaves: 420\n", "\n", "Start Iteration on 420 Leaves\n", "Number of remaining edges: 4035\n", "Number of leaves: 151\n", "\n", "Start Iteration on 151 Leaves\n", "Number of remaining edges: 3628\n", "Number of leaves: 47\n", "\n", "Start Iteration on 47 Leaves\n", "Number of remaining edges: 3518\n", "Number of leaves: 17\n", "\n", "Start Iteration on 17 Leaves\n", "Number of remaining edges: 3461\n", "Number of leaves: 7\n", "\n", "Start Iteration on 7 Leaves\n", "Number of remaining edges: 3445\n", "Number of leaves: 5\n", "\n", "Start Iteration on 5 Leaves\n", "Number of remaining edges: 3438\n", "Number of leaves: 1\n", "\n", "Start Iteration on 1 Leaves\n", "Number of remaining edges: 3435\n", "Number of leaves: 0\n", "\n", "Finished __get_ranking_for_vantage_garaph number 10\n", "Working on vantage number 11\n", "Start Iteration on 26754 Leaves\n", "Number of remaining edges: 10368\n", "Number of leaves: 3767\n", "\n", "Start Iteration on 3767 Leaves\n", "Number of remaining edges: 4364\n", "Number of leaves: 896\n", "\n", "Start Iteration on 896 Leaves\n", "Number of remaining edges: 2589\n", "Number of leaves: 270\n", "\n", "Start Iteration on 270 Leaves\n", "Number of remaining edges: 1992\n", "Number of leaves: 89\n", "\n", "Start Iteration on 89 Leaves\n", "Number of remaining edges: 1767\n", "Number of leaves: 40\n", "\n", "Start Iteration on 40 Leaves\n", "Number of remaining edges: 1669\n", "Number of leaves: 19\n", "\n", "Start Iteration on 19 Leaves\n", "Number of remaining edges: 1625\n", "Number of leaves: 8\n", "\n", "Start Iteration on 8 Leaves\n", "Number of remaining edges: 1610\n", "Number of leaves: 4\n", "\n", "Start Iteration on 4 Leaves\n", "Number of remaining edges: 1604\n", "Number of leaves: 1\n", "\n", "Start Iteration on 1 Leaves\n", "Number of remaining edges: 1601\n", "Number of leaves: 0\n", "\n", "Finished __get_ranking_for_vantage_garaph number 11\n", "Working on vantage number 12\n", "Start Iteration on 53411 Leaves\n", "Number of remaining edges: 17728\n", "Number of leaves: 5482\n", "\n", "Start Iteration on 5482 Leaves\n", "Number of remaining edges: 7959\n", "Number of leaves: 1340\n", "\n", "Start Iteration on 1340 Leaves\n", "Number of remaining edges: 5065\n", "Number of leaves: 423\n", "\n", "Start Iteration on 423 Leaves\n", "Number of remaining edges: 4064\n", "Number of leaves: 149\n", "\n", "Start Iteration on 149 Leaves\n", "Number of remaining edges: 3688\n", "Number of leaves: 41\n", "\n", "Start Iteration on 41 Leaves\n", "Number of remaining edges: 3585\n", "Number of leaves: 19\n", "\n", "Start Iteration on 19 Leaves\n", "Number of remaining edges: 3528\n", "Number of leaves: 9\n", "\n", "Start Iteration on 9 Leaves\n", "Number of remaining edges: 3512\n", "Number of leaves: 4\n", "\n", "Start Iteration on 4 Leaves\n", "Number of remaining edges: 3496\n", "Number of leaves: 3\n", "\n", "Start Iteration on 3 Leaves\n", "Number of remaining edges: 3488\n", "Number of leaves: 0\n", "\n", "Finished __get_ranking_for_vantage_garaph number 12\n", "Working on vantage number 13\n", "Start Iteration on 53344 Leaves\n"]}], "source": ["sark = SARK(bgp_routes)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2019-12-29T12:27:35.965876Z", "start_time": "2019-12-29T12:27:35.950021Z"}}, "outputs": [{"ename": "NameError", "evalue": "name 'sark' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-7-881511527008>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;32mwith\u001b[0m \u001b[0mopen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mMODELS_PATH\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mToR_MODEL_NAME\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0;34m'_tor_dict.pickle'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'wb'\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mhandle\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m             \u001b[0mpickle\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdump\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msark\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtor_dict\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mhandle\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mprotocol\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mpickle\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mHIGHEST_PROTOCOL\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'sark' is not defined"]}], "source": ["with open(MODELS_PATH + ToR_MODEL_NAME + '_tor_dict.pickle', 'wb') as handle:\n", "            pickle.dump(sark.tor_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load SARK Results"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2019-12-30T06:46:53.021218Z", "start_time": "2019-12-30T06:46:52.913945Z"}}, "outputs": [], "source": ["# OLD_ToR_MODEL_NAME = \"Orig_ToR_Classification_SARK\"\n", "with open(MODELS_PATH + ToR_MODEL_NAME + '_tor_dict.pickle', 'rb') as handle:\n", "    sark_tor_dict = pickle.load(handle)"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2018-10-20T11:24:40.580036Z", "start_time": "2018-10-20T11:24:33.351927Z"}}, "source": ["# Final evaluation of the model\n", "## Evaluate accuracy over the test set"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2019-12-30T06:47:01.330014Z", "start_time": "2019-12-30T06:47:01.322428Z"}}, "outputs": [], "source": ["def generate_labels_for_set(tor_dict, pairs):\n", "    labels = []\n", "    for pair in pairs:\n", "        if (pair[0], pair[1]) in tor_dict:\n", "            labels.append(tor_dict[(pair[0], pair[1])])\n", "        elif (pair[1], pair[0]) in tor_dict:\n", "            if tor_dict[(pair[1], pair[0])] == 0 or tor_dict[(pair[1], pair[0])] == 2:\n", "                labels.append(tor_dict[(pair[1], pair[0])])\n", "            else:\n", "                labels.append((tor_dict[(pair[1], pair[0])] + 2)%4)\n", "        else:\n", "            labels.append(-1)\n", "    return np.asarray(labels)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2019-12-30T06:47:09.505851Z", "start_time": "2019-12-30T06:47:08.878091Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["116153\n", "116153\n"]}], "source": ["y_test_prediction = generate_labels_for_set(sark_tor_dict, x_test)\n", "\n", "print(len(y_test_prediction))\n", "y_test_prediction_new = []\n", "for i in range(len(y_test_prediction)):\n", "    if y_test_prediction[i] %2 == 0:\n", "        y_test_prediction_new.append(0)\n", "    elif y_test_prediction[i] == 3:\n", "        y_test_prediction_new.append(2)\n", "    elif y_test_prediction[i] == 1:\n", "        y_test_prediction_new.append(1)\n", "    else:\n", "        y_test_prediction_new.append(-1)\n", "\n", "y_test_prediction_new = np.asarray(y_test_prediction_new)\n", "print(len(y_test_prediction_new))\n", "y_test_prediction = y_test_prediction_new"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T14:22:38.465648Z", "start_time": "2019-01-05T14:22:38.348441Z"}}, "outputs": [], "source": ["# y_test_prediction = sark.generate_labels_for_set(x_test)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2019-12-30T06:47:37.541836Z", "start_time": "2019-12-30T06:47:37.534487Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["49550\n"]}], "source": ["y_test_prediction_eval = y_test_prediction[np.where(y_test_prediction!=-1)]\n", "y_test_eval = y_test[np.where(y_test_prediction!=-1)]\n", "\n", "print(len(y_test_prediction_eval))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2019-12-30T06:47:42.585135Z", "start_time": "2019-12-30T06:47:42.576877Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 81.85%\n"]}], "source": ["from sklearn.metrics import accuracy_score\n", "# test_scores = accuracy_score(y_test, y_test_prediction)\n", "# print(\"Accuracy: %.2f%%\" % (test_scores*100))\n", "\n", "test_scores = accuracy_score(y_test_eval, y_test_prediction_eval)\n", "print(\"Accuracy: %.2f%%\" % (test_scores*100))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:13.913361Z", "start_time": "2018-11-17T15:27:13.845207Z"}}, "outputs": [], "source": ["# x_test_cleaned = np.asarray([np.asarray(x_test[i]) for i in range(len(x_test)) if y_test_prediction[i] != -1])\n", "# y_test_cleaned = np.asarray([y_test[i] for i in range(len(y_test)) if y_test_prediction[i] != -1])\n", "# y_test_prediction_cleaned = np.asarray([y_test_prediction[i] for i in range(len(y_test_prediction)) if y_test_prediction[i] != -1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:15.867226Z", "start_time": "2018-11-17T15:27:15.861071Z"}}, "outputs": [], "source": ["# print(len(x_test_cleaned), len(y_test_cleaned), len(y_test_prediction_cleaned))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:17.061659Z", "start_time": "2018-11-17T15:27:17.052861Z"}}, "outputs": [], "source": ["# from sklearn.metrics import accuracy_score\n", "# test_scores = accuracy_score(y_test_cleaned, y_test_prediction_cleaned)\n", "# print(\"Accuracy: %.2f%%\" % (test_scores*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test if by learning (asn1, asn2) -> p2c then (asn2, asn1) -> c2p and vice versa"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:34:36.931489Z", "start_time": "2018-11-17T15:34:36.651143Z"}}, "outputs": [], "source": ["p2c = TOR_ORIG_LABELS_DICT['P2C']\n", "c2p = TOR_ORIG_LABELS_DICT['C2P']\n", "\n", "p2c_training = np.asarray([np.asarray(x_training[i]) for i in range(len(x_training)) if y_training[i] == p2c])\n", "p2c_training_oposite = np.asarray([np.asarray([pair[1], pair[0]]) for pair in p2c_training])\n", "p2c_training_labels = [p2c]*len(p2c_training)\n", "p2c_training_oposite_labels = [c2p]*len(p2c_training_oposite)\n", "print(p2c_training.shape, p2c_training_oposite.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:34:38.408003Z", "start_time": "2018-11-17T15:34:38.009050Z"}}, "outputs": [], "source": ["p2c_training_labels_prediction = gao.generate_labels_for_set(p2c_training)\n", "p2c_training_scores = accuracy_score(p2c_training_labels, p2c_training_labels_prediction)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_scores*100))\n", "\n", "p2c_training_oposite_labels_prediction = gao.generate_labels_for_set(p2c_training_oposite)\n", "p2c_training_oposite_scores = accuracy_score(p2c_training_oposite_labels, p2c_training_oposite_labels_prediction)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_oposite_scores*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot and save a confusion matrix for results over the test set"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define a function"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2019-12-30T06:47:50.451417Z", "start_time": "2019-12-30T06:47:50.226495Z"}}, "outputs": [], "source": ["%matplotlib inline\n", "\n", "import matplotlib\n", "import pylab as pl\n", "from sklearn.metrics import confusion_matrix\n", "import itertools\n", "import matplotlib.pyplot as plt\n", "import matplotlib.cm as cm\n", "\n", "def plot_confusion_matrix(cm, classes,\n", "                          normalize=False,\n", "                          title='Confusion matrix',\n", "                          fname='Confusion matrix',\n", "                          cmap=plt.cm.Blues):\n", "    \"\"\"\n", "    This function prints and plots the confusion matrix.\n", "    Normalization can be applied by setting `normalize=True`.\n", "    \"\"\"\n", "    if normalize:\n", "        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "        print(\"Normalized confusion matrix\")\n", "    else:\n", "        print('Confusion matrix, without normalization')\n", "\n", "#     print(cm)\n", "\n", "    plt.imshow(cm, interpolation='nearest', cmap=cmap)\n", "#     plt.title(title)\n", "    plt.colorbar()\n", "    tick_marks = np.arange(len(classes))\n", "    plt.xticks(tick_marks, classes, rotation=45)\n", "    plt.yticks(tick_marks, classes)\n", "\n", "    fmt = '.1f' if normalize else 'd'\n", "    thresh = cm.max() / 2.\n", "    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):\n", "        if normalize:\n", "            plt.text(j, i, format(cm[i, j]*100, fmt) + '%',\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")\n", "        else:\n", "            plt.text(j, i, format(cm[i, j], fmt),\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")    \n", "        \n", "    plt.tight_layout()\n", "    plt.ylabel('True Label')\n", "    plt.xlabel('Predicted Label')\n", "    plt.savefig(fname, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2019-12-30T06:47:53.433341Z", "start_time": "2019-12-30T06:47:52.534383Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Confusion matrix, without normalization\n", "Normalized confusion matrix\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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*****************************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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Compute confusion matrix\n", "cnf_matrix = confusion_matrix(y_test_eval, y_test_prediction_eval)\n", "np.set_printoptions(precision=2)\n", "\n", "# Plot non-normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names,\n", "                      title='Confusion matrix, without normalization',\n", "                      fname=RESULTS_PATH + ToR_MODEL_NAME + \"_\" + 'Confusion_matrix_without_normalization')\n", "\n", "# Plot normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names, normalize=True,\n", "                      title='Normalized Confusion Msatrix',\n", "                      fname=RESULTS_PATH +ToR_MODEL_NAME + \"_\" + 'Normalized_confusion_matrix')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["# Export the model to a file"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-16T12:50:04.937659Z", "start_time": "2018-11-16T12:50:04.873865Z"}, "hidden": true}, "outputs": [], "source": ["model_json = pairs_model.to_json()\n", "with open(MODELS_PATH + ToR_MODEL_NAME + '.json', \"w\") as json_file:\n", "    json_file.write(model_json)\n", "pairs_model.save_weights(MODELS_PATH + ToR_MODEL_NAME + '.h5')\n", "print(\"Save Model\")"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["# Export results to a csv file (with original ASNs)\n", "## Define functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:53:31.858073Z", "start_time": "2018-10-20T15:53:31.831311Z"}, "hidden": true}, "outputs": [], "source": ["def index2ASN(dataset_indexed, ASN_index_map):\n", "    dataset = []\n", "    index_ASN_map = {index: ASN for ASN, index in ASN_index_map.iteritems()}\n", "    for row_indexed in dataset_indexed:\n", "        row = []\n", "        for index in row_indexed:\n", "            if index != 0:\n", "                row += [index_ASN_map[index]]\n", "        dataset.append(row)\n", "    \n", "    return dataset\n", "\n", "def index2ASN_labeled(dataset_indexed, labels_indexed, ASN_index_map):\n", "    dataset = []\n", "    index_ASN_map = {index: ASN for ASN, index in ASN_index_map.iteritems()}\n", "    labels_colors_map = {0:'GREEN', 1:'RED'}\n", "    \n", "    for i, row_indexed in enumerate(dataset_indexed):\n", "        row = []\n", "        for index in row_indexed:\n", "            if index != 0:\n", "                row += [index_ASN_map[index]]\n", "        row += [labels_colors_map[labels_indexed[i]]]\n", "        dataset.append(row)\n", "    \n", "    return dataset\n", "\n", "import csv\n", "def export_csv(dataset, csv_name):\n", "    with open(csv_name + '.csv', 'wb') as csv_file:\n", "        csv_writer = csv.writer(csv_file)\n", "        for row in dataset:\n", "            csv_writer.writerow(row)"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true, "hidden": true}, "source": ["## Load a relevant dataset {all, misclassified, decided, undecided} and get model predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:35:46.412152Z", "start_time": "2018-10-20T15:30:04.317489Z"}, "hidden": true}, "outputs": [], "source": ["### misclassified from the entire dataset ###\n", "\n", "dataset = np.load(DATA_PATH + \"bgp_routes_indexed_dataset.npy\")\n", "labels = np.load(DATA_PATH + \"bgp_routes_labels.npy\")\n", "\n", "# remove UNDECIDED\n", "dataset = np.asarray([np.asarray(dataset[i]) for i in range(len(dataset)) if labels[i] != 2])\n", "labels = np.asarray([labels[i] for i in range(len(labels)) if labels[i] != 2])\n", "\n", "# pad sequences\n", "dataset = sequence.pad_sequences(dataset, maxlen=max_len)\n", "# Get Model Predictions\n", "predictions = model.predict_classes(dataset, verbose=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:54:32.435559Z", "start_time": "2018-10-20T15:54:28.652217Z"}, "hidden": true}, "outputs": [], "source": ["# Create misclassified dataset\n", "x_misclassified = np.asarray([route for i,route in enumerate(dataset) if labels[i] != predictions[i]])\n", "y_misclassified_prediction = np.asarray([label for i,label in enumerate(predictions) if labels[i] != predictions[i]])\n", "print len(x_misclassified), len(y_misclassified_prediction)"]}, {"cell_type": "markdown", "metadata": {"hidden": true}, "source": ["## Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:54:39.339072Z", "start_time": "2018-10-20T15:54:39.169037Z"}, "hidden": true}, "outputs": [], "source": ["dataset_misclassified = index2ASN_labeled(x_misclassified, y_misclassified_prediction, ASN_index_map)\n", "export_csv(dataset_misclassified, RESULTS_PATH + MODEL_NAME + \"_misclassified\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 2}