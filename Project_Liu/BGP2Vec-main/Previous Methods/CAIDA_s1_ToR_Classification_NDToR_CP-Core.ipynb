{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Import python packages"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:51:28.539319Z", "start_time": "2020-01-01T19:51:27.595924Z"}}, "outputs": [], "source": ["from collections import defaultdict\n", "import pickle\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from collections import Counter\n", "import random\n", "\n", "np.random.seed(7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Define parameters and load bgp_routes and ToR datasets"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:51:41.376327Z", "start_time": "2020-01-01T19:51:31.482306Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3669655,) (3669655,)\n", "(580762, 2) (580762,)\n"]}], "source": ["ToR_MODEL_NAME = \"CAIDA_s1_ToR_Classification_NDToR_CP-Core\"\n", "\n", "TEST_SIZE = 0.2\n", "TOR_LABELS_DICT = {'P2P':0, 'C2P': 1,'P2C': 2}\n", "class_names = ['P2P', 'C2P', 'P2C']\n", "DATA_PATH = '../../Data/'\n", "MODELS_PATH = '../../Models/'\n", "RESULTS_PATH = '../../Results/'\n", "\n", "\n", "bgp_routes = np.load(DATA_PATH + \"bgp_routes_dataset.npy\")\n", "bgp_routes_labels = np.load(DATA_PATH + \"bgp_routes_labels.npy\")\n", "print(bgp_routes.shape, bgp_routes_labels.shape)\n", "\n", "DATA = \"caida_s1_tor\"\n", "tor_dataset = np.load(DATA_PATH + DATA + \"_dataset.npy\")\n", "tor_labels = np.load(DATA_PATH + DATA + \"_labels.npy\")\n", "\n", "print(tor_dataset.shape, tor_labels.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate training and test sets\n", "## Shauffle dataset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:51:41.517892Z", "start_time": "2020-01-01T19:51:41.388865Z"}}, "outputs": [], "source": ["from sklearn.utils import shuffle\n", "dataset, labels = shuffle(tor_dataset, tor_labels, random_state=7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate a balanced dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T13:08:27.960627Z", "start_time": "2018-11-17T13:08:27.890678Z"}}, "outputs": [], "source": ["# def generate_balanced_dataset(dataset, labels, labels_set):\n", "#     sets_dict = dict()\n", "#     for label in labels_set:\n", "#         sets_dict[label] = np.asarray([np.asarray(dataset[i]) for i in range(len(dataset)) if labels[i] == label])\n", "    \n", "#     min_set_len = min([len(label_set) for label_set in sets_dict.values()])\n", "    \n", "#     for label, label_set in sets_dict.items():\n", "#         sets_dict[label] = label_set[np.random.choice(label_set.shape[0], min_set_len, replace=False)]\n", "    \n", "#     dataset = np.concatenate((sets_dict.values()))\n", "#     labels = []\n", "#     for label, label_set in sets_dict.items():\n", "#         labels += [label]*len(label_set)\n", "#         print label, len(label_set)\n", "#     labels = np.asarray(labels)\n", "#     return shuffle(dataset, labels, random_state=7)\n", "\n", "# dataset, labels = generate_balanced_dataset(dataset, labels, (0,1,3))\n", "# print dataset.shape, labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train Test Split"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:51:41.654641Z", "start_time": "2020-01-01T19:51:41.531782Z"}}, "outputs": [], "source": ["x_training, x_test, y_training, y_test = train_test_split(dataset, labels, test_size=TEST_SIZE)\n", "\n", "del dataset, labels"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:51:41.963731Z", "start_time": "2020-01-01T19:51:41.673138Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(464609, 2) (464609,)\n", "(116153, 2) (116153,)\n", "0.7999989668745545\n", "Counter({0: 275382, 1: 94716, 2: 94511}) Counter({0: 68570, 2: 23894, 1: 23689})\n"]}], "source": ["print(x_training.shape, y_training.shape)\n", "print(x_test.shape, y_test.shape)\n", "\n", "print(1.0*len(x_training)/(len(x_test)+len(x_training)))\n", "\n", "from collections import Counter\n", "training_c = Counter(y_training)\n", "test_c = Counter(y_test)\n", "print(training_c, test_c)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:51:42.870536Z", "start_time": "2020-01-01T19:51:42.861815Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 20.386174180870366\n", "0 59.27177476114324\n", "2 20.342051057986392\n", "0 59.03420488493625\n", "2 20.571143233493753\n", "1 20.39465188157\n"]}], "source": ["for k,v in training_c.items():\n", "    print(k, 100.0*v/len(x_training))\n", "print\n", "for k,v in test_c.items():\n", "    print(k, 100.0*v/len(x_test))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Run ND-ToR Algo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load k_shell"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:16:33.069082Z", "start_time": "2019-12-28T16:16:33.029151Z"}}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '../../Models/k_shell.pickle'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-18-69fb90c4c964>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0;32mwith\u001b[0m \u001b[0mopen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mMODELS_PATH\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0;34m'k_shell.pickle'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'rb'\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mhandle\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m     \u001b[0mk_shell\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpickle\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mhandle\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mk_shell\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '../../Models/k_shell.pickle'"]}], "source": ["with open(MODELS_PATH + 's1_k_shell.pickle', 'rb') as handle:\n", "    k_shell = pickle.load(handle)\n", "print(len(k_shell))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load CAIDA results and create CP-Core with NetworkX"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:09:02.332513Z", "start_time": "2019-12-28T16:08:58.992148Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["343952\n"]}], "source": ["caida_p2p = [tor for i, tor in enumerate(tor_dataset) if tor_labels[i] == 0]\n", "\n", "TIER1 = [\"174\", \"209\", \"286\", \"701\", \"1239\", \"1299\", \"2828\", \"2914\", \"3257\", \"3320\", \"3356\",\n", "         \"3491\", \"5511\", \"6453\", \"6461\", \"6762\", \"6830\", \"7018\", \"12956\"]\n", "# with open(caida_path, \"r\") as f:\n", "#     for line in f:\n", "#         as0, as1, label = [int(part) for part in line.split()[0].split('|')]\n", "#         if label == 0 and as0 in ASN_index_map and as1 in ASN_index_map:\n", "#             caida_p2p.append((ASN_index_map[as0], ASN_index_map[as1]))\n", "caida_p2p = [tor for i, tor in enumerate(tor_dataset) if tor_labels[i] == 0]\n", "print(len(caida_p2p))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:09:54.311120Z", "start_time": "2019-12-28T16:09:41.690522Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["12254\n", "12254 12254\n"]}], "source": ["import networkx as nx\n", "\n", "verteces = set()\n", "for pair in caida_p2p:\n", "    verteces.add(pair[0])\n", "    verteces.add(pair[1])\n", "print(len(verteces))\n", "\n", "vertex2ind = dict()\n", "ind2vertex = dict()\n", "for i, vertex in enumerate(verteces):\n", "    vertex2ind[vertex] = i\n", "    ind2vertex[i] = vertex\n", "\n", "print(len(vertex2ind), len(ind2vertex))\n", "\n", "g = nx.DiGraph()\n", "g.add_edges_from([(vertex2ind[pair[0]], vertex2ind[pair[1]]) for pair in caida_p2p])\n", "g.add_edges_from([(vertex2ind[pair[1]], vertex2ind[pair[0]]) for pair in caida_p2p])\n", "\n", "SCCs = [c for c in sorted(nx.strongly_connected_components(g),key=len, reverse=True)]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:10:33.810535Z", "start_time": "2019-12-28T16:10:33.771861Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["114\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "11889\n", "11889\n"]}], "source": ["print(len(SCCs))\n", "for i, scc in enumerate(SCCs):\n", "    for as_tier1 in TIER1:\n", "        if vertex2ind[as_tier1] not in scc:\n", "            break\n", "        print(i)\n", "print(len(SCCs[0]))\n", "scc = SCCs[0]\n", "scc = set([ind2vertex[ind] for ind in scc])\n", "print(len(scc))"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:12:51.275044Z", "start_time": "2019-12-28T16:12:46.031280Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["343446\n", "numbr of edges 171723.0\n"]}], "source": ["cp_core = set()\n", "for pair in caida_p2p:\n", "    if pair[0] in scc and pair[1] in scc:\n", "        cp_core.add(tuple(pair))\n", "        cp_core.add(tuple((pair[1], pair[0])))\n", "print(len(cp_core))\n", "print(\"numbr of edges \" + str(len(cp_core)/2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load CAIDA results and create CP-Core"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:25:50.178920Z", "start_time": "2019-01-05T10:25:49.237305Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["192088\n"]}], "source": ["caida_path = 'CAIDA_20181001.as-rel_cleaned.txt'\n", "caida_p2p = list()\n", "\n", "TIER1 = [7018, 209, 3356, 3549, 4323, 3320, 3257, 286, 6830, 2914, 5511, 3491, 1239, 6453, 6762, 12956, 701, 702, 703, 2828, 6461]\n", "TIER1 = [ASN_index_map[asn] for asn in TIER1]\n", "\n", "with open(caida_path, \"r\") as f:\n", "    for line in f:\n", "        as0, as1, label = [int(part) for part in line.split()[0].split('|')]\n", "        if label == 0 and as0 in ASN_index_map and as1 in ASN_index_map:\n", "            caida_p2p.append((ASN_index_map[as0], ASN_index_map[as1]))\n", "print(len(caida_p2p))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:32:15.437000Z", "start_time": "2019-01-05T10:32:15.379704Z"}, "code_folding": [3]}, "outputs": [], "source": ["#This class represents a directed graph using adjacency list representation \n", "class Graph: \n", "   \n", "    def __init__(self,vertices): \n", "        self.V = vertices #No. of vertices \n", "        self.graph = defaultdict(list) # default dictionary to store graph\n", "        self.scc_list = []\n", "   \n", "    # function to add an edge to graph \n", "    def addEdge(self,u,v): \n", "        self.graph[u].append(v) \n", "   \n", "    # A function used by DFS \n", "    def DFSUtil(self,v,visited): \n", "        # Mark the current node as visited and print it \n", "        visited[v]= True\n", "#         print v,\n", "        self.scc_list[-1].append(v)\n", "        #Recur for all the vertices adjacent to this vertex \n", "        for i in self.graph[v]: \n", "            if visited[i]==False: \n", "                self.D<PERSON><PERSON><PERSON>(i,visited) \n", "  \n", "  \n", "    def fillOrder(self,v,visited, stack): \n", "        # Mark the current node as visited  \n", "        visited[v]= True\n", "        #Recur for all the vertices adjacent to this vertex \n", "        for i in self.graph[v]: \n", "            if visited[i]==False: \n", "                self.fillOrder(i, visited, stack) \n", "        stack = stack.append(v) \n", "      \n", "  \n", "    # Function that returns reverse (or transpose) of this graph \n", "    def get<PERSON><PERSON><PERSON><PERSON>(self): \n", "        g = Graph(self.V) \n", "  \n", "        # Recur for all the vertices adjacent to this vertex \n", "        for i in self.graph: \n", "            for j in self.graph[i]: \n", "                g.add<PERSON>dge(j,i) \n", "        return g \n", "  \n", "   \n", "   \n", "    # The main function that finds and prints all strongly \n", "    # connected components \n", "    def printSCCs(self): \n", "          \n", "        stack = [] \n", "        # Mark all the vertices as not visited (For first DFS) \n", "        visited =[False]*(self.V) \n", "        # Fill vertices in stack according to their finishing \n", "        # times \n", "        for i in range(self.V): \n", "            if visited[i]==False: \n", "                self.fillOrder(i, visited, stack) \n", "  \n", "        # Create a reversed graph \n", "        gr = self.getTranspose() \n", "          \n", "        # Mark all the vertices as not visited (For second DFS) \n", "        visited =[False]*(self.V) \n", "  \n", "        # Now process all vertices in order defined by <PERSON><PERSON> \n", "        while stack: \n", "            i = stack.pop() \n", "            if visited[i]==False:\n", "                gr.scc_list.append([])\n", "                gr.<PERSON><PERSON><PERSON><PERSON>(i, visited) \n", "#                 print\"\"\n", "        \n", "        scc = gr.scc_list\n", "        return scc\n", "\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:45:11.810632Z", "start_time": "2019-01-05T10:45:11.347684Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["13083\n", "(13083, 13083)\n"]}], "source": ["verteces = set()\n", "for pair in caida_p2p:\n", "    verteces.add(pair[0])\n", "    verteces.add(pair[1])\n", "print(len(verteces))\n", "\n", "vertex2ind = dict()\n", "ind2vertex = dict()\n", "for i, vertex in enumerate(verteces):\n", "    vertex2ind[vertex] = i\n", "    ind2vertex[i] = vertex\n", "\n", "print(len(vertex2ind), len(ind2vertex))\n", "\n", "g = Graph(len(verteces))\n", "for pair in caida_p2p:\n", "    g.addEdge(vertex2ind[pair[0]], vertex2ind[pair[1]])\n", "    g.addEdge(vertex2ind[pair[1]], vertex2ind[pair[0]])\n", "\n", "SCCs = g.printSCCs()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:48:02.516359Z", "start_time": "2019-01-05T10:48:02.492207Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["135\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "12618\n", "12618\n"]}], "source": ["print(len(SCCs))\n", "for i, scc in enumerate(SCCs):\n", "    for as_tier1 in TIER1:\n", "        if vertex2ind[as_tier1] not in scc:\n", "            break\n", "        print(i)\n", "print(len(SCCs[134]))\n", "scc = SCCs[134]\n", "scc = set([ind2vertex[ind] for ind in scc])\n", "print(len(scc))"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:48:39.959504Z", "start_time": "2019-01-05T10:48:39.759351Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["383512\n", "numbr of edges 191756\n"]}], "source": ["cp_core = set()\n", "for pair in caida_p2p:\n", "    if pair[0] in scc and pair[1] in scc:\n", "        cp_core.add(pair)\n", "        cp_core.add((pair[1], pair[0]))\n", "print(len(cp_core))\n", "print(\"numbr of edges \" + str(len(cp_core)/2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create k_max-core"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"ExecuteTime": {"end_time": "2019-01-11T19:33:47.978710Z", "start_time": "2019-01-11T19:33:47.957902Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["21\n"]}], "source": ["k_max = max(ndtor_cp.k_shell.values())\n", "k_max_core = [vertex for vertex, k in ndtor_cp.k_shell.items() if k == k_max]\n", "print(len(k_max_core))"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"ExecuteTime": {"end_time": "2019-01-11T19:33:52.057885Z", "start_time": "2019-01-11T19:33:51.757105Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["420\n"]}], "source": ["k_max_edges = set()\n", "for i in range(len(k_max_core)):\n", "    for j in range(i):\n", "        if (k_max_core[i], k_max_core[j]) in x_training or (k_max_core[i], k_max_core[j]) in x_test:\n", "            k_max_edges.add((k_max_core[i], k_max_core[j]))\n", "        if (k_max_core[j], k_max_core[i]) in x_training or (k_max_core[j], k_max_core[i]) in x_test:\n", "            k_max_edges.add((k_max_core[j], k_max_core[i]))\n", "            \n", "print(len(k_max_edges))"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["## Create x_training_core"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"ExecuteTime": {"end_time": "2019-01-11T19:36:20.486675Z", "start_time": "2019-01-11T19:36:20.351637Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(90388, 47794)\n"]}], "source": ["x_training_edges = set()\n", "x_training_vertecs = set()\n", "for pair in x_training:\n", "    x_training_edges.add((pair[0], pair[1]))\n", "    x_training_vertecs.add(pair[0])\n", "    x_training_vertecs.add(pair[1])\n", "print(len(x_training_edges), len(x_training_vertecs))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run NDTOR_CP"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:30:35.954847Z", "start_time": "2019-12-28T16:30:35.777059Z"}, "code_folding": [23, 29, 105, 127, 161]}, "outputs": [], "source": ["class NDTOR_CP:\n", "    def __init__(self, core_verteces, core_edges, routing_tables, core_labels=None, is_core=True, threshold=0.85, k_shell=None):\n", "        if is_core:\n", "            self.tor_dict = self.__intialize_tor_dict_with_core(core_edges)\n", "            print('Finished __intialize_tor_dict_with_core')\n", "            self.pahse2_routes = self.__split_path_through_core(routing_tables, core_verteces)\n", "        else:\n", "            self.tor_dict = self.__intialize_tor_dict_with_training_set(core_edges, core_labels)\n", "            print('Finished __intialize_tor_dict_with_training_set')\n", "            self.pahse2_routes = routing_tables\n", "        print('Finished __split_path_through_core with ' + str(len(self.pahse2_routes)) + ' remaining for phase 2')\n", "        self.unclassified_pairs = self.__pahse2(threshold)\n", "        print('Finished __pahse2 with ' + str(len(self.unclassified_pairs)) + \" unclassified pairs\")\n", "        self.__pahse3()\n", "        print('Finished __pahse3 with ' + str(len(self.unclassified_pairs)) + \" unclassified pairs\")\n", "        \n", "        if len(self.unclassified_pairs) > 0:\n", "            if k_shell is None:\n", "                self.k_shell = self.__compute_k_shells(routing_tables)\n", "            else:\n", "                self.k_shell = k_shell\n", "            print(\"Finished __compute_k_shells\")\n", "            self.__compare_k_shells()\n", "            print(\"Finished __compare_k_shells\")\n", "    \n", "    \n", "    def __intialize_tor_dict_with_training_set(self, core_edges, core_labels):\n", "        tor_dict = dict()\n", "        for i, edge in enumerate(core_edges):\n", "            tor_dict[edge] = core_labels[i]\n", "        return tor_dict\n", "    \n", "\n", "    def __intialize_tor_dict_with_core(self, core_edges):\n", "        tor_dict = dict()\n", "        for edge in core_edges:\n", "            tor_dict[edge] = 0 # 0 - p2p\n", "        return tor_dict\n", "        \n", "    def __split_path_through_core(self, routes, core_verteces):\n", "        pahse2_routes = list()\n", "        for path in routes:\n", "            core_inds = list()\n", "            for j, vertex in enumerate(path):\n", "                if vertex in core_verteces:\n", "                    core_inds.append(j)\n", "            if len(core_inds) == 0:\n", "                pahse2_routes.append(path)\n", "            else:\n", "                for i in range(core_inds[0]):\n", "                    if (path[i], path[i+1]) not in self.tor_dict:\n", "                        self.tor_dict[(path[i], path[i+1])] = 1 # 1 - c2p\n", "                        self.tor_dict[(path[i+1], path[i])] = 3\n", "                for i in range(core_inds[0], core_inds[-1]):\n", "                    if (path[i], path[i+1]) not in self.tor_dict:\n", "                        self.tor_dict[(path[i], path[i+1])] = 0 # 0 - p2p\n", "                        self.tor_dict[(path[i+1], path[i])] = 0 \n", "                for i in range(core_inds[-1], len(path)-1):\n", "                    if (path[i], path[i+1]) not in self.tor_dict:\n", "                        self.tor_dict[(path[i], path[i+1])] = 3 # 0 - p2c\n", "                        self.tor_dict[(path[i+1], path[i])] = 1\n", "        return pahse2_routes\n", "    \n", "    \n", "    def __pahse2(self, threshold):\n", "        votings_p2c = defaultdict(lambda:0)\n", "        votings_c2p = defaultdict(lambda:0)\n", "        voting_pairs = set()\n", "        \n", "        for path in self.pahse2_routes:\n", "            pairs = list(zip(path[:-1], path[1:]))\n", "            pairs_tor = []\n", "            for i, pair in enumerate(pairs):\n", "                if pair in self.tor_dict:\n", "                    pairs_tor.append(self.tor_dict[pair])\n", "                else:\n", "                    pairs_tor.append(-1)\n", "                    voting_pairs.add(pair)\n", "            for i in range(len(pairs)):\n", "                if pairs_tor[i] == -1:\n", "                    if i > 1 and pairs_tor[i-1] == 3: #p2c\n", "                        pairs_tor[i] = 3\n", "                        votings_p2c[pairs[i]] += 1\n", "                    if i + 1 < len(pairs) and pairs_tor[i+1] == 1: #c2p\n", "                        pairs_tor[i] = 1\n", "                        votings_c2p[pairs[i]] += 1\n", "        \n", "        unclassified_pairs = set()\n", "        for pair in voting_pairs:\n", "            if (votings_p2c[pair] + votings_c2p[pair]) > 0:\n", "                rank = (votings_p2c[pair]*1.0)/(votings_p2c[pair] + votings_c2p[pair])\n", "                if rank >= threshold:\n", "                    self.tor_dict[pair] = 3\n", "                elif rank <= (1 - threshold):\n", "                    self.tor_dict[pair] = 1\n", "                else:\n", "                    unclassified_pairs.add(pair)\n", "            else:\n", "                unclassified_pairs.add(pair)\n", "        return unclassified_pairs\n", "    \n", "    def __pahse3(self):\n", "        for path in self.pahse2_routes:\n", "            pairs = list(zip(path[:-1], path[1:]))\n", "            if len(pairs) > 2: \n", "                for i in range(1,len(pairs)-1):\n", "                    if pairs[i] not in self.tor_dict and pairs[i-1] in self.tor_dict and pairs[i+1] in self.tor_dict:\n", "                        if self.tor_dict[pairs[i-1]] == 1 and self.tor_dict[pairs[i+1]] == 3:\n", "                            self.tor_dict[pairs[i]] = 0\n", "                            self.unclassified_pairs.remove(pairs[i])\n", "                        elif self.tor_dict[pairs[i-1]] == 3 and self.tor_dict[pairs[i+1]] == 1:\n", "                            self.tor_dict[pairs[i]] = 0\n", "                            self.unclassified_pairs.remove(pairs[i])\n", "    \n", "    \n", "    def __get_k_shell(self, k, edges, k_shell):\n", "        neighbors = defaultdict(set)\n", "        k_shell_verteces = set()\n", "        for edge in edges:\n", "            neighbors[edge[0]].add(edge)\n", "            neighbors[edge[1]].add(edge)\n", "        for asn, asn_edges in neighbors.items():\n", "            if len(asn_edges) <= k:\n", "                k_shell[asn] = k\n", "                k_shell_verteces.add(asn)\n", "                \n", "        return neighbors, k_shell_verteces\n", "    \n", "    \n", "    def __get_graph_for_routes(self, P):\n", "        edges = []\n", "        for route in P:\n", "            for edge in zip(route[:-1], route[1:]):\n", "                edges.append(edge)\n", "        return set(edges)\n", "                \n", "    \n", "    def __remove_k_shell_edges(self, edges, k_shell_verteces, neighbors):\n", "        for vertex in k_shell_verteces:\n", "            edges = edges - neighbors[vertex]\n", "        return edges\n", "                \n", "                \n", "    def __compute_k_shells(self, routes):\n", "        k_shell = dict()\n", "        k = 1\n", "        edges = self.__get_graph_for_routes(routes)\n", "        \n", "        while len(edges) > 0:\n", "            print(\"K: \" + str(k) + \" Start Iteration on \" + str(len(edges)) + \" edges\")\n", "            neighbors, k_shell_verteces = self.__get_k_shell(k, edges, k_shell)\n", "            k += 1\n", "            edges = self.__remove_k_shell_edges(edges, k_shell_verteces, neighbors)\n", "            print(\"Number of remaining edges: \" + str(len(edges)))\n", "            print()\n", "            \n", "        return k_shell\n", "        \n", "        \n", "    def __compare_k_shells(self):\n", "        for pair in self.unclassified_pairs:\n", "            try:\n", "                as0_k = self.k_shell[pair[0]]\n", "            except:\n", "                as0_k = 0\n", "            try:\n", "                as1_k = self.k_shell[pair[1]]\n", "            except:\n", "                as0_k = 0\n", "            if as0_k == as1_k:\n", "                self.tor_dict[pair] = 0 # p2p\n", "            elif as0_k > as1_k:\n", "                self.tor_dict[pair] = 3 # p2c\n", "            else:\n", "                self.tor_dict[pair] = 1 # c2p\n", "    \n", "                \n", "    def tor_dict2dataset(self):\n", "        dataset = []\n", "        labels = []\n", "        for pair, label in self.tor_dict.items():\n", "            dataset.append(np.asarray(pair))\n", "            labels.append(label)\n", "        print(\"Finished __tor_dict2dataset\")\n", "        return np.asarray(dataset), np.asarray(labels)\n", "\n", "\n", "    def generate_labels_for_set(self, pairs):\n", "        labels = []\n", "        for pair in pairs:\n", "            if (pair[0], pair[1]) in self.tor_dict:\n", "                labels.append(self.tor_dict[(pair[0], pair[1])])\n", "            elif (pair[1], pair[0]) in self.tor_dict:\n", "                if self.tor_dict[(pair[1], pair[0])] == 0 or self.tor_dict[(pair[1], pair[0])] == 2:\n", "                    labels.append(self.tor_dict[(pair[1], pair[0])])\n", "                else:\n", "                    labels.append((self.tor_dict[(pair[1], pair[0])] + 2)%4)\n", "            else:\n", "                labels.append(-1)\n", "        return np.asarray(labels)\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:03:11.797765Z", "start_time": "2019-12-28T16:31:09.928989Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished __intialize_tor_dict_with_core\n", "Finished __split_path_through_core with 276 remaining for phase 2\n", "Finished __pahse2 with 142 unclassified pairs\n", "Finished __pahse3 with 142 unclassified pairs\n", "K: 1 Start Iteration on 147667 edges\n", "Number of remaining edges: 123786\n", "\n", "K: 2 Start Iteration on 123786 edges\n", "Number of remaining edges: 80747\n", "\n", "K: 3 Start Iteration on 80747 edges\n", "Number of remaining edges: 59956\n", "\n", "K: 4 Start Iteration on 59956 edges\n", "Number of remaining edges: 47881\n", "\n", "K: 5 Start Iteration on 47881 edges\n", "Number of remaining edges: 39960\n", "\n", "K: 6 Start Iteration on 39960 edges\n", "Number of remaining edges: 34330\n", "\n", "K: 7 Start Iteration on 34330 edges\n", "Number of remaining edges: 30048\n", "\n", "K: 8 Start Iteration on 30048 edges\n", "Number of remaining edges: 26555\n", "\n", "K: 9 Start Iteration on 26555 edges\n", "Number of remaining edges: 23538\n", "\n", "K: 10 Start Iteration on 23538 edges\n", "Number of remaining edges: 20722\n", "\n", "K: 11 Start Iteration on 20722 edges\n", "Number of remaining edges: 18171\n", "\n", "K: 12 Start Iteration on 18171 edges\n", "Number of remaining edges: 16102\n", "\n", "K: 13 Start Iteration on 16102 edges\n", "Number of remaining edges: 14291\n", "\n", "K: 14 Start Iteration on 14291 edges\n", "Number of remaining edges: 12517\n", "\n", "K: 15 Start Iteration on 12517 edges\n", "Number of remaining edges: 10872\n", "\n", "K: 16 Start Iteration on 10872 edges\n", "Number of remaining edges: 9525\n", "\n", "K: 17 Start Iteration on 9525 edges\n", "Number of remaining edges: 8561\n", "\n", "K: 18 Start Iteration on 8561 edges\n", "Number of remaining edges: 7541\n", "\n", "K: 19 Start Iteration on 7541 edges\n", "Number of remaining edges: 6955\n", "\n", "K: 20 Start Iteration on 6955 edges\n", "Number of remaining edges: 6518\n", "\n", "K: 21 Start Iteration on 6518 edges\n", "Number of remaining edges: 6108\n", "\n", "K: 22 Start Iteration on 6108 edges\n", "Number of remaining edges: 5780\n", "\n", "K: 23 Start Iteration on 5780 edges\n", "Number of remaining edges: 5393\n", "\n", "K: 24 Start Iteration on 5393 edges\n", "Number of remaining edges: 4987\n", "\n", "K: 25 Start Iteration on 4987 edges\n", "Number of remaining edges: 4614\n", "\n", "K: 26 Start Iteration on 4614 edges\n", "Number of remaining edges: 4356\n", "\n", "K: 27 Start Iteration on 4356 edges\n", "Number of remaining edges: 4142\n", "\n", "K: 28 Start Iteration on 4142 edges\n", "Number of remaining edges: 3835\n", "\n", "K: 29 Start Iteration on 3835 edges\n", "Number of remaining edges: 3662\n", "\n", "K: 30 Start Iteration on 3662 edges\n", "Number of remaining edges: 3485\n", "\n", "K: 31 Start Iteration on 3485 edges\n", "Number of remaining edges: 3213\n", "\n", "K: 32 Start Iteration on 3213 edges\n", "Number of remaining edges: 2938\n", "\n", "K: 33 Start Iteration on 2938 edges\n", "Number of remaining edges: 2648\n", "\n", "K: 34 Start Iteration on 2648 edges\n", "Number of remaining edges: 2327\n", "\n", "K: 35 Start Iteration on 2327 edges\n", "Number of remaining edges: 1926\n", "\n", "K: 36 Start Iteration on 1926 edges\n", "Number of remaining edges: 1664\n", "\n", "K: 37 Start Iteration on 1664 edges\n", "Number of remaining edges: 1262\n", "\n", "K: 38 Start Iteration on 1262 edges\n", "Number of remaining edges: 812\n", "\n", "K: 39 Start Iteration on 812 edges\n", "Number of remaining edges: 362\n", "\n", "K: 40 Start Iteration on 362 edges\n", "Number of remaining edges: 0\n", "\n", "Finished __compute_k_shells\n", "Finished __compare_k_shells\n"]}], "source": ["ndtor_cp = NDTOR_CP(scc, cp_core, bgp_routes,k_shell=None)   # CP - Core"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"ExecuteTime": {"end_time": "2019-01-11T19:34:17.678290Z", "start_time": "2019-01-11T19:34:00.427980Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished __intialize_tor_dict_with_core\n", "Finished __split_path_through_core with 372339 remaining for phase 2\n", "Finished __pahse2 with 17235 unclassified pairs\n", "Finished __pahse3 with 14145 unclassified pairs\n", "Finished __compute_k_shells\n", "Finished __compare_k_shells\n"]}], "source": ["ndtor_k_max_core = NDTOR_CP(k_max_core, k_max_edges, bgp_routes, k_shell=k_shell)   # k_max_core - Core"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"ExecuteTime": {"end_time": "2019-01-11T19:45:29.890441Z", "start_time": "2019-01-11T19:45:07.572546Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished __intialize_tor_dict_with_training_set\n", "Finished __split_path_through_core with 3669655 remaining for phase 2\n", "Finished __pahse2 with 19960 unclassified pairs\n", "Finished __pahse3 with 17062 unclassified pairs\n", "Finished __compute_k_shells\n", "Finished __compare_k_shells\n"]}], "source": ["### Reverse to original labels\n", "core_labels = list()\n", "for label in y_training:\n", "    if label == 2:\n", "        core_labels.append(3)\n", "    else:\n", "        core_labels.append(label)\n", "\n", "ndtor_x_training_core = NDTOR_CP(x_training_vertecs, x_training_edges, bgp_routes, core_labels=core_labels, is_core=False, k_shell=k_shell)\n"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"ExecuteTime": {"end_time": "2019-01-11T19:45:58.174139Z", "start_time": "2019-01-11T19:45:55.731185Z"}}, "outputs": [], "source": ["ToR_MODEL_NAME = \"Cleaned_Orig_3_ToR_Classification_NDToR_x_training_core\"\n", "\n", "with open(MODELS_PATH + ToR_MODEL_NAME + '_tor_dict.pickle', 'wb') as handle:\n", "    pickle.dump(ndtor_k_max_core.tor_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)\n", "\n", "# with open(MODELS_PATH + 'k_shell.pickle', 'wb') as handle:\n", "#     pickle.dump(ndtor_cp.k_shell, handle, protocol=pickle.HIGHEST_PROTOCOL)"]}, {"cell_type": "code", "execution_count": 48, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T13:45:11.237303Z", "start_time": "2019-01-05T13:45:11.213810Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["21\n"]}], "source": ["k_max = max(ndtor_cp.k_shell.values())\n", "k_max_core = [vertex for vertex, k in ndtor_cp.k_shell.items() if k == k_max]\n", "print(len(k_max_core))"]}, {"cell_type": "code", "execution_count": 49, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T13:46:40.776218Z", "start_time": "2019-01-05T13:46:40.758274Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3356,)\n", "(6939,)\n", "(1299,)\n", "(174,)\n", "(3257,)\n", "(2914,)\n", "(6453,)\n", "(209,)\n", "(1239,)\n", "(6762,)\n", "(9002,)\n", "(701,)\n", "(6461,)\n", "(4637,)\n", "(3491,)\n", "(286,)\n", "(37100,)\n", "(2497,)\n", "(3303,)\n", "(2516,)\n", "(1273,)\n"]}], "source": ["index_ASN_map = {index: ASN for ASN, index in ASN_index_map.items()}\n", "\n", "for ind in k_max_core:\n", "    print(index_ASN_map[ind],)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save kshell"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(MODELS_PATH + ToR_MODEL_NAME + '_tor_dict.pickle', 'wb') as handle:\n", "    pickle.dump(ndtor_cp.tor_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)\n", "\n", "with open(MODELS_PATH + ToR_MODEL_NAME + 's1_k_shell.pickle', 'wb') as handle:\n", "    pickle.dump(ndtor_cp.k_shell, handle, protocol=pickle.HIGHEST_PROTOCOL)\n", "\n", "with open(MODELS_PATH + 's1_k_shell.pickle', 'rb') as handle:\n", "    k_shell = pickle.load(handle)\n", "print(len(k_shell))"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2018-10-20T11:24:40.580036Z", "start_time": "2018-10-20T11:24:33.351927Z"}}, "source": ["# Final evaluation of the model\n", "## Evaluate accuracy over the test set"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2020-01-01T19:52:20.870905Z", "start_time": "2020-01-01T19:52:17.742973Z"}}, "outputs": [], "source": ["with open(MODELS_PATH + ToR_MODEL_NAME + '_tor_dict.pickle', 'rb') as handle:\n", "    cp_core_tor_dict = pickle.load(handle)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:10:49.217940Z", "start_time": "2019-12-28T17:10:48.431398Z"}}, "outputs": [], "source": ["y_test_prediction = ndtor_cp.generate_labels_for_set(x_test)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:11:17.032530Z", "start_time": "2019-12-28T17:11:16.981350Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0, 1, 3, -1}\n"]}], "source": ["print(set(y_test_prediction))"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:12:58.694998Z", "start_time": "2019-12-28T17:12:58.269245Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["116153\n", "116153\n"]}], "source": ["print(len(y_test_prediction))\n", "y_test_prediction_new = []\n", "for i in range(len(y_test_prediction)):\n", "    if y_test_prediction[i] %2 == 0:\n", "        y_test_prediction_new.append(0)\n", "    elif y_test_prediction[i] == 3:\n", "        y_test_prediction_new.append(2)\n", "    elif y_test_prediction[i] == 1:\n", "        y_test_prediction_new.append(1)\n", "    else:\n", "        y_test_prediction_new.append(-1)\n", "\n", "y_test_prediction_new = np.asarray(y_test_prediction_new)\n", "print(len(y_test_prediction_new))\n", "y_test_prediction = y_test_prediction_new"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:13:25.017290Z", "start_time": "2019-12-28T17:13:24.923248Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0, 1, 2, -1}\n"]}], "source": ["print(set(y_test_prediction))"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:14:56.748179Z", "start_time": "2019-12-28T17:14:56.484717Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0, 1, 2}\n", "109774 109774\n"]}], "source": ["y_test = [y_test[i] for i, label in enumerate(y_test_prediction) if label!=-1]\n", "y_test_prediction = [label for i, label in enumerate(y_test_prediction) if label!=-1]\n", "print(set(y_test_prediction))\n", "print(len(y_test), len(y_test_prediction))"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:15:26.273276Z", "start_time": "2019-12-28T17:15:26.031511Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 89.04%\n"]}], "source": ["from sklearn.metrics import accuracy_score\n", "test_scores = accuracy_score(y_test, y_test_prediction)\n", "print(\"Accuracy: %.2f%%\" % (test_scores*100))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:13.913361Z", "start_time": "2018-11-17T15:27:13.845207Z"}}, "outputs": [], "source": ["# x_test_cleaned = np.asarray([np.asarray(x_test[i]) for i in range(len(x_test)) if y_test_prediction[i] != -1])\n", "# y_test_cleaned = np.asarray([y_test[i] for i in range(len(y_test)) if y_test_prediction[i] != -1])\n", "# y_test_prediction_cleaned = np.asarray([y_test_prediction[i] for i in range(len(y_test_prediction)) if y_test_prediction[i] != -1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:15.867226Z", "start_time": "2018-11-17T15:27:15.861071Z"}}, "outputs": [], "source": ["# print(len(x_test_cleaned), len(y_test_cleaned), len(y_test_prediction_cleaned))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:17.061659Z", "start_time": "2018-11-17T15:27:17.052861Z"}}, "outputs": [], "source": ["# from sklearn.metrics import accuracy_score\n", "# test_scores = accuracy_score(y_test_cleaned, y_test_prediction_cleaned)\n", "# print(\"Accuracy: %.2f%%\" % (test_scores*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test if by learning (asn1, asn2) -> p2c then (asn2, asn1) -> c2p and vice versa"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2019-01-04T17:50:32.317308Z", "start_time": "2019-01-04T17:50:32.017509Z"}}, "outputs": [], "source": ["p2c = TOR_ORIG_LABELS_DICT['P2C']\n", "c2p = TOR_ORIG_LABELS_DICT['C2P']\n", "\n", "p2c_training = np.asarray([np.asarray(x_training[i]) for i in range(len(x_training)) if y_training[i] == p2c])\n", "p2c_training_oposite = np.asarray([np.asarray([pair[1], pair[0]]) for pair in p2c_training])\n", "p2c_training_labels = [p2c]*len(p2c_training)\n", "p2c_training_oposite_labels = [c2p]*len(p2c_training_oposite)\n", "print(p2c_training.shape, p2c_training_oposite.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2019-01-04T17:51:02.400638Z", "start_time": "2019-01-04T17:51:02.106675Z"}}, "outputs": [], "source": ["p2c_training_labels_prediction = generate_labels_for_set(caida_tor_dict, p2c_training)\n", "p2c_training_scores = accuracy_score(p2c_training_labels, p2c_training_labels_prediction)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_scores*100))\n", "\n", "p2c_training_oposite_labels_prediction = generate_labels_for_set(caida_tor_dict, p2c_training_oposite)\n", "p2c_training_oposite_scores = accuracy_score(p2c_training_oposite_labels, p2c_training_oposite_labels_prediction)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_oposite_scores*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot and save a confusion matrix for results over the test set"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define a function"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:07:29.381877Z", "start_time": "2019-12-28T17:07:28.969173Z"}}, "outputs": [], "source": ["%matplotlib inline\n", "\n", "import matplotlib\n", "import pylab as pl\n", "from sklearn.metrics import confusion_matrix\n", "import itertools\n", "import matplotlib.pyplot as plt\n", "import matplotlib.cm as cm\n", "\n", "def plot_confusion_matrix(cm, classes,\n", "                          normalize=False,\n", "                          title='Confusion matrix',\n", "                          fname='Confusion matrix',\n", "                          cmap=plt.cm.Blues):\n", "    \"\"\"\n", "    This function prints and plots the confusion matrix.\n", "    Normalization can be applied by setting `normalize=True`.\n", "    \"\"\"\n", "    if normalize:\n", "        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "        print(\"Normalized confusion matrix\")\n", "    else:\n", "        print('Confusion matrix, without normalization')\n", "\n", "#     print(cm)\n", "\n", "    plt.imshow(cm, interpolation='nearest', cmap=cmap)\n", "#     plt.title(title)\n", "    plt.colorbar()\n", "    tick_marks = np.arange(len(classes))\n", "    plt.xticks(tick_marks, classes, rotation=45)\n", "    plt.yticks(tick_marks, classes)\n", "\n", "    fmt = '.1f' if normalize else 'd'\n", "    thresh = cm.max() / 2.\n", "    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):\n", "        if normalize:\n", "            plt.text(j, i, format(cm[i, j]*100, fmt) + '%',\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")\n", "        else:\n", "            plt.text(j, i, format(cm[i, j], fmt),\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")    \n", "        \n", "    plt.tight_layout()\n", "    plt.ylabel('True Label')\n", "    plt.xlabel('Predicted Label')\n", "    plt.savefig(fname, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:15:56.615091Z", "start_time": "2019-12-28T17:15:54.652975Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Confusion matrix, without normalization\n", "Normalized confusion matrix\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Compute confusion matrix\n", "cnf_matrix = confusion_matrix(y_test, y_test_prediction)\n", "np.set_printoptions(precision=2)\n", "\n", "# Plot non-normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names,\n", "                      title='Confusion matrix, without normalization',\n", "                      fname=RESULTS_PATH + ToR_MODEL_NAME + \"_\" + 'Confusion_matrix_without_normalization')\n", "\n", "# Plot normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names, normalize=True,\n", "                      title='Normalized Confusion Msatrix',\n", "                      fname=RESULTS_PATH +ToR_MODEL_NAME + \"_\" + 'Normalized_confusion_matrix')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["# Export the model to a file"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-16T12:50:04.937659Z", "start_time": "2018-11-16T12:50:04.873865Z"}, "hidden": true}, "outputs": [], "source": ["model_json = pairs_model.to_json()\n", "with open(MODELS_PATH + ToR_MODEL_NAME + '.json', \"w\") as json_file:\n", "    json_file.write(model_json)\n", "pairs_model.save_weights(MODELS_PATH + ToR_MODEL_NAME + '.h5')\n", "print(\"Save Model\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Export results to a csv file (with original ASNs)\n", "## Define functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:53:31.858073Z", "start_time": "2018-10-20T15:53:31.831311Z"}}, "outputs": [], "source": ["def index2ASN(dataset_indexed, ASN_index_map):\n", "    dataset = []\n", "    index_ASN_map = {index: ASN for ASN, index in ASN_index_map.items()}\n", "    for row_indexed in dataset_indexed:\n", "        row = []\n", "        for index in row_indexed:\n", "            if index != 0:\n", "                row += [index_ASN_map[index]]\n", "        dataset.append(row)\n", "    \n", "    return dataset\n", "\n", "def index2ASN_labeled(dataset_indexed, labels_indexed, ASN_index_map):\n", "    dataset = []\n", "    index_ASN_map = {index: ASN for ASN, index in ASN_index_map.items()}\n", "    labels_colors_map = {0:'GREEN', 1:'RED'}\n", "    \n", "    for i, row_indexed in enumerate(dataset_indexed):\n", "        row = []\n", "        for index in row_indexed:\n", "            if index != 0:\n", "                row += [index_ASN_map[index]]\n", "        row += [labels_colors_map[labels_indexed[i]]]\n", "        dataset.append(row)\n", "    \n", "    return dataset\n", "\n", "import csv\n", "def export_csv(dataset, csv_name):\n", "    with open(csv_name + '.csv', 'wb') as csv_file:\n", "        csv_writer = csv.writer(csv_file)\n", "        for row in dataset:\n", "            csv_writer.writerow(row)"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["## Load a relevant dataset {all, misclassified, decided, undecided} and get model predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:35:46.412152Z", "start_time": "2018-10-20T15:30:04.317489Z"}, "hidden": true}, "outputs": [], "source": ["### misclassified from the entire dataset ###\n", "\n", "dataset = np.load(DATA_PATH + \"bgp_routes_indexed_dataset.npy\")\n", "labels = np.load(DATA_PATH + \"bgp_routes_labels.npy\")\n", "\n", "# remove UNDECIDED\n", "dataset = np.asarray([np.asarray(dataset[i]) for i in range(len(dataset)) if labels[i] != 2])\n", "labels = np.asarray([labels[i] for i in range(len(labels)) if labels[i] != 2])\n", "\n", "# pad sequences\n", "dataset = sequence.pad_sequences(dataset, maxlen=max_len)\n", "# Get Model Predictions\n", "predictions = model.predict_classes(dataset, verbose=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:54:32.435559Z", "start_time": "2018-10-20T15:54:28.652217Z"}, "hidden": true}, "outputs": [], "source": ["# Create misclassified dataset\n", "x_misclassified = np.asarray([route for i,route in enumerate(dataset) if labels[i] != predictions[i]])\n", "y_misclassified_prediction = np.asarray([label for i,label in enumerate(predictions) if labels[i] != predictions[i]])\n", "print len(x_misclassified), len(y_misclassified_prediction)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:54:39.339072Z", "start_time": "2018-10-20T15:54:39.169037Z"}}, "outputs": [], "source": ["dataset_misclassified = index2ASN_labeled(x_misclassified, y_misclassified_prediction, ASN_index_map)\n", "export_csv(dataset_misclassified, RESULTS_PATH + MODEL_NAME + \"_misclassified\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 2}