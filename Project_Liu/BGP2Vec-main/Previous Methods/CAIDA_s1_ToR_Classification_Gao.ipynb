{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Import python packages"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:47:42.386505Z", "start_time": "2019-12-28T16:47:40.777109Z"}}, "outputs": [], "source": ["from gao import Gao\n", "\n", "import pickle\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from collections import Counter\n", "import random\n", "\n", "np.random.seed(7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Define parameters and load bgp_routes and ToR datasets"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:48:00.323800Z", "start_time": "2019-12-28T16:47:44.259171Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3669655,) (3669655,)\n", "(580762, 2) (580762,)\n"]}], "source": ["ToR_MODEL_NAME = \"CAIDA_s1_ToR_Classification_Gao\"\n", "\n", "TEST_SIZE = 0.2\n", "TOR_LABELS_DICT = {'P2P':0, 'C2P': 1,'P2C': 2}\n", "class_names = ['P2P', 'C2P', 'P2C']\n", "DATA_PATH = '../../Data/'\n", "MODELS_PATH = '../../Models/'\n", "RESULTS_PATH = '../../Results/'\n", "\n", "\n", "bgp_routes = np.load(DATA_PATH + \"bgp_routes_dataset.npy\")\n", "bgp_routes_labels = np.load(DATA_PATH + \"bgp_routes_labels.npy\")\n", "print(bgp_routes.shape, bgp_routes_labels.shape)\n", "\n", "DATA = \"caida_s1_tor\"\n", "tor_dataset = np.load(DATA_PATH + DATA + \"_dataset.npy\")\n", "tor_labels = np.load(DATA_PATH + DATA + \"_labels.npy\")\n", "\n", "print(tor_dataset.shape, tor_labels.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate training and test sets\n", "## Shauffle dataset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:48:00.577824Z", "start_time": "2019-12-28T16:48:00.406181Z"}}, "outputs": [], "source": ["from sklearn.utils import shuffle\n", "dataset, labels = shuffle(tor_dataset, tor_labels, random_state=7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate a balanced dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T13:08:27.960627Z", "start_time": "2018-11-17T13:08:27.890678Z"}}, "outputs": [], "source": ["# def generate_balanced_dataset(dataset, labels, labels_set):\n", "#     sets_dict = dict()\n", "#     for label in labels_set:\n", "#         sets_dict[label] = np.asarray([np.asarray(dataset[i]) for i in range(len(dataset)) if labels[i] == label])\n", "    \n", "#     min_set_len = min([len(label_set) for label_set in sets_dict.values()])\n", "    \n", "#     for label, label_set in sets_dict.iteritems():\n", "#         sets_dict[label] = label_set[np.random.choice(label_set.shape[0], min_set_len, replace=False)]\n", "    \n", "#     dataset = np.concatenate((sets_dict.values()))\n", "#     labels = []\n", "#     for label, label_set in sets_dict.iteritems():\n", "#         labels += [label]*len(label_set)\n", "#         print label, len(label_set)\n", "#     labels = np.asarray(labels)\n", "#     return shuffle(dataset, labels, random_state=7)\n", "\n", "# dataset, labels = generate_balanced_dataset(dataset, labels, (0,1,3))\n", "# print dataset.shape, labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train Test Split"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:48:00.829781Z", "start_time": "2019-12-28T16:48:00.646358Z"}}, "outputs": [], "source": ["x_training, x_test, y_training, y_test = train_test_split(dataset, labels, test_size=TEST_SIZE)\n", "\n", "del dataset, labels"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Run <PERSON>"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:52:20.670034Z", "start_time": "2019-12-28T16:48:00.904655Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished __compute_as_degree\n", "Finished __compute_as_transit_rank\n", "Finished __assign_p2c_c2p_siblings_tors\n", "Finished __identify_not_peering\n", "Finished __assign_p2p_tors\n"]}], "source": ["gao = Gao(bgp_routes)"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2018-10-20T11:24:40.580036Z", "start_time": "2018-10-20T11:24:33.351927Z"}}, "source": ["# Final evaluation of the model\n", "## Evaluate accuracy over the test set"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:52:58.117429Z", "start_time": "2019-12-28T16:52:56.665253Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["116153\n", "116153\n"]}], "source": ["y_test_prediction = gao.generate_labels_for_set(x_test)\n", "\n", "print(len(y_test_prediction))\n", "y_test_prediction_new = []\n", "for i in range(len(y_test_prediction)):\n", "    if y_test_prediction[i] %2 == 0:\n", "        y_test_prediction_new.append(0)\n", "    elif y_test_prediction[i] == 3:\n", "        y_test_prediction_new.append(2)\n", "    else:\n", "        y_test_prediction_new.append(1)\n", "\n", "y_test_prediction_new = np.asarray(y_test_prediction_new)\n", "print(len(y_test_prediction_new))\n", "y_test_prediction = y_test_prediction_new"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:53:00.107058Z", "start_time": "2019-12-28T16:53:00.072184Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 38.43%\n"]}], "source": ["from sklearn.metrics import accuracy_score\n", "test_scores = accuracy_score(y_test, y_test_prediction)\n", "print(\"Accuracy: %.2f%%\" % (test_scores*100))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:13.913361Z", "start_time": "2018-11-17T15:27:13.845207Z"}}, "outputs": [], "source": ["# x_test_cleaned = np.asarray([np.asarray(x_test[i]) for i in range(len(x_test)) if y_test_prediction[i] != -1])\n", "# y_test_cleaned = np.asarray([y_test[i] for i in range(len(y_test)) if y_test_prediction[i] != -1])\n", "# y_test_prediction_cleaned = np.asarray([y_test_prediction[i] for i in range(len(y_test_prediction)) if y_test_prediction[i] != -1])"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:15.867226Z", "start_time": "2018-11-17T15:27:15.861071Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(22676, 22676, 22676)\n"]}], "source": ["# print(len(x_test_cleaned), len(y_test_cleaned), len(y_test_prediction_cleaned))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:17.061659Z", "start_time": "2018-11-17T15:27:17.052861Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 89.72%\n"]}], "source": ["# from sklearn.metrics import accuracy_score\n", "# test_scores = accuracy_score(y_test_cleaned, y_test_prediction_cleaned)\n", "# print(\"Accuracy: %.2f%%\" % (test_scores*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test if by learning (asn1, asn2) -> p2c then (asn2, asn1) -> c2p and vice versa"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:53:07.000941Z", "start_time": "2019-12-28T16:53:06.962919Z"}}, "outputs": [{"ename": "NameError", "evalue": "name 'TOR_ORIG_LABELS_DICT' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-8-2e282c394516>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mp2c\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mTOR_ORIG_LABELS_DICT\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'P2C'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m \u001b[0mc2p\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mTOR_ORIG_LABELS_DICT\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'C2P'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0mp2c_training\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0masarray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0masarray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx_training\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mi\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx_training\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mif\u001b[0m \u001b[0my_training\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0mp2c\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0mp2c_training_oposite\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0masarray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0masarray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mpair\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpair\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mpair\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mp2c_training\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'TOR_ORIG_LABELS_DICT' is not defined"]}], "source": ["p2c = TOR_ORIG_LABELS_DICT['P2C']\n", "c2p = TOR_ORIG_LABELS_DICT['C2P']\n", "\n", "p2c_training = np.asarray([np.asarray(x_training[i]) for i in range(len(x_training)) if y_training[i] == p2c])\n", "p2c_training_oposite = np.asarray([np.asarray([pair[1], pair[0]]) for pair in p2c_training])\n", "p2c_training_labels = [p2c]*len(p2c_training)\n", "p2c_training_oposite_labels = [c2p]*len(p2c_training_oposite)\n", "print(p2c_training.shape, p2c_training_oposite.shape)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"ExecuteTime": {"end_time": "2019-01-11T18:59:45.609189Z", "start_time": "2019-01-11T18:59:45.040515Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 0.63%\n", "Accuracy: 98.65%\n"]}], "source": ["p2c_training_labels_prediction = gao.generate_labels_for_set(p2c_training)\n", "p2c_training_scores = accuracy_score(p2c_training_labels, p2c_training_labels_prediction)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_scores*100))\n", "\n", "p2c_training_oposite_labels_prediction = gao.generate_labels_for_set(p2c_training_oposite)\n", "p2c_training_oposite_scores = accuracy_score(p2c_training_oposite_labels, p2c_training_oposite_labels_prediction)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_oposite_scores*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot and save a confusion matrix for results over the test set"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define a function"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:54:27.119040Z", "start_time": "2019-12-28T16:54:26.644604Z"}}, "outputs": [], "source": ["%matplotlib inline\n", "\n", "import matplotlib\n", "import pylab as pl\n", "from sklearn.metrics import confusion_matrix\n", "import itertools\n", "import matplotlib.pyplot as plt\n", "import matplotlib.cm as cm\n", "\n", "def plot_confusion_matrix(cm, classes,\n", "                          normalize=False,\n", "                          title='Confusion matrix',\n", "                          fname='Confusion matrix',\n", "                          cmap=plt.cm.Blues):\n", "    \"\"\"\n", "    This function prints and plots the confusion matrix.\n", "    Normalization can be applied by setting `normalize=True`.\n", "    \"\"\"\n", "    if normalize:\n", "        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "        print(\"Normalized confusion matrix\")\n", "    else:\n", "        print('Confusion matrix, without normalization')\n", "\n", "#     print(cm)\n", "\n", "    plt.imshow(cm, interpolation='nearest', cmap=cmap)\n", "#     plt.title(title)\n", "    plt.colorbar()\n", "    tick_marks = np.arange(len(classes))\n", "    plt.xticks(tick_marks, classes, rotation=45)\n", "    plt.yticks(tick_marks, classes)\n", "\n", "    fmt = '.1f' if normalize else 'd'\n", "    thresh = cm.max() / 2.\n", "    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):\n", "        if normalize:\n", "            plt.text(j, i, format(cm[i, j]*100, fmt) + '%',\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")\n", "        else:\n", "            plt.text(j, i, format(cm[i, j], fmt),\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")    \n", "        \n", "    plt.tight_layout()\n", "    plt.ylabel('True Label')\n", "    plt.xlabel('Predicted Label')\n", "    plt.savefig(fname, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:54:30.733765Z", "start_time": "2019-12-28T16:54:28.284033Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Confusion matrix, without normalization\n", "Normalized confusion matrix\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAVkAAAEmCAYAAADIhuPPAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDMuMC4zLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvnQurowAAIABJREFUeJzt3XmclXP/x/HXZ5YoyVZotbRIobSJFlm6FSn80F62O0shty2hFSHJnkK4s6VspRS6RdGe0l3uVNYWN2HibqZlps/vj3NmzEyznMlcc+aq99PjPB7nXNf3fM/nOq4+8z3f6/v9XubuiIhIMBLiHYCIyN5MSVZEJEBKsiIiAVKSFREJkJKsiEiAlGRFRAKkJCsiEiAlWRGRACnJiogEKCneAfwVFStW9BpHHR3vMEJn2Vcb4x1CaJ1Uu3K8Qwit5Z8v3ezulYqrvsQKR7mnp8Vc3tN+nunu7Yrr82MV6iRb46ijmTtvUbzDCJ3D2g6LdwihNWvanfEOIbQqHpj8XXHW5+nb2K9ul5jLb/v88YrF+fmxCnWSFZF9mAFm8Y6iUEqyIhJeVvovKynJikh4qSUrIhIUU0tWRCRQasmKiATEUEtWRCQ4ppasiEig1JIVEQmQWrIiIgExg4TEeEdRKCVZEQkvdReIiARF42RFRIKVoD5ZEZFgaJysiEjANLpARCQo6pMVEQmWWrIiIgFSS1ZEJCCmtQtERIKllqyISFA0rVZEJFjqLhARCYgmI4iIBEnjZEVEgqXuAhGRAKklKyISILVkRUQCYuqTFREJllqyIiLBsRAk2dLf1i5FrulzBUdVO4ImJ5+Y535355abbuDE42vTrHEDPv98KQBfrV5Ni+ZNOKVJQxbMnwdAeno657VrS2pqaonFX9L6/t8pLH7+Wpa8cC39Lj4FgEFXnMHC8dcw/9mrmfpQDyofVj7f9x9YrgzrJt/E6BvbA1AmOZF3HuzO4uevpc8FTbLKPXFLBxrUPjLYg4mjk+vXotUpDWlzWmPOan1KnmXmzvmYNqc1pkXTBpzf7kwANv/8M+e1PZ2WzRoyfeo7WWV7dL6ITZs2lkjsQTIiSTbWR7woyRZBj56X8fbU9/LdP3PGe6xdu5YvVn3FE0+Npf/11wHw3LNjGXbPCF5+dRKPjh4FwDNjx9C1ew/KlStXIrGXtHrHVOLyDo1odc0zNLvyadqfWoeaVQ9l9Guf0uyKp2l+1Vjem/cVd/Q+Pd86Bl95JnOWf5f1um3Tmnz+1SaaXjGGKzo0BuDEmkeQYMbyNT8Gfkzx9Pa0D5n92RJmfbJgt31bUlK47abreWniW3y6aDnjJ7wGwJuTX6Nzt568N2sOTzwaOe9mTH+XkxqeTOXKVUo0/kCYYQmxP+JFSbYIWrZqzaGHHJrv/mlT36Fbj56YGc1Oac6WlBQ2bdpEcnIy29LSSE1LJTk5mZSUFKZPe5fuPXqVYPQlq+5RlVi4aj1p29PJyHDmLP+OTq3r8kfqjqwy5fYvg+fz/pPrVObwQw7gw0XrsrbtzNhF2f2SSEr887QddOUZDB//UVCHEQpvTHqVDh0voFr1GgBUqnQ4AEnJyWzblsaO7dtJSEggPT2dsU89Rr8bb45nuMVKLdl9zMaNG6lWrXrW6ypVq7Fp4wauvqYvjz82mhv7Xcstt9/BiHuHcduAgaHoT9pTK7/5iZYNjuLQCmUpu18S7ZrXotrhBwEw5KozWTOpP13OPpHhz+2eIM3g/uv+xsAxH+TYPmvxOo44tDyfPH0Vo1/7lPNOq8Pnqzex6Zf/lcgxxYuZcfEF7TmzVTNeHP/MbvvXrV1DSspvdGx/Fme2asbEVyYAcPElXfnXhx9w6UXncdvAQYx/Zgydu+5dv57CkGQDvfBlZhnAiujnfAn0Bg4D/gkcCewCxrn7o9HyLwCnA1ui+/q6+7wgYyxO7ru3y8yM6jVqMOODSDJZt3YtP27aRJ3j6nLl5b3YuWMHdw8eRu06dUo63ECt/m4zo175lHdH9WRr2g6+WPtf0tN3ATDk2X8x5Nl/cUv3llxzUTPueX52jvdefUFTZi5Yw/qff8+xPSPDuWz4mwAkJSYw9aEeXDzwVR7o+zeqH34QL89czrTPviqR4ytJ0z74mMqVq/Dzzz9xccd21K5Tl9Natsran56ezvLPl/Lmu++zLS2Ndme3onHTU6hVuw6vvTEFgJTffuOx0SN54eVJ9O93NVtSUrju+v40PeXUeB1WsQhDQyXolmyauzd09xOAHcA1QDpws7sfDzQH+ppZvWzvudXdGwIDgLEBx1esqlatyvr1P2S93rhhPUfm6vsaOvgu7h48jDFPPkbnLt248+4h3HfvsJIOtUS8OP1zTvv7ONre8AK//ZHG2g2/5Nj/+ocruKD18bu975T61bjmwmb857UbGXHt3+h2TgOG9zkrR5mrL2jKSzOWc0r96uzYmUGPoZMZ0Kt1oMcTL5n9p5UqHc6551/A0iWLcuyvUqUaZ7Y9hwMOOIDDKlbktNNasvLfX+QoM/KBe7jp1jt4c9JrNDi5EY899Qz3DL27xI4hEFbER5yUZHfBHKCWu29y96UA7v4HkRZu1TzKfwLUKsH4/rLzOnTklZcm4O4sXDCfCgcdROXKlbP2z/nkYypXqUKt2rVJTU0lISGBxMRE0vbSEQaVDo78LK1+eAU6tTqe1z/8NzWr/tmnfV6L4/jq+827ve/ye96izqWPULfLo9wx5n1embmcu8fNytp/cPn9aX9abV6euZxy+yWzyx13Z78ye9+IxK1bt/LHH39kPZ896wOOr1c/R5n2553P/M/mkp6eTmpqKksWL6LOcXWz9q9bu4YfN22iRcvWpKWlkmAJYMb2bdtK9FiKmxF7V8Fe212QycySgPbAjFzbjwZOBna/ZArnE+lqyF1XH6APQPUaNYo50oL17tmNOZ/M5pfNm6l9bHXuunsIO3fuBOCqPtdwTvtzmTljOiceX5uy5cox9pnxWe91dx4YcS8TXpkIwBVX9uGKy3qQnp7Oo48/VaLHUVJeHX4ph1Yox870DPo/Mp2U/21jzG3nU7t6RXa58/1/U7hh1DQAGh1Xmas6NuG6kVMLrXdg79O5/59zAPhg0VquvrApi5+/lmenLAn0eOLh55/+S+9uFwOQnp7B/13ahbPansPzz0V+5F1+5dXUqXs8Z559Dq2bNyIhIYEevS/n+HonZNVx37BBDBwU+bV00SVd6NXl/xg35gkG3DW45A+omIWhu8Dy6kcstsr/7JOFSEv2ZnffEd1XHvgYuNfd34xue4E/+2R/Bm5y93/nV3+jxk187rxF+e2WfBzWdu/snigJ66fdGe8QQqvigclL3L1J4SVjk3TYsV7h3HtiLv/bS92L9fNjFXRLNi3av5qDmSUDbwAvZybYbG5198kBxyUie4EwtGRLfAiXRb6V54Av3f3hkv58EdlLBHDhy8zamdlqM1trZgPy2F/DzD4ys8/N7AszO7ewOuMxTrYF0BM408yWRR+FBioikltxXvgys0TgSSLXj+oBXXONfAK4C3jd3U8GugCFXlAJtLvA3XebmO7uc8nn74q7XxZkPCKy98gcXVCMmgFr3f1rADN7DegErMpWxoEK0ecHAYUuArH3jXkRkX1GEdckqGhmi7O9Hufu47K9rgr8kO31eiD3ijxDgPfN7HrgAODswj5USVZEwsmKfOFrcyGjC/KqLPfwq67AC+4+ysxOBSaY2Qnuviu/SpVkRSS0irm7YD1QPdvrauzeHXAl0A7A3eeZ2f5AReCn/CrVAjEiElrFPONrEVDbzI4xszJELmxNyVXme+Cs6GcfD+xPZEx/vtSSFZFQKu4LX+6ebmb9gJlAIjDe3Vea2TBgsbtPAW4GnjGzm4h0JVzmhczoUpIVkfAq5rkI7j4dmJ5r26Bsz1cRGYYaMyVZEQmnol/4igslWREJLSVZEZEAKcmKiASp9OdYJVkRCS+1ZEVEAmJmJCSU/qH+SrIiElpqyYqIBKn051glWREJL7VkRUSCoskIIiLBMSAEOVZJVkTCqtjvjBAIJVkRCa0Q5FglWREJL7VkRUSCYmrJiogExoCEot1IMS6UZEUktJRkRUSCou4CEZHgRMbJlv4sqyQrIiGlcbIiIoEKQY5VkhWR8FJLVkQkKLrwJSISHF34EhEJWAhyrJKsiISXWrIiIgEKQY4Nf5INw5dc6vz+c7wjCK0D9g/9P5m9hpmm1YqIBEiTEUREAhWCHKskKyLhpZasiEhQNBlBRCQ4mowgIhIwJVkRkQCFIMcqyYpIeKklKyISFF34EhEJjmkygohIsEKQY0mIdwAiInsqMcFifsTCzNqZ2WozW2tmA/Ipc6mZrTKzlWb2SmF1qiUrIqFkVrwXvswsEXgSaAusBxaZ2RR3X5WtTG3gDqCFu/9mZocXVq9asiISWgkW+yMGzYC17v61u+8AXgM65Srzd+BJd/8NwN1/KjTGoh2SiEjpYWYxP4CKZrY426NPruqqAj9ke70+ui27OkAdM/vUzOabWbvCYsy3u8DM3gI8v/3uflFhlYuIBKmIvQWb3b1JQdXlsS13DkwCagNtgGrAHDM7wd1T8qu0oD7ZJwrYJyISV0ZkGFcxWg9Uz/a6GrAxjzLz3X0n8I2ZrSaSdBflV2m+SdbdZ2U+N7MyQA13X7sHgYuIBKKYb4ywCKhtZscAG4AuQLdcZd4GugIvmFlFIt0HXxcYY2GfambnASuAD6KvG0a7EkRE4qcI/bGxjEJw93SgHzAT+BJ43d1XmtkwM+sYLTYT+MXMVgEfAbe6+y8F1RvLEK5hwCnRCnH3ZWZWK4b3iYgEqrgnI7j7dGB6rm2Dsj134B/RR0xiSbI73T0l11+CfC+IiYiUBAMSQjDlK5Yk+6WZXQokRPsqbgTmBxuWiEjhQpBjYxon2w9oDOwC3gK2A/2DDEpEpDCZtwSP9REvhbZk3X0rcLuZDY289LTgwxIRKVwYugtiGV3QyMw+B74C1pjZEjNrFHxoIiIFsyI84iWWPtnngf7u/hGAmbWJbmsQYFwiIoXaW9aT3ZqZYAHcfbaZ/S/AmEREChUZXRDvKApX0NoFJ0WfLjCzJ4FXiQzd6kx0zKyISNzEOMkg3gpqyT6Z6/VJ2Z5rnKyIxF0IcmyBaxe0KslARESKKuwt2Sxmdg5QH9g/c5u73xdUUCIihQlLn2wsQ7ieAnoTmatbFugB7LNrF7w/cwYN6tflhONr89CD9++2/4fvv6dd2zNp3rQRzRo1YMZ7kWnQ8z77lGaNGtDy1GasWxtZzCwlJYWO57UjMh1679O3axsWTxrIksl30q9bGwBOrFOV2S/ezKLXBzL5kas58ID983zv04O7892sESyeNDDH9ntu6MTCiXfw7PCeWdu6nteUvl3bBHUYcfX+zBmcVP846tetxcg8zre5cz7h1KaNKL9/Em++MTlr+1erV3Nas8Y0a9SA+fPmAZCens6555xNampqicUftOJcICYoscz4aunu3YBf3P1uIovFVAs2rNIpIyODm27sx9tTp7N0+UomTXyNL1etylHm/hH3cNHFlzB/0VJefOlV+t/QF4BHRz/MKxMnM3T4vTwzbkyk7H3DufX2O0Lxk6eo6tWszOUXnUarniNp1nkE7VufQM0alRgzqBt3PfYOTS+9jykfLeem3mfl+f4JU+fTqW/OywIVyu9P8wbH0KzzCBITEqhfqwr775dMz/ObM3bSJyVxWCUqIyOD/jf05Z2p7/H5F6uY9Nqru51v1avXYNxzL9C5S84V+Z59ZizD77ufVyZO5pHRDwEw7ukxdOvek3LlypXYMQQtDONkY0mymTO8tpnZkcA24OjAIirFFi9aSM2atTjm2GMpU6YMF1/amXenvpOjjJnxx++/A/D771uoXLkKAMnJyaSlpZGamkpyUjJfr1vHxg0badX69BI/jpJQ95gjWbjiW9K27SQjYxdzlqyl0xkNqH3U4cxdEmnJ/2v+f7jgrIZ5vv/Tpev4dUvOFteuXU6Z5EgPV9n9ktmZnsFNvc/iqddmk56+K9gDioNFC3Oeb5d07rLb+XbU0Udz4kknkZCQ859yjvMtOZmUlBSmT5tK9569SvIQAmVW/HerDUIsfbLvmdnBwEPAMiADeDHQqEqpjRs2ULXan434qlWrsWjRghxl7rx7CB3PPYcxTz1B6tatvDvjAwBuuW0A/a67mrJly/Ls8/9k4O23MmjIsBKNvyStXLeRIf3O59CDDiBt+w7atazP0lXfs2rdJjq0OZF3Z6/goraNqHbEITHX+b/U7bw9axnzXxvA7IWr+f1/aTSudxQjxs0I8EjiZ+PGDVSr9udC/VWrVmPhwgUFvONPV1/bl6su78X27dt54qmx3HfPMG6/48697ldTGI4nlrULhkSfTjKzd4n0yx4TS+XRlu8jQFMiC8t8CwwBHgUqEEnY97r7xGj52UBlIq3l/wFXuPvqWA8maHn1neb+nzxp4qv06NWbG2+6mQXz53HVZb1YvGwFDRo25OO5kb6xuXM+oXKVyrg7Pbt1ITk5mREPPsQRRxxRIsdRElZ/819GvfAB747px9a07Xzx1QbS0zO4esjLjLrtYu74e3umfbyCHTszilTvwy9+yMMvfgjAU4O6MXzMNC678FTObn48K9Zs4IFnZwZxOHERy/mWnxo1avD+rNkArFu7lk2bNlLnuLpc0bsnO3buYPCQ4dSuU6c4w42LEOTYot2t1t3T3P1XIqtxFcgiZ8NbwGx3r+nu9YCBRJJ0L3evD7QDHom2lDN1d/cGRFrLI4sSX9CqVqvGhvXrs15v2LA+qzsg04vPj+f/Lr4UgFOan8q27dvYvHlz1n5354ER9zJg4N3cd88w7ho0hC7duvPUE4+VzEGUoBffnsdp3R6g7ZWP8NuWraz9/me++va/nH/dk7To/iCvz1jCN+t/3qO6GxwX+UWx5ruf6N7hFHrcPp76tapQs0al4jyEuKpatRrr1/9589QNG9ZTpUqVAt6Rt8GD7mTwkOE89cRjdOnWnbsHDeXe4UOLM9S4MIwEi/0RL3t6S/BYIj6DyILfT2ducPdl7v6xu6+Jvt4I/ATk9S/jE0rZKIbGTZqydu0avv3mG3bs2MHk1ydyXoeOOcpUq1GDjz6K3B7tP19+ybZt26hU6c/De2nCi7Rrfy6HHHIIqampJCQkkJCQQFra3nPFN1OlQ8oDUP3IQ+h0ZgNen7E4a5uZMeDv5/DM5Ll7VPeg6zowfMw0kpMSs/rbdu1yyu1fpniCLwWaNM15vk2a+Npu51th5nzyMVWqVKVW7dpZ51tiYiKpe8P5ZpGWbKyPeIlpnGweYhlzdAKwpKACZtYMKAOsy2P3+UTuLVZqJCUl8fAjj9PxvHZk7MqgV+/LqVe/PsOGDKJR4yZ0OL8j9z/wEH2v7cMTjz4CZox79vmsn3ipqam8POGfTJ0e+Ul7Q/+b6Nb5YpLLlOHFCa/E89AC8epDV3HowQewMz2D/ve/TsofafTt2oarO7cG4J1/LeOf70TWf69c6SCeGtSNC6+PjLx4ccRltGpcm4oHl2ftjOEMf3o6L74d6W45v81JLFn5HZt+3gLAgi++ZdHrA/n3mg2s+GpDHI40GElJSYx+9AnOP+8cMjIy6H3ZFbudb4sXLaLzJReS8ttvTJ82lXuGDWbp8pVA5FfT/ffdw0uvvg7AlVf14fJe3UlPT+fRJ8bE89CKTRj6ZC2/MZrRmyXmtdOAv7n7AQVWbHYDcIy735TP/srAbKC3u8+PbptNpE82jUj/7fXu/kOu9/UB+gBUr1Gj8eq13xYUhuTh0GbXxzuE0Ppt0RPxDiG0yibbEndvUlz1HV7rBO88clLM5Z+4qF6xfn6sCmrJFnQ2xXKmrQQuzmuHmVUApgF3ZSbYbLq7++L8KnX3ccA4gEaNm+ydo/hFpFBGOFqyBa1dMOsv1v0v4D4z+7u7PwNgZk2BcsAg4J/uHvufIRGRXMIwrXZP+2QL5e5uZhcSGT0wgMiwrG+J3ISxNXCYmV0WLX6Zuy8LKhYR2Tvt00kWskYPXJrHruH5lG8TZDwisveIjBoo/Vk25iRrZvu5+/YggxERKYowtGRjWYWrmZmtANZEXzcws8cDj0xEpABGONYuiGUywmNAB+AXAHdfTmSigYhIXCUU4REvsXQXJLj7d7n6Poo24VxEJAAh6JKNKcn+EJ2Z5WaWCFwPfBVsWCIiBbM4r0kQq1iS7LVEugxqAP8FPoxuExGJqxDk2JiWOvwJ6FICsYiIFEkYRhcUmmTN7BnyWMPA3fsEEpGISAwiN1Is/Vk2lu6CD7M93x+4EPghn7IiIiUmBDk2pu6Cidlfm9kE4IPAIhIRiYXtJd0FeTgGOKq4AxERKSqL631oYxNLn+xv/NknmwD8CgwIMigRkcJE+mTjHUXhCkyy0ft0NQAyl5vf5fmt8i0iUsLiOV02VgXONosm1LfcPSP6UIIVkVIhsyUb6yNeYpnSu9DMGgUeiYhIUQRwI0Uza2dmq81sbXQd7PzKXWxmbmaF3s4m3+4CM0ty93SgJfB3M1sHbI0cGu7uSrwiElfFOU42umzAk0BbYD2wyMymuPuqXOUOBG4AFsRSb0F9sguBRsAFexSxiEiAArjw1QxY6+5fA5jZa0AnYFWucsOBB4FbYqm0oCRrAO6e1+26RUTirogN2Ypmlv0mreOiN2bNVJWcE63WA6fk/Dw7Gaju7u+a2V9OspXM7B/57XT3h2P5ABGRYBgJRRsnu7mQW4LnVVnWxX4zSwBGA5cV5UMLSrKJQPl8PlhEJK4itwQv1irXA9Wzva4GbMz2+kDgBGB2dH3tI4EpZtbR3bO3kHMoKMlucvdhex6viEiAin9o1iKgtpkdQ2RuQBegW+ZOd98CVMz6eLPZwC0FJViIoU9WRKS0Ks7RBe6ebmb9gJlEfsmPd/eVZjYMWOzuU/ak3oKS7Fl7UqGISEkIoLsAd58OTM+1bVA+ZdvEUme+Sdbdfy1KcCIiJS0M02r3ZBUuEZG4M+J7F9pYKcmKSDhZ5GaKpZ2SrIiEVulPsUqyIhJSe9M9vkRESqXSn2KVZEUkxELQkFWSFZGwMl34EhEJioZwiYgETC3ZEqC7jhXdv2eOjHcIoXVol/HxDkGyKf0pdi9IsiKyj9JkBBGR4BiQqCQrIhKc0p9ilWRFJMRC0JBVkhWRcIoM4Sr9WVZJVkRCSy1ZEZHAGKaWrIhIcNSSFREJiPpkRUSCZGrJiogESklWRCRAuvAlIhIQTasVEQlYCHKskqyIhJe6C0REAhK5W228oyickqyIhJRmfImIBEfjZEVEghWCHKskKyLhFOmTLf1pVklWREKr9KdYJVkRCbMQZFklWREJLY0uEBEJkMbJiogESUlWRCQYhroLRESCo8kIIiLBCkGOJSHeAYiI7DErwiOW6szamdlqM1trZgPy2P8PM1tlZl+Y2SwzO6qwOpVkRSSkrEj/FVqbWSLwJNAeqAd0NbN6uYp9DjRx95OAycCDhdWrJCsioWUW+yMGzYC17v61u+8AXgM6ZS/g7h+5e2r05XygWmGVKsmKSCgVpacgmmMrmtnibI8+uaqsCvyQ7fX66Lb8XAm8V1icSrJF9P7MGTQ8oS4nHl+bh0bev9v+77/7jnPPOZtmjRvQru0ZbFi/HoCvVq+mRfMmnNKkIQvmzwMgPT2d89q1JTU1dbd6wm7AjVfTrN5RtG/dJGvb6PuHcl6bZpx/5in0vvR8/vvjxnzf/8cfv9OiQU2G3HETANu3b+fyLh1p37oJLz0/NqvcnTf3ZeWKZcEdSBz061CfxaMvZNHDF/JC/zbsl5zI2L6tWPXkJcwf2Yn5Iztx0tGH7va+1vWPzNo/f2Qnfn2lF+c3rQHA+BtPZ8GoCxjarXFW+QEXN6BDdH9oFS3Lbnb3Jtke4/KoLTfP82PNegBNgJGFhagkWwQZGRn848Z+vDVlOkuWr2TSxNf48stVOcoMHHAr3Xr0ZOGS5QwYeDeD7h4IwHPPjmXYPSN4+dVJPDp6FADPjB1D1+49KFeuXIkfS9Au6tKT8a+9nWPbVX1vYtrshUz91wLObNueJ0aNyPf9j9w/jGantsp6PeejDzjhpJOZNnshEyeMB+DLlV+wa9cu6p/YMJiDiIMqh5bjuvb1aHn7FJr+4y0SE4xLWhwDwMAJi2h+6zs0v/Udvvj2193e+8nKH7P2tx/6HqnbM/hw+QZOOOoQAE65+W1OO/4IKpRL5siDy9KkViXeXfR9iR5fcSvOPlkiLdfq2V5XA3ZrCZjZ2cCdQEd3315YpUqyRbB40UKOrVmLY449ljJlynDxpZ15d+o7Ocr858tVnHHGWQCc3uYMpkX3Jycnsy0tjdS0VJKTk0lJSWH6tHfp3qNXiR9HSWh2aksOPjhna+vAAytkPU9N3Yrl01H27+VL2fzzT7Rsc1bWtuTkZLZt20Z6enrWttH3D6P/7XcXc+Txl5RolC2TSGKCUW6/RDb9VvRfOhc2P4b3l60nbUcGO9N3UbZMImZQJimRjF3O3V0aMXzi0gCiL1nF3Ce7CKhtZseYWRmgCzAl5+fZycBYIgn2p1gqVZItgo0bN1Ct+p/93FWrVmPThg05ypxwUgPefusNAKa88xZ//PEHv/zyC1df05fHHxvNjf2u5Zbb72DEvcO4bcDAfBPN3mrUfYNpeXJtprwxkRtv2z1B7tq1i/uG3MHtg+/Lsb3F6Wex+af/8n/tW/P3vv/gwxnvckKDkzniyColFXqJ2PhrKo9M+Terx3Tm62e6sCV1J7OWRxpTQ7o2ZsGoC3jgsmaUSSr4n+7FLY5h0tyvAVi9YQs//LyVeQ924o3PvqHmkRUwYPk3u7eGQ6UICTaWf2bung70A2YCXwKvu/tKMxtmZh2jxUYC5YFJZrbMzKbkU12WwCYjmFkGsCL6GV8CvYHDgH8CRwK7gHHu/mi299wCXAWkAxnAKHf/Z1AxFpX77t0zuZPkiPtH8o/+1/PShBdp0bIVVapWJSkpieo1ajDjg48AWLd2LT9u2kSd4+py5eW92LljB3cPHkbtOnVK5Dji6eaBQ7l54FDGPDqSCeOfpn+uRPvwirOYAAANzklEQVTS82Npc9Y5VKma86JtUlISo59+AYCdO3dyeeeOjJ0wiXsH3c7GDT9w4SXdOLtdh5I6jMAcfEAZOjStQb2+k0jZup2Xbz6TLq1qMvjlxfyYkkaZpASeuKYFN19wEiMm590XfeTBZalf4xA+WLY+a9ttLyzIej55wNlcP/ZTbruoAScefSj/+mIDz3/4VeDHFoTinlbr7tOB6bm2Dcr2/Oyi1hlkSzbN3Ru6+wnADuAaIsnzZnc/HmgO9M0ch2Zm1wBtgWbR97SmlE3oqFq1Gut/+PPE3bBhPUdWydmSqlylCq++/gbzFi5lyLB7ATjooINylBk6+C7uHjyMMU8+Rucu3bjz7iHcd++w4A+gFOl4UWdmvvvObtuXLV7IhPFPc3qTutw/dCBvvf4KDw7PmYhffn4cF3XuzueLF5CcXIbHxk3gqdEPlFTogTrjpCp899P/2Pz7NtIznHcWfEfz4w7nx5Q0AHak72LCR2toUrtivnVcdNoxTF34PekZuzcKOjStwdJ1mym3XzL1ahxCz4c/omvrWpQtkxjYMQXFKPbugkCUVHfBHKCWu29y96UA7v4HkRZu5hCJgcB17v57dP8Wd3+xhOKLSeMmTVm3dg3ffvMNO3bsYPLrEzmvQ8ccZTZv3syuXbsAeOjBEfTqfXmO/XM++ZjKVapQq3ZtUlNTSUhIIDExkbS9cIRBbt9+vTbr+ayZ0zi29u4t94fHPM+cpV/x8eL/MGDwfVx4aTduu3t41v4tKb/x0QfvceGl3UlLTSUhwTAztm8v9PpDKKzfvJWmdSplJb02J1bmPxtSOPLgslllzm96FCu/T8m3jktbHsvrc9fttj0p0bju3HqMfmcF5fZLyvpllmBGmaTwJVko9glfgQh87QIzSyIyg2JGru1HAycDC8zsQOBAd9/9zNi9vj5AH4DqNUp2+ElSUhKjHnmcTh3akZGRQa/LLqdevfoMHzqIRo2acN75HZnzyWwG3xXpa23RqhWjH30y6/3uzgMj7mXCKxMBuOLKPlxxWQ/S09N59PGnSvRYgtb/6t4s+OwTfvv1F1o0rMWNt97Fx7Nm8vXaNSQkJFClWnWGj3wMgBXLlvDKi88yYvSYQut9fNQIrrvpdsyM1me05aXnx3Fum6Z063VV0IdUIhat+Zm3533LZyM7kZ7hLP/mF8Z/sJq37/wbFSvsj5nxxbe/cMO4zwBoVPMwrmpbl+ue/hSAGpXKU+2wA5iz6sfd6r663fG8PHstaTsyWPHdr5gZC0ddwMzP17MldUdJHmbxKVW/dfNmefUzFkvFf/bJQqQle3N0FgVmVh74GLjX3d80swrAt+6+++C/AjRq3MTnzltUnGHvEzalbIt3CKF14nWvxjuE0Ep748ol7t6k8JKxOaFBI588Y27M5Y+vckCxfn6sgmzJprn7bgMYzSwZeAN42d3fBHD3381sq5kd6+5fBxiTiOxFwjA4p0SHcFnkUvxzwJfu/nCu3SOAJ6OtWsysQh7T3kREsqhPdnctgJ7ACjPLHH8yMDpsYgyR8WeLzGwnsBMYVcLxiUiYhKAlG1iSdffyeWybSz5fi0c6hx8khqXDRER0+xkRkSDp9jMiIsFSkhURCUzMq2vFlZKsiISWWrIiIgGJ99CsWCnJikh4hSDLKsmKSGipT1ZEJEDqkxURCVAIcqySrIiElCYjiIgErfRnWSVZEQmlzNvPlHZKsiISWglKsiIiwdEQLhGRIJX+HKskKyLhFYIcqyQrIuFkGsIlIhIs9cmKiASp9OdYJVkRCa8Q5FglWREJL/XJiogERrefEREJTFim1SbEOwARkb2ZWrIiEloJIWjKKsmKSDhpMoKISHB0t1oRkaCFIMsqyYpIaGkIl4hIgNQnKyISoBDkWCVZEQmxEGRZJVkRCa0w9Mmau8c7hj1mZj8D38U7jgJUBDbHO4gQ0ve2Z0r793aUu1cqrsrMbAaRY47VZndvV1yfH6tQJ9nSzswWu3uTeMcRNvre9oy+t9JJaxeIiARISVZEJEBKssEaF+8AQkrf257R91YKqU9WRCRAasmKiARISVZEJEBKslLqmYVhhrpI3pRkS5CSRezM7Cgzq2Rm5dzdzUznaozMrLGZnWNm+8c7FlGSDZSZtTaznmbWCyCaLJRoC2Fm7YBpwGhgiplVdPddcQ4rFMysPfAycBRwfJzDEZRkAxNNFGOAY4HBZjYWIok2roGVcmbWFngQ6Av0B74ERpmZ1tkohJm1AB4D+rj7OHf/PN4xiZJsIMysKfAUcLO7DwVOBU41M015zIdFlAVuBha5+8fuvhmYDGxx9/T4RhgKjYDn3f2TzO4V/XKKPyXZYFQBVgGJZnaou/9EpEW2Pb5hlV4ekQbcClQxs1uiuzoA5ZUsYrIdOCj6PDH7DjM7U3208aGfYMXIzPZ3923u/o6ZlQG6AGXM7HRgPyKJV3Ixs5OJ/GH63t1XRBPsI9EuFwfaZV78Ut9sTma2n7tn/vHeDPQ3s2HuvtXMyrj7jui+ZsBOYE5cAt2HqSVbTKIJYbSZjTWzeu4+CZgJXAG0BK529wwzSyywon1M9ELN68C5wHtm1t7dVwLXAzuAWdHvTQk2l+g594iZjYmec28C84FPzeyAzARrZj2BbsC38Yt236VptcUg82QHbgIuAvYHekVbX52Ai4FJwEJ3/zF+kZYuZnYcMAX4e7QfsRdwB3Cqu6eYWT1gFLAQGOXuv8cx3FIlj3OurLv3iO57jkjL9VNgK5Eul4uif7ykhCnJ/gXRfsIKwHhgoru/Ht0+C3jZ3cdHX3cl0nXwEvCGWmRgZo2IdKFUdvc3M1uqZjYV6OHuW6LlTgSGEknEv8Qx5FKhkHPuJXd/Pvq6A1CeyB/8Oe6+Lk4h7/PUJ/vXJLn7FjO7HVifrX9sMVA2s5C7v2pmqUSumivBmp0H3A88AMwDyPa9VACOBLaYWa1oH22XbH2L+7qCzrlymYXc/d24RSg5KMnuoeh4zivM7HNgjbuvzbb7B+DAaLmOwI/u/k4cwix1ohcBHyXSWp2fbXsZIJ3IH6dUM+sCXGNmnTJbtfu6IpxzHYBf3f0zMzONzY4vXfjaA9H+sHuBz4ic2B3N7JRsRRIjxexCIgPrfy75KEutxsDj7j4/c4JBNBHsiLZm5xHpl+0L3KAEG1HEc+4hYCNo8ktpoJZsEZnZocB0oJO7TzWz6kRO6srZiv1A5KLEWuD/3P2bko+0dMnWojoGyEycGfBnIjCzakQG1NcHmrv7V/GItbTZw3Pu2xIPVPKklmwRufuvwPnA/WZWwd1/IDL+8IhsxTYQGd/ZT1d0I7K1qN4CmptZ48yxr9kWf7k4ur+xEuyfdM6Fm1qye8Ddp5nZLmCJmc0kcsHhRci6+rsMaODuKXEMs7RaAMwFOpsZ7r4EINoHeynQ1d1L823e40LnXHhpCNdfYGZnA+8DR7r7T2ZWNjo1VApgZlWBK4GzgEXANiKt2EvcfUU8YyvtdM6Fj5LsXxSdsfQQcEZ0jQKJQXQxmMbA2cAm4CN1EcRG51y4KMkWg+isrsFAE6JrncQ5JNnL6ZwLDyXZYmJm5d39f/GOQ/YdOufCQUlWRCRAGsIlIhIgJVkRkQApyYqIBEhJVkQkQEqy+zAzyzCzZWb2bzObZGblCn9XvnW1MbN3o887mtmAAsoebGbX7cFnDMl2769CtxdQT5GuyBe1fpHslGT3bWnu3tDdTyByq5drsu+0iCKfI+4+xd3vL6DIwUCRk6xIGCnJSqY5QC0zO9rMvjSzp4ClQHUz+5uZzTOzpdEWb3mILL9nZv8xs7lEboFCdPtlZvZE9PkRZvaWmS2PPk4jsmB3zWgremS03K1mtsjMvjCzodnqutPMVpvZh8BxRTkgM3vbzJaY2Uoz65Nr36jo8cwys0rRbTXNbEb0PXPMrO4efI8iOSjJCtF1XdsDmesGHAf8091PJnKPqLuAs929EZEV+P9hkdtLP0NkdahWRO5mkJfHgI/dvQGRZQxXAgOAddFW9K1m9jegNpH7UjUEGptZazNrTOS2PScTSeJNi3hoV7h7YyKzom4ws8Oi2w8AlkaP52MiM6cAxgHXR99zC/BUET9PZDdahWvfVtbMlkWfzwGeI3Jr7u+y3bWgOVCPyB1QAcoQWVi7LvCNu68BMLOXgBytxagzgV4A7p5B5LYyh+Qq87fo4/Po6/JEku6BwFvunhr9jClFPL4bootYA1SP1vkLsAuYGN3+EvBmtHV+GjApepwQuQeZyF+iJLtvS3P3htk3RBPM1uybgA/cvWuucg2JrF9aHAwY4e5jc31G/z39DDNrQ2TxmVPdPdXMZhO5qWBenMivupTc34fIX6XuAinMfKCFmdUCMLNyZlYH+A9wjJnVjJbrms/7ZwHXRt+baGYVgD+I3o8qaiaRe1dl9vVWNbPDgU+AC82srJkdSKRrIlYHAb9FE2xdIi3yTAlEllYE6AbMjd5u/BszuyQag5lZgyJ8nkielGSlQO7+M3AZ8KqZfUEk6dZ1921EugemRS985bfQ9o3AGWa2AlgC1I/e2vvT6NCxke7+PvAKMC9abjJwoLsvJfKzfhnwBpEujfzcZWbrMx/ADCApGvPwaNyZtgL1zWwJke6MYdHt3YErzWw5kb7jTrF+TyL50QIxIiIBUktWRCRASrIiIgFSkhURCZCSrIhIgJRkRUQCpCQrIhIgJVkRkQD9P/crfdMVNmdOAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Compute confusion matrix\n", "cnf_matrix = confusion_matrix(y_test, y_test_prediction)\n", "np.set_printoptions(precision=2)\n", "\n", "# Plot non-normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names,\n", "                      title='Confusion matrix, without normalization',\n", "                      fname=RESULTS_PATH + ToR_MODEL_NAME + \"_\" + 'Confusion_matrix_without_normalization')\n", "\n", "# Plot normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names, normalize=True,\n", "                      title='Normalized Confusion Msatrix',\n", "                      fname=RESULTS_PATH +ToR_MODEL_NAME + \"_\" + 'Normalized_confusion_matrix')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["# Export the model to a file"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-16T12:50:04.937659Z", "start_time": "2018-11-16T12:50:04.873865Z"}, "hidden": true}, "outputs": [], "source": ["model_json = pairs_model.to_json()\n", "with open(MODELS_PATH + ToR_MODEL_NAME + '.json', \"w\") as json_file:\n", "    json_file.write(model_json)\n", "pairs_model.save_weights(MODELS_PATH + ToR_MODEL_NAME + '.h5')\n", "print(\"Save Model\")"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["# Export results to a csv file (with original ASNs)\n", "## Define functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:53:31.858073Z", "start_time": "2018-10-20T15:53:31.831311Z"}, "hidden": true}, "outputs": [], "source": ["def index2ASN(dataset_indexed, ASN_index_map):\n", "    dataset = []\n", "    index_ASN_map = {index: ASN for ASN, index in ASN_index_map.iteritems()}\n", "    for row_indexed in dataset_indexed:\n", "        row = []\n", "        for index in row_indexed:\n", "            if index != 0:\n", "                row += [index_ASN_map[index]]\n", "        dataset.append(row)\n", "    \n", "    return dataset\n", "\n", "def index2ASN_labeled(dataset_indexed, labels_indexed, ASN_index_map):\n", "    dataset = []\n", "    index_ASN_map = {index: ASN for ASN, index in ASN_index_map.iteritems()}\n", "    labels_colors_map = {0:'GREEN', 1:'RED'}\n", "    \n", "    for i, row_indexed in enumerate(dataset_indexed):\n", "        row = []\n", "        for index in row_indexed:\n", "            if index != 0:\n", "                row += [index_ASN_map[index]]\n", "        row += [labels_colors_map[labels_indexed[i]]]\n", "        dataset.append(row)\n", "    \n", "    return dataset\n", "\n", "import csv\n", "def export_csv(dataset, csv_name):\n", "    with open(csv_name + '.csv', 'wb') as csv_file:\n", "        csv_writer = csv.writer(csv_file)\n", "        for row in dataset:\n", "            csv_writer.writerow(row)"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true, "hidden": true}, "source": ["## Load a relevant dataset {all, misclassified, decided, undecided} and get model predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:35:46.412152Z", "start_time": "2018-10-20T15:30:04.317489Z"}, "hidden": true}, "outputs": [], "source": ["### misclassified from the entire dataset ###\n", "\n", "dataset = np.load(DATA_PATH + \"bgp_routes_indexed_dataset.npy\")\n", "labels = np.load(DATA_PATH + \"bgp_routes_labels.npy\")\n", "\n", "# remove UNDECIDED\n", "dataset = np.asarray([np.asarray(dataset[i]) for i in range(len(dataset)) if labels[i] != 2])\n", "labels = np.asarray([labels[i] for i in range(len(labels)) if labels[i] != 2])\n", "\n", "# pad sequences\n", "dataset = sequence.pad_sequences(dataset, maxlen=max_len)\n", "# Get Model Predictions\n", "predictions = model.predict_classes(dataset, verbose=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:54:32.435559Z", "start_time": "2018-10-20T15:54:28.652217Z"}, "hidden": true}, "outputs": [], "source": ["# Create misclassified dataset\n", "x_misclassified = np.asarray([route for i,route in enumerate(dataset) if labels[i] != predictions[i]])\n", "y_misclassified_prediction = np.asarray([label for i,label in enumerate(predictions) if labels[i] != predictions[i]])\n", "print len(x_misclassified), len(y_misclassified_prediction)"]}, {"cell_type": "markdown", "metadata": {"hidden": true}, "source": ["## Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:54:39.339072Z", "start_time": "2018-10-20T15:54:39.169037Z"}, "hidden": true}, "outputs": [], "source": ["dataset_misclassified = index2ASN_labeled(x_misclassified, y_misclassified_prediction, ASN_index_map)\n", "export_csv(dataset_misclassified, RESULTS_PATH + MODEL_NAME + \"_misclassified\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 2}