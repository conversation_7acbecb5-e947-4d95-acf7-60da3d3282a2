{"cells": [{"cell_type": "markdown", "metadata": {"id": "C0QLeiS8rTZg"}, "source": ["# Define parameters and load ASN index map and bgp_routes"]}, {"cell_type": "code", "execution_count": 88, "metadata": {"ExecuteTime": {"end_time": "2019-05-04T16:40:53.445212Z", "start_time": "2019-05-04T16:40:47.664864Z"}, "id": "lVtoAjF_rTZh"}, "outputs": [], "source": ["import numpy as np\n", "\n", "TOR_ORIG_LABELS_DICT = {'P2P':0, 'C2P': 1,'Siblings': 2, 'P2C': 3}\n", "TOR_CSV_LABELS_DICT = {'P2P':0,'P2C': -1}\n", "DATA_PATH = ''"]}, {"cell_type": "markdown", "metadata": {"id": "OSJ51XmUrTZi"}, "source": ["# Create a list of all pairs and inverse_pairs in the bgp_routes dataset"]}, {"cell_type": "markdown", "metadata": {"id": "forZEqYmrTZi"}, "source": ["# Import tors.csv and generate ToR dataset"]}, {"cell_type": "code", "execution_count": 89, "metadata": {"ExecuteTime": {"end_time": "2019-05-04T16:41:15.290482Z", "start_time": "2019-05-04T16:41:12.671008Z"}, "id": "u0xhH1CArTZi", "outputId": "a9d21547-abf8-407d-e999-ac6924bf12d3", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["861840 861840\n"]}], "source": ["import csv\n", "\n", "ToR_CSV = '20180301.as-rel2.txt'\n", "\n", "tor_dataset = []\n", "tor_labels = []\n", "\n", "with open(DATA_PATH + ToR_CSV, 'r') as csv_file:\n", "    reader = csv.reader(csv_file,delimiter='|')\n", "    for i, row in enumerate(reader):\n", "      if row[0][0] != '#' and int(row[2]) in TOR_CSV_LABELS_DICT.values():\n", "        tor_dataset.append(np.asarray(row[:2]))\n", "        tor_dataset.append(np.asarray(row[1::-1]))\n", "        tor_labels += [int(row[2])%4, abs(int(row[2]))]\n", "\n", "print(len(tor_dataset), len(tor_labels))"]}, {"cell_type": "markdown", "metadata": {"id": "kWmj8OumrTZi"}, "source": ["## Optional: Remove all pairs that are not in the Routeview"]}, {"cell_type": "markdown", "metadata": {"id": "lOCO8dXzrTZi"}, "source": ["## Count number of Tor of each kind"]}, {"cell_type": "code", "execution_count": 90, "metadata": {"ExecuteTime": {"end_time": "2019-05-04T16:45:24.803917Z", "start_time": "2019-05-04T16:45:24.791983Z"}, "id": "IBJekecWrTZj", "outputId": "c2b2d39e-0fad-4935-caeb-724ab03506ac", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Counter({0: 619648, 3: 121096, 1: 121096})\n"]}], "source": ["from collections import Counter\n", "\n", "c = Counter(tor_labels)\n", "\n", "print(c)"]}, {"cell_type": "markdown", "metadata": {"id": "Z0zpfQRLrTZj"}, "source": ["# Export ToRs to np files"]}, {"cell_type": "code", "execution_count": 91, "metadata": {"ExecuteTime": {"end_time": "2019-05-04T16:47:18.855619Z", "start_time": "2019-05-04T16:47:18.748739Z"}, "id": "1a98n4SprTZj"}, "outputs": [], "source": ["def export_dataset(dataset_dict, data_path):\n", "    # with open(file_path + \".pkl\", 'wb') as outfile:\n", "    #     pickle.dump(dataset_list, outfile, pickle.HIGHEST_PROTOCOL)\n", "    for name, array in dataset_dict.items():\n", "        np.save(data_path + \"_\" + name, array)\n", "\n", "dataset_dict = dict()\n", "\n", "dataset_dict[\"dataset\"] = np.asarray(tor_dataset)\n", "dataset_dict[\"labels\"] = np.asarray(tor_labels)\n", "\n", "DATA = 'caida_s1_tor'\n", "export_dataset(dataset_dict, DATA_PATH + DATA)"]}, {"cell_type": "code", "source": ["dataset = np.load(DATA_PATH + DATA + \"_dataset.npy\")\n", "labels = np.load(DATA_PATH + DATA + \"_labels.npy\")\n", "\n", "print(dataset.shape, labels.shape)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2TxDxziMzxXL", "outputId": "d0dce8ad-6ed4-4e3c-9679-bb01a34a3c8a"}, "execution_count": 92, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["(861840, 2) (861840,)\n"]}]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "position": {"height": "144px", "left": "1311px", "right": "20px", "top": "152px", "width": "350px"}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}