#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
本脚本用于对评估数据集（CSV格式）应用与MSLSTM训练时相同的小波变换预处理。

工作流程:
1. 从原始的 `feature_extraction.py` 模块导入所需的小波变换函数 (`Multi_Scale_Wavelet0`)。
2. 定义原始评估数据（CSV）的输入目录和变换后数据的输出目录。
3. 遍历输入目录中的所有CSV文件。
4. 对每个文件：
    a. 读取数据。
    b. 提取特征列。
    c. 对特征应用小波变换。
    d. 将变换后的特征与原始的时间戳列（'index'）重新组合。
    e. 将结果保存到输出目录中，文件名保持不变。
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
import pywt # 直接导入 pywt

# --- 将 feature_extraction.py 中的核心函数直接复制到此处 ---

def Multi_Scale_Wavelet0(trainX, level, is_multi=True, wave_type='db1'):
    """
    从 feature_extraction.py 复制而来的小波变换函数。
    """
    temp = [[] for i in range(level)]
    N = trainX.shape[0]
    if (is_multi == True) and (level > 1):
        for i in range(level):
            x = []
            for _feature in range(len(trainX[0])):
                coeffs = pywt.wavedec(trainX[:,_feature], wave_type, level=level)
                current_level = level  - i
                for j in range(i+1,level+1):
                    coeffs[j] = None
                _rec = pywt.waverec(coeffs, wave_type)
                x.append(_rec[:N])

            temp[current_level - 1].extend(np.transpose(np.array(x)))

    else:
        for tab in range(level):
            current_level = level - tab
            temp[current_level - 1].extend(trainX)
    
    # 移除原始的打印语句
    # print((np.array(temp)).shape)

    return  np.array(temp), trainX

# --- 脚本原有代码继续 ---

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- 主要配置 ---
SOURCE_DIR = '/data/five_month/features'
TARGET_DIR = '/data/five_month/features_wavelet'
WAVELET_LEVEL = 2 # 这个值需要根据原始训练配置来确定，我们先假设为2

def main():
    """主执行函数"""
    logger.info("--- 开始对评估数据进行小波变换 ---")

    if not os.path.isdir(SOURCE_DIR):
        logger.error(f"错误：源目录 '{SOURCE_DIR}' 不存在。")
        return

    os.makedirs(TARGET_DIR, exist_ok=True)
    logger.info(f"变换后的数据将保存到: '{TARGET_DIR}'")

    source_files = sorted([f for f in os.listdir(SOURCE_DIR) if f.endswith('.csv')])
    if not source_files:
        logger.warning(f"在源目录 '{SOURCE_DIR}' 中没有找到任何 .csv 文件。")
        return

    logger.info(f"找到 {len(source_files)} 个文件需要处理。")

    total_files = len(source_files)
    for i, filename in enumerate(source_files):
        source_path = os.path.join(SOURCE_DIR, filename)
        target_path = os.path.join(TARGET_DIR, filename)

        logger.info(f"({i+1}/{total_files}) 正在处理: {filename}")

        try:
            # 1. 读取数据
            df = pd.read_csv(source_path)

            if df.empty:
                logger.warning(f"  - 文件为空，跳过。")
                # 创建一个空的同名文件在目标目录，以示处理过
                open(target_path, 'a').close()
                continue
            
            # 2. 提取特征和时间戳
            timestamps = df['index']
            features = df.iloc[:, 1:].values.astype(np.float32)

            # 3. 应用小波变换
            # Multi_Scale_Wavelet0 返回一个元组 (wavelet_features, original_features)
            # 我们需要的是第一个元素
            wavelet_features_scaled, _ = Multi_Scale_Wavelet0(features, level=WAVELET_LEVEL)
            
            # 原始实现似乎总是取第一个尺度的结果
            processed_features = wavelet_features_scaled[0]

            # 确保特征维度匹配 (原始代码中会填充到26维，我们也需要这样做)
            EXPECTED_DIM = 26
            current_dim = processed_features.shape[1]
            if current_dim < EXPECTED_DIM:
                padding = np.zeros((processed_features.shape[0], EXPECTED_DIM - current_dim))
                processed_features = np.hstack((processed_features, padding))
            elif current_dim > EXPECTED_DIM:
                processed_features = processed_features[:, :EXPECTED_DIM]


            # 4. 重建 DataFrame
            # 创建新的列名
            feature_columns = [f'feature_{j}' for j in range(processed_features.shape[1])]
            new_df = pd.DataFrame(processed_features, columns=feature_columns)
            new_df.insert(0, 'index', timestamps)

            # 5. 保存到目标目录
            new_df.to_csv(target_path, index=False)
            logger.info(f"  - 成功转换并保存到 {target_path}")

        except Exception as e:
            logger.error(f"  - 处理文件 {filename} 时发生错误: {e}", exc_info=True)

    logger.info("--- 所有文件处理完毕 ---")

if __name__ == "__main__":
    main() 