class_weights: !!python/object/apply:torch._utils._rebuild_tensor_v2
- !!python/object/apply:torch.storage._load_from_bytes
  - !!binary |
    gAKKCmz8nEb5IGqoUBkugAJN6QMugAJ9cQAoWBAAAABwcm90b2NvbF92ZXJzaW9ucQFN6QNYDQAA
    AGxpdHRsZV9lbmRpYW5xAohYCgAAAHR5cGVfc2l6ZXNxA31xBChYBQAAAHNob3J0cQVLAlgDAAAA
    aW50cQZLBFgEAAAAbG9uZ3EHSwR1dS6AAihYBwAAAHN0b3JhZ2VxAGN0b3JjaApGbG9hdFN0b3Jh
    Z2UKcQFYDwAAADE4NzY1MDk1ODA0MTQ0MHECWAMAAABjcHVxA0sCTnRxBFEugAJdcQBYDwAAADE4
    NzY1MDk1ODA0MTQ0MHEBYS4CAAAAAAAAAAAAgD9GdhhC
- 0
- !!python/tuple
  - 2
- !!python/tuple
  - 1
- false
- !!python/object/apply:collections.OrderedDict
  - []
dropout_rate: 0.2
hidden_size: 256
input_size: 26
learning_rate: 0.001
num_layers: 2
optimizer_name: SGD
output_size: 2
window_size: 10
