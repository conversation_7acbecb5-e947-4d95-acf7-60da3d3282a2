#!/usr/bin/env python3
"""
专门训练hijack事件的模型
只使用hijack事件进行训练，最后4个hijack事件作为测试集
"""
import os
import logging
from typing import List, Optional
import numpy as np
import torch
from torch.utils.data import DataLoader
from feature_extraction import create_windows_from_json, SlidingWindowDataset
from classification import GRUClassifier
from evaluate_model import evaluate_model
import pytorch_lightning as pl

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置参数
CONFIG = {
    "window_size": 10,
    "hid_dim": 256,
    "n_layers": 2,
    "fore_out": 2,
    "batch_size": 128,
    "dropout_rate": 0.2,
    "val_split": 0.2
}

EXPECTED_DIM = 26

def prepare_hijack_dataset(hijack_events: List[str], train_dataset_path: str, val_dataset_path: str, config: dict) -> bool:
    """
    准备hijack事件的训练集和验证集
    前N-4个事件作为训练集，最后4个事件作为验证集
    """
    if os.path.exists(train_dataset_path) and os.path.exists(val_dataset_path):
        logger.info(f"Hijack datasets already exist. Skipping preparation.")
        return True

    # 按名称排序，取最后4个作为验证集
    hijack_events_sorted = sorted(hijack_events)
    train_events = hijack_events_sorted[:-4]
    val_events = hijack_events_sorted[-4:]
    
    logger.info(f"Hijack events - Training: {len(train_events)}, Validation: {len(val_events)}")
    logger.info(f"Training events: {[os.path.basename(e) for e in train_events]}")
    logger.info(f"Validation events: {[os.path.basename(e) for e in val_events]}")

    # 处理训练集
    train_X, train_y = [], []
    for event_dir in train_events:
        event_name = os.path.basename(event_dir)
        feature_file = os.path.join(event_dir, "features", "fea.json")
        
        if not os.path.exists(feature_file):
            logger.warning(f"Feature file not found: {feature_file}")
            continue
            
        logger.info(f"Processing training event: {event_name}")
        X, y = create_windows_from_json(feature_file, window=config["window_size"], oversample_ratio=1)
        
        if X.nelement() > 0:
            train_X.append(X)
            train_y.append(y)

    # 处理验证集
    val_X, val_y = [], []
    for event_dir in val_events:
        event_name = os.path.basename(event_dir)
        feature_file = os.path.join(event_dir, "features", "fea.json")
        
        if not os.path.exists(feature_file):
            logger.warning(f"Feature file not found: {feature_file}")
            continue
            
        logger.info(f"Processing validation event: {event_name}")
        X, y = create_windows_from_json(feature_file, window=config["window_size"], oversample_ratio=1)
        
        if X.nelement() > 0:
            val_X.append(X)
            val_y.append(y)

    if not train_X or not val_X:
        logger.error("Failed to create training or validation datasets.")
        return False

    # 合并并保存训练集
    combined_train_X = torch.cat(train_X, dim=0)
    combined_train_y = torch.cat(train_y, dim=0)
    train_dataset = SlidingWindowDataset(combined_train_X, combined_train_y)
    os.makedirs(os.path.dirname(train_dataset_path), exist_ok=True)
    torch.save(train_dataset, train_dataset_path)
    logger.info(f"Training dataset: {len(combined_train_X)} windows, saved to {train_dataset_path}")

    # 合并并保存验证集
    combined_val_X = torch.cat(val_X, dim=0)
    combined_val_y = torch.cat(val_y, dim=0)
    val_dataset = SlidingWindowDataset(combined_val_X, combined_val_y)
    torch.save(val_dataset, val_dataset_path)
    logger.info(f"Validation dataset: {len(combined_val_X)} windows, saved to {val_dataset_path}")
    
    return True

def train_hijack_model(train_dataset_path: str, val_dataset_path: str, model_dir: str, config: dict,
                       max_epochs: int, learning_rate: float, optimizer_name: str,
                       stage_name: str, resume_from_ckpt: Optional[str] = None):
    """
    训练hijack专用模型
    """
    try:
        # 加载数据集
        train_dataset = torch.load(train_dataset_path, weights_only=False)
        val_dataset = torch.load(val_dataset_path, weights_only=False)

        # 计算类别权重
        train_labels = [train_dataset[i][1].item() for i in range(len(train_dataset))]
        normal_count = train_labels.count(0)
        anomaly_count = train_labels.count(1)
        total = len(train_labels)
        
        # 使用极强的权重策略来处理hijack事件的严重不平衡
        imbalance_ratio = normal_count / anomaly_count if anomaly_count > 0 else 1.0
        adaptive_weight_normal = 1.0
        adaptive_weight_anomaly = min(imbalance_ratio * 1.2, 80.0)  # 提高到80倍上限，使用1.2系数
        class_weights = torch.tensor([adaptive_weight_normal, adaptive_weight_anomaly], dtype=torch.float32)
        
        logger.info(f"Hijack class distribution - Normal: {normal_count} ({normal_count/total*100:.2f}%), Anomaly: {anomaly_count} ({anomaly_count/total*100:.2f}%)")
        logger.info(f"Imbalance ratio: {imbalance_ratio:.2f}")
        logger.info(f"Class weights - Normal: {adaptive_weight_normal:.4f}, Anomaly: {adaptive_weight_anomaly:.4f} (ratio: {adaptive_weight_anomaly/adaptive_weight_normal:.2f}x)")

        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=config["batch_size"], shuffle=True, num_workers=0)
        val_loader = DataLoader(val_dataset, batch_size=config["batch_size"], shuffle=False, num_workers=0)
        logger.info(f"Training with {len(train_dataset)} samples, validating with {len(val_dataset)} samples.")

        # 创建或加载模型
        if resume_from_ckpt and os.path.exists(resume_from_ckpt):
            logger.info(f"Resuming training from checkpoint: {resume_from_ckpt}")
            model = GRUClassifier.load_from_checkpoint(
                resume_from_ckpt,
                input_size=EXPECTED_DIM,
                hidden_size=config["hid_dim"],
                num_layers=config["n_layers"],
                output_size=config["fore_out"],
                class_weights=class_weights,
                learning_rate=learning_rate,
                window_size=config["window_size"],
                dropout_rate=config["dropout_rate"],
                optimizer_name=optimizer_name
            )
        else:
            logger.info(f"Creating new model for stage: {stage_name}")
            model = GRUClassifier(
                input_size=EXPECTED_DIM,
                hidden_size=config["hid_dim"],
                num_layers=config["n_layers"],
                output_size=config["fore_out"],
                class_weights=class_weights,
                learning_rate=learning_rate,
                window_size=config["window_size"],
                dropout_rate=config["dropout_rate"],
                optimizer_name=optimizer_name
            )

        # 设置训练器
        os.makedirs(model_dir, exist_ok=True)
        checkpoint_callback = pl.callbacks.ModelCheckpoint(
            dirpath=model_dir,
            filename=f'hijack_model_{stage_name}_best_' + 'epoch={epoch:02d}-val_loss={val_loss:.2f}',
            monitor='val_loss',
            mode='min',
            save_top_k=1,
            verbose=True
        )

        trainer = pl.Trainer(
            max_epochs=max_epochs,
            callbacks=[checkpoint_callback],
            accelerator="auto",
            devices="auto",
            enable_progress_bar=True,
            log_every_n_steps=50
        )

        logger.info(f"--- Starting Training Stage: {stage_name} ---")
        trainer.fit(model, train_loader, val_loader)

        best_model_path = checkpoint_callback.best_model_path
        logger.info(f"Training completed. Best model saved at: {best_model_path}")
        return best_model_path

    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        return None

def main():
    base_dir = "/data/data/anomaly-event-routedata"
    script_dir = os.path.dirname(__file__)
    dataset_dir = os.path.join(script_dir, "hijack_dataset")
    train_dataset_path = os.path.join(dataset_dir, "hijack_train_dataset.pt")
    val_dataset_path = os.path.join(dataset_dir, "hijack_val_dataset.pt")
    models_root = os.path.join(script_dir, "hijack_models")
    summary_file = os.path.join(script_dir, "hijack_metrics_summary.csv")

    # 获取所有hijack事件
    all_events = [os.path.join(base_dir, d) for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))]
    hijack_events = [event for event in all_events if os.path.basename(event).startswith('hijack')]
    
    logger.info(f"Found {len(hijack_events)} hijack events")

    # 1. 准备hijack数据集
    if not prepare_hijack_dataset(hijack_events, train_dataset_path, val_dataset_path, CONFIG):
        logger.error("Failed to prepare hijack datasets.")
        return

    # 2. 训练hijack专用模型
    model_dir = os.path.join(models_root, "hijack_expert")

    # Stage 1: Adam Training
    logger.info("--- Launching Stage 1: Adam Training for Hijack events ---")
    adam_model_path = train_hijack_model(
        train_dataset_path=train_dataset_path,
        val_dataset_path=val_dataset_path,
        model_dir=model_dir,
        config=CONFIG,
        max_epochs=30,
        learning_rate=0.001,
        optimizer_name='Adam',
        stage_name='adam_30epochs_lr1e-3'
    )

    if not adam_model_path:
        logger.error("Adam training stage failed. Halting.")
        return

    # Stage 2: SGDM Fine-tuning
    logger.info("--- Launching Stage 2: SGDM Fine-tuning for Hijack events ---")
    final_model_path = train_hijack_model(
        train_dataset_path=train_dataset_path,
        val_dataset_path=val_dataset_path,
        model_dir=model_dir,
        config=CONFIG,
        max_epochs=20,
        learning_rate=1e-3,
        optimizer_name='SGD',
        stage_name='sgdm_20epochs',
        resume_from_ckpt=adam_model_path
    )

    if not final_model_path:
        logger.error("SGDM fine-tuning stage failed. Skipping evaluation.")
        return

    # 3. 评估hijack事件
    logger.info("--- Starting Evaluation on Hijack events ---")
    
    # 创建评估结果文件
    with open(summary_file, "w", encoding='utf-8') as f:
        f.write("事件名称,事件类型,评估状态,总报警次数,虚警次数,是否检测到异常,F1分数,召回率,精确率,最佳阈值\n")

    for evt_dir in hijack_events:
        event_name = os.path.basename(evt_dir)
        logger.info(f"Evaluating hijack event: {event_name}")
        
        metrics = evaluate_model(final_model_path, evt_dir, CONFIG)
        
        with open(summary_file, "a", encoding='utf-8') as f:
            if metrics:
                f.write(
                    f"{event_name},hijack,成功,"
                    f"{metrics.get('总报警次数', 'N/A')},{metrics.get('虚警次数', 'N/A')},"
                    f"{metrics.get('是否检测到异常', 'N/A')},{metrics.get('F1分数', 0.0):.4f},"
                    f"{metrics.get('召回率', 0.0):.4f},{metrics.get('精确率', 0.0):.4f},"
                    f"{metrics.get('最佳阈值', 0.5):.2f}\n"
                )
            else:
                f.write(f"{event_name},hijack,失败,N/A,N/A,否,0.0,0.0,0.0,N/A\n")

    logger.info(f"Hijack evaluation completed. Results saved to: {summary_file}")

if __name__ == "__main__":
    main()
