import numpy as np
import os
import pickle
import pywt
import pandas as pd
from sklearn.preprocessing import StandardScaler
import torch
from torch.utils.data import DataLoader, Dataset, SubsetRandomSampler
import time
from collections import Counter
import json
import sys
import logging

# 初始化 logger
logger = logging.getLogger(__name__)

np.set_printoptions(threshold=sys.maxsize)

def Multi_Scale_Wavelet(data, level, wave_type='db1'):
    temp = [[] for i in range(level)]
    N = data.shape[0]
    if (level > 1):
        for i in range(level):
            x = []
            for _feature in range(len(data.iloc[0])):
                coeffs = pywt.wavedec(data.iloc[:,_feature], wave_type, level=level)
                current_level = level  - i
                for j in range(i+1,level+1):
                    coeffs[j] = None
                _rec = pywt.waverec(coeffs, wave_type)
                x.append(_rec[:N])

            temp[i].extend(np.array(x).T)
    else:
        for tab in range(level):
            current_level = level - tab
            temp[current_level - 1].extend(data)
    
    return  np.array(temp), data

def Multi_Scale_Wavelet0(data, level):
    temp = [[] for i in range(level)]
    N = data.shape[0]
    if (level > 1):
        for i in range(level):
            x = []
            for _feature in range(len(data[0])):
                coeffs = pywt.wavedec(data[:,_feature], 'db1', level=level)
                current_level = level  - i
                for j in range(i+1,level+1):
                    coeffs[j] = None
                _rec = pywt.waverec(coeffs, 'db1')
                x.append(_rec[:N])

            temp[i].extend(np.array(x).T)
    else:
        for tab in range(level):
            current_level = level - tab
            temp[current_level - 1].extend(data)
    
    return  np.array(temp), data

class SlidingWindowDataset(Dataset):
    def __init__(self, X, y):
        self.X = X
        self.y = y

    def __len__(self):
        return len(self.X)

    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]

    @classmethod
    def from_json(cls, json_file, window, oversample_ratio=1):
        """
        从 JSON 文件创建数据集，并应用小波变换和过采样。
        """
        labels, features = clean_data(json_file)

        if features.shape[0] <= window:
            return cls(torch.empty(0, window, 26), torch.empty(0))

        # 先进行小波变换
        transformed_features, _ = Multi_Scale_Wavelet(pd.DataFrame(features), level=2)
        
        # 再进行滑动窗口
        X_list, y_list = [], []
        # 使用变换后的特征长度来确定循环范围
        for i in range(transformed_features[0].shape[0] - window):
            # 从变换后的多尺度特征中构建窗口
            window_features = np.array([scale[i:i+window] for scale in transformed_features])
            # 将多尺度数据调整为 (window, features)
            window_features = np.transpose(window_features, (1, 2, 0))
            # 我们只需要第一个尺度作为输入，或者根据需要进行组合
            # 这里我们简单地取第一个尺度的特征
            X_list.append(window_features[:, :, 0]) # 或者其他组合方式
            y_list.append(labels[i+window])
        
        if not X_list:
             return cls(torch.empty(0, window, 26), torch.empty(0))

        X = torch.tensor(np.array(X_list), dtype=torch.float32)
        y = torch.tensor(np.array(y_list), dtype=torch.long)

        # 执行过采样
        if oversample_ratio > 1 and 1 in y:
            anomaly_indices = (y == 1).nonzero(as_tuple=True)[0]
            X_anomaly = X[anomaly_indices]
            y_anomaly = y[anomaly_indices]
            
            # 复制 N-1 次
            X_oversampled = X_anomaly.repeat(oversample_ratio - 1, 1, 1)
            y_oversampled = y_anomaly.repeat(oversample_ratio - 1)
            
            X = torch.cat([X, X_oversampled], dim=0)
            y = torch.cat([y, y_oversampled], dim=0)

            print(f"Oversampling anomalies by a factor of {oversample_ratio}. "
                  f"Original anomaly count: {len(anomaly_indices)}, "
                  f"New anomaly count: {len(anomaly_indices) * oversample_ratio}")

        return cls(X, y)


def create_windows_from_json(json_file, window, oversample_ratio=1):
    """
    从 JSON 文件创建滑动窗口数据，返回 X 和 y 张量。
    这是一个独立的函数，用于与 SlidingWindowDataset.from_json 保持兼容。
    """
    dataset = SlidingWindowDataset.from_json(json_file, window, oversample_ratio)
    return dataset.X, dataset.y


def create_data_loaders(train_dataset, batch_size, val_split=0.2, shuffle=True, test_dataset=None):
    train_loader, val_loader, test_loader = None, None, None
    if val_split == 0.0:
        print(f"train_size: {len(train_dataset)}")
        train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle)
    else:
        dataset_size = len(train_dataset)
        indices = list(range(dataset_size))
        split = int(np.floor(val_split * dataset_size))
        if shuffle:
            np.random.shuffle(indices)
        train_indices, val_indices = indices[split:], indices[:split]

        train_sampler = SubsetRandomSampler(train_indices)
        valid_sampler = SubsetRandomSampler(val_indices)

        train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, sampler=train_sampler, drop_last=True)
        val_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, sampler=valid_sampler, drop_last=True)

        print(f"train_size: {len(train_indices)}")
        print(f"validation_size: {len(val_indices)}")

    if test_dataset is not None:
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        print(f"test_size: {len(test_dataset)}")
    return train_loader, val_loader, test_loader


def standarize(data):
    # print(data[:10])
    # print(data.shape)
    # exit()
    res = (data - data.mean(axis=0)) / data.std(axis=0) 
    # print("std:",data.std(axis=0))
    
    # 使用 nan_to_num 将因除以零产生的 NaN/inf 替换为 0
    res = np.nan_to_num(res, nan=0.0, posinf=0.0, neginf=0.0)
    
    # res = data / np.sqrt(np.sum(data**2))
    # print("res:", res)
    return res 


#清洗数据，确保json文件中只用0 和 1 字符串，没有整型
def clean_data(feature_file):
    """
    从 fea.json 文件中加载数据，清洗并分离标签和特征。
    这个函数现在能正确处理作为JSON对象（字典）或列表存储的数据。
    """
    with open(feature_file, 'r') as f:
        data = json.load(f)

    res_fea = []
    res_label = []
    
    # FIX: 检查数据是字典还是列表，并相应地准备迭代
    if isinstance(data, dict):
        # 按键的数字顺序排序，以保持原始顺序
        items_to_process = [v for k, v in sorted(data.items(), key=lambda k: int(k[0]))]
    elif isinstance(data, list):
        items_to_process = data
    else:
        logger.error(f"不支持的数据格式: {type(data)} in file {feature_file}")
        return np.array([]), np.array([])

    for item in items_to_process:
        try:
            # 确保证据是一个字典并且包含'label'
            if not isinstance(item, dict) or 'label' not in item:
                continue

            # 分离标签
            label = int(item.pop('label'))
            res_label.append(label)

            # 剩下的都是特征（排除 'index' 字段）
            filtered_items = [(k, v) for k, v in item.items() if k != 'index']
            # 按键排序以确保每次特征顺序都一致
            sorted_features = sorted(filtered_items, key=lambda x: int(x[0]) if x[0].isdigit() else float('inf'))
            feature_values = [v for k, v in sorted_features]
            res_fea.append(feature_values)

        except (ValueError, TypeError) as e:
            # 跳过无法处理的条目
            print(f"警告: 跳过一个无效条目，错误: {e}, 条目: {item}")
            continue
        
    if not res_fea:
        return np.array([]), np.array([])

    return np.array(res_label), np.array(res_fea, dtype=np.float32)

def prepare_data_and_get_features(event_dir, output_pt_path, config):
    """
    为单个事件准备训练数据，从 fea.json 生成 dataset.pt。
    这个函数现在被 run_event_training.py 调用。
    """
    json_path = os.path.join(event_dir, "features", "fea.json")
    if not os.path.exists(json_path):
        logging.error(f"fea.json 文件不存在: {json_path}")
        return False

    try:
        labels, features = clean_data(json_path)
        if features.shape[0] == 0:
            logging.warning(f"事件 {os.path.basename(event_dir)} 清洗后没有有效数据。")
            return False

        wavelet_features, _ = Multi_Scale_Wavelet0(features, level=config['level'])
        res_0 = wavelet_features[0]

        # -------- 新增：统一特征维度为 26 --------
        EXPECTED_DIM = 26
        cur_dim = res_0.shape[1]
        if cur_dim < EXPECTED_DIM:
            pad = np.zeros((res_0.shape[0], EXPECTED_DIM - cur_dim), dtype=res_0.dtype)
            res_0 = np.concatenate([res_0, pad], axis=1)
            logging.info(f"特征维度 {cur_dim} < 26，已补零至 26")
        elif cur_dim > EXPECTED_DIM:
            res_0 = res_0[:, :EXPECTED_DIM]
            logging.info(f"特征维度 {cur_dim} > 26，已截断至 26")

        logging.info(f"诊断: 用于训练的特征维度 (res_0.shape): {res_0.shape}")
        
        dataset = SlidingWindowDataset(res_0, window=config['window_size'], y=labels)

        torch.save(dataset, output_pt_path)
        logging.info(f"成功生成 Pytorch 数据集: {output_pt_path}")
        return True

    except Exception as e:
        logging.error(f"为事件 {os.path.basename(event_dir)} 准备数据时出错: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    # 修改为从原始数据目录读取fea.json
    sampling_file = "/data/data/anomaly-event-routedata/hijack-20050507-Google_hijack/features/fea.json"
    
    with open(sampling_file, 'r') as f:
        data = json.load(f)
    
    # 使用 clean_data 函数清洗数据
    data, y = clean_data(sampling_file)

    # 数据是一个二维数组，y 是标签
    print("Cleaned data shape:", data.shape)
    print("Cleaned labels shape:", y.shape)

    # 接下来的处理和转换操作
    start_time = time.time()
    (res_0), _ = Multi_Scale_Wavelet0(data[:,:], level=2)[0]
    end_time = time.time()
    print("Used time:", end_time - start_time)

    # 创建数据集
    dataset = SlidingWindowDataset(res_0, window=10, y=y)

    # 保存数据集
    torch.save(dataset, '/data/Project_Liu/BGP-Baseline-main/MSLSTM/sendXiaohui/dataset/sampling_1.0.pt')
 