#!/usr/bin/env python3
import os
import json
import torch
import numpy as np
import pandas as pd
import subprocess
import logging
import shutil
from typing import Optional, List

from classification import GRUClassifier
from torch.utils.data import ConcatDataset, TensorDataset, DataLoader, random_split
from pytorch_lightning.callbacks import Model<PERSON>heckpoint, EarlyStopping
import pytorch_lightning as pl
from collections import Counter
from feature_extraction import SlidingWindowDataset, create_windows_from_json
# 导入我们重构好的评估函数，并给予别名
from evaluate_model import evaluate_model as evaluate_event_model

# 统一特征维度
EXPECTED_DIM = 26

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 全局配置
CONFIG = {
    "window_size": 10,
    "hid_dim": 256,
    "n_layers": 2,
    "dropout": 0.2,
    "fore_out": 2,
    "for_n_layer": 2,
    "for_hid_dim": 128,
    "batch_size": 128,
    "val_split": 0.2,
}

def prepare_combined_dataset(all_event_dirs: List[str], train_dataset_path: str, val_dataset_path: str, config: dict) -> bool:
    """
    遍历所有事件目录，按事件类型划分训练集和验证集。
    每种事件类型的最后2个事件作为验证集，其余作为训练集。
    """
    if os.path.exists(train_dataset_path) and os.path.exists(val_dataset_path):
        logger.info(f"Combined datasets already exist. Skipping preparation.")
        return True

    # 按事件类型分组
    events_by_type = {'hijack': [], 'leak': [], 'outage': []}

    for event_dir in all_event_dirs:
        event_name = os.path.basename(event_dir)
        feature_file = os.path.join(event_dir, "features", "fea.json")

        if not os.path.exists(feature_file):
            logger.warning(f"Feature file not found for event {event_name}: {feature_file}. Skipping.")
            continue

        event_type = event_name.split("-")[0]
        if event_type in events_by_type:
            events_by_type[event_type].append(event_dir)
        else:
            logger.warning(f"Unknown event type: {event_type} for event {event_name}")

    # 为每种事件类型划分训练/验证集
    train_events = []
    val_events = []

    # 不同事件类型使用不同的验证集数量
    val_counts = {'hijack': 4, 'leak': 3, 'outage': 2}

    for event_type, events in events_by_type.items():
        val_count = val_counts.get(event_type, 2)
        if len(events) < val_count:
            logger.warning(f"Event type {event_type} has less than {val_count} events. All will be used for training.")
            train_events.extend(events)
        else:
            # 按名称排序，取最后N个作为验证集
            events_sorted = sorted(events)
            train_events.extend(events_sorted[:-val_count])
            val_events.extend(events_sorted[-val_count:])
            logger.info(f"{event_type}: {len(events_sorted[:-val_count])} training events, {len(events_sorted[-val_count:])} validation events")

    logger.info(f"Total: {len(train_events)} training events, {len(val_events)} validation events")

    # 处理训练集
    train_X, train_y = [], []
    for event_dir in train_events:
        event_name = os.path.basename(event_dir)
        feature_file = os.path.join(event_dir, "features", "fea.json")

        logger.info(f"Processing training event: {event_name}")
        X, y = create_windows_from_json(feature_file, window=config["window_size"], oversample_ratio=1)

        if X.nelement() > 0:
            train_X.append(X)
            train_y.append(y)

    # 处理验证集
    val_X, val_y = [], []
    for event_dir in val_events:
        event_name = os.path.basename(event_dir)
        feature_file = os.path.join(event_dir, "features", "fea.json")

        logger.info(f"Processing validation event: {event_name}")
        X, y = create_windows_from_json(feature_file, window=config["window_size"], oversample_ratio=1)

        if X.nelement() > 0:
            val_X.append(X)
            val_y.append(y)

    if not train_X or not val_X:
        logger.error("Failed to create training or validation datasets.")
        return False

    # 合并并保存训练集
    combined_train_X = torch.cat(train_X, dim=0)
    combined_train_y = torch.cat(train_y, dim=0)
    train_dataset = SlidingWindowDataset(combined_train_X, combined_train_y)
    os.makedirs(os.path.dirname(train_dataset_path), exist_ok=True)
    torch.save(train_dataset, train_dataset_path)
    logger.info(f"Training dataset: {len(combined_train_X)} windows, saved to {train_dataset_path}")

    # 合并并保存验证集
    combined_val_X = torch.cat(val_X, dim=0)
    combined_val_y = torch.cat(val_y, dim=0)
    val_dataset = SlidingWindowDataset(combined_val_X, combined_val_y)
    os.makedirs(os.path.dirname(val_dataset_path), exist_ok=True)
    torch.save(val_dataset, val_dataset_path)
    logger.info(f"Validation dataset: {len(combined_val_X)} windows, saved to {val_dataset_path}")

    return True

def train_expert_model(train_dataset_path: str, val_dataset_path: str, model_dir: str, config: dict,
                       max_epochs: int, learning_rate: float, optimizer_name: str,
                       stage_name: str, resume_from_ckpt: Optional[str] = None):
    """
    使用预先划分好的训练集和验证集，训练一个二分类专家模型。
    返回: 最佳模型路径 或 None
    """
    try:
        # --- 1. 加载数据集 ---
        if not os.path.exists(train_dataset_path):
            logger.error(f"Training dataset not found at {train_dataset_path}. Cannot train model.")
            return None
        if not os.path.exists(val_dataset_path):
            logger.error(f"Validation dataset not found at {val_dataset_path}. Cannot train model.")
            return None

        train_dataset = torch.load(train_dataset_path, weights_only=False)
        val_dataset = torch.load(val_dataset_path, weights_only=False)

        device = "cuda" if torch.cuda.is_available() else "cpu"

        # --- 2. 计算动态样本权重 ---
        train_labels = [train_dataset[i][1].item() for i in range(len(train_dataset))]
        normal_count = train_labels.count(0)
        anomaly_count = train_labels.count(1)
        total = len(train_labels)

        # 全局类别权重（基础权重）
        global_weight_normal = total / (2 * normal_count) if normal_count > 0 else 1.0
        global_weight_anomaly = total / (2 * anomaly_count) if anomaly_count > 0 else 1.0

        # 简化权重计算：使用全局类别权重，但增加异常样本权重
        # 根据数据不平衡程度动态调整异常样本权重
        imbalance_ratio = normal_count / anomaly_count if anomaly_count > 0 else 1.0

        # 使用中等强度的权重策略来处理类别不平衡
        adaptive_weight_normal = 1.0
        adaptive_weight_anomaly = min(imbalance_ratio * 1.0, 15.0)  # 限制最大权重为15倍，使用中等强度的系数

        class_weights = torch.tensor([adaptive_weight_normal, adaptive_weight_anomaly], dtype=torch.float32)

        logger.info(f"Class distribution - Normal: {normal_count} ({normal_count/total*100:.2f}%), Anomaly: {anomaly_count} ({anomaly_count/total*100:.2f}%)")
        logger.info(f"Imbalance ratio: {imbalance_ratio:.2f}")
        logger.info(f"Adaptive class weights - Normal: {adaptive_weight_normal:.4f}, Anomaly: {adaptive_weight_anomaly:.4f} (ratio: {adaptive_weight_anomaly/adaptive_weight_normal:.2f}x)")

        # --- 3. 创建数据加载器 ---
        train_loader = DataLoader(train_dataset, batch_size=config["batch_size"], shuffle=True, num_workers=0)
        val_loader = DataLoader(val_dataset, batch_size=config["batch_size"], shuffle=False, num_workers=0)
        logger.info(f"Training with {len(train_dataset)} samples, validating with {len(val_dataset)} samples.")

        # --- 3. 训练模型 ---
        os.makedirs(model_dir, exist_ok=True)
        monitor = "val_loss"
        filename_template = f'expert_model_{stage_name}' + '_best_{epoch:02d}-{val_loss:.2f}'
        
        checkpoint_callback = ModelCheckpoint(
            dirpath=model_dir,
            filename=filename_template,
            save_top_k=1,
            verbose=True,
            monitor=monitor,
            mode='min'
        )

        model = GRUClassifier(
            input_size=EXPECTED_DIM,
            hidden_size=config["hid_dim"],
            num_layers=config["n_layers"],
            output_size=config["fore_out"],
            class_weights=class_weights,
            learning_rate=learning_rate,
            window_size=config["window_size"],
            dropout_rate=config["dropout"],
            optimizer_name=optimizer_name
        )

        trainer = pl.Trainer(
            accelerator="gpu",
            devices=1,
            max_epochs=max_epochs,
            callbacks=[checkpoint_callback],
            logger=False,
            enable_progress_bar=True,
            enable_model_summary=True,
        )

        if resume_from_ckpt and optimizer_name == 'SGD':
            logger.info(f"Loading weights from {resume_from_ckpt} for fine-tuning, but not restoring trainer state.")
            model = GRUClassifier.load_from_checkpoint(
                resume_from_ckpt,
                input_size=EXPECTED_DIM,
                hidden_size=config["hid_dim"],
                num_layers=config["n_layers"],
                output_size=config["fore_out"],
                class_weights=class_weights,
                learning_rate=learning_rate,
                window_size=config["window_size"],
                dropout_rate=config["dropout"],
                optimizer_name=optimizer_name
            )
            resume_from_ckpt = None
            logger.info("Weights loaded. Proceeding with a fresh training session for fine-tuning.")

        logger.info(f"--- Starting Training Stage: {stage_name} ---")
        trainer.fit(model, train_loader, val_loader, ckpt_path=resume_from_ckpt)

        best_model_path = checkpoint_callback.best_model_path
        if not best_model_path or not os.path.exists(best_model_path):
            logger.error(f"Training stage '{stage_name}' finished, but no best model checkpoint was saved.")
            return None
            
        logger.info(f"Stage '{stage_name}' complete. Best model for this stage: {os.path.basename(best_model_path)}")
        return best_model_path

    except Exception as e:
        logger.error(f"Failed to run training stage '{stage_name}': {e}", exc_info=True)
        return None


def main():
    base_dir = "/data/data/anomaly-event-routedata"
    script_dir = os.path.dirname(__file__)
    dataset_dir = os.path.join(script_dir, "dataset")
    train_dataset_path = os.path.join(dataset_dir, "train_dataset.pt")
    val_dataset_path = os.path.join(dataset_dir, "val_dataset.pt")
    models_root = os.path.join(script_dir, "models")
    summary_file = os.path.join(script_dir, "metrics_summary.csv")

    all_events = sorted([os.path.join(base_dir, d) for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))])

    # 1. 准备按事件划分的训练集和验证集
    if not prepare_combined_dataset(all_events, train_dataset_path, val_dataset_path, CONFIG):
        logger.error("Halting execution because datasets could not be prepared.")
        return

    # 为所有事件训练一个统一的模型
    # 注意：模型目录不再按类型划分，因为我们训练的是一个通用模型
    model_dir = os.path.join(models_root, "unified_model")

    # --- STAGE 1: Adam Training ---
    logger.info(f"--- Launching Stage 1: Adam Training for ALL events ---")
    adam_model_path = train_expert_model(
        train_dataset_path=train_dataset_path,
        val_dataset_path=val_dataset_path,
        model_dir=model_dir,
        config=CONFIG,
        max_epochs=30,
        learning_rate=0.001,
        optimizer_name='Adam',
        stage_name='adam_30epochs_lr1e-3'
    )

    if not adam_model_path:
        logger.error(f"Adam training stage failed. Halting.")
        return

    # --- STAGE 2: SGDM Fine-tuning ---
    logger.info(f"--- Launching Stage 2: SGDM Fine-tuning for ALL events ---")
    final_model_path = train_expert_model(
        train_dataset_path=train_dataset_path,
        val_dataset_path=val_dataset_path,
        model_dir=model_dir,
        config=CONFIG,
        max_epochs=20,
        learning_rate=1e-3,
        optimizer_name='SGD',
        stage_name='sgdm_20epochs',
        resume_from_ckpt=adam_model_path
    )

    if not final_model_path:
        logger.error(f"SGDM fine-tuning stage failed. Skipping evaluation.")
        return

    # --- 4. 使用最终模型评估所有独立事件 ---
    logger.info(f"--- Evaluating all events with the final unified model ---")
    header = "事件,所属类型,评估状态,总报警次数,虚警次数,是否检测到异常,F1分数,召回率,精确率,最佳阈值\n"
    with open(summary_file, "w") as f:
        f.write(header)

    for evt_dir in all_events:
        # 注意：现在我们为每个事件独立评估
        metrics = evaluate_event_model(final_model_path, evt_dir, CONFIG)

        with open(summary_file, "a") as f:
            event_name = os.path.basename(evt_dir)
            evt_type = event_name.split('-')[0]
            if metrics:
                f.write(
                    f"{event_name},{evt_type},成功,"
                    f"{metrics.get('总报警次数', 'N/A')},{metrics.get('虚警次数', 'N/A')},"
                    f"{metrics.get('是否检测到异常', 'N/A')},{metrics.get('F1分数', 0.0):.4f},"
                    f"{metrics.get('召回率', 0.0):.4f},{metrics.get('精确率', 0.0):.4f},"
                    f"{metrics.get('最佳阈值', 0.5):.2f}\n"
                )
            else:
                f.write(f"{event_name},{evt_type},失败,N/A,N/A,否,0.0,0.0,0.0,N/A\n")

    logger.info(f"===== All processing complete. Summary written to {summary_file} =====")

if __name__ == "__main__":
    main() 