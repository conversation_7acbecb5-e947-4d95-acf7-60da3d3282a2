import torch
import torch.nn as nn
import pytorch_lightning as pl
from torchmetrics import Accuracy

# 恢复用户自己的 GRU 模型，并按要求进行参数化修改
class GRUClassifier(pl.LightningModule):
    def __init__(self, input_size, hidden_size, num_layers, output_size, class_weights, learning_rate, window_size, dropout_rate=0.2, optimizer_name='Adam'):
        super().__init__()
        # 保存超参数，这对于从 checkpoint 加载和复现非常重要
        self.save_hyperparameters()

        self.gru = nn.GRU(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            bidirectional=False,
            # 仅在多层GRU时应用层间dropout
            dropout=dropout_rate if num_layers > 1 else 0
        )
        
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size, 128),
            nn.ReLU(),
            # 使用从配置中传入的dropout率
            nn.Dropout(dropout_rate),
            nn.Linear(128, output_size),
            # 按要求，在最终层加入Softmax
            nn.Softmax(dim=1)
        )
        
        # CrossEntropyLoss 期望的是 logits，但为了严格匹配旧模型行为，我们接受 Softmax 的输出
        self.loss_func = nn.CrossEntropyLoss(weight=class_weights)
        self.accuracy = Accuracy(task="multiclass", num_classes=output_size)

    def forward(self, x):
        # x shape: (batch_size, window_size, feature_dim)
        gru_out, _ = self.gru(x)
        # 从最后一个时间步获取输出用于分类
        last_time_step_out = gru_out[:, -1, :]
        probabilities = self.classifier(last_time_step_out)
        return probabilities

    def training_step(self, batch, batch_idx):
        x, y = batch
        y = y.long()
        probs = self(x)
        loss = self.loss_func(probs, y)
        acc = self.accuracy(probs, y)
        
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True, logger=True)
        self.log('train_acc', acc, on_step=True, on_epoch=True, prog_bar=True, logger=True)
        return loss

    def validation_step(self, batch, batch_idx):
        x, y = batch
        y = y.long()
        probs = self(x)
        loss = self.loss_func(probs, y)
        acc = self.accuracy(probs, y)

        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True, logger=True)
        self.log('val_acc', acc, on_step=False, on_epoch=True, prog_bar=True, logger=True)
        return loss

    def configure_optimizers(self):
        """配置优化器"""
        # 使用 hparams 来访问保存的超参数
        if self.hparams.optimizer_name == 'Adam':
            optimizer = torch.optim.Adam(self.parameters(), lr=self.hparams.learning_rate)
        # 允许 'SGD' 或 'SGDM'
        elif self.hparams.optimizer_name in ['SGD', 'SGDM']:
            optimizer = torch.optim.SGD(self.parameters(), lr=self.hparams.learning_rate, momentum=0.9)
        else:
            raise ValueError(f"Unsupported optimizer: {self.hparams.optimizer_name}")
        return optimizer

    def predict_step(self, batch, batch_idx, dataloader_idx=0):
        x, y = batch
        probabilities = self.forward(x)
        return probabilities, y
