#!/usr/bin/env python3
import json
import pandas as pd
import os
import numpy as np
import subprocess
import time
from datetime import datetime

def process_event(event_dir):
    """
    处理单个事件目录，生成对应的fea.json文件
    
    参数:
    - event_dir: 事件目录路径
    
    返回:
    - 成功则返回True，否则返回False
    """
    event_name = os.path.basename(event_dir)
    # print(f"\n===== 处理事件: {event_name} =====")
    
    # 构建文件路径
    features_file = os.path.join(event_dir, "features", f"{event_name}_features.csv")
    labels_file = os.path.join(event_dir, "minute_labels.csv")
    output_file = os.path.join(event_dir, "features", "fea.json")
    
    # 检查文件是否存在
    if not os.path.exists(features_file):
        # print(f"特征文件不存在: {features_file}")
        return False
    
    if not os.path.exists(labels_file):
        # print(f"标签文件不存在: {labels_file}")
        return False
    
    # 检查output_file所在的目录是否存在，如果不存在则创建
    output_dir = os.path.dirname(output_file)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        # print(f"创建目录: {output_dir}")
    
    # 读取特征和标签
    try:
        features_df = pd.read_csv(features_file)
        labels_df = pd.read_csv(labels_file)
        
        # print(f"读取了 {len(features_df)} 行特征数据")
        # print(f"读取了 {len(labels_df)} 行标签数据")
        
        # 检查特征数据是否包含标签列
        if 'label' in features_df.columns:
            # print("特征文件已包含标签列")
            pass
        else:
            # print("特征文件不包含标签列，将使用标签文件")
            
            # 如果特征数据没有标签列，则使用标签文件中的标签
            # 注意：这里假设特征和标签的行数相同，且一一对应
            if len(features_df) != len(labels_df):
                # print(f"警告: 特征行数 ({len(features_df)}) 与标签行数 ({len(labels_df)}) 不匹配")
                pass
                
            # 将标签添加到特征数据中
            features_df['label'] = labels_df['label'].values[:len(features_df)]
        
        # 获取所有特征列（排除index和label）
        feature_columns = [col for col in features_df.columns if col not in ['index', 'label']]
        # print(f"特征列数量: {len(feature_columns)}")
        
        # 合并特征和标签
        all_data = []
        for i in range(len(labels_df['label'])):
            # 确保特征和标签对齐
            if i < len(features_df):
                # 创建一个包含所有特征和标签的字典
                sample_data = {}
                # 将特征名与特征值对应起来
                for col_idx, col_name in enumerate(feature_columns):
                    sample_data[col_name] = features_df.iloc[i][col_name]
                
                # FIX: Ensure label is an integer
                try:
                    sample_data['label'] = int(labels_df['label'].iloc[i])
                except (ValueError, TypeError):
                    sample_data['label'] = 0 # Default to 0 if conversion fails
                
                all_data.append(sample_data)
        
        # 创建JSON数据
        json_data = []
        for i, item in enumerate(all_data):
            json_data.append(item)
        
        # 保存到JSON文件
        with open(output_file, 'w') as f:
            json.dump(json_data, f, indent=2)
        
        # print(f"成功生成JSON文件: {output_file}")
        # print(f"生成了 {len(json_data)} 个样本，每个样本有 {len(feature_columns)} 个特征维度")
        
        # # 打印一个样本示例
        # print("\n样本示例:")
        # print(json.dumps(json_data[0], indent=2)[:500] + "...")  # 只显示前500个字符
        
        return True
        
    except Exception as e:
        # print(f"处理事件时出错: {e}")
        return False

def evaluate_model(feature_file, model_path, output_dir):
    """
    使用evaluate_model.py脚本评估模型
    
    参数:
    - feature_file: fea.json文件的路径
    - model_path: 模型文件路径
    - output_dir: 输出目录
    """
    try:
        event_name = os.path.basename(os.path.dirname(os.path.dirname(feature_file)))
        output_file = os.path.join(output_dir, f"{event_name}_evaluation.txt")
        
        print(f"\n===== 评估事件: {event_name} =====")
        
        # 构建评估命令
        cmd = [
            "python", 
            "/data/Project_Liu/BGP-Baseline-main/MSLSTM/sendXiaohui/evaluate_model.py",
            "--model", model_path,
            "--features", feature_file
        ]
        
        # 运行评估命令，并将输出保存到文件
        with open(output_file, "w") as f:
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 将输出写入文件
            f.write(f"====== 模型评估结果: {event_name} ======\n")
            f.write(f"特征文件: {feature_file}\n")
            f.write(f"模型文件: {model_path}\n")
            f.write(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            if result.stdout:
                f.write("===== 标准输出 =====\n")
                f.write(result.stdout)
                f.write("\n")
            
            if result.stderr:
                f.write("===== 错误输出 =====\n")
                f.write(result.stderr)
                f.write("\n")
            
            # 提取关键指标
            lines = result.stdout.strip().split('\n')
            metrics = {}
            for line in lines:
                if "总报警次数" in line or "虚警次数" in line or "是否检测到异常" in line or "F1分数" in line or "召回率 (Recall)" in line or "精确率 (Precision)" in line:
                    parts = line.split(":", 1)
                    if len(parts) == 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        metrics[key] = value
                        
            f.write("\n===== 关键指标摘要 =====\n")
            for key, value in metrics.items():
                f.write(f"{key}: {value}\n")
        
        # 打印评估结果摘要到控制台
        print(f"评估结果已保存到: {output_file}")
        for key, value in metrics.items():
            print(f"{key}: {value}")
        
        return True
    except Exception as e:
        print(f"评估模型时出错: {e}")
        return False

def main():
    # 模型路径
    model_path = "/data/Project_Liu/BGP-Baseline-main/MSLSTM/sendXiaohui/model/models.pth"
    
    # 评估结果输出目录
    output_dir = "/data/Project_Liu/BGP-Baseline-main/MSLSTM/sendXiaohui/evaluation_results"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建评估结果输出目录: {output_dir}")
    
    # 处理所有事件
    base_dir = "/data/data/anomaly-event-routedata"
    events = [os.path.join(base_dir, d) for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))]
    
    # 创建汇总文件
    summary_file = os.path.join(output_dir, "evaluation_summary.csv")
    with open(summary_file, "w") as f:
        f.write("事件,总报警次数,虚警次数,是否检测到异常,F1分数,召回率,精确率\n")
    
    success_count = 0
    evaluation_count = 0
    
    # 处理每个事件
    for event_dir in sorted(events):
        event_name = os.path.basename(event_dir)
        print(f"\n\n======= 开始处理事件 {event_name} ({events.index(event_dir)+1}/{len(events)}) =======")
        
        # 处理事件，生成fea.json
        if process_event(event_dir):
            success_count += 1
            
            # 评估模型
            feature_file = os.path.join(event_dir, "features", "fea.json")
            if os.path.exists(feature_file):
                if evaluate_model(feature_file, model_path, output_dir):
                    evaluation_count += 1
                    
                    # 读取评估结果，添加到汇总文件
                    eval_file = os.path.join(output_dir, f"{event_name}_evaluation.txt")
                    if os.path.exists(eval_file):
                        metrics = {}
                        with open(eval_file, "r") as f:
                            for line in f:
                                if "总报警次数" in line or "虚警次数" in line or "是否检测到异常" in line or "F1分数" in line or "召回率 (Recall)" in line or "精确率 (Precision)" in line:
                                    parts = line.split(":", 1)
                                    if len(parts) == 2:
                                        key = parts[0].strip()
                                        value = parts[1].strip()
                                        metrics[key] = value
                        
                        # 将结果添加到汇总文件
                        with open(summary_file, "a") as f:
                            total_alarms = metrics.get("总报警次数", "0")
                            false_alarms = metrics.get("虚警次数", "0")
                            anomaly_detected = metrics.get("是否检测到异常", "否")
                            f1 = metrics.get("F1分数", "0.0000")
                            recall = metrics.get("召回率 (Recall)", "0.0000")
                            precision = metrics.get("精确率 (Precision)", "0.0000")
                            
                            f.write(f"{event_name},{total_alarms},{false_alarms},{anomaly_detected},{f1},{recall},{precision}\n")
        
        # 休息一下，避免系统过载
        time.sleep(1)
    
    print(f"\n\n===== 处理完成 =====")
    print(f"成功处理了 {success_count}/{len(events)} 个事件")
    print(f"成功评估了 {evaluation_count}/{success_count} 个事件")
    print(f"评估结果汇总已保存到: {summary_file}")

if __name__ == "__main__":
    main() 