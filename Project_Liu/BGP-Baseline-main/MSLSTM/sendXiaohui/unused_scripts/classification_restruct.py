import os
import numpy as np
import torch
from torch import nn
import pytorch_lightning as pl
from torch.utils.data import Dataset, DataLoader, random_split
import logging
from pytorch_lightning.callbacks import ModelCheckpoint
from sklearn.metrics import confusion_matrix, precision_score, recall_score, f1_score
from sklearn.preprocessing import StandardScaler
import csv
from collections import defaultdict
import joblib
import shutil

class ForecastingDataset(Dataset):
    """
    为时间序列预测任务创建数据集。
    """
    def __init__(self, features, labels, window_size, is_train=False):
        """
        Args:
            features (np.array): 特征数据。
            labels (np.array): 标签数据。
            window_size (int): 输入窗口的长度。
            is_train (bool): 如果是训练集，则对正常数据进行10倍增强。
        """
        # if is_train:
            # 数据增强：仅对训练数据进行
            # features = np.repeat(features, 10, axis=0)
            # labels = np.repeat(labels, 10, axis=0)

        self.features = features
        self.labels = labels
        self.window_size = window_size

    def __len__(self):
        return len(self.features) - self.window_size

    def __getitem__(self, idx):
        x = self.features[idx:idx + self.window_size]
        y = self.features[idx + self.window_size]
        label = self.labels[idx + self.window_size - 1]
        return torch.tensor(x, dtype=torch.float32), torch.tensor(y, dtype=torch.float32), torch.tensor(label, dtype=torch.long)

class mymodel(pl.LightningModule):
    def __init__(self, in_dim, hid_dim, n_layers, dropout):
        super().__init__()
        self.save_hyperparameters()
        self.gru = nn.GRU(
            input_size=in_dim,
            hidden_size=hid_dim,
            num_layers=n_layers,
            batch_first=True,
            dropout=dropout if n_layers > 1 else 0
        )
        self.forecasting_head = nn.Linear(hid_dim, in_dim)
        self.criterion = nn.MSELoss()
        self.validation_step_outputs = []
        self.anomaly_threshold = None

    def forward(self, x):
        gru_out, _ = self.gru(x)
        last_time_step_out = gru_out[:, -1, :]
        prediction = self.forecasting_head(last_time_step_out)
        return prediction

    def training_step(self, batch, batch_idx):
        x, y, _ = batch
        prediction = self.forward(x)
        loss = self.criterion(prediction, y)
        self.log('train_loss', loss, prog_bar=True)
        return loss

    def validation_step(self, batch, batch_idx):
        x, y, _ = batch
        prediction = self.forward(x)
        prediction_errors = torch.mean((prediction - y)**2, dim=1)
        self.validation_step_outputs.append(prediction_errors)
        self.log('val_loss', torch.mean(prediction_errors), prog_bar=True)

    def on_validation_epoch_end(self):
        if not self.validation_step_outputs:
            self.validation_step_outputs.clear()
            return
        all_errors = torch.cat(self.validation_step_outputs).cpu().numpy()
        self.anomaly_threshold = np.percentile(all_errors, 95)
        self.log('anomaly_threshold', self.anomaly_threshold)
        self.validation_step_outputs.clear()

    def configure_optimizers(self):
        return torch.optim.Adam(self.parameters(), lr=0.001)

    def predict_step(self, batch, batch_idx):
        x, y, labels = batch
        prediction = self.forward(x)
        errors = torch.mean((prediction - y)**2, dim=1)
        
        if self.anomaly_threshold is not None:
            preds = (errors > self.anomaly_threshold).long()
        else:
            preds = torch.zeros_like(labels)
            
        return preds, labels

def train_consolidated_model(event_paths, event_type):
    """
    为一种事件类型（如 'hijack'）聚合所有正常数据并训练一个通用模型。
    如果已有训练好的模型，则加载它。
    """
    print(f"\n{'='*25}\nProcessing Model for: {event_type.upper()}\n{'='*25}")

    # Define paths for the permanently saved model and scaler
    permanent_save_dir = "trained_models"
    os.makedirs(permanent_save_dir, exist_ok=True)
    model_path = os.path.join(permanent_save_dir, f"{event_type}_model.ckpt")
    scaler_path = os.path.join(permanent_save_dir, f"{event_type}_scaler.pkl")

    # --- 1. Aggregate data and create scaler (needed for both training and validation) ---
    all_normal_features = []
    for event_path in event_paths:
        dataset_file = os.path.join(event_path, "dataset.pt")
        if not os.path.exists(dataset_file):
            continue
        
        loaded_data = torch.load(dataset_file, weights_only=False)
        
        event_features, event_labels = [], []
        for features, label in loaded_data:
            event_features.extend(features)
            event_labels.extend([label] * len(features))
        
        event_features_np = np.array(event_features)
        event_labels_np = np.array(event_labels)
        
        normal_features = event_features_np[event_labels_np == 0]
        if len(normal_features) > 0:
            all_normal_features.append(normal_features)

    if not all_normal_features:
        print(f"No normal data found for event type {event_type}. Skipping.")
        return None, None

    consolidated_normal_features = np.concatenate(all_normal_features, axis=0)
    scaler = StandardScaler()
    scaler.fit(consolidated_normal_features)
    scaled_normal_features = scaler.transform(consolidated_normal_features)
    normal_labels = np.zeros(len(scaled_normal_features))
    
    window_size = 10
    val_full_dataset = ForecastingDataset(scaled_normal_features, normal_labels, window_size, is_train=False)

    if len(val_full_dataset) < 2:
        print(f"Not enough data for train/val split for {event_type}. Skipping")
        return None, None
    
    train_size = int(0.8 * len(val_full_dataset))
    val_size = len(val_full_dataset) - train_size
    _, val_dataset = random_split(val_full_dataset, [train_size, val_size])
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)

    # --- 2. Check for existing model or train a new one ---
    if os.path.exists(model_path) and os.path.exists(scaler_path):
        print(f"Found existing model for '{event_type}'. Loading from {model_path}")
        model_to_predict = mymodel.load_from_checkpoint(model_path)
        # We still need to load the correct scaler
        scaler = joblib.load(scaler_path)
    else:
        print(f"No existing model found for '{event_type}'. Starting training...")
        train_dataset = ForecastingDataset(scaled_normal_features, normal_labels, window_size, is_train=True)
        train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
        n_feature = scaled_normal_features.shape[1]
        model = mymodel(in_dim=n_feature, hid_dim=64, n_layers=4, dropout=0.4)
        
        checkpoint_callback = ModelCheckpoint(monitor='val_loss', mode='min')
        trainer = pl.Trainer(
            precision=32, max_epochs=30, accelerator="gpu", 
            callbacks=[checkpoint_callback], logger=False, 
            enable_progress_bar=True, enable_model_summary=False
        )
        
        trainer.fit(model, train_loader, val_loader)
        
        best_model_path = checkpoint_callback.best_model_path
        model_to_predict = None
        if best_model_path and os.path.exists(best_model_path):
            print(f"Identified best model for {event_type} at: {best_model_path}")
            model_to_predict = mymodel.load_from_checkpoint(best_model_path)
            # Save the best model and the scaler to the permanent directory
            print(f"Saving model to {model_path} and scaler to {scaler_path}")
            shutil.copy(best_model_path, model_path)
            joblib.dump(scaler, scaler_path)
        else:
            print(f"No best model checkpoint found for {event_type}. Using last epoch model and not saving permanently.")
            model_to_predict = model

    # --- 3. Set anomaly threshold for the selected model ---
    if model_to_predict:
        print(f"Calculating anomaly threshold for the '{event_type}' model...")
        # A new trainer instance is needed for validation if we loaded a pre-trained model
        eval_trainer = pl.Trainer(accelerator="gpu", logger=False, enable_progress_bar=False, enable_model_summary=False)
        eval_trainer.validate(model_to_predict, val_loader, verbose=False)
        if model_to_predict.anomaly_threshold is None:
            print(f"Warning: Could not set anomaly threshold for {event_type} model after validation.")

    return model_to_predict, scaler


def evaluate_on_event(event_path, event_name, model, scaler, writer):
    """
    使用训练好的通用模型评估单个事件。
    """
    print(f"\n--- Evaluating: {event_name} ---")

    path = os.path.join(event_path, "dataset.pt")
    if not os.path.exists(path):
        print(f"Dataset not found for {event_name}. Skipping evaluation.")
        return

    loaded_data = torch.load(path, weights_only=False)
    
    all_features, all_labels = [], []
    for features, label in loaded_data:
        all_features.extend(features)
        all_labels.extend([label] * len(features))

    features_np = np.array(all_features)
    labels_np = np.array(all_labels)

    if len(features_np) == 0:
        print(f"No data in {event_name}. Skipping evaluation.")
        return

    features_np_scaled = scaler.transform(features_np)

    window_size = 10
    test_dataset = ForecastingDataset(features_np_scaled, labels_np, window_size=window_size, is_train=False)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)

    if model.anomaly_threshold is None:
        print(f"Model for this type has no threshold. Cannot evaluate {event_name}.")
        return

    trainer = pl.Trainer(accelerator="gpu", logger=False, enable_progress_bar=False, enable_model_summary=False)
    res = trainer.predict(model, test_loader)

    pre_, label_ = [], []
    for r in res:
        pre_ += r[0].tolist()
        label_ += r[1].tolist()
    
    if len(label_) == 0:
        print(f"No predictions generated for {event_name}. Skipping.")
        return

    tn, fp, fn, tp = confusion_matrix(label_, pre_, labels=[0, 1]).ravel()

    alarm_count = tp + fp
    false_positives = fp
    precision_val = precision_score(label_, pre_, pos_label=1, zero_division=0)
    recall_val = recall_score(label_, pre_, pos_label=1, zero_division=0)
    f1_val = f1_score(label_, pre_, pos_label=1, zero_division=0)

    print(f"Results for {event_name}: F1={f1_val:.4f}, Recall={recall_val:.4f}, Precision={precision_val:.4f}")
    writer.writerow([event_name, alarm_count, false_positives, f1_val, recall_val, precision_val])


if __name__ == "__main__":
    # 配置区
    base_dataset_dir = "/data/Project_Liu/BGP-Baseline-main/MSLSTM/sendXiaohui/dataset/"
    output_csv_path = "results.csv"
    exclude_events = ["outage-20211004-Facebook_outage"] # 要排除的事件
    
    # 脚本主体
    pl.seed_everything(42) # 为了可复现性
    
    # 1. 对所有事件按类型分组
    all_event_dirs = [d for d in os.listdir(base_dataset_dir) if os.path.isdir(os.path.join(base_dataset_dir, d)) and d not in exclude_events]
    
    event_groups = defaultdict(list)
    for event_name in all_event_dirs:
        event_type = event_name.split('-')[0]
        event_groups[event_type].append(event_name)

    with open(output_csv_path, 'w', newline='') as csvfile:
        csv_writer = csv.writer(csvfile)
        csv_writer.writerow(["event_name", "alarm_count", "false_positives", "f1_score", "recall", "precision"])
        
        # 2. 按事件类型训练和评估
        for event_type, event_names in sorted(event_groups.items()):
            event_paths = [os.path.join(base_dataset_dir, name) for name in event_names]
            
            # 为该类型训练一个通用模型
            consolidated_model, scaler = train_consolidated_model(event_paths, event_type)
            
            if consolidated_model is None or scaler is None:
                print(f"Skipping evaluation for type {event_type} due to training failure.")
                continue

            # 使用通用模型评估该类型下的每个事件
            for event_name in sorted(event_names):
                event_path = os.path.join(base_dataset_dir, event_name)
                try:
                    evaluate_on_event(event_path, event_name, consolidated_model, scaler, csv_writer)
                except Exception as e:
                    print(f"!!! An unexpected error occurred while evaluating {event_name}: {e}")
                    csv_writer.writerow([event_name, "ERROR", str(e), -1, -1, -1])

    print(f"\nProcessing complete. Results saved to {output_csv_path}")
