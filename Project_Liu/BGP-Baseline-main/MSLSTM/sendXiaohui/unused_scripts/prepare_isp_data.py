import os
import pandas as pd
import numpy as np
import torch
from sklearn.preprocessing import StandardScaler
from dataset import SlidingWindowDataset
import joblib
import argparse

def load_and_process_event_data(event_name, base_dir):
    event_path = os.path.join(base_dir, event_name)
    features_file = os.path.join(event_path, 'features', "fea.json")
    
    if not os.path.exists(features_file):
        print(f"Skipping {event_name}: Features file not found at {features_file}")
        return None, None
    
    try:
        features_df = pd.read_json(features_file)
        
        # The last column is the label, others are features
        labels = features_df.iloc[:, -1].values.astype(int)
        features = features_df.iloc[:, :-1].values.astype(np.float32)
        
        print(f"Loaded {event_name}: Features shape {features.shape}, Labels shape {labels.shape}")
        return features, labels
    except Exception as e:
        print(f"Error loading/processing {event_name} data: {e}. Skipping.")
        return None, None


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Prepare data for a specialized BGP event model.")
    parser.add_argument('--event_type', type=str, required=True, choices=['hijack', 'leak', 'outage'],
                        help="The type of event to prepare data for (e.g., 'hijack', 'leak', 'outage').")
    args = parser.parse_args()
    event_type = args.event_type

    base_data_dir = '/data/data/anomaly-event-routedata'
    output_dir = f'./output_{event_type}'
    window_size = 10
    
    os.makedirs(output_dir, exist_ok=True)

    # Select events based on the specified event_type
    all_events = [d for d in os.listdir(base_data_dir) if os.path.isdir(os.path.join(base_data_dir, d))]
    train_event_names = [e for e in all_events if e.startswith(f'{event_type}-')]
    
    print(f"\n--- Preparing data for '{event_type}' model ---")
    print(f"Found {len(train_event_names)} '{event_type}' events for specialized training.")

    # --- Process Training Data ---
    all_train_features = []
    all_train_labels = []
    print("\n--- Loading and processing training events ---")
    for event_name in train_event_names:
        features, labels = load_and_process_event_data(event_name, base_data_dir)
        if features is not None and labels is not None:
            all_train_features.append(features)
            all_train_labels.append(labels)

    if not all_train_features:
        print("No valid training data found. Exiting.")
        exit()

    all_train_features_np = np.concatenate(all_train_features, axis=0)
    all_train_labels_np = np.concatenate(all_train_labels, axis=0)
    print(f"Combined training data: Features shape {all_train_features_np.shape}, Labels shape {all_train_labels_np.shape}")

    # --- Scale Features ---
    print("\n--- Scaling features ---")
    scaler = StandardScaler()
    scaled_train_features = scaler.fit_transform(all_train_features_np)
    
    # Save the scaler for later use in prediction
    scaler_path = os.path.join(output_dir, 'scaler.pkl')
    joblib.dump(scaler, scaler_path)
    print(f"Scaler saved to {scaler_path}")

    print(f"Scaled training features shape: {scaled_train_features.shape}")

    # --- Create and Save Datasets ---
    print("\n--- Creating and saving training dataset ---")
    train_dataset = SlidingWindowDataset(
        data=scaled_train_features,
        window=window_size,
        y=all_train_labels_np
    )

    train_output_path = os.path.join(output_dir, 'train_data.pt')
    torch.save(train_dataset, train_output_path)

    print(f"Successfully saved training dataset to {train_output_path}")
    print("Data preparation complete.") 