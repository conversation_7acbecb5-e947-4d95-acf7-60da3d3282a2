import os
import numpy as np
import torch
from torch import nn
import pytorch_lightning as pl
from feature_extraction import SlidingWindowDataset, create_data_loaders
import logging
from pytorch_lightning.callbacks import ModelCheckpoint
import torch.nn.functional as F
from sklearn.metrics import accuracy_score, recall_score, f1_score, confusion_matrix, precision_score, classification_report
import random

class GRULayer(nn.Module):
    """Gated Recurrent Unit (GRU) Layer
    :param in_dim: number of input features
    :param hid_dim: hidden size of the GRU
    :param n_layers: number of layers in GRU
    :param dropout: dropout rate
    """

    def __init__(self, in_dim, hid_dim, n_layers, dropout):
        super(GRULayer, self).__init__()
        self.hid_dim = hid_dim
        self.n_layers = n_layers
        self.dropout = 0.0 if n_layers == 1 else dropout
        self.gru = nn.GRU(in_dim, hid_dim, num_layers=n_layers, batch_first=True, dropout=self.dropout)

    def forward(self, x):
        # GRU输入格式: [batch_size, seq_len, in_dim]
        # GRU输出: out (shape: batch_size, seq_len, hidden_size), h (shape: num_layers, batch_size, hidden_size)
        out, h = self.gru(x)
        # 我们只需要最终隐藏状态 h
        return out, h

class forcastModel(nn.Module):
    
    def __init__(self, in_dim, hid_dim, out_dim, dropout, n_layers):
        super(forcastModel, self).__init__()
        # 创建线性层序列，确保维度匹配
        layers = []
        
        # 第一层：输入维度 -> 隐藏维度
        # 修正：输入是 n_layers * hid_dim 维度的向量
        input_dim = in_dim * n_layers
        layers.append(nn.Linear(input_dim, hid_dim))
        
        # 中间层：隐藏维度 -> 隐藏维度
        for _ in range(n_layers - 1):
            layers.append(nn.Linear(hid_dim, hid_dim))
        
        # 输出层：隐藏维度 -> 输出维度
        layers.append(nn.Linear(hid_dim, out_dim))
        
        self.layers = nn.ModuleList(layers)
        self.dropout = nn.Dropout(dropout)
        self.relu = nn.ReLU()
    
    def forward(self, x):
        # 打印输入形状以便调试
        # print(f"forcastModel input shape: {x.shape}")
        
        for l in range(len(self.layers)-1):
            x = self.relu(self.layers[l](x))
            x = self.dropout(x)
        
        # 打印输出形状以便调试
        # print(f"forcastModel output shape: {self.layers[-1](x).shape}")
        
        return self.layers[-1](x)

class mymodel(pl.LightningModule):
    def __init__(self, in_dim, hid_dim, n_layers, dropout, fore_out=1, for_n_layer=2, for_hid_dim=60):
        # 先调用父类初始化
        super(mymodel, self).__init__()
        
        # 设置属性
        self.n_layers = n_layers
        self.in_dim = in_dim  # 特征维度
        self.hid_dim = hid_dim
        self.fore_out = 1  # 修改为单输出
        self.dropout = dropout
        self.for_n_layer = for_n_layer
        self.for_hid_dim = for_hid_dim

        # GRU层
        self.gru = GRULayer(self.in_dim, self.hid_dim, self.n_layers, self.dropout)

        # 归一化层 - 支持不同特征维度
        self.norm1 = nn.LayerNorm(self.in_dim, eps=1e-10)  # 输入特征的归一化
        self.norm2 = nn.LayerNorm(self.hid_dim * self.n_layers, eps=1e-10)  # GRU输出的归一化，注意维度是hid_dim * n_layers
        
        # 预测模型 - 输出维度为1，注意输入维度是hid_dim * n_layers
        self.fore = forcastModel(self.hid_dim, self.for_hid_dim, self.fore_out, self.dropout, self.n_layers)
        
        # 中心点 - 用于Deep SVDD计算
        self.register_buffer('center', torch.zeros(1, self.fore_out))
        
        # 阈值 - 用于异常检测
        self.anomaly_threshold = None

        self.validation_step_outputs = []
        self.train_step_outputs = []
        
    def forward(self, x):
        """前向传播，返回样本的特征表示"""
        # 将输入转换为float类型
        x = x.float()
        
        # 准备输入数据
        batch_size, seq_len, feat_dim = x.shape
        
        # 添加小的随机扰动到输入，防止模型坍缩
        if not self.training:  # 只在评估时添加扰动
            noise_scale = 0.01  # 1%的扰动
            noise = torch.randn_like(x) * noise_scale
            x = x + noise
        
        # 应用LayerNorm - 直接作用于最后一个维度
        x_norm = x.reshape(batch_size * seq_len, feat_dim)
        x_norm = self.norm1(x_norm)
        x_norm = x_norm.reshape(batch_size, seq_len, feat_dim)
        
        # GRU层处理
        _, h_end = self.gru(x_norm)  # h_end形状: [n_layers, batch_size, hid_dim]
        
        # 转置并重塑隐藏状态，使其适合后续处理
        h_end = h_end.permute(1, 0, 2)  # 变为 [batch_size, n_layers, hid_dim]
        h_end = h_end.reshape(batch_size, -1)  # 扁平化为 [batch_size, n_layers * hid_dim]
        
        # 应用LayerNorm到扁平化的隐藏状态
        h_end = self.norm2(h_end)
        
        # 经过全连接层得到特征表示
        output = self.fore(h_end)
        
        # 添加小的随机扰动，防止所有输出完全相同
        if self.training:
            # 训练时不添加扰动，保持稳定性
            return output
        else:
            # 评估时添加微小扰动，确保不同输入产生不同输出
            # 扰动大小为0.05（5%）
            noise = torch.randn_like(output) * 0.05
            return output + noise

    def training_step(self, batch, batch_idx):
        x, y = batch
        y = y.long()
        
        # 只使用正常样本(y==0)进行训练
        normal_mask = (y == 0)
        if normal_mask.sum() == 0:
            # 如果批次中没有正常样本，返回零损失
            dummy_loss = torch.tensor(0.0, device=self.device)
            self.train_step_outputs.append({"loss": 0.0})
            return dummy_loss
            
        # 过滤出正常样本
        x_normal = x[normal_mask]
        
        # 得到模型输出
        outputs = self.forward(x_normal)
        
        # 计算到中心点的距离损失 (Deep SVDD)
        # 如果中心点未初始化(全为0)且这是第一批数据，则使用第一批正常样本的平均输出作为中心
        if self.center.sum() == 0:
            with torch.no_grad():
                self.center = outputs.mean(dim=0, keepdim=True)
                print(f"初始化中心点: {self.center.shape}, 值: {self.center.item() if self.center.numel() == 1 else '(张量)'}")
        
        # 计算距离损失 - 所有正常样本应该靠近中心点
        dist_loss = torch.mean((outputs - self.center) ** 2)
        
        # 添加正则化损失，防止特征坍缩
        # 计算批次内样本之间的距离方差，鼓励不同样本有不同的表示
        if x_normal.size(0) > 1:  # 确保批次中至少有两个样本
            # 计算批次内样本两两之间的欧氏距离
            pairwise_distances = torch.cdist(outputs, outputs, p=2)
            # 移除对角线上的零（样本与自身的距离）
            mask = ~torch.eye(pairwise_distances.size(0), dtype=torch.bool, device=pairwise_distances.device)
            pairwise_distances = pairwise_distances[mask]
            # 计算距离的方差，我们希望这个值大一些（样本之间有差异）
            distance_variance = torch.var(pairwise_distances)
            # 添加一个负方差项到损失中，鼓励方差增大
            variance_loss = -0.1 * torch.log(distance_variance + 1e-6)
            
            # 组合损失
            total_loss = dist_loss + variance_loss
            self.log("variance_loss", variance_loss.item(), prog_bar=True)
        else:
            total_loss = dist_loss
        
        self.log("dist_loss", dist_loss.item(), prog_bar=True)
        self.train_step_outputs.append({"loss": total_loss.detach().cpu().item()})
        return total_loss

    def on_train_epoch_end(self) -> None:
        train_loss = 0.0
        idx = 0
        for i in self.train_step_outputs:
            idx += 1
            train_loss += i['loss']
        train_loss = train_loss / idx if idx > 0 else 0.0
        logger_.info(f"Epoch: {self.current_epoch}: training_epoch_end--loss/train: {train_loss}")
        self.train_step_outputs = []  # 清空列表，避免内存泄漏

    def on_validation_epoch_end(self):
        loss = 0.0
        accuracy = 0.0
        all_normal_distances = []
        all_anomaly_distances = []
        thresholds = []
        
        for i in self.validation_step_outputs:
            loss += i['loss']
            accuracy += i['accuracy']
            all_normal_distances.extend(i['normal_distances'])
            all_anomaly_distances.extend(i['anomaly_distances'])
            if 'threshold' in i and i['threshold'] > 0:
                thresholds.append(i['threshold'])
        
        # 计算平均值
        n = len(self.validation_step_outputs)
        if n > 0:
            loss /= n
            accuracy /= n
        
        # 计算最终阈值 - 使用所有验证批次中计算的阈值的平均值
        if thresholds:
            final_threshold = sum(thresholds) / len(thresholds)
            self.anomaly_threshold = torch.tensor(final_threshold, device=self.device)
        elif all_normal_distances:  # 如果没有保存阈值但有正常样本距离
            # 使用所有正常样本距离的97.5%分位数作为阈值
            final_threshold = sorted(all_normal_distances)[int(len(all_normal_distances) * 0.975)]
            self.anomaly_threshold = torch.tensor(final_threshold, device=self.device)
        
        # 记录统计信息
        logger_.info(f"Epoch: {self.current_epoch}: val_epoch_end--loss: {loss}, accuracy: {accuracy}, threshold: {self.anomaly_threshold.item() if self.anomaly_threshold is not None else 0.0}")
        
        # 添加指标记录，确保包含ModelCheckpoint需要的loss/val
        self.log("loss/val", loss, prog_bar=True)
        self.log("accuracy/val", accuracy, prog_bar=True)
        
        # 清空输出列表
        self.validation_step_outputs = []

    def validation_step(self, batch, batch_index):
        x, y = batch
        y = y.long()
        
        # 获取模型输出
        outputs = self.forward(x)
        
        # 计算到中心点的距离
        distances = torch.sum((outputs - self.center) ** 2, dim=1)
        
        # 分别计算正常样本和异常样本的距离
        normal_mask = (y == 0)
        anomaly_mask = (y == 1)
        
        normal_distances = distances[normal_mask].detach().cpu().tolist() if normal_mask.sum() > 0 else []
        anomaly_distances = distances[anomaly_mask].detach().cpu().tolist() if anomaly_mask.sum() > 0 else []
        
        # 计算距离统计信息
        if normal_distances:
            normal_min = min(normal_distances)
            normal_max = max(normal_distances)
            normal_mean = sum(normal_distances) / len(normal_distances)
            self.log("normal_min_dist", normal_min, prog_bar=True)
            self.log("normal_max_dist", normal_max, prog_bar=True)
            self.log("normal_mean_dist", normal_mean, prog_bar=True)
        
        if anomaly_distances:
            anomaly_min = min(anomaly_distances)
            anomaly_max = max(anomaly_distances)
            anomaly_mean = sum(anomaly_distances) / len(anomaly_distances)
            self.log("anomaly_min_dist", anomaly_min, prog_bar=True)
            self.log("anomaly_max_dist", anomaly_max, prog_bar=True)
            self.log("anomaly_mean_dist", anomaly_mean, prog_bar=True)
        
        # 动态计算阈值 - 使用正常样本距离的97.5%分位数
        if normal_distances:
            # 使用97.5%分位数作为阈值，在95%和99%之间寻找平衡点
            threshold = torch.tensor(sorted(normal_distances)[int(len(normal_distances) * 0.975)], device=self.device)
            self.anomaly_threshold = threshold
            self.log("threshold", threshold, prog_bar=True)
        
        # 如果有阈值，计算预测结果
        if self.anomaly_threshold is not None:
            # 距离大于阈值被视为异常
            pred = (distances > self.anomaly_threshold).long()
            accuracy = (pred == y).float().mean().item()
        else:
            # 如果没有阈值，无法计算准确率
            accuracy = 0.0
        
        # 使用MSE作为验证损失
        val_loss = torch.mean((outputs[normal_mask] - self.center) ** 2) if normal_mask.sum() > 0 else torch.tensor(0.0, device=self.device)
        
        self.validation_step_outputs.append({
            'loss': val_loss.detach().cpu().item(),
            'accuracy': accuracy,
            'threshold': self.anomaly_threshold.item() if self.anomaly_threshold is not None else 0.0,
            'normal_distances': normal_distances,
            'anomaly_distances': anomaly_distances
        })
        
        return {'loss': val_loss, 'accuracy': accuracy}


    def configure_optimizers(self):
        optimizer = torch.optim.Adam(self.parameters(), lr=0.001)
        return optimizer    

    def predict_step(self, batch, batch_idx):
        x, y = batch
        outputs = self.forward(x)
        
        # 计算到中心点的距离
        distances = torch.sum((outputs - self.center) ** 2, dim=1)
        
        # 如果有阈值，使用阈值进行预测；否则使用临时阈值
        threshold = self.anomaly_threshold if self.anomaly_threshold is not None else 0.5
        
        # 距离大于阈值被视为异常(1)，否则为正常(0)
        pred = (distances > threshold).long()
        
        return pred, y, distances

checkpoint_callback = ModelCheckpoint(
    monitor='loss/val',
    filename='sample-mnist-{epoch:02d}-{val_loss:.2f}',
    save_top_k=1,
    mode='min',
    save_last=True
)

logger_ = logging.getLogger("pytorch_lightning")
logger = pl.loggers.TensorBoardLogger(save_dir='./', version='my_name12', name='lightning_logs')

if __name__ == "__main__":
    path = "/data/Project_Liu/BGP-Baseline-main/MSLSTM/sendXiaohui/dataset/hijack-20050507-Google_hijack/dataset.pt"  # 使用Google hijack数据集
    
    train_dataset = torch.load(path, weights_only=False)  # 设置weights_only=False以加载自定义类

    # 筛选出正常样本(y==0)用于训练
    normal_dataset = []
    anomaly_dataset = []
    
    for item in train_dataset:
        if item[1] == 0:  # 正常样本
            normal_dataset.append(item)
        else:  # 异常样本
            anomaly_dataset.append(item)
    
    print(f"总样本数: {len(train_dataset)}")
    print(f"正常样本数: {len(normal_dataset)}")
    print(f"异常样本数: {len(anomaly_dataset)}")
    
    # 打乱正常样本顺序
    ind_list = list(range(len(normal_dataset)))
    random.shuffle(ind_list)
    
    shuffled_normal_dataset = []
    for i in ind_list:
        shuffled_normal_dataset.append(normal_dataset[i])
    
    # 创建数据加载器 - 训练集只用正常样本，验证集使用正常+异常样本
    train_loader, val_normal_loader, _ = create_data_loaders(shuffled_normal_dataset, batch_size=10, val_split=0.2)
    
    # 创建验证集 - 包含一部分正常样本和异常样本
    val_mixed_dataset = anomaly_dataset + normal_dataset[:len(normal_dataset)//5]  # 使用20%的正常样本
    val_mixed_loader = torch.utils.data.DataLoader(val_mixed_dataset, batch_size=10, shuffle=True)
    
    window_size = 10
    n_feature = 26  # 修改为与run_event_training.py中一致的特征维度
    
    # 创建单类分类器模型 - 输出维度为1
    my = mymodel(in_dim=n_feature, hid_dim=64, n_layers=4, dropout=0.2)
    num_epochs = 30
    val_check_interval = 0.25
    precision = 32
    trainer = pl.Trainer(precision=precision, max_epochs=num_epochs, val_check_interval=1.0, log_every_n_steps=3, accelerator="gpu", callbacks=[checkpoint_callback], logger=logger, enable_progress_bar=True, detect_anomaly=True)
    trainer.fit(my, train_loader, val_mixed_loader)
    res = trainer.predict(my, val_mixed_loader)

    pre_ = []
    label_ = []
    distances_ = []
    for r in res:
        pre_ += r[0].tolist()  # 预测标签 (0=正常, 1=异常)
        label_ += r[1].tolist()  # 真实标签
        distances_ += r[2].tolist()  # 样本到中心的距离
    
    print("预测结果统计:")
    print(classification_report(label_, pre_))
    print('准确率:', accuracy_score(label_, pre_))
    
    # 计算ROC曲线所需数据 (可选，如果有matplotlib可以绘制)
    print("异常检测阈值:", my.anomaly_threshold.item() if my.anomaly_threshold is not None else "未设置")
    
    # 显示正常样本和异常样本的平均距离
    normal_dist = [d for d, l in zip(distances_, label_) if l == 0]
    anomaly_dist = [d for d, l in zip(distances_, label_) if l == 1]
    
    if normal_dist:
        print("正常样本平均距离:", sum(normal_dist) / len(normal_dist))
    if anomaly_dist:
        print("异常样本平均距离:", sum(anomaly_dist) / len(anomaly_dist))
