#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import torch
import numpy as np
import argparse
import logging
from sklearn.metrics import f1_score, precision_score, recall_score, confusion_matrix
from torch.utils.data import DataLoader

from classification import GRUClassifier
# 更改导入，以反映重构
from feature_extraction import create_windows_from_json, SlidingWindowDataset

# Setup logging
logger = logging.getLogger(__name__)

def evaluate_model(model_path: str, event_data_path: str, config: dict):
    """
    加载一个训练好的模型，并评估一个特定事件的数据。
    它会为这个事件动态创建窗口，然后寻找最佳阈值。
    """
    features_path = os.path.join(event_data_path, "features", "fea.json")
    if not os.path.exists(features_path):
        logger.warning(f"Feature file not found at {features_path}. Skipping evaluation for this event.")
        return None

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 1. 加载模型
    try:
        model = GRUClassifier.load_from_checkpoint(model_path, map_location=device)
        model.to(device)
        model.eval()
    except Exception as e:
        logger.error(f"Failed to load model for event {os.path.basename(event_data_path)}: {e}", exc_info=True)
        return None

    # 2. 准备评估数据 (使用新的函数)
    try:
        # 在评估时，我们不希望进行过采样，所以 oversample_ratio=1
        X, y = create_windows_from_json(
            features_path, 
            window=config.get("window_size", 10),
            oversample_ratio=1
        )
        
        if X.nelement() == 0:
            logger.warning(f"No data windows created for evaluation of event {os.path.basename(event_data_path)}. Skipping.")
            # 如果没有数据，依然可以生成一个“无报警”的报告
            return {
                "总报警次数": 0, "虚警次数": 0, "是否检测到异常": "否",
                "F1分数": 0.0, "召回率": 0.0, "精确率": 0.0,
                "TP": 0, "FP": 0, "TN": len(y), "FN": 0, "最佳阈值": 0.5,
            }

        eval_dataset = SlidingWindowDataset(X, y)
        dataloader = DataLoader(eval_dataset, batch_size=config.get("batch_size", 128), shuffle=False, num_workers=0)
    except Exception as e:
        logger.error(f"Failed to load data for event {os.path.basename(event_data_path)}: {e}", exc_info=True)
        return None

    # 3. 获取模型预测（概率）
    all_probs = []
    all_true_labels = []
    with torch.no_grad():
        for x_batch, y_batch in dataloader:
            x_batch = x_batch.to(device)
            probs = model(x_batch)
            all_probs.append(probs.cpu())
            all_true_labels.append(y_batch.cpu())

    if not all_probs:
        logger.warning(f"No predictions were made for event {os.path.basename(event_data_path)}. Skipping metrics calculation.")
        return None

    all_probs = torch.cat(all_probs).numpy()
    all_true_labels = torch.cat(all_true_labels).numpy()

    y_prob_anomaly = all_probs[:, 1]

    # 4. 寻找最优决策阈值以最大化 F1 分数
    best_f1 = -1.0
    best_threshold = 0.05
    for threshold in np.arange(0.01, 0.5, 0.005):  # 降低阈值范围，适应高权重模型
        y_pred = (y_prob_anomaly > threshold).astype(int)
        # 仅当存在正标签和正预测时，F1才有意义
        if np.sum(all_true_labels) > 0 or np.sum(y_pred) > 0:
            f1 = f1_score(all_true_labels, y_pred, zero_division=0)
            if f1 > best_f1:
                best_f1 = f1
                best_threshold = threshold

    # 5. 使用找到的最佳阈值计算最终指标
    final_preds = (y_prob_anomaly > best_threshold).astype(int)

    # 即使没有正样本，也要计算混淆矩阵以获取FP和TN
    tn, fp, fn, tp = confusion_matrix(all_true_labels, final_preds, labels=[0, 1]).ravel()

    metrics = {
        "总报警次数": int(tp + fp),
        "虚警次数": int(fp),
        "是否检测到异常": "是" if int(tp) > 0 else "否",
        "F1分数": best_f1 if best_f1 != -1.0 else 0.0,
        "召回率": recall_score(all_true_labels, final_preds, zero_division=0),
        "精确率": precision_score(all_true_labels, final_preds, zero_division=0),
        "TP": int(tp), "FP": int(fp), "TN": int(tn), "FN": int(fn),
        "最佳阈值": best_threshold,
    }
    
    return metrics

# 主函数部分保持不变，用于独立测试
if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Evaluate a binary classification model. Can be run standalone.")
    parser.add_argument("--model", type=str, required=True, help="Path to the trained model checkpoint (.ckpt).")
    parser.add_argument("--event_dir", type=str, required=True, help="Path to the event directory containing fea.json.")
    
    args = parser.parse_args()
    
    dummy_config = {
        "batch_size": 128,
        "window_size": 10
    }
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    final_metrics = evaluate_model(args.model, args.event_dir, dummy_config)
    if final_metrics:
        logger.info(f"--- Evaluation Results for Event: {os.path.basename(args.event_dir)} ---")
        for key, value in final_metrics.items():
            logger.info(f"  - {key}: {value:.4f}" if isinstance(value, float) else f"  - {key}: {value}")
        logger.info("-" * (len(os.path.basename(args.event_dir)) + 30))
    else:
        logger.error("Evaluation failed.") 