# BGP-Baseline
We have collected all of the state-of-the-art of BGP anomaly detection and deployed them in our platform.


## Baseline
| Method   | Paper Title|
|--------|----|
| ISP-Operated  | <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, et al. ISP self-operated BGP anomaly detection based on weakly supervised learning[C]//2021 IEEE 29th International Conference on Network Protocols (ICNP). IEEE, 2021: 1-11.  |
| BEAM   |<PERSON>, <PERSON>, <PERSON>, et al. Learning with Semantics: Towards a Semantics-Aware Routing Anomaly Detection System[C]//33rd USENIX Security Symposium (USENIX Security 24). 2024: 5143-5160. |
| BGPviewer | <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, et al. A multi-view framework for BGP anomaly detection via graph attention network[J]. Computer Networks, 2022, 214: 109129. |
| MSLSTM |<PERSON>, <PERSON>, <PERSON>v <PERSON>, et al. Multi-scale LSTM model for BGP anomaly classification[J]. IEEE Transactions on Services Computing, 2018, 14(3): 765-778.  |
| Artemis |<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. ARTEMIS: Neutralizing BGP hijacking within a minute[J]. IEEE/ACM transactions on networking, 2018, 26(6): 2471-2486. |
| BGPvector |Sha<PERSON>ra T, Shavitt Y. AP2Vec: an unsupervised approach for BGP hijacking detection[J]. IEEE Transactions on Network and Service Management, 2022, 19(3): 2255-2268. |