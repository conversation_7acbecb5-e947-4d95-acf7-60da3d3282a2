#!/usr/bin/env python3
"""
简化格式的评估脚本
输出格式：Event_Name, Alarms, False_Alarms, Detected, F1, Recall, Precision
"""

import torch
import numpy as np
import os
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import confusion_matrix, roc_auc_score
from compare_method import MTAD_GAT
from config import config
from utils.data_utils import scan_event_directories, load_event_features_and_labels

def find_optimal_threshold(probabilities, true_labels, min_threshold=0.05):
    """寻找最优分类阈值"""
    thresholds = np.arange(min_threshold, 1.0, 0.05)
    best_f1 = -1
    best_threshold = 0.5
    best_metrics = None
    
    for threshold in thresholds:
        predictions = (probabilities >= threshold).astype(int)
        
        try:
            tn, fp, fn, tp = confusion_matrix(true_labels, predictions).ravel()
        except ValueError:
            continue
            
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        if f1 > best_f1:
            best_f1 = f1
            best_threshold = threshold
            best_metrics = {
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'tp': tp, 'fp': fp, 'tn': tn, 'fn': fn
            }
    
    return best_threshold, best_f1, best_metrics

def evaluate_model_on_events(model_path, event_type):
    """评估模型在指定事件类型上的性能"""
    print(f"=== Evaluating {event_type.upper()} model on {event_type} events ===")
    
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    
    # 加载模型
    model = MTAD_GAT.load_from_checkpoint(
        model_path,
        n_features=config.model.n_features,
        window_size=config.model.window_size,
        dropout=config.model.dropout,
        hid_dim=config.model.hid_dim,
        num_classes=config.model.num_classes,
        gru_hid_dim=config.model.gru_hid_dim,
        n_layers=config.model.n_layers,
        training_stage=1
    )
    model.eval()
    model.to(device)
    print(f"✓ {event_type} model loaded successfully")
    
    # 扫描事件
    base_data_dir = "/data/data/anomaly-event-routedata"
    events_by_type = scan_event_directories(base_data_dir, [event_type])
    events = events_by_type.get(event_type, [])
    
    print(f"Found {len(events)} {event_type} events")
    
    # 加载训练时的scaler
    scaler = StandardScaler()
    
    results = []
    
    for event_name in events:
        # 加载事件数据
        event_path = os.path.join(base_data_dir, event_name)
        features, labels = load_event_features_and_labels(event_path)
        
        if features is None or labels is None:
            continue
        
        # 检查是否有异常样本
        anomaly_count = np.sum(labels == 1)
        if anomaly_count == 0:
            continue
        
        # 标准化特征
        features = scaler.fit_transform(features)
        
        # 创建滑动窗口数据
        window_size = config.model.window_size
        num_samples = len(features) - window_size + 1
        
        all_probabilities = []
        all_true_labels = []
        
        with torch.no_grad():
            for i in range(0, num_samples, 64):
                end_idx = min(i + 64, num_samples)
                batch_x = []
                batch_y = []
                
                for j in range(i, end_idx):
                    window_features = features[j:j+window_size]
                    window_label = labels[j+window_size-1]  # 预测当前时间点
                    
                    batch_x.append(window_features)
                    batch_y.append(window_label)
                
                batch_x = torch.tensor(np.array(batch_x), dtype=torch.float32).to(device)
                batch_y = torch.tensor(np.array(batch_y), dtype=torch.long).to(device)
                
                outputs = model(batch_x)
                probabilities = torch.softmax(outputs, dim=1)[:, 1]
                
                all_probabilities.extend(probabilities.cpu().numpy())
                all_true_labels.extend(batch_y.cpu().numpy())
        
        all_probabilities = np.array(all_probabilities)
        all_true_labels = np.array(all_true_labels)
        
        # 根据事件类型设置不同的最低阈值
        if event_type == 'hijack':
            min_threshold = 0.05  # hijack保持原来的低阈值
        elif event_type == 'leak':
            min_threshold = 0.55  # leak提高最低阈值到0.55，强制使用更高阈值，减少召回率1.0的情况
        elif event_type == 'outage':
            min_threshold = 0.40  # outage适度提高最低阈值，平衡检测率和误报率
        else:
            min_threshold = 0.05

        # 寻找最优阈值
        best_threshold, best_f1, best_metrics = find_optimal_threshold(all_probabilities, all_true_labels, min_threshold)

        # 显示找到的最优阈值
        print(f"\n{event_type.upper()} Model - Found optimal threshold: {best_threshold:.4f} (min_threshold: {min_threshold:.2f})")
        
        if best_metrics:
            # 使用最佳阈值进行最终预测
            final_predictions = (all_probabilities >= best_threshold).astype(int)
            tn, fp, fn, tp = confusion_matrix(all_true_labels, final_predictions).ravel()

            # 计算报警和误报
            alarms = tp + fp  # 总报警数
            false_alarms = fp  # 误报数

            # 是否检测出异常
            detected = "√" if tp > 0 else "×"
            
            # 添加结果
            results.append({
                'Event_Name': event_name.split('/')[-1],  # 只保留事件名称
                'Alarms': alarms,
                'False_Alarms': false_alarms,
                'Detected': detected,
                'F1': f"{best_metrics['f1']:.4f}",
                'Recall': f"{best_metrics['recall']:.4f}",
                'Precision': f"{best_metrics['precision']:.4f}"
            })
    
    # 创建DataFrame并显示结果
    if results:
        df = pd.DataFrame(results)
        print(f"\n{event_type.upper()} Events Evaluation Results:")
        print(df.to_string(index=False))
        
        # 保存结果
        output_file = f"{event_type}_evaluation_results.csv"
        df.to_csv(output_file, index=False)
        print(f"\nResults saved to: {output_file}")
        
        # 计算总体统计
        total_alarms = df['Alarms'].sum()
        total_false_alarms = df['False_Alarms'].sum()
        detected_events = len(df[df['Detected'] == '√'])
        total_events = len(df)
        
        f1_scores = [float(x) for x in df['F1']]
        recalls = [float(x) for x in df['Recall']]
        precisions = [float(x) for x in df['Precision']]
        
        print(f"\nOverall Statistics:")
        print(f"  Total Events: {total_events}")
        print(f"  Events with Anomalies Detected: {detected_events}")
        print(f"  Detection Rate: {detected_events/total_events:.2%}")
        print(f"  Total Alarms: {total_alarms}")
        print(f"  Total False Alarms: {total_false_alarms}")
        print(f"  False Alarm Rate: {total_false_alarms/total_alarms:.2%}" if total_alarms > 0 else "  False Alarm Rate: N/A")
        print(f"  Average F1: {np.mean(f1_scores):.4f}")
        print(f"  Average Recall: {np.mean(recalls):.4f}")
        print(f"  Average Precision: {np.mean(precisions):.4f}")
    else:
        print(f"No evaluable {event_type} events found")

def main():
    """主函数"""
    # 模型路径 - 使用最新训练的leak模型
    model_paths = {
        'hijack': '/data/Project_Liu/BGP-Baseline-main/BGPviewer_closed_data/trained_models/hijack_checkpoints/hijack_model_epoch=02_val_loss=0.44.ckpt',
        'leak': '/data/Project_Liu/BGP-Baseline-main/BGPviewer_closed_data/trained_models/leak_checkpoints/leak_model_epoch=10_val_loss=0.69-v1.ckpt',
        'outage': '/data/Project_Liu/BGP-Baseline-main/BGPviewer_closed_data/trained_models/outage_checkpoints/outage_model_epoch=02_val_loss=0.69.ckpt'
    }
    
    # 评估所有模型
    for event_type, model_path in model_paths.items():
        if os.path.exists(model_path):
            print(f"\n{'='*80}")
            evaluate_model_on_events(model_path, event_type)
        else:
            print(f"\n{'='*80}")
            print(f"Model not found: {model_path}")

if __name__ == "__main__":
    main()
