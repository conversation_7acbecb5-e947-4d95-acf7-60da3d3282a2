import numpy as np
from sklearn.preprocessing import StandardScaler

from config import config
from utils.data_utils import (
    scan_event_directories, load_event_features_and_labels,
    augment_minority_class, print_dataset_statistics,
    save_pickle_data, ensure_directory_exists
)

class BGPDataProcessor:
    def __init__(self, data_root: str = None):
        """
        BGP数据处理器

        Args:
            data_root: 数据根目录，如果为None则使用配置中的默认值
        """
        self.data_root = data_root or config.data.data_root
        self.event_types = config.data.event_types
        self.augmentation_factors = config.data.augmentation_factors
        self.output_dir = config.paths.get_processed_data_path()

        # 确保输出目录存在
        ensure_directory_exists(self.output_dir)

    def scan_events(self):
        """扫描数据目录，按事件类型分组"""
        return scan_event_directories(self.data_root, self.event_types)

    def load_event_data(self, event_path):
        """加载单个事件的特征和标签数据"""
        return load_event_features_and_labels(event_path)

    def normalize_features(self, features):
        """标准化特征数据"""
        print("正在标准化特征...")
        print(f"标准化前特征范围: [{features.min():.6f}, {features.max():.6f}]")
        print(f"标准化前特征均值: {features.mean():.6f}")
        print(f"标准化前特征标准差: {features.std():.6f}")

        scaler = StandardScaler()
        normalized_features = scaler.fit_transform(features)

        print(f"标准化后特征范围: [{normalized_features.min():.6f}, {normalized_features.max():.6f}]")
        print(f"标准化后特征均值: {normalized_features.mean():.6f}")
        print(f"标准化后特征标准差: {normalized_features.std():.6f}")

        return normalized_features

    def augment_anomaly_samples(self, features, labels, event_type):
        """对异常样本进行数据增强"""
        print(f"Original {event_type} distribution:")
        print_dataset_statistics(features, labels, f"{event_type} (before augmentation)")

        if len(features) == 0:
            print(f"Warning: No samples found in {event_type}")
            return features, labels

        # 使用工具函数进行数据增强
        factor = self.augmentation_factors[event_type]
        augmented_features, augmented_labels = augment_minority_class(
            features, labels, target_class=1, augmentation_factor=factor
        )

        print(f"Augmented {event_type} distribution:")
        print_dataset_statistics(augmented_features, augmented_labels, f"{event_type} (after augmentation)")

        return augmented_features, augmented_labels

    def combine_events_by_type(self, events_by_type):
        """合并同类型事件的数据"""
        combined_datasets = {}

        for event_type, event_paths in events_by_type.items():
            print(f"\nProcessing {event_type} events...")

            all_features = []
            all_labels = []

            for event_path in event_paths:
                features, labels = self.load_event_data(event_path)
                if features is not None and labels is not None:
                    all_features.append(features)
                    all_labels.append(labels)

            if not all_features:
                print(f"No valid data found for {event_type}")
                continue

            # 合并所有事件的数据
            combined_features = np.vstack(all_features)
            combined_labels = np.hstack(all_labels)

            # 标准化特征
            normalized_features = self.normalize_features(combined_features)

            # 数据增强
            augmented_features, augmented_labels = self.augment_anomaly_samples(
                normalized_features, combined_labels, event_type
            )

            combined_datasets[event_type] = {
                'features': augmented_features,
                'labels': augmented_labels
            }

            print(f"Final {event_type} dataset: {augmented_features.shape}")

        return combined_datasets

    def save_datasets(self, datasets):
        """保存处理后的数据集"""
        for event_type, data in datasets.items():
            output_file = config.paths.get_dataset_file_path(event_type)
            save_pickle_data(data, output_file)
            print(f"Saved {event_type} dataset to {output_file}")

    def process_all(self):
        """处理所有数据的主函数"""
        print("Starting BGP anomaly data processing...")
        print(f"Data root: {self.data_root}")
        print(f"Output directory: {self.output_dir}")

        # 确保配置目录存在
        config.ensure_directories()

        # 扫描事件
        events_by_type = self.scan_events()
        print(f"Found events: {dict([(k, len(v)) for k, v in events_by_type.items()])}")

        if not any(events_by_type.values()):
            print("No events found. Please check the data root directory.")
            return {}

        # 合并和处理数据
        datasets = self.combine_events_by_type(events_by_type)

        # 保存数据集
        if datasets:
            self.save_datasets(datasets)
        else:
            print("No datasets to save.")

        return datasets


def main():
    """主函数"""
    processor = BGPDataProcessor()
    datasets = processor.process_all()

    # 打印最终统计信息
    if datasets:
        print("\n=== Final Dataset Statistics ===")
        for event_type, data in datasets.items():
            features = data['features']
            labels = data['labels']
            print_dataset_statistics(features, labels, f"Final {event_type.upper()} Dataset")
    else:
        print("No datasets were processed successfully.")


if __name__ == "__main__":
    main()
