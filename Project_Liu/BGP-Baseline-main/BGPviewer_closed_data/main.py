#!/usr/bin/env python3
"""
BGP异常检测项目主程序
完整的四步工作流：数据处理 -> 特征提取 -> 模型训练 -> 模型评估
"""

import argparse
import sys
from pathlib import Path

from config import config
from data_processor import BGPDataProcessor
from train_models import BGPModelTrainer
from fine_tune_models import BGPModelFineTuner


def step1_data_processing():
    """步骤1: 数据处理"""
    print("\n" + "="*60)
    print("步骤 1/4: BGP异常事件数据处理")
    print("="*60)
    
    processor = BGPDataProcessor()
    datasets = processor.process_all()
    
    if not datasets:
        print("❌ 数据处理失败，请检查数据源")
        return False
    
    print("✅ 数据处理完成")
    return True


def step2_feature_extraction():
    """步骤2: 特征提取"""
    print("\n" + "="*60)
    print("步骤 2/4: 特征提取")
    print("="*60)
    
    # 注意：特征提取已经在数据处理步骤中完成
    # 这里只是验证特征文件是否存在
    
    all_exist = True
    for event_type in config.data.event_types:
        dataset_file = config.paths.get_dataset_file_path(event_type)
        if not Path(dataset_file).exists():
            print(f"❌ {event_type} 数据集文件不存在: {dataset_file}")
            all_exist = False
        else:
            print(f"✅ {event_type} 特征数据已准备就绪")
    
    if not all_exist:
        print("❌ 特征提取未完成，请先运行数据处理步骤")
        return False
    
    print("✅ 特征提取验证完成")
    return True


def step3_model_training():
    """步骤3: 模型训练"""
    print("\n" + "="*60)
    print("步骤 3/4: MTAD-GAT模型训练")
    print("="*60)
    
    try:
        trainer = BGPModelTrainer()
        trained_models = trainer.train_all_models()
        
        if not trained_models:
            print("❌ 模型训练失败")
            return False
        
        print("✅ 模型训练完成")
        return True
        
    except Exception as e:
        print(f"❌ 模型训练过程中出现错误: {e}")
        return False


def step4_model_evaluation():
    """步骤4: 模型评估"""
    print("\n" + "="*60)
    print("步骤 4/6: 模型性能评估")
    print("="*60)

    print("⚠️  评估功能暂时跳过，使用 evaluate_fine_tuned_events.py 进行评估")
    print("✅ 模型评估步骤跳过")
    return True


def step5_model_fine_tuning():
    """步骤5: 模型微调"""
    print("\n" + "="*60)
    print("步骤 5/6: 使用SGDM优化器进行模型微调")
    print("="*60)

    try:
        fine_tuner = BGPModelFineTuner()
        fine_tuned_models = fine_tuner.fine_tune_all_models()

        if not fine_tuned_models:
            print("❌ 模型微调失败")
            return False

        print("✅ 模型微调完成")
        return True

    except Exception as e:
        print(f"❌ 模型微调过程中出现错误: {e}")
        return False


def step6_fine_tuned_model_evaluation():
    """步骤6: 微调后模型评估"""
    print("\n" + "="*60)
    print("步骤 6/6: 微调后模型性能评估")
    print("="*60)

    try:
        print("请使用以下命令评估微调后的模型对各个事件的性能:")
        print("python evaluate_fine_tuned_events.py --event_type hijack")
        print("python evaluate_fine_tuned_events.py --event_type leak")
        print("python evaluate_fine_tuned_events.py --event_type outage")

        print("✅ 微调后模型评估指南完成")
        return True

    except Exception as e:
        print(f"❌ 微调后模型评估过程中出现错误: {e}")
        return False


def run_full_pipeline():
    """运行完整的四步工作流"""
    print("🚀 开始BGP异常检测完整工作流")
    print(f"配置信息:")
    print(f"  - 事件类型: {config.data.event_types}")
    print(f"  - 数据根目录: {config.data.data_root}")
    print(f"  - 输出目录: {config.paths.get_processed_data_path()}")
    print(f"  - 模型目录: {config.paths.get_trained_models_path()}")
    
    steps = [
        ("数据处理", step1_data_processing),
        ("特征提取", step2_feature_extraction),
        ("模型训练", step3_model_training),
        ("模型评估", step4_model_evaluation),
        ("模型微调", step5_model_fine_tuning),
        ("微调后评估", step6_fine_tuned_model_evaluation)
    ]
    
    for i, (step_name, step_func) in enumerate(steps, 1):
        if not step_func():
            print(f"\n❌ 工作流在步骤{i}({step_name})失败")
            return False
    
    print("\n" + "="*60)
    print("🎉 BGP异常检测完整工作流执行成功！")
    print("="*60)
    print("结果文件位置:")
    print(f"  - 处理后数据: {config.paths.get_processed_data_path()}")
    print(f"  - 训练模型: {config.paths.get_trained_models_path()}")
    print(f"  - 评估结果: {config.paths.get_trained_models_path()}/evaluation_results.pkl")
    print(f"  - 微调模型: {config.paths.get_trained_models_path()}/fine_tuned_model_paths.pkl")
    print(f"  - 微调评估: {config.paths.get_trained_models_path()}/fine_tuned_evaluation_results_*.txt")
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BGP异常检测项目")
    parser.add_argument(
        "--step",
        type=int,
        choices=[1, 2, 3, 4, 5, 6],
        help="运行指定步骤 (1:数据处理, 2:特征提取, 3:模型训练, 4:模型评估, 5:模型微调, 6:微调后评估)"
    )
    parser.add_argument(
        "--full", 
        action="store_true", 
        help="运行完整工作流"
    )
    
    args = parser.parse_args()
    
    if args.full:
        success = run_full_pipeline()
    elif args.step:
        step_functions = {
            1: step1_data_processing,
            2: step2_feature_extraction,
            3: step3_model_training,
            4: step4_model_evaluation,
            5: step5_model_fine_tuning,
            6: step6_fine_tuned_model_evaluation
        }
        success = step_functions[args.step]()
    else:
        parser.print_help()
        return
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
