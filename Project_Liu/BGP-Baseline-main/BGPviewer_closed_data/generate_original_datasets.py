#!/usr/bin/env python3
"""
生成真正的原始数据集（不进行数据增强）
"""

import os
import numpy as np
from config import config
from utils.data_utils import (
    scan_event_directories, 
    load_event_features_and_labels,
    save_pickle_data,
    print_dataset_statistics
)

class OriginalDatasetGenerator:
    """生成原始数据集（不增强）"""
    
    def __init__(self):
        self.base_data_dir = "/data/data/anomaly-event-routedata"  # 硬编码数据目录
        self.processed_data_dir = config.paths.get_processed_data_path()
        
        # 确保输出目录存在
        os.makedirs(self.processed_data_dir, exist_ok=True)
    
    def normalize_features(self, features):
        """标准化特征"""
        from sklearn.preprocessing import StandardScaler
        scaler = StandardScaler()
        return scaler.fit_transform(features)
    
    def process_event_type(self, event_type):
        """处理单个事件类型，生成原始数据集"""
        print(f"\n=== Processing {event_type.upper()} Events (Original Data) ===")
        
        # 扫描事件目录
        events_by_type = scan_event_directories(self.base_data_dir, [event_type])
        events = events_by_type.get(event_type, [])
        
        if not events:
            print(f"No {event_type} events found")
            return None
        
        print(f"Found {len(events)} {event_type} events:")
        for event in events:
            print(f"  - {event}")
        
        all_features = []
        all_labels = []
        
        # 加载所有事件数据
        for event_name in events:
            event_path = os.path.join(self.base_data_dir, event_name)
            features, labels = load_event_features_and_labels(event_path)
            
            if features is not None and labels is not None:
                print(f"Loaded {event_name}: {features.shape[0]} samples")
                all_features.append(features)
                all_labels.append(labels)
            else:
                print(f"Failed to load {event_name}")
        
        if not all_features:
            print(f"No valid data found for {event_type}")
            return None
        
        # 合并所有数据
        combined_features = np.vstack(all_features)
        combined_labels = np.hstack(all_labels)
        
        print(f"\nCombined {event_type} data:")
        print(f"  Total samples: {len(combined_features)}")
        print(f"  Features: {combined_features.shape[1]}")
        
        # 打印原始标签分布
        normal_count = np.sum(combined_labels == 0)
        anomaly_count = np.sum(combined_labels == 1)
        print(f"  Original distribution:")
        print(f"    Normal: {normal_count} ({normal_count/len(combined_labels)*100:.2f}%)")
        print(f"    Anomaly: {anomaly_count} ({anomaly_count/len(combined_labels)*100:.2f}%)")
        
        # 标准化特征
        normalized_features = self.normalize_features(combined_features)
        
        # 保存原始数据集
        dataset = {
            'features': normalized_features,
            'labels': combined_labels
        }
        
        return dataset
    
    def save_dataset(self, event_type, dataset):
        """保存数据集"""
        if dataset is None:
            return False
        
        # 保存原始数据集
        original_file = os.path.join(self.processed_data_dir, f"{event_type}_dataset_original.pkl")
        save_pickle_data(dataset, original_file)
        print(f"✓ Saved original dataset: {original_file}")
        
        return True
    
    def generate_all_original_datasets(self):
        """生成所有事件类型的原始数据集"""
        event_types = ['hijack', 'leak', 'outage']
        
        print("=== Generating Original Datasets (No Augmentation) ===")
        
        results = {}
        
        for event_type in event_types:
            dataset = self.process_event_type(event_type)
            if dataset and self.save_dataset(event_type, dataset):
                results[event_type] = dataset
                
                # 打印统计信息
                features = dataset['features']
                labels = dataset['labels']
                print_dataset_statistics(features, labels, f"Original {event_type.upper()} Dataset")
        
        # 总结
        print(f"\n=== Summary ===")
        print(f"Successfully generated {len(results)} original datasets:")
        for event_type in results:
            print(f"  ✓ {event_type}")
        
        return results

def main():
    """主函数"""
    generator = OriginalDatasetGenerator()
    datasets = generator.generate_all_original_datasets()
    
    if datasets:
        print(f"\n🎉 All original datasets generated successfully!")
    else:
        print(f"\n❌ Failed to generate datasets")

if __name__ == "__main__":
    main()
