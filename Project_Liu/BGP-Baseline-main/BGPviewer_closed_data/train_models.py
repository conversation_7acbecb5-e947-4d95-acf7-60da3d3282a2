import torch
import pytorch_lightning as pl
from pytorch_lightning.callbacks import <PERSON><PERSON>heckpoint, LearningRateMonitor, EarlyStopping
from pytorch_lightning.loggers import TensorBoardLogger
import numpy as np
from sklearn.model_selection import train_test_split
from collections import Counter

from config import config
from mydataset import SlidingWindowDataset, create_data_loaders
from compare_method import MTAD_GAT
from utils.data_utils import load_pickle_data, save_pickle_data, print_dataset_statistics

class BGPModelTrainer:
    def __init__(self):
        """
        BGP模型训练器
        使用统一配置管理所有参数
        """
        self.data_dir = config.paths.get_processed_data_path()
        self.output_dir = config.paths.get_trained_models_path()
        self.event_types = config.data.event_types

        # 从配置获取参数
        self.model_params = config.get_model_params()
        self.training_params = config.get_training_params()

        # 确保目录存在
        config.ensure_directories()

    def load_dataset(self, event_type):
        """加载指定事件类型的数据集"""
        dataset_file = config.paths.get_dataset_file_path(event_type)

        try:
            data = load_pickle_data(dataset_file)
        except FileNotFoundError:
            raise FileNotFoundError(f"Dataset file not found: {dataset_file}")

        features = data['features']
        labels = data['labels']

        print(f"Loaded {event_type} dataset: {features.shape}")
        print_dataset_statistics(features, labels, f"{event_type} dataset")

        return features, labels
    
    def create_datasets(self, features, labels):
        """创建训练和验证数据集"""
        # 随机划分训练和验证集
        train_features, val_features, train_labels, val_labels = train_test_split(
            features, labels, 
            test_size=self.training_params['test_size'],
            random_state=self.training_params['random_state'],
            stratify=labels  # 保持标签分布
        )
        
        print(f"Train set: {train_features.shape[0]} samples")
        print(f"Val set: {val_features.shape[0]} samples")
        print(f"Train labels: {Counter(train_labels)}")
        print(f"Val labels: {Counter(val_labels)}")
        
        # 创建滑动窗口数据集
        train_dataset = SlidingWindowDataset(
            data=train_features, 
            y=train_labels, 
            window=config.model.window_size
        )
        
        val_dataset = SlidingWindowDataset(
            data=val_features, 
            y=val_labels, 
            window=config.model.window_size
        )
        
        return train_dataset, val_dataset, train_labels, val_labels
    
    def train_model(self, event_type):
        """训练指定事件类型的模型"""
        print(f"\n=== Training {event_type.upper()} Model ===")
        
        # 加载数据
        features, labels = self.load_dataset(event_type)
        
        # 创建数据集
        train_dataset, val_dataset, train_labels, val_labels = self.create_datasets(features, labels)
        
        # 创建数据加载器
        train_loader, val_loader, _ = create_data_loaders(
            train_dataset,
            batch_size=self.training_params['batch_size'],
            num_workers=self.training_params['num_workers']
        )

        # 计算类别权重以处理不平衡数据
        class_counts = Counter(train_labels)
        total_samples = len(train_labels)
        class_weights = torch.tensor([
            total_samples / (len(class_counts) * class_counts[0]),  # 正常类权重
            total_samples / (len(class_counts) * class_counts[1])   # 异常类权重
        ], dtype=torch.float32)

        print(f"类别分布: {class_counts}")
        print(f"类别权重: {class_weights}")

        # 阶段1: 高学习率全局搜索
        print(f"\n=== 阶段1: 高学习率全局搜索 ({event_type}) ===")
        model_params_stage1 = self.model_params.copy()
        model_params_stage1['class_weights'] = class_weights
        model_params_stage1['training_stage'] = 1
        model_stage1 = MTAD_GAT(**model_params_stage1)
        
        # 设置回调函数
        checkpoint_dir = f"{self.output_dir}/{event_type}_checkpoints"
        checkpoint_callback = ModelCheckpoint(
            dirpath=checkpoint_dir,
            filename=f"{event_type}_model_{{epoch:02d}}_{{val_loss:.2f}}",
            save_top_k=1,
            monitor="val_loss",
            mode="min",
            save_last=True
        )

        # 早停回调
        early_stop_callback = EarlyStopping(
            monitor="val_loss",
            min_delta=self.training_params['early_stop_min_delta'],
            patience=self.training_params['early_stop_patience'],
            verbose=True,
            mode="min"
        )

        lr_monitor = LearningRateMonitor(logging_interval='step')
        
        # 设置日志记录器
        logger = TensorBoardLogger(
            save_dir=self.output_dir,
            name=f"{event_type}_logs"
        )
        
        # 创建训练器
        trainer = pl.Trainer(
            max_epochs=self.training_params['max_epochs'],
            val_check_interval=self.training_params['val_check_interval'],
            log_every_n_steps=10,
            accelerator="gpu" if torch.cuda.is_available() else "cpu",
            callbacks=[checkpoint_callback, early_stop_callback, lr_monitor],
            logger=logger,
            enable_progress_bar=True
        )
        
        # 开始阶段1训练
        print(f"Starting Stage 1 training for {event_type}...")
        trainer.fit(model_stage1, train_loader, val_loader)

        # 获取阶段1最佳模型
        stage1_best_path = checkpoint_callback.best_model_path
        print(f"✓ Stage 1 completed. Best model: {stage1_best_path}")

        return stage1_best_path, trainer, model_stage1
    
    def train_all_models(self):
        """训练所有事件类型的模型"""
        print("Starting training for all event types...")
        
        trained_models = {}
        
        for event_type in self.event_types:
            try:
                model_path, trainer, model = self.train_model(event_type)
                trained_models[event_type] = {
                    'model_path': model_path,
                    'trainer': trainer,
                    'model': model
                }
                print(f"✓ {event_type} model training completed")
                
            except Exception as e:
                print(f"✗ Error training {event_type} model: {e}")
                continue
        
        # 保存模型路径信息
        model_paths_file = config.paths.get_model_paths_file_path()
        model_paths = {k: v['model_path'] for k, v in trained_models.items()}
        save_pickle_data(model_paths, model_paths_file)
        
        print(f"\nTraining completed! Model paths saved to {model_paths_file}")
        print("Trained models:")
        for event_type, info in trained_models.items():
            print(f"  {event_type}: {info['model_path']}")
        
        return trained_models

    def train_single_model(self, event_type: str):
        """训练单个模型"""
        print(f"\n=== Training {event_type.upper()} Model ===")

        try:
            # 加载数据集
            features, labels = self.load_dataset(event_type)

            # 创建数据集
            train_dataset, val_dataset, train_labels, val_labels = self.create_datasets(features, labels)

            # 创建数据加载器
            import torch.utils.data
            train_loader = torch.utils.data.DataLoader(
                train_dataset,
                batch_size=config.training.batch_size,
                shuffle=True,
                num_workers=config.training.num_workers
            )
            val_loader = torch.utils.data.DataLoader(
                val_dataset,
                batch_size=config.training.batch_size,
                shuffle=False,
                num_workers=config.training.num_workers
            )

            # 训练模型
            model_info = self.train_model(event_type)

            if model_info:
                print(f"✓ {event_type} model training completed")
                return model_info
            else:
                print(f"✗ Error training {event_type} model")
                return None

        except Exception as e:
            print(f"✗ Error training {event_type} model: {e}")
            return None

if __name__ == "__main__":
    import sys

    # 检查CUDA可用性
    if torch.cuda.is_available():
        print(f"CUDA available: {torch.cuda.get_device_name()}")
    else:
        print("CUDA not available, using CPU")

    # 创建训练器
    trainer = BGPModelTrainer()

    # 检查命令行参数
    if len(sys.argv) > 1:
        event_type = sys.argv[1].lower()
        if event_type in ['hijack', 'leak', 'outage']:
            print(f"Training only {event_type} model...")
            trained_model = trainer.train_single_model(event_type)
            if trained_model:
                if isinstance(trained_model, dict):
                    print(f"✓ {event_type} model training completed: {trained_model['model_path']}")
                else:
                    print(f"✓ {event_type} model training completed")
            else:
                print(f"✗ {event_type} model training failed")
        else:
            print(f"Unknown event type: {event_type}")
            print("Available types: hijack, leak, outage")
    else:
        # 训练所有模型
        trained_models = trainer.train_all_models()
