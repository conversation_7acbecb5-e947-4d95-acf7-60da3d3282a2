"""
统一的评估指标计算工具
"""
import numpy as np
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_auc_score
)
from typing import Dict, List, Tuple, Any
import time


def early_performance(labels: List[int], predictions: List[int]) -> int:
    """
    计算早期性能指标 - 检测到第一个异常后多久能预测出异常
    
    Args:
        labels: 真实标签列表
        predictions: 预测标签列表
    
    Returns:
        检测延迟时间，如果未检测到则返回None
    """
    # 将非零值转换为1
    predict = [1 if i != 0 else 0 for i in predictions]
    label = [1 if i != 0 else 0 for i in labels]
    
    if 1 not in label:
        return None
        
    first_anomaly = label.index(1)
    
    # 检查异常发生后20个时间步内是否有预测
    end_idx = min(first_anomaly + 20, len(predict))
    search_window = predict[first_anomaly:end_idx]
    
    if 1 in search_window:
        return search_window.index(1)
    
    return None


def calculate_metrics(y_true: np.ndarray, y_pred: np.ndarray, 
                     y_prob: np.ndarray = None) -> Dict[str, float]:
    """
    计算完整的分类评估指标
    
    Args:
        y_true: 真实标签
        y_pred: 预测标签
        y_prob: 预测概率（可选，用于计算AUC）
    
    Returns:
        包含各种评估指标的字典
    """
    metrics = {}
    
    # 基础分类指标
    metrics['accuracy'] = accuracy_score(y_true, y_pred)
    metrics['precision'] = precision_score(y_true, y_pred, average='weighted', zero_division=0)
    metrics['recall'] = recall_score(y_true, y_pred, average='weighted', zero_division=0)
    metrics['f1_score'] = f1_score(y_true, y_pred, average='weighted', zero_division=0)
    
    # 混淆矩阵
    cm = confusion_matrix(y_true, y_pred)
    metrics['confusion_matrix'] = cm
    
    # 如果提供了概率，计算AUC
    if y_prob is not None:
        try:
            if len(np.unique(y_true)) == 2:  # 二分类
                metrics['auc'] = roc_auc_score(y_true, y_prob)
            else:  # 多分类
                metrics['auc'] = roc_auc_score(y_true, y_prob, multi_class='ovr', average='weighted')
        except ValueError:
            metrics['auc'] = 0.0
    
    # 早期性能指标
    early_perf = early_performance(y_true.tolist(), y_pred.tolist())
    metrics['early_detection_delay'] = early_perf
    
    return metrics


def print_evaluation_results(event_type: str, metrics: Dict[str, Any], 
                           inference_time: float = None):
    """
    打印格式化的评估结果
    
    Args:
        event_type: 事件类型
        metrics: 评估指标字典
        inference_time: 推理时间（可选）
    """
    print(f"\n=== {event_type.upper()} Model Evaluation Results ===")
    print(f"Accuracy: {metrics['accuracy']:.4f}")
    print(f"Precision: {metrics['precision']:.4f}")
    print(f"Recall: {metrics['recall']:.4f}")
    print(f"F1-Score: {metrics['f1_score']:.4f}")
    
    if 'auc' in metrics:
        print(f"AUC: {metrics['auc']:.4f}")
    
    if metrics['early_detection_delay'] is not None:
        print(f"Early Detection Delay: {metrics['early_detection_delay']} steps")
    else:
        print("Early Detection Delay: No anomaly detected in time window")
    
    if inference_time is not None:
        print(f"Average Inference Time: {inference_time:.4f} seconds")
    
    print(f"Confusion Matrix:")
    print(metrics['confusion_matrix'])
    print("-" * 50)


def calculate_class_weights(labels: np.ndarray) -> np.ndarray:
    """
    计算类别权重以处理不平衡数据
    
    Args:
        labels: 标签数组
    
    Returns:
        类别权重数组
    """
    from collections import Counter
    
    label_counts = Counter(labels)
    total_samples = len(labels)
    num_classes = len(label_counts)
    
    weights = np.zeros(num_classes)
    for class_id, count in label_counts.items():
        weights[class_id] = total_samples / (num_classes * count)
    
    return weights


def get_classification_report(y_true: np.ndarray, y_pred: np.ndarray, 
                            target_names: List[str] = None) -> str:
    """
    获取详细的分类报告
    
    Args:
        y_true: 真实标签
        y_pred: 预测标签
        target_names: 类别名称列表
    
    Returns:
        分类报告字符串
    """
    if target_names is None:
        target_names = ['Normal', 'Anomaly']
    
    return classification_report(y_true, y_pred, target_names=target_names, zero_division=0)
