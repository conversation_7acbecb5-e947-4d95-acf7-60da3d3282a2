"""
数据处理相关工具函数
"""
import os
import pickle
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Tuple, Any, List
from collections import Counter


def load_pickle_data(file_path: str) -> Any:
    """
    加载pickle文件
    
    Args:
        file_path: 文件路径
    
    Returns:
        加载的数据
    
    Raises:
        FileNotFoundError: 文件不存在
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    with open(file_path, 'rb') as f:
        return pickle.load(f)


def save_pickle_data(data: Any, file_path: str):
    """
    保存数据到pickle文件
    
    Args:
        data: 要保存的数据
        file_path: 文件路径
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    with open(file_path, 'wb') as f:
        pickle.dump(data, f)


def ensure_directory_exists(directory_path: str):
    """
    确保目录存在，如果不存在则创建
    
    Args:
        directory_path: 目录路径
    """
    os.makedirs(directory_path, exist_ok=True)


def load_csv_with_error_handling(file_path: str, **kwargs) -> pd.DataFrame:
    """
    安全地加载CSV文件，带错误处理
    
    Args:
        file_path: CSV文件路径
        **kwargs: pandas.read_csv的其他参数
    
    Returns:
        DataFrame或None（如果加载失败）
    """
    try:
        return pd.read_csv(file_path, **kwargs)
    except Exception as e:
        print(f"Error loading CSV file {file_path}: {e}")
        return None


def validate_features_labels_match(features: np.ndarray, labels: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
    """
    验证并对齐特征和标签的长度
    
    Args:
        features: 特征数组
        labels: 标签数组
    
    Returns:
        对齐后的特征和标签数组
    """
    min_len = min(len(features), len(labels))
    return features[:min_len], labels[:min_len]


def print_dataset_statistics(features: np.ndarray, labels: np.ndarray, dataset_name: str = "Dataset"):
    """
    打印数据集统计信息
    
    Args:
        features: 特征数组
        labels: 标签数组
        dataset_name: 数据集名称
    """
    label_counts = Counter(labels)
    total_samples = len(labels)
    
    print(f"\n=== {dataset_name} Statistics ===")
    print(f"Total samples: {total_samples}")
    print(f"Feature dimensions: {features.shape[1] if len(features.shape) > 1 else 1}")
    print(f"Label distribution:")
    for label, count in sorted(label_counts.items()):
        percentage = (count / total_samples) * 100
        label_name = "Normal" if label == 0 else "Anomaly"
        print(f"  {label_name} ({label}): {count} ({percentage:.2f}%)")


def augment_minority_class(features: np.ndarray, labels: np.ndarray,
                          target_class: int, augmentation_factor: int) -> Tuple[np.ndarray, np.ndarray]:
    """
    时序感知的数据增强方法

    Args:
        features: 特征数组 (时序数据)
        labels: 标签数组
        target_class: 目标类别（通常是少数类）
        augmentation_factor: 增强倍数

    Returns:
        增强后的特征和标签数组
    """
    print(f"开始时序感知的数据增强，目标类别: {target_class}, 增强倍数: {augmentation_factor}")

    # 找到异常样本的位置
    anomaly_indices = np.where(labels == target_class)[0]

    if len(anomaly_indices) == 0:
        print(f"Warning: No samples found for class {target_class}")
        return features, labels

    print(f"发现 {len(anomaly_indices)} 个异常样本")

    # 使用时序感知的增强策略
    augmented_features, augmented_labels = temporal_aware_augmentation(
        features, labels, anomaly_indices, augmentation_factor
    )

    return augmented_features, augmented_labels


def temporal_aware_augmentation(features: np.ndarray, labels: np.ndarray,
                               anomaly_indices: np.ndarray, augmentation_factor: int) -> Tuple[np.ndarray, np.ndarray]:
    """
    时序感知的数据增强实现

    策略：
    1. 异常注入：在正常时序中注入异常模式
    2. 时序扰动：对包含异常的时序段进行轻微变换
    3. 上下文保持：保持异常前后的正常上下文
    """
    augmented_features_list = [features]
    augmented_labels_list = [labels]

    # 策略1: 异常注入 - 在正常序列中注入异常模式
    print("执行策略1: 异常注入")
    for _ in range(augmentation_factor // 3):
        aug_features, aug_labels = anomaly_injection(features, labels, anomaly_indices)
        if aug_features is not None:
            augmented_features_list.append(aug_features)
            augmented_labels_list.append(aug_labels)

    # 策略2: 时序扰动 - 对异常时序段进行变换
    print("执行策略2: 时序扰动")
    for _ in range(augmentation_factor // 3):
        aug_features, aug_labels = temporal_perturbation(features, labels, anomaly_indices)
        if aug_features is not None:
            augmented_features_list.append(aug_features)
            augmented_labels_list.append(aug_labels)

    # 策略3: 上下文变换 - 改变异常的上下文但保持异常本身
    print("执行策略3: 上下文变换")
    for _ in range(augmentation_factor - 2 * (augmentation_factor // 3)):
        aug_features, aug_labels = context_transformation(features, labels, anomaly_indices)
        if aug_features is not None:
            augmented_features_list.append(aug_features)
            augmented_labels_list.append(aug_labels)

    # 合并所有增强数据
    final_features = np.vstack(augmented_features_list)
    final_labels = np.hstack(augmented_labels_list)

    print(f"增强完成: 原始 {len(features)} -> 增强后 {len(final_features)} 样本")

    return final_features, final_labels


def anomaly_injection(features: np.ndarray, labels: np.ndarray, anomaly_indices: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
    """
    异常注入：在正常时序中注入异常模式
    """
    if len(anomaly_indices) == 0:
        return None, None

    # 复制原始数据
    new_features = features.copy()
    new_labels = labels.copy()

    # 找到正常样本的位置
    normal_indices = np.where(labels == 0)[0]

    if len(normal_indices) < 10:  # 需要足够的正常样本
        return None, None

    # 随机选择一些正常位置进行异常注入
    num_injections = min(len(anomaly_indices), len(normal_indices) // 10)
    injection_positions = np.random.choice(normal_indices, num_injections, replace=False)

    for pos in injection_positions:
        # 随机选择一个异常样本的特征模式
        anomaly_idx = np.random.choice(anomaly_indices)
        anomaly_pattern = features[anomaly_idx]

        # 注入异常模式（加权混合，保持一些原始特征）
        alpha = 0.7  # 异常模式的权重
        new_features[pos] = alpha * anomaly_pattern + (1 - alpha) * new_features[pos]
        new_labels[pos] = 1  # 标记为异常

    return new_features, new_labels


def temporal_perturbation(features: np.ndarray, labels: np.ndarray, anomaly_indices: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
    """
    时序扰动：对包含异常的时序段进行轻微变换
    """
    if len(anomaly_indices) == 0:
        return None, None

    # 复制原始数据
    new_features = features.copy()
    new_labels = labels.copy()

    # 对异常样本进行扰动
    for idx in anomaly_indices:
        # 添加高斯噪声
        noise_std = 0.05 * np.std(features[idx])
        noise = np.random.normal(0, noise_std, features[idx].shape)
        new_features[idx] = features[idx] + noise

        # 特征缩放扰动
        scale_factor = np.random.uniform(0.9, 1.1)
        new_features[idx] *= scale_factor

        # 确保扰动后的值在合理范围内
        new_features[idx] = np.clip(new_features[idx],
                                   features.min() - 2*features.std(),
                                   features.max() + 2*features.std())

    return new_features, new_labels


def context_transformation(features: np.ndarray, labels: np.ndarray, anomaly_indices: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
    """
    上下文变换：改变异常的时序上下文
    """
    if len(anomaly_indices) == 0:
        return None, None

    # 复制原始数据
    new_features = features.copy()
    new_labels = labels.copy()

    # 为异常样本创建新的上下文
    for idx in anomaly_indices:
        # 寻找前后的正常样本
        context_window = 5  # 上下文窗口大小

        # 前向上下文
        start_idx = max(0, idx - context_window)
        end_idx = min(len(features), idx + context_window + 1)

        # 对上下文进行轻微变换
        for ctx_idx in range(start_idx, end_idx):
            if ctx_idx != idx and labels[ctx_idx] == 0:  # 只变换正常样本
                # 添加轻微的时序相关噪声
                temporal_noise = np.random.normal(0, 0.02 * np.std(features[ctx_idx]))
                new_features[ctx_idx] = features[ctx_idx] + temporal_noise

    return new_features, new_labels


def scan_event_directories(data_root: str, event_types: List[str]) -> dict:
    """
    扫描数据目录，按事件类型分组
    
    Args:
        data_root: 数据根目录
        event_types: 事件类型列表
    
    Returns:
        按事件类型分组的目录字典
    """
    from collections import defaultdict
    
    events_by_type = defaultdict(list)
    
    if not os.path.exists(data_root):
        print(f"Warning: Data root directory does not exist: {data_root}")
        return events_by_type
    
    for event_dir in os.listdir(data_root):
        event_path = os.path.join(data_root, event_dir)
        if not os.path.isdir(event_path):
            continue
            
        # 根据目录名确定事件类型
        for event_type in event_types:
            if event_dir.startswith(event_type):
                events_by_type[event_type].append(event_path)
                break
    
    return events_by_type


def load_event_features_and_labels(event_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """
    加载单个事件的特征和标签数据
    
    Args:
        event_path: 事件目录路径
    
    Returns:
        特征数组和标签数组，如果加载失败返回(None, None)
    """
    event_name = os.path.basename(event_path)
    features_file = os.path.join(event_path, "features", f"{event_name}_features.csv")
    labels_file = os.path.join(event_path, "minute_labels.csv")
    
    # 检查文件是否存在
    if not os.path.exists(features_file):
        print(f"Warning: Features file not found: {features_file}")
        return None, None
    if not os.path.exists(labels_file):
        print(f"Warning: Labels file not found: {labels_file}")
        return None, None
    
    try:
        # 读取特征数据
        features_df = load_csv_with_error_handling(features_file)
        if features_df is None:
            return None, None
            
        # 去掉index列（如果存在）
        if 'index' in features_df.columns:
            features_df = features_df.drop('index', axis=1)
        features = features_df.values.astype(np.float32)
        
        # 读取标签数据
        labels_df = load_csv_with_error_handling(labels_file)
        if labels_df is None:
            return None, None
            
        labels = labels_df['label'].values.astype(np.int32)
        
        # 确保特征和标签数量匹配
        features, labels = validate_features_labels_match(features, labels)
        
        print(f"Loaded {event_name}: {features.shape[0]} samples, {features.shape[1]} features")
        return features, labels
        
    except Exception as e:
        print(f"Error loading {event_path}: {e}")
        return None, None
