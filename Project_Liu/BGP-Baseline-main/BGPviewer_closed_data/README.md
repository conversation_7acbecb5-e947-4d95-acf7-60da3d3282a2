# BGP异常检测项目 (重构版)

## 项目概述

本项目实现了基于MTAD-GAT模型的BGP异常检测系统，能够检测三种类型的BGP异常事件：
- **Hijack** (劫持)
- **Leak** (泄露) 
- **Outage** (中断)

## 重构改进

### 主要改进点
1. **统一配置管理**: 使用Python配置类替代硬编码参数
2. **消除代码重复**: 提取公共逻辑到工具模块
3. **模块化设计**: 清晰的功能分离和标准化接口
4. **错误处理**: 完善的异常处理和日志记录

### 项目结构
```
BGPviewer_closed_data/
├── config.py                 # 统一配置管理
├── main.py                   # 主程序入口
├── data_processor.py         # 数据处理模块 (包含特征提取)
├── train_models.py          # 模型训练模块
├── evaluate_models.py       # 模型评估模块
├── compare_method.py        # MTAD_GAT模型定义
├── mydataset.py            # 数据集类
├── modules.py              # 神经网络模块
├── utils/                   # 工具模块
│   ├── __init__.py
│   ├── metrics.py          # 评估指标工具
│   └── data_utils.py       # 数据处理工具
├── processed_data/         # 处理后的数据
└── trained_models/         # 训练好的模型
```

## 四步工作流

### 1. 数据处理
- 扫描BGP异常事件数据
- 按事件类型分组和合并
- 数据增强 (hijack×20, leak×10, outage×5)
- 保存处理后的数据集

### 2. 特征提取
- 特征提取已集成到数据处理步骤中
- 从BGP更新消息中提取26维特征
- 特征包括路径长度、编辑距离、到达间隔等
- 生成时间窗口特征序列

### 3. 模型训练
- 使用MTAD-GAT模型分别训练三种异常类型
- 支持GPU加速训练
- 自动保存最佳模型检查点

### 4. 模型评估
- 加载训练好的模型进行评估
- 计算准确率、精确率、召回率、F1分数等指标
- 生成详细的评估报告

## 使用方法

### 环境要求
```bash
pip install torch pytorch-lightning scikit-learn pandas numpy
```

### 配置数据路径
编辑 `config.py` 中的数据根目录：
```python
data_root: str = "/path/to/your/anomaly-event-routedata"
```

### 运行方式

#### 1. 运行完整工作流
```bash
python main.py --full
```

#### 2. 运行单个步骤
```bash
# 步骤1: 数据处理
python main.py --step 1

# 步骤2: 特征提取验证
python main.py --step 2

# 步骤3: 模型训练
python main.py --step 3

# 步骤4: 模型评估
python main.py --step 4
```

#### 3. 直接运行模块
```bash
# 数据处理
python data_processor.py

# 模型训练
python train_models.py

# 模型评估
python evaluate_models.py
```

## 配置说明

### 模型配置 (ModelConfig)
- `n_features`: 特征维度 (默认: 26)
- `window_size`: 时间窗口大小 (默认: 10)
- `dropout`: Dropout率 (默认: 0.2)
- `n_layers`: GRU层数 (默认: 3)

### 训练配置 (TrainingConfig)
- `batch_size`: 批次大小 (默认: 32)
- `max_epochs`: 最大训练轮数 (默认: 100)
- `learning_rate`: 学习率 (默认: 0.001)
- `num_workers`: 数据加载工作进程数 (默认: 4)

### 数据配置 (DataConfig)
- `augmentation_factors`: 数据增强倍数
- `event_types`: 事件类型列表
- `data_root`: 数据根目录

## 输出文件

### 处理后数据
- `processed_data/hijack_dataset.pkl`
- `processed_data/leak_dataset.pkl`
- `processed_data/outage_dataset.pkl`

### 训练模型
- `trained_models/{event_type}_checkpoints/`
- `trained_models/model_paths.pkl`

### 评估结果
- `trained_models/evaluation_results.pkl`

## 性能指标

系统评估以下指标：
- **准确率 (Accuracy)**
- **精确率 (Precision)**
- **召回率 (Recall)**
- **F1分数 (F1-Score)**
- **AUC值**
- **早期检测延迟**
- **混淆矩阵**

## 故障排除

### 常见问题
1. **数据文件未找到**: 检查 `config.py` 中的 `data_root` 路径
2. **CUDA内存不足**: 减小 `batch_size` 或使用CPU训练
3. **模型加载失败**: 确保先完成模型训练步骤

### 日志信息
程序运行时会输出详细的进度信息和统计数据，便于调试和监控。

## 扩展说明

### 添加新的事件类型
1. 在 `config.py` 的 `DataConfig` 中添加新事件类型
2. 设置相应的数据增强因子
3. 确保数据目录包含对应的事件数据

### 调整模型参数
修改 `config.py` 中的 `ModelConfig` 和 `TrainingConfig` 类即可。
