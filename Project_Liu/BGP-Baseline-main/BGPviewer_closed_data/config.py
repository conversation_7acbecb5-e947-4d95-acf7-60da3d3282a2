"""
BGP异常检测项目统一配置管理
"""
import os
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, List, Tuple


@dataclass
class ModelConfig:
    """模型相关配置"""
    n_features: int = 26
    window_size: int = 10
    dropout: float = 0.2
    n_layers: int = 3
    gru_hid_dim: int = 78  # 修复：应该等于3*n_features (26*3=78)
    hid_dim: int = 100
    num_classes: int = 2  # 二分类：正常/异常


@dataclass
class TrainingConfig:
    """训练相关配置"""
    batch_size: int = 32
    max_epochs: int = 50
    val_check_interval: float = 1.0
    test_size: float = 0.2
    random_state: int = 42
    learning_rate: float = 0.001  # 降低学习率，更稳定的训练
    step_size: int = 10
    num_workers: int = 10  # DataLoader工作进程数
    # 早停机制配置
    early_stop_patience: int = 15  # 早停耐心值：15个epoch没有改善就停止
    early_stop_min_delta: float = 0.01  # 最小改善阈值，放宽到0.01


@dataclass
class DataConfig:
    """数据相关配置"""
    # 数据增强因子
    augmentation_factors: Dict[str, int] = None
    
    # 事件类型
    event_types: List[str] = None
    
    # 数据根目录
    data_root: str = "/data/data/anomaly-event-routedata"
    
    def __post_init__(self):
        if self.augmentation_factors is None:
            self.augmentation_factors = {
                'hijack': 20,
                'leak': 10,
                'outage': 5
            }
        
        if self.event_types is None:
            self.event_types = ['hijack', 'leak', 'outage']


@dataclass
class PathConfig:
    """路径相关配置"""
    # 项目根目录
    project_root: str = "."
    
    # 数据目录
    processed_data_dir: str = "processed_data"
    trained_models_dir: str = "trained_models"
    
    # 文件名模板 - 使用原始数据
    dataset_file_template: str = "{event_type}_dataset_original.pkl"
    model_paths_file: str = "model_paths.pkl"
    
    def get_processed_data_path(self) -> str:
        """获取处理后数据目录的完整路径"""
        return os.path.join(self.project_root, self.processed_data_dir)
    
    def get_trained_models_path(self) -> str:
        """获取训练模型目录的完整路径"""
        return os.path.join(self.project_root, self.trained_models_dir)
    
    def get_dataset_file_path(self, event_type: str) -> str:
        """获取数据集文件的完整路径"""
        filename = self.dataset_file_template.format(event_type=event_type)
        return os.path.join(self.get_processed_data_path(), filename)
    
    def get_model_paths_file_path(self) -> str:
        """获取模型路径文件的完整路径"""
        return os.path.join(self.get_trained_models_path(), self.model_paths_file)


class BGPConfig:
    """BGP异常检测项目主配置类"""
    
    def __init__(self):
        self.model = ModelConfig()
        self.training = TrainingConfig()
        self.data = DataConfig()
        self.paths = PathConfig()
    
    def get_model_params(self) -> Dict:
        """获取模型参数字典"""
        return {
            'n_features': self.model.n_features,
            'window_size': self.model.window_size,
            'dropout': self.model.dropout,
            'n_layers': self.model.n_layers,
            'gru_hid_dim': self.model.gru_hid_dim,
            'hid_dim': self.model.hid_dim,
            'num_classes': self.model.num_classes
        }
    
    def get_training_params(self) -> Dict:
        """获取训练参数字典"""
        return {
            'batch_size': self.training.batch_size,
            'max_epochs': self.training.max_epochs,
            'val_check_interval': self.training.val_check_interval,
            'test_size': self.training.test_size,
            'random_state': self.training.random_state,
            'learning_rate': self.training.learning_rate,
            'step_size': self.training.step_size,
            'num_workers': self.training.num_workers,
            'early_stop_patience': self.training.early_stop_patience,
            'early_stop_min_delta': self.training.early_stop_min_delta
        }
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        os.makedirs(self.paths.get_processed_data_path(), exist_ok=True)
        os.makedirs(self.paths.get_trained_models_path(), exist_ok=True)
        
        # 为每种事件类型创建checkpoint目录
        for event_type in self.data.event_types:
            checkpoint_dir = os.path.join(
                self.paths.get_trained_models_path(), 
                f"{event_type}_checkpoints"
            )
            os.makedirs(checkpoint_dir, exist_ok=True)


# 全局配置实例
config = BGPConfig()
