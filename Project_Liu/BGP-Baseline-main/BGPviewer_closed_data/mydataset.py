import torch
from torch.utils.data import DataLoader, Dataset, SubsetRandomSampler
import pickle
import numpy as np
from collections import Counter

class SlidingWindowDataset(Dataset):
    def __init__(self, data, window, y, target_dim=None, horizon=0):
        super(SlidingWindowDataset, self).__init__()
        self.data = data
        self.window = window
        self.target_dim = target_dim
        self.horizon = horizon
        self.y = y

    def __getitem__(self, index):
        x = self.data[index : (index + self.window)]
        y = self.y[index + self.window - 1 + self.horizon]  # 修复：预测当前时间点（窗口内最后一个）
        return x, y

    def __len__(self):
        return len(self.data) - self.window + 1  # 修复：预测当前时间点

def create_data_loaders(train_dataset, batch_size, val_split=0.2, shuffle=True, test_dataset=None, num_workers=4):
    train_loader, val_loader, test_loader = None, None, None
    if val_split == 0.0:
        print(f"train_size: {len(train_dataset)}")
        train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle, num_workers=num_workers)
    else:
        dataset_size = len(train_dataset)
        indices = list(range(dataset_size))
        split = int(np.floor(val_split * dataset_size))
        if shuffle:
            np.random.shuffle(indices)
        train_indices, val_indices = indices[split:], indices[:split]

        train_sampler = SubsetRandomSampler(train_indices)
        valid_sampler = SubsetRandomSampler(val_indices)

        train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, sampler=train_sampler, num_workers=num_workers)
        val_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, sampler=valid_sampler, num_workers=num_workers)

        print(f"train_size: {len(train_indices)}")
        print(f"validation_size: {len(val_indices)}")

    if test_dataset is not None:
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=num_workers)
        print(f"test_size: {len(test_dataset)}")

    return train_loader, val_loader, test_loader

if __name__ == "__main__":
    
    # file_path = "/home/<USER>/Packet/CompareMethod/Multiview/balanced_dataset/balanced_S_Outage1.pkl"
    # open_world = "/home/<USER>/Packet/CompareMethod/Multiview/S_res.npy"
    data = "/home/<USER>/Packet/CompareMethod/Multiview/results/sampling_1.0.txt"
    # data = np.load(open_world)
    # print(data.shape)
    # exit()
    sampling_file = "/home/<USER>/Packet/CompareMethod/Multiview/results/sampling_0.8.txt"
    sampling_data = []
    sampling_y = []
    with open(sampling_file, 'r') as f:
        for line in f:
            if line != ' ':
                line = line.rstrip().split(',')
                sampling_data.append(line[:-1])
                sampling_y.append(line[-1])
            else:
                pass
    
    # with open(file_path,'rb') as fp:
    #     data = pickle.load(fp)
    
    data = np.array(sampling_data).astype(np.float32)
    y = np.array(sampling_y).astype(np.int16)
    
    print("The shape of data:", data.shape)
    print("The shape of y:", y.shape)
    # print(y)
    # exit()
    # # 
    # data = np.load(open_world)
    
    # data = data.astype(np.float32)
    # x = data[:, :]
    # y = data[:, -1]

    train_dataset = SlidingWindowDataset(data=data, y=y, window=10)

    # torch.save(train_dataset, '/home/<USER>/Packet/CompareMethod/Multiview/data/open_world.pt')
    torch.save(train_dataset, '/home/<USER>/Packet/CompareMethod/Multiview/data/sampling_0.8.pt')

    train_loader, val_loader, _ = create_data_loaders(train_dataset, batch_size=10)
    print("The size of data:", train_loader.dataset.data.shape)
    