#!/usr/bin/env python3
"""
BGP异常检测模型二次训练（微调）脚本
在已训练模型基础上使用SGDM优化器进行精度提升
"""

import torch
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, LearningRateMonitor, EarlyStopping
from pytorch_lightning.loggers import TensorBoardLogger
import numpy as np
from sklearn.model_selection import train_test_split

from config import config
from mydataset import SlidingWindowDataset, create_data_loaders
from compare_method import MTAD_GAT
from utils.data_utils import load_pickle_data, save_pickle_data, print_dataset_statistics


class FineTunedMTAD_GAT(MTAD_GAT):
    """
    微调版本的MTAD_GAT模型，使用SGDM优化器
    """
    
    def __init__(self, fine_tune_lr=0.0001, momentum=0.9, weight_decay=1e-4, **kwargs):
        super().__init__(**kwargs)
        self.fine_tune_lr = fine_tune_lr
        self.momentum = momentum
        self.weight_decay = weight_decay
        self.save_hyperparameters()
    
    def configure_optimizers(self):
        """配置SGDM优化器用于微调"""
        optimizer = torch.optim.SGD(
            self.parameters(),
            lr=self.fine_tune_lr,
            momentum=self.momentum,
            weight_decay=self.weight_decay,
            nesterov=True  # 使用Nesterov动量
        )
        
        # 使用余弦退火学习率调度器
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=50,  # 50个epoch后重置
            eta_min=self.fine_tune_lr * 0.01  # 最小学习率
        )
        
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "monitor": "val_loss",
                "interval": "epoch",
                "frequency": 1
            }
        }


class BGPModelFineTuner:
    def __init__(self):
        """
        BGP模型微调器
        """
        self.data_dir = config.paths.get_processed_data_path()
        self.models_dir = config.paths.get_trained_models_path()
        self.event_types = config.data.event_types
        
        # 微调参数
        self.fine_tune_params = {
            'fine_tune_lr': 0.0001,  # 较小的学习率
            'momentum': 0.9,
            'weight_decay': 1e-4,
            'max_epochs': 50  # 较少的epoch数
        }
        
        # 从配置获取模型参数
        self.model_params = config.get_model_params()
        self.training_params = config.get_training_params()
        
        # 确保目录存在
        config.ensure_directories()
    
    def load_pretrained_model(self, event_type):
        """加载预训练模型"""
        try:
            model_paths = load_pickle_data(config.paths.get_model_paths_file_path())
            pretrained_path = model_paths[event_type]
            
            print(f"加载预训练模型: {pretrained_path}")
            
            # 加载预训练模型
            pretrained_model = MTAD_GAT.load_from_checkpoint(
                checkpoint_path=pretrained_path,
                **self.model_params
            )
            
            return pretrained_model
            
        except Exception as e:
            print(f"加载预训练模型失败: {e}")
            return None
    
    def create_fine_tuned_model(self, pretrained_model):
        """创建微调模型并转移权重"""
        # 创建新的微调模型，只传递模型参数和微调的优化器参数
        fine_tuned_model = FineTunedMTAD_GAT(
            **self.model_params,
            fine_tune_lr=self.fine_tune_params['fine_tune_lr'],
            momentum=self.fine_tune_params['momentum'],
            weight_decay=self.fine_tune_params['weight_decay']
        )
        
        # 转移预训练权重
        fine_tuned_model.load_state_dict(pretrained_model.state_dict(), strict=False)
        
        print("成功转移预训练权重到微调模型")
        return fine_tuned_model
    
    def load_dataset(self, event_type):
        """加载指定事件类型的数据集"""
        dataset_file = config.paths.get_dataset_file_path(event_type)
        
        try:
            data = load_pickle_data(dataset_file)
        except FileNotFoundError:
            raise FileNotFoundError(f"Dataset file not found: {dataset_file}")
        
        features = data['features']
        labels = data['labels']
        
        print(f"加载 {event_type} 数据集: {features.shape}")
        print_dataset_statistics(features, labels, f"{event_type} dataset")
        
        return features, labels
    
    def create_datasets(self, features, labels):
        """创建训练和验证数据集"""
        # 划分训练和验证集
        train_features, val_features, train_labels, val_labels = train_test_split(
            features, labels,
            test_size=self.training_params['test_size'],
            random_state=self.training_params['random_state'],
            stratify=labels
        )
        
        # 创建数据集
        train_dataset = SlidingWindowDataset(
            data=train_features,
            y=train_labels,
            window=config.model.window_size
        )
        
        val_dataset = SlidingWindowDataset(
            data=val_features,
            y=val_labels,
            window=config.model.window_size
        )
        
        return train_dataset, val_dataset
    
    def fine_tune_model(self, event_type):
        """微调单个事件类型的模型"""
        print(f"\n开始微调 {event_type} 模型...")
        
        # 加载预训练模型
        pretrained_model = self.load_pretrained_model(event_type)
        if pretrained_model is None:
            print(f"跳过 {event_type}，无法加载预训练模型")
            return None
        
        # 创建微调模型
        model = self.create_fine_tuned_model(pretrained_model)
        
        # 加载数据
        features, labels = self.load_dataset(event_type)
        train_dataset, val_dataset = self.create_datasets(features, labels)
        
        # 创建数据加载器
        train_loader, val_loader, _ = create_data_loaders(
            train_dataset,
            batch_size=self.training_params['batch_size'],
            num_workers=self.training_params['num_workers']
        )
        
        # 设置回调函数
        checkpoint_dir = f"{self.models_dir}/{event_type}_fine_tuned_checkpoints"
        checkpoint_callback = ModelCheckpoint(
            dirpath=checkpoint_dir,
            filename=f"{event_type}_fine_tuned_model_{{epoch:02d}}_{{val_loss:.2f}}",
            save_top_k=1,
            monitor="val_loss",
            mode="min",
            save_last=True
        )
        
        # 早停回调
        early_stop_callback = EarlyStopping(
            monitor="val_loss",
            min_delta=config.training.early_stop_min_delta,
            patience=config.training.early_stop_patience,
            verbose=True,
            mode="min"
        )
        
        # 学习率监控
        lr_monitor = LearningRateMonitor(logging_interval='epoch')
        
        # 设置日志记录器
        logger = TensorBoardLogger(
            save_dir=self.models_dir,
            name=f"{event_type}_fine_tune_logs"
        )
        
        # 创建训练器
        trainer = pl.Trainer(
            max_epochs=self.fine_tune_params['max_epochs'],
            val_check_interval=self.training_params['val_check_interval'],
            callbacks=[checkpoint_callback, early_stop_callback, lr_monitor],
            logger=logger,
            accelerator="auto",
            devices="auto",
            log_every_n_steps=10,
            enable_progress_bar=True
        )
        
        print(f"开始微调 {event_type} 模型...")
        print(f"训练集大小: {len(train_dataset)}")
        print(f"验证集大小: {len(val_dataset)}")
        
        # 开始微调
        trainer.fit(model, train_loader, val_loader)
        
        # 获取最佳模型路径
        best_model_path = checkpoint_callback.best_model_path
        
        print(f"✅ {event_type} 模型微调完成!")
        print(f"最佳模型保存在: {best_model_path}")
        
        return {
            'event_type': event_type,
            'model_path': best_model_path,
            'best_val_loss': float(checkpoint_callback.best_model_score),
            'trainer': trainer
        }
    
    def fine_tune_all_models(self):
        """微调所有事件类型的模型"""
        print("开始微调所有模型...")
        print("使用SGDM优化器进行精度提升")
        
        fine_tuned_models = {}
        
        for event_type in self.event_types:
            try:
                result = self.fine_tune_model(event_type)
                if result:
                    fine_tuned_models[event_type] = result
            except Exception as e:
                print(f"微调 {event_type} 模型时出错: {e}")
                continue
        
        # 保存微调后的模型路径
        if fine_tuned_models:
            fine_tuned_paths = {k: v['model_path'] for k, v in fine_tuned_models.items()}
            fine_tuned_paths_file = f"{self.models_dir}/fine_tuned_model_paths.pkl"
            save_pickle_data(fine_tuned_paths, fine_tuned_paths_file)
            
            print(f"\n✅ 所有模型微调完成!")
            print(f"微调后模型路径已保存到: {fine_tuned_paths_file}")
            
            # 打印微调结果摘要
            print(f"\n{'='*60}")
            print("微调结果摘要:")
            print(f"{'='*60}")
            for event_type, result in fine_tuned_models.items():
                print(f"{event_type.upper()}: 最佳验证损失 = {result['best_val_loss']:.4f}")
        
        return fine_tuned_models


def main():
    """主函数"""
    fine_tuner = BGPModelFineTuner()
    fine_tuned_models = fine_tuner.fine_tune_all_models()
    
    if fine_tuned_models:
        print(f"\n🎉 微调完成! 共微调了 {len(fine_tuned_models)} 个模型")
        print("现在可以使用微调后的模型进行评估")
    else:
        print("❌ 没有成功微调任何模型")


if __name__ == "__main__":
    main()
