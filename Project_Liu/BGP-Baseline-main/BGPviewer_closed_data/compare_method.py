from modules import (ConvLayer,
    FeatureAttentionLayer,
    TemporalAttentionLayer,
    GRULayer,
    Forecasting_Model,)
from torch import nn
import pytorch_lightning as pl
import torch
import torch.nn.functional as F

from config import config


class MTAD_GAT(pl.LightningModule):
    def __init__(self, n_features, window_size, dropout, hid_dim, num_classes, gru_hid_dim, n_layers, class_weights=None, training_stage=1):
        super(MTAD_GAT, self).__init__()
        self.n_features = n_features
        self.window_size = window_size
        self.dropout = dropout
        self.hid_dim = hid_dim
        self.out_dim = num_classes

        self.gru_hid_dim = gru_hid_dim
        self.n_layers = n_layers

        # 添加类别权重支持
        self.class_weights = class_weights

        # 训练阶段：1=高学习率全局搜索，2=低学习率精细优化
        self.training_stage = training_stage

        # 注意力正则化权重
        self.attention_reg_weight = 0.01

        # 方案C: 更好的激活函数
        self.activation = nn.GELU()  # 替代ReLU，减少梯度消失

        # 方案B: 残差连接的权重
        self.residual_weight = 0.1

        # 修复BatchNorm配置：应该对特征维度进行标准化
        self.norm0 = nn.BatchNorm1d(self.n_features, momentum=0.01, eps=1e-3)  # 对n_features维度标准化
        self.norm = nn.BatchNorm1d(self.n_features * 3, momentum=0.01, eps=1e-3)  # 对拼接后的特征维度标准化
        self.conv = ConvLayer(self.n_features)
        self.featureAttentionLayer = FeatureAttentionLayer(self.n_features, self.window_size, self.dropout, alpha=0.5)
        self.temporal_gat = TemporalAttentionLayer(self.n_features, self.window_size, self.dropout, alpha=0.5,  use_gatv2=True)

        # 修正GRU输入维度：特征注意力 + 时序注意力 + 原始卷积特征
        self.forecasting_model = Forecasting_Model(self.gru_hid_dim, self.hid_dim, self.out_dim, self.n_layers, dropout)
        self.gru = GRULayer(self.n_features * 3, self.gru_hid_dim, n_layers, dropout)  # 输入维度应该是 3*n_features

    def forward(self, x):
        # 方案B: 保存原始输入用于残差连接
        x_original = x

        # 输入标准化 + 残差连接
        # 调整形状以适配BatchNorm1d: (B, T, F) -> (B*T, F) -> BatchNorm -> (B, T, F)
        B, T, F = x.shape
        x_reshaped = x.view(B * T, F)  # (B*T, F)
        x_norm_reshaped = self.norm0(x_reshaped)  # BatchNorm1d
        x_norm = x_norm_reshaped.view(B, T, F)  # 恢复形状
        # 残差连接：保持部分原始信息
        x_norm = x_norm + self.residual_weight * x_original

        # 1D卷积提取高级特征 + 残差连接
        x_conv = self.conv(x_norm)
        # 如果维度匹配，添加残差连接
        if x_conv.shape == x_norm.shape:
            x_conv = x_conv + self.residual_weight * x_norm

        # 应用更好的激活函数
        x_conv = self.activation(x_conv)

        # 特征注意力：学习特征间关系
        h_fea = self.featureAttentionLayer(x_conv)

        # 时序注意力：学习时间步间关系
        t_fea = self.temporal_gat(x_conv)

        # 组合所有特征：原始卷积特征 + 特征注意力 + 时序注意力
        h_cat = torch.cat([x_conv, h_fea, t_fea], dim=2)  # (b, n, 3*n_features)

        # 第二次标准化 + 残差连接
        # 调整形状以适配BatchNorm1d: (B, T, 3*F) -> (B*T, 3*F) -> BatchNorm -> (B, T, 3*F)
        B, T, F3 = h_cat.shape  # F3 = 3*F
        h_cat_reshaped = h_cat.view(B * T, F3)  # (B*T, 3*F)
        h_cat_norm_reshaped = self.norm(h_cat_reshaped)  # BatchNorm1d
        h_cat_norm = h_cat_norm_reshaped.view(B, T, F3)  # 恢复形状
        # 残差连接：保持部分组合特征信息
        h_cat_final = h_cat_norm + self.residual_weight * h_cat

        # GRU处理时序信息
        _, h_end = self.gru(h_cat_final)  # 获取最后的隐藏状态

        # 最终预测
        predictions = self.forecasting_model(h_end)
        return predictions

    def compute_attention_regularization(self):
        """计算注意力正则化损失 - 修复版本"""
        reg_loss = 0.0

        # 特征注意力正则化 - 鼓励权重有足够的方差，但不要过小
        fea_attention_weights = self.featureAttentionLayer.a
        fea_var = torch.var(fea_attention_weights)
        # 如果方差太小，增加惩罚；如果方差合理，减少惩罚
        fea_reg = torch.max(torch.tensor(0.01, device=fea_var.device) - fea_var, torch.tensor(0.0, device=fea_var.device))
        reg_loss += fea_reg

        # 时序注意力正则化
        temp_attention_weights = self.temporal_gat.a
        temp_var = torch.var(temp_attention_weights)
        temp_reg = torch.max(torch.tensor(0.01, device=temp_var.device) - temp_var, torch.tensor(0.0, device=temp_var.device))
        reg_loss += temp_reg

        # 防止权重过小的正则化
        fea_norm = torch.norm(fea_attention_weights, p=2)
        temp_norm = torch.norm(temp_attention_weights, p=2)

        # 如果权重太小，增加惩罚
        min_norm = 0.1
        if fea_norm < min_norm:
            reg_loss += (min_norm - fea_norm) * 0.1
        if temp_norm < min_norm:
            reg_loss += (min_norm - temp_norm) * 0.1

        return reg_loss

    def training_step(self, batch, batch_idx):
        x, y = batch
        y = y.long()
        x_out = self.forward(x)

        # 使用类别权重的交叉熵损失
        if self.class_weights is not None:
            # 确保类别权重在正确的设备上
            class_weights = self.class_weights.to(x.device)
            ce_loss = F.cross_entropy(x_out, y, weight=class_weights)
        else:
            ce_loss = F.cross_entropy(x_out, y)

        # 添加注意力正则化
        attention_reg = self.compute_attention_regularization()
        total_loss = ce_loss + self.attention_reg_weight * attention_reg

        pred = x_out.argmax(-1)
        accuracy = (pred == y).sum() / pred.shape[0]

        # 记录指标
        self.log("train_loss", total_loss, prog_bar=True)
        self.log("train_ce_loss", ce_loss, prog_bar=False)
        self.log("train_attention_reg", attention_reg, prog_bar=False)
        self.log("train_acc", accuracy, prog_bar=True)

        return total_loss

    def on_train_epoch_end(self) -> None:
        # 在PyTorch Lightning 2.0中，我们需要手动收集和处理输出
        # 这里我们简化处理，主要依赖于training_step中的日志记录
        pass

    def on_validation_epoch_end(self) -> None:
        # 在PyTorch Lightning 2.0中，我们需要手动收集和处理输出
        # 这里我们简化处理，主要依赖于validation_step中的日志记录
        pass

    def validation_step(self, batch, batch_idx):
        x, y = batch
        y = y.long()
        x_out = self.forward(x)

        # 使用类别权重的交叉熵损失
        if self.class_weights is not None:
            # 确保类别权重在正确的设备上
            class_weights = self.class_weights.to(x.device)
            loss = F.cross_entropy(x_out, y, weight=class_weights)
        else:
            loss = F.cross_entropy(x_out, y)

        pred = x_out.argmax(-1)
        accuracy = (pred == y).sum() / pred.shape[0]

        # 记录指标
        self.log("val_loss", loss, prog_bar=True)
        self.log("val_acc", accuracy, prog_bar=True)

        return loss

    def configure_optimizers(self):
        """两阶段优化策略 + 注意力层特殊学习率"""

        if self.training_stage == 1:
            # 阶段1: 高学习率全局搜索 (Adam)
            print("🚀 训练阶段1: 高学习率全局搜索 (Adam) + 注意力层增强学习率")

            # 为不同层设置差异化学习率
            attention_params = []
            forecasting_params = []
            other_params = []

            for name, param in self.named_parameters():
                if 'featureAttentionLayer.a' in name or 'temporal_gat.a' in name:
                    attention_params.append(param)
                    print(f"注意力参数: {name} - 使用高学习率")
                elif 'forecasting_model' in name:
                    forecasting_params.append(param)
                    print(f"预测层参数: {name} - 使用中等学习率")
                else:
                    other_params.append(param)

            optimizer = torch.optim.Adam([
                {'params': attention_params, 'lr': 0.05},    # 注意力层5倍学习率
                {'params': forecasting_params, 'lr': 0.02},  # 预测层2倍学习率
                {'params': other_params, 'lr': 0.01}         # 其他层正常学习率
            ], weight_decay=1e-4, betas=(0.9, 0.999))

            # 使用余弦退火调度器，适合全局搜索
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=30,  # 30个epoch的余弦周期
                eta_min=0.001  # 最小学习率
            )

            return {
                "optimizer": optimizer,
                "lr_scheduler": {
                    "scheduler": scheduler,
                    "interval": "epoch",
                    "frequency": 1
                }
            }

        else:
            # 阶段2: 低学习率精细优化 (SGD + Momentum)
            print("🎯 训练阶段2: 低学习率精细优化 (SGD + Momentum) + 注意力层增强学习率")

            # 为不同层设置差异化学习率
            attention_params = []
            forecasting_params = []
            other_params = []

            for name, param in self.named_parameters():
                if 'featureAttentionLayer.a' in name or 'temporal_gat.a' in name:
                    attention_params.append(param)
                    print(f"注意力参数: {name} - 使用高学习率")
                elif 'forecasting_model' in name:
                    forecasting_params.append(param)
                    print(f"预测层参数: {name} - 使用中等学习率")
                else:
                    other_params.append(param)

            optimizer = torch.optim.SGD([
                {'params': attention_params, 'lr': 0.005},   # 注意力层5倍学习率
                {'params': forecasting_params, 'lr': 0.002}, # 预测层2倍学习率
                {'params': other_params, 'lr': 0.001}        # 其他层正常学习率
            ], momentum=0.9, weight_decay=1e-5, nesterov=True)

            # 使用ReduceLROnPlateau，当验证损失不下降时自动降低学习率
            scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                mode='min',
                factor=0.5,
                patience=8,  # 8个epoch没有改善就衰减
                min_lr=1e-6,
                verbose=True
            )

            return {
                "optimizer": optimizer,
                "lr_scheduler": {
                    "scheduler": scheduler,
                    "monitor": "val_loss",
                    "interval": "epoch",
                    "frequency": 1
                }
            }

    def predict_step(self, batch, batch_idx):
        
        x, y = batch
        # y = y.long()
        # self.starter.record()
        x_out = self.forward(x.float())
        pred = x_out.argmax(-1)
        return pred, y


    