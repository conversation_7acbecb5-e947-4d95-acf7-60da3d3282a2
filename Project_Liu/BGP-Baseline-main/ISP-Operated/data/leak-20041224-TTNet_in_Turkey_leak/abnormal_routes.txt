根据BGP数据动态发现的恶意泄漏路由详情:

路由: BGP4MP|**********|A|*************|2914|***********/21|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|***********/22|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|***********/22|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|***********/21|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|***********/21|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|***********/22|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/20|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/20|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|***********/21|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|***********/22|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|*************/20|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|*************/20|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|***********/21|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|***********/22|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/20|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/20|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|*************/20|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|***********/22|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|***********/21|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|*************/20|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|3741|***********/21|3741 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|3741|***********/22|3741 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|3741|*************/20|3741 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|3741|*************/20|3741 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|***********/21|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|***********/22|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*************/20|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*************/20|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|***********/21|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|***********/22|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|267|2914:420 2914:2201 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|***********/22|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|267|2914:420 2914:2201 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|267|2914:420 2914:2201 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|***********/21|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|267|2914:420 2914:2201 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|***********/21|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|***********/22|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|***********/21|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|***********/22|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/20|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/20|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|***********/22|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|***********/21|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|***********/21|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**************|0|0|174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|***********/22|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**************|0|0|174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/20|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**************|0|0|174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/20|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|**************|0|0|174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|***********/21|513 8220 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|************|0|0||NAG||
AS路径: [513, 8220, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|***********/22|513 8220 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|************|0|0||NAG||
AS路径: [513, 8220, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*************/20|513 8220 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|************|0|0||NAG||
AS路径: [513, 8220, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*************/20|513 8220 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 8513 9021|IGP|************|0|0||NAG||
AS路径: [513, 8220, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 8513, 9021]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|3549|************/20|3549 23148 {6762,9121,32885,64550}|IGP|**************|0|3105|3549:4462 3549:30840|NAG|23148 ************|
AS路径: [3549, 23148, 6762, 9121, 32885, 64550]
发现的私有AS泄漏: ['Private AS detected: [64550]']
---
路由: BGP4MP|**********|A|*************|2914|************/20|2914 3561 23148 {6762,9121,64550}|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:3561|NAG|23148 ************|
AS路径: [2914, 3561, 23148, 6762, 9121, 64550]
发现的私有AS泄漏: ['Private AS detected: [64550]']
---
路由: BGP4MP|**********|A|*************|2914|************/20|2914 3561 23148 {6762,9121,64550}|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:3561|NAG|23148 ************|
AS路径: [2914, 3561, 23148, 6762, 9121, 64550]
发现的私有AS泄漏: ['Private AS detected: [64550]']
---
路由: BGP4MP|**********|A|************|9177|************/20|9177 3320 3561 23148 {6762,9121,64550}|IGP|************|0|0||NAG|23148 ************|
AS路径: [9177, 3320, 3561, 23148, 6762, 9121, 64550]
发现的私有AS泄漏: ['Private AS detected: [64550]']
---
路由: BGP4MP|1103880354|A|************|513|************/20|513 3320 3561 23148 {6762,9121,64550}|IGP|************|0|0||NAG|23148 ************|
AS路径: [513, 3320, 3561, 23148, 6762, 9121, 64550]
发现的私有AS泄漏: ['Private AS detected: [64550]']
---
路由: BGP4MP|1103880370|A|*************|1103|************/20|1103 1273 3561 23148 {6762,9121,64550}|IGP|*************|0|0||NAG|23148 ************|
AS路径: [1103, 1273, 3561, 23148, 6762, 9121, 64550]
发现的私有AS泄漏: ['Private AS detected: [64550]']
---
路由: BGP4MP|1103880377|A|************|4608|************/20|4608 1221 4637 23148 {6762,9121,64550}|IGP|************|0|0||NAG|23148 ************|
AS路径: [4608, 1221, 4637, 23148, 6762, 9121, 64550]
发现的私有AS泄漏: ['Private AS detected: [64550]']
---
路由: BGP4MP|1103880380|A|**********|3333|************/20|3333 286 3561 3561 23148 {6762,9121,64550}|IGP|**********|0|0||NAG|23148 ************|
AS路径: [3333, 286, 3561, 3561, 23148, 6762, 9121, 64550]
发现的私有AS泄漏: ['Private AS detected: [64550]']
---
路由: BGP4MP|1103880424|A|*************|4777|************/20|4777 2497 2914 3561 23148 {6762,9121,64550}|IGP|*************|0|0||NAG|23148 ************|
AS路径: [4777, 2497, 2914, 3561, 23148, 6762, 9121, 64550]
发现的私有AS泄漏: ['Private AS detected: [64550]']
---
路由: BGP4MP|1103880541|A|**************|13129|************/20|13129 174 3561 23148 {6762,9121,64550}|IGP|**************|0|0|174:20666 174:21100 13129:3010 16631:1000|NAG|23148 ************|
AS路径: [13129, 174, 3561, 23148, 6762, 9121, 64550]
发现的私有AS泄漏: ['Private AS detected: [64550]']
---
路由: BGP4MP|1103880629|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103881654|A|**************|13129|************/20|13129 174 3561 23148 {6762,9121,64550}|IGP|**************|0|0|174:11102 174:20666 174:21100 13129:3010 16631:1000|NAG|23148 ************|
AS路径: [13129, 174, 3561, 23148, 6762, 9121, 64550]
发现的私有AS泄漏: ['Private AS detected: [64550]']
---
路由: BGP4MP|1103882572|A|*************|1103|***********/19|1103 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103882572|A|*************|1103|*************/24|1103 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103882572|A|*************|1103|*************/24|1103 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103882595|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103883336|A|*************|3741|*************/24|3741 6774 9000 9121 9021 24891 24891|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 9021, 24891, 24891]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 9021), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103883359|A|*************|1103|*************/24|1103 6774 9000 9121 9021 24891 24891|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 9021, 24891, 24891]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 9021), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103884346|A|*************|1103|*************/24|1103 6774 9000 9121 15924 20728|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103884346|A|*************|1103|*************/24|1103 6774 9000 9121 8685 25230|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885352|A|*************|1103|*************/24|1103 6774 9000 9121 8685 25230|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885378|A|*************|1103|*************/24|1103 6774 9000 9121 15924 20728|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885405|A|*************|1103|***********/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885405|A|*************|1103|*************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885405|A|*************|1103|*************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885405|A|*************|1103|*************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885405|A|*************|1103|************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885405|A|*************|1103|************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885405|A|*************|1103|************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885405|A|*************|1103|***********/16|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885405|A|*************|1103|*************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885427|A|************|513|***********/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885427|A|************|513|***********/16|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885427|A|************|513|************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885427|A|************|513|************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885427|A|************|513|************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885427|A|************|513|*************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885427|A|************|513|*************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885427|A|************|513|*************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885427|A|************|513|*************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885487|A|*************|1103|*************/24|1103 6774 9000 9121 8685 25230|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885513|A|*************|1103|***********/19|1103 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885513|A|*************|1103|*************/24|1103 6774 9000 9121 15924 20728|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885513|A|*************|1103|*************/24|1103 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103885513|A|*************|1103|*************/24|1103 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103888267|A|*************|1103|***********/19|1103 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889696|A|**************|3549|************/24|3549 701 22351 25145 25145 9121 12735 8809|IGP|**************|0|2754|3549:2021 3549:30840|NAG||
AS路径: [3549, 701, 22351, 25145, 25145, 9121, 12735, 8809]
发现的异常三元组及原因: [{'triplet': (25145, 9121, 12735), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889699|A|**************|3549|************/24|3549 701 22351 25145 25145 9121 12735 8809|IGP|**************|0|2604|3549:2141 3549:30840|NAG||
AS路径: [3549, 701, 22351, 25145, 25145, 9121, 12735, 8809]
发现的异常三元组及原因: [{'triplet': (25145, 9121, 12735), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889712|A|*************|2914|************/24|2914 1299 702 22351 25145 25145 9121 12735 8809|IGP|*************|0|235|2914:420 2914:2203 2914:3200 65504:1299|NAG||
AS路径: [2914, 1299, 702, 22351, 25145, 25145, 9121, 12735, 8809]
发现的异常三元组及原因: [{'triplet': (25145, 9121, 12735), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889717|A|************|513|************/24|513 3320 702 22351 25145 25145 9121 12735 8809|IGP|************|0|0||NAG||
AS路径: [513, 3320, 702, 22351, 25145, 25145, 9121, 12735, 8809]
发现的异常三元组及原因: [{'triplet': (25145, 9121, 12735), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889728|A|*************|1103|************/24|1103 3549 702 22351 25145 25145 9121 12735 8809|IGP|*************|0|0||NAG||
AS路径: [1103, 3549, 702, 22351, 25145, 25145, 9121, 12735, 8809]
发现的异常三元组及原因: [{'triplet': (25145, 9121, 12735), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889728|A|**********|3333|************/24|3333 286 702 22351 25145 25145 9121 12735 8809|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 702, 22351, 25145, 25145, 9121, 12735, 8809]
发现的异常三元组及原因: [{'triplet': (25145, 9121, 12735), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889733|A|************|9177|************/24|9177 3320 702 22351 25145 25145 9121 12735 8809|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 702, 22351, 25145, 25145, 9121, 12735, 8809]
发现的异常三元组及原因: [{'triplet': (25145, 9121, 12735), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889742|A|**************|13129|************/24|13129 174 702 22351 25145 25145 9121 12735 8809|IGP|**************|0|0|174:11102 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 702, 22351, 25145, 25145, 9121, 12735, 8809]
发现的异常三元组及原因: [{'triplet': (25145, 9121, 12735), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889754|A|**********|3333|************/24|3333 702 22351 25145 25145 9121 12735 8809|IGP|**********|0|0||NAG||
AS路径: [3333, 702, 22351, 25145, 25145, 9121, 12735, 8809]
发现的异常三元组及原因: [{'triplet': (25145, 9121, 12735), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889754|A|*************|1103|************/24|1103 702 22351 25145 25145 9121 12735 8809|IGP|*************|0|0||NAG||
AS路径: [1103, 702, 22351, 25145, 25145, 9121, 12735, 8809]
发现的异常三元组及原因: [{'triplet': (25145, 9121, 12735), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889771|A|*************|4777|************/24|4777 2497 701 22351 25145 25145 9121 12735 8809|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 701, 22351, 25145, 25145, 9121, 12735, 8809]
发现的异常三元组及原因: [{'triplet': (25145, 9121, 12735), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889906|A|************|513|*************/24|513 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889906|A|************|513|*************/24|513 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889906|A|************|513|***********/19|513 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889914|A|**********|3333|***********/19|3333 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889914|A|**********|3333|*************/24|3333 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889914|A|**********|3333|*************/24|3333 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889914|A|**************|13129|*************/24|13129 6774 9000 9121 13263 21081|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889914|A|**************|13129|*************/24|13129 6774 9000 9121 13263 21081|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889914|A|**************|13129|***********/19|13129 6774 9000 9121 8707|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889916|A|*************|1103|***********/19|1103 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889916|A|*************|1103|*************/24|1103 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889916|A|*************|1103|*************/24|1103 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889920|A|*************|2914|***********/19|2914 3561 1273 6774 6774 6774 9000 9121 8707|IGP|*************|0|32|2914:420 2914:2000 2914:3000 65504:3561|NAG||
AS路径: [2914, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889920|A|**************|3549|***********/19|3549 3561 1273 6774 6774 6774 9000 9121 8707|IGP|**************|0|3002|3549:2201 3549:30840|NAG||
AS路径: [3549, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889921|A|**************|3549|***********/19|3549 3561 1273 6774 6774 6774 9000 9121 8707|IGP|**************|0|2515|3549:2102 3549:30840|NAG||
AS路径: [3549, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889921|A|*************|3741|***********/19|3741 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889921|A|*************|3741|*************/24|3741 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889921|A|*************|3741|*************/24|3741 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889922|A|*************|2914|***********/19|2914 1299 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889922|A|**************|3549|***********/19|3549 1299 6774 9000 9121 8707|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889922|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|66|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889922|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|66|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889922|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|45|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889922|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|45|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889922|A|**************|3549|*************/24|3549 1299 6774 9000 9121 13263 21081|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889922|A|**************|3549|*************/24|3549 1299 6774 9000 9121 13263 21081|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889922|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889922|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889922|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889922|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889927|A|************|9177|***********/19|9177 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889927|A|************|9177|*************/24|9177 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889927|A|************|9177|*************/24|9177 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889932|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|3155|3549:2352 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889932|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|3155|3549:2352 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889932|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|3155|3549:2352 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889933|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889933|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889933|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889934|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889934|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889934|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889950|A|************|4608|***********/19|4608 7474 7473 3561 1273 6774 6774 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [4608, 7474, 7473, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889952|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889952|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889952|A|*********|7018|***********/19|7018 1239 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889972|A|*************|4777|*************/24|4777 2497 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889972|A|*************|4777|*************/24|4777 2516 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103889972|A|*************|4777|***********/19|4777 2516 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890673|A|*************|3741|*************/24|3741 1273 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890673|A|*************|3741|***********/19|3741 702 1299 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 702, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890673|A|*************|3741|*************/24|3741 702 1299 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 702, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890673|A|**********|3333|*************/24|3333 3356 1299 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890673|A|**********|3333|*************/24|3333 3356 1273 6774 6774 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890673|A|**********|3333|***********/19|3333 3356 1273 6774 6774 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890676|A|*************|1103|***********/19|1103 3549 1299 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 3549, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890676|A|*************|1103|*************/24|1103 3549 1299 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 3549, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890676|A|*************|1103|*************/24|1103 3549 1299 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 3549, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890681|A|**************|13129|***********/19|13129 174 6774 9000 9121 8707|IGP|**************|0|0|174:11103 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890681|A|**************|13129|*************/24|13129 174 6774 9000 9121 13263 21081|IGP|**************|0|0|174:11103 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890681|A|**************|13129|*************/24|13129 174 6774 9000 9121 13263 21081|IGP|**************|0|0|174:11103 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890689|A|************|4608|***********/19|4608 1221 4637 1273 6774 6774 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890693|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890693|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890693|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890696|A|************|513|*************/24|513 8220 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 8220, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890696|A|************|513|*************/24|513 8220 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 8220, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890696|A|************|513|***********/19|513 8220 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 8220, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890701|A|*************|3741|*************/24|3741 701 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890701|A|*************|3741|*************/24|3741 701 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890701|A|*************|3741|***********/19|3741 701 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890701|A|**********|3333|***********/19|3333 3356 1299 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890704|A|*************|1103|***********/19|1103 3549 1299 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 3549, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890705|A|************|9177|***********/19|9177 3320 1299 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890705|A|************|9177|*************/24|9177 3320 1299 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890705|A|************|9177|*************/24|9177 3320 1299 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890709|A|**************|13129|*************/24|13129 174 2914 1239 6774 9000 9121 13263 21081|IGP|**************|0|0|174:10037 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890709|A|**************|13129|***********/19|13129 174 3356 1273 6774 6774 6774 9000 9121 8707|IGP|**************|0|0|174:10037 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890713|A|*************|2914|***********/19|2914 3356 1273 6774 6774 6774 9000 9121 8707|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:3356|NAG||
AS路径: [2914, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890714|A|*************|2914|*************/24|2914 3356 1273 6774 6774 6774 9000 9121 13263 21081|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:3356|NAG||
AS路径: [2914, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890718|A|************|4608|*************/24|4608 1221 4637 1239 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890718|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890718|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890718|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890720|A|**************|3549|***********/19|3549 3356 1273 6774 6774 6774 9000 9121 8707|IGP|**************|0|3155|3549:2351 3549:30840|NAG||
AS路径: [3549, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890724|A|************|513|***********/19|513 3320 1239 1273 6774 6774 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 3320, 1239, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890724|A|************|513|*************/24|513 8220 7018 1239 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 8220, 7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890724|A|************|513|*************/24|513 8220 7018 1239 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 8220, 7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890729|A|**********|3333|***********/19|3333 3356 1239 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890729|A|**********|3333|*************/24|3333 3356 1239 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890730|A|************|9177|***********/19|9177 3320 1239 3561 1273 6774 6774 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 1239, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890732|A|*************|1103|***********/19|1103 1273 3356 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 3356, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890737|A|**************|13129|***********/19|13129 174 3561 1273 6774 6774 6774 9000 9121 8707|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890750|A|************|513|***********/19|513 8220 7018 1239 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 8220, 7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890755|A|*************|4777|*************/24|4777 2516 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890756|A|**********|3333|***********/19|3333 1103 1273 3356 1239 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 1103, 1273, 3356, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890757|A|*************|3741|***********/19|3741 1273 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890757|A|*************|3741|*************/24|3741 702 1299 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 702, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890757|A|*************|3741|*************/24|3741 702 1299 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 702, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890759|A|*************|1103|***********/19|1103 1273 701 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 701, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890761|A|************|9177|***********/19|9177 6730 702 1299 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 6730, 702, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890783|A|**********|3333|***********/19|3333 1103 1273 701 1239 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 1103, 1273, 701, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890787|A|*************|3741|*************/24|3741 701 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890787|A|*************|3741|*************/24|3741 701 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890787|A|*************|3741|***********/19|3741 701 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890811|A|*************|4777|*************/24|4777 2516 209 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 209, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890811|A|*************|4777|***********/19|4777 2516 209 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 209, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890811|A|*************|4777|*************/24|4777 2516 7911 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 7911, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103890841|A|*************|4777|***********/19|4777 2516 7911 1239 3561 1273 6774 6774 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 7911, 1239, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891316|A|**********|3333|***********/19|3333 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891318|A|************|9177|***********/19|9177 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891325|A|*************|1103|***********/19|1103 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891329|A|**************|13129|***********/19|13129 174 6774 9000 9121 8707|IGP|**************|0|0|174:11102 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891334|A|************|513|***********/19|513 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891336|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|477|2914:420 2914:2401 2914:3400 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891338|A|*************|2914|***********/19|2914 1299 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891338|A|**************|3549|***********/19|3549 3561 1273 6774 6774 6774 9000 9121 8707|IGP|**************|0|3002|3549:2201 3549:30840|NAG||
AS路径: [3549, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891338|A|**************|3549|***********/19|3549 1299 6774 9000 9121 8707|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891345|A|*********|7018|***********/19|7018 1299 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891346|A|**************|3549|***********/19|3549 1299 6774 9000 9121 8707|IGP|**************|0|3155|3549:2352 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891350|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2955|3549:2193 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891353|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891353|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2869|3549:2021 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891353|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891353|A|*************|3741|***********/19|3741 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891357|A|**************|13129|***********/19|13129 6774 9000 9121 8707|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891370|A|*************|4777|***********/19|4777 2497 3356 1273 6774 6774 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891371|A|*********|7018|***********/19|7018 1239 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891371|A|************|4608|***********/19|4608 7474 7473 3356 1273 6774 6774 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [4608, 7474, 7473, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891397|A|*************|4777|***********/19|4777 2516 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891774|A|**********|3333|***********/19|3333 3356 1273 6774 6774 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891779|A|**************|13129|***********/19|13129 174 6774 9000 9121 8707|IGP|**************|0|0|174:11102 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891784|A|*************|1103|***********/19|1103 3549 1299 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 3549, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891792|A|************|513|***********/19|513 8220 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 8220, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891795|A|************|9177|***********/19|9177 3320 1299 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891806|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891807|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|32|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891808|A|*************|3741|***********/19|3741 701 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891808|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2869|3549:2021 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891809|A|**************|13129|***********/19|13129 174 6774 9000 9121 8707|IGP|**************|0|0|174:11103 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891811|A|*************|1103|***********/19|1103 3549 1299 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 3549, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891812|A|**************|3549|***********/19|3549 1299 6774 9000 9121 8707|IGP|**************|0|3155|3549:2352 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891813|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|44|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891825|A|*************|2914|***********/19|2914 1299 6774 9000 9121 8707|IGP|*************|0|267|2914:420 2914:2201 2914:3200 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891827|A|*************|2914|***********/19|2914 1239 1273 6774 6774 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891828|A|**************|3549|***********/19|3549 1239 1273 6774 6774 6774 9000 9121 8707|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891830|A|**********|3333|***********/19|3333 3356 1239 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891834|A|*********|7018|***********/19|7018 1239 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891836|A|**************|13129|***********/19|13129 174 3561 1273 6774 6774 6774 9000 9121 8707|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891837|A|**************|3549|***********/19|3549 1239 1273 6774 6774 6774 9000 9121 8707|IGP|**************|0|3007|3549:2291 3549:30840|NAG||
AS路径: [3549, 1239, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891837|A|*************|3741|***********/19|3741 7018 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891839|A|*************|1103|***********/19|1103 3549 1239 1273 6774 6774 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 3549, 1239, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891843|A|************|513|***********/19|513 3320 1239 1299 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 3320, 1239, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891854|A|************|9177|***********/19|9177 3320 1239 1299 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 1239, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891857|A|************|4608|***********/19|4608 7474 7473 3356 1239 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [4608, 7474, 7473, 3356, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891863|A|**************|13129|***********/19|13129 174 2914 1239 1273 6774 6774 6774 9000 9121 8707|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 2914, 1239, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891865|A|*************|3741|***********/19|3741 1273 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891865|A|*************|1103|***********/19|1103 1273 3356 1299 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 3356, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891882|A|************|9177|***********/19|9177 6730 702 1299 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 6730, 702, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891885|A|**********|3333|***********/19|3333 1103 1273 3356 1299 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 1103, 1273, 3356, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891892|A|*************|3741|***********/19|3741 702 1299 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 702, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891900|A|*************|4777|***********/19|4777 2516 7911 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 7911, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103891947|A|*************|3741|***********/19|3741 702 701 1239 1273 6774 6774 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 702, 701, 1239, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893634|A|*************|2914|***********/19|2914 3561 1273 6774 6774 6774 9000 9121 8707|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:3561|NAG||
AS路径: [2914, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893634|A|**************|3549|***********/19|3549 3561 1273 6774 6774 6774 9000 9121 8707|IGP|**************|0|3002|3549:2201 3549:30840|NAG||
AS路径: [3549, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893634|A|*************|2914|***********/19|2914 1299 6774 9000 9121 8707|IGP|*************|0|45|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893634|A|**************|3549|***********/19|3549 3561 1273 6774 6774 6774 9000 9121 8707|IGP|**************|0|2515|3549:2102 3549:30840|NAG||
AS路径: [3549, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893634|A|*************|2914|***********/19|2914 1299 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893634|A|**************|3549|***********/19|3549 1299 6774 9000 9121 8707|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893639|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893641|A|**********|3333|***********/19|3333 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893644|A|*************|3741|***********/19|3741 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893648|A|*********|7018|***********/19|7018 1299 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893652|A|************|513|***********/19|513 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893652|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893652|A|**************|13129|***********/19|13129 6774 9000 9121 8707|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893655|A|*************|1103|***********/19|1103 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893658|A|************|9177|***********/19|9177 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893663|A|*************|2914|*************/24|2914 3561 1273 6774 6774 6774 9000 9121 13263 21081|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:3561|NAG||
AS路径: [2914, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893663|A|**************|3549|*************/24|3549 3561 1273 6774 6774 6774 9000 9121 13263 21081|IGP|**************|0|2515|3549:2102 3549:30840|NAG||
AS路径: [3549, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893663|A|*************|2914|*************/24|2914 3561 1273 6774 6774 6774 9000 9121 13263 21081|IGP|*************|0|32|2914:420 2914:2000 2914:3000 65504:3561|NAG||
AS路径: [2914, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893663|A|*************|2914|*************/24|2914 3561 1273 6774 6774 6774 9000 9121 13263 21081|IGP|*************|0|73|2914:420 2914:2000 2914:3000 65504:3561|NAG||
AS路径: [2914, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893663|A|*************|2914|*************/24|2914 3561 1273 6774 6774 6774 9000 9121 13263 21081|IGP|*************|0|82|2914:420 2914:2000 2914:3000 65504:3561|NAG||
AS路径: [2914, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893663|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|76|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893663|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|76|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893664|A|**************|3549|*************/24|3549 1299 6774 9000 9121 13263 21081|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893664|A|**************|3549|*************/24|3549 1299 6774 9000 9121 13263 21081|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893664|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|66|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893664|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|66|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893664|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|45|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893664|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|45|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893664|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893664|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893666|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893666|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893667|A|**********|3333|*************/24|3333 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893667|A|**********|3333|*************/24|3333 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893670|A|*************|3741|*************/24|3741 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893670|A|*************|3741|*************/24|3741 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893674|A|*********|7018|***********/19|7018 1239 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893674|A|*********|7018|*************/24|7018 1299 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893674|A|*********|7018|*************/24|7018 1299 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893677|A|************|513|*************/24|513 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893677|A|************|513|*************/24|513 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893678|A|*************|4777|***********/19|4777 2497 3356 1273 6774 6774 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893681|A|**************|13129|*************/24|13129 6774 9000 9121 13263 21081|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893681|A|**************|13129|*************/24|13129 6774 9000 9121 13263 21081|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893682|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893682|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893682|A|*************|1103|*************/24|1103 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893682|A|*************|1103|*************/24|1103 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893688|A|************|9177|*************/24|9177 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893688|A|************|9177|*************/24|9177 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893690|A|************|4608|***********/19|4608 7474 7473 3356 1273 6774 6774 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [4608, 7474, 7473, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893690|A|************|4608|*************/24|4608 7474 7473 3356 1273 6774 6774 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 7474, 7473, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893701|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893701|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893706|A|*************|4777|*************/24|4777 2497 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893706|A|*************|4777|*************/24|4777 2497 3356 1273 6774 6774 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893706|A|*************|4777|***********/19|4777 2516 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893718|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893718|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893736|A|*************|4777|*************/24|4777 2516 7911 1299 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 7911, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103893764|A|*************|4777|*************/24|4777 2516 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894103|A|************|513|*************/24|513 3320 1273 6774 6774 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894103|A|************|9177|*************/24|9177 3320 1273 6774 6774 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894103|A|************|513|***********/19|513 3320 1273 6774 6774 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 3320, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894104|A|************|513|*************/24|513 8220 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 8220, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894104|A|************|9177|***********/19|9177 3320 1273 6774 6774 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894115|A|*************|1103|***********/19|1103 3549 1299 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 3549, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894115|A|**************|13129|***********/19|13129 174 6774 9000 9121 8707|IGP|**************|0|0|174:11103 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894115|A|**************|13129|*************/24|13129 174 6774 9000 9121 13263 21081|IGP|**************|0|0|174:11103 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894115|A|**************|13129|*************/24|13129 174 6774 9000 9121 13263 21081|IGP|**************|0|0|174:11103 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894121|A|**********|3333|***********/19|3333 3356 1273 6774 6774 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894121|A|**********|3333|*************/24|3333 3356 1299 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894126|A|*************|3741|***********/19|3741 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894129|A|************|9177|***********/19|9177 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894129|A|************|9177|*************/24|9177 3320 1299 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894129|A|************|9177|*************/24|9177 3320 1299 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894130|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894130|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894130|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894131|A|************|513|***********/19|513 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894131|A|************|513|*************/24|513 8220 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 8220, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894136|A|************|4608|***********/19|4608 1221 4637 1273 6774 6774 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894138|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894138|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894138|A|*********|7018|***********/19|7018 1239 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894139|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|65|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894139|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|65|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894139|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|65|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894140|A|**************|13129|***********/19|13129 6774 9000 9121 8707|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894141|A|*************|1103|*************/24|1103 3549 1299 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 3549, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894141|A|*************|1103|*************/24|1103 3549 1299 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 3549, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894141|A|*************|1103|***********/19|1103 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894142|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2869|3549:2021 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894142|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2869|3549:2021 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894142|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2869|3549:2021 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894145|A|*************|2914|***********/19|2914 3561 1273 6774 6774 6774 9000 9121 8707|IGP|*************|0|32|2914:420 2914:2000 2914:3000 65504:3561|NAG||
AS路径: [2914, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894145|A|*************|2914|***********/19|2914 3356 1273 6774 6774 6774 9000 9121 8707|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:3356|NAG||
AS路径: [2914, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894145|A|*************|2914|***********/19|2914 3561 1273 6774 6774 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:3561|NAG||
AS路径: [2914, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894145|A|*************|2914|*************/24|2914 3561 1273 6774 6774 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:3561|NAG||
AS路径: [2914, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894146|A|*************|2914|***********/19|2914 1299 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894148|A|**********|3333|***********/19|3333 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894148|A|**********|3333|*************/24|3333 3356 1239 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894150|A|**************|3549|***********/19|3549 1299 6774 9000 9121 8707|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894154|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2955|3549:2193 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894155|A|*************|3741|*************/24|3741 7018 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894155|A|*************|3741|*************/24|3741 7018 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894157|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894158|A|************|513|*************/24|513 3320 1239 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894158|A|************|513|*************/24|513 3320 1239 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894165|A|*********|7018|***********/19|7018 1299 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894166|A|************|4608|***********/19|4608 1221 4637 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894170|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894175|A|**********|3333|*************/24|3333 1103 3549 1239 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 1103, 3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894181|A|*************|3741|*************/24|3741 702 1299 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 702, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894181|A|*************|3741|*************/24|3741 1273 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894192|A|*********|7018|***********/19|7018 1239 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894194|A|*************|1103|*************/24|1103 1273 701 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894194|A|*************|1103|*************/24|1103 1273 701 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894196|A|************|4608|*************/24|4608 1221 4637 1239 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894196|A|************|4608|*************/24|4608 1221 4637 3356 1273 6774 6774 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894209|A|*************|3741|*************/24|3741 701 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894209|A|*************|3741|*************/24|3741 701 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894210|A|*************|4777|*************/24|4777 2516 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894212|A|************|9177|*************/24|9177 6730 702 1299 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 6730, 702, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894212|A|************|9177|*************/24|9177 6730 702 1299 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 6730, 702, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894226|A|************|4608|*************/24|4608 1221 4637 701 1239 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894226|A|************|4608|*************/24|4608 1221 4637 701 1239 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894230|A|**********|3333|*************/24|3333 1103 1273 701 1239 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 1103, 1273, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103894230|A|**********|3333|*************/24|3333 1103 1273 701 1239 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 1103, 1273, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895607|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895607|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895607|A|**************|3549|*************/24|3549 3561 1273 6774 6774 6774 9000 9121 13263 21081|IGP|**************|0|3002|3549:2201 3549:30840|NAG||
AS路径: [3549, 3561, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895607|A|**************|3549|*************/24|3549 1299 6774 9000 9121 13263 21081|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895607|A|**************|3549|*************/24|3549 1299 6774 9000 9121 13263 21081|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895619|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895619|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895620|A|**************|13129|*************/24|13129 6774 9000 9121 13263 21081|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895620|A|**************|13129|*************/24|13129 6774 9000 9121 13263 21081|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895626|A|*********|7018|*************/24|7018 1299 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895626|A|*********|7018|*************/24|7018 1299 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895626|A|**********|3333|*************/24|3333 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895626|A|**********|3333|*************/24|3333 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895629|A|************|9177|*************/24|9177 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895629|A|************|9177|*************/24|9177 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895630|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895630|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895632|A|*************|3741|*************/24|3741 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895632|A|*************|3741|*************/24|3741 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895635|A|*************|1103|*************/24|1103 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895635|A|*************|1103|*************/24|1103 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895652|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895652|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895652|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895652|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895654|A|*************|4777|*************/24|4777 2516 7911 1299 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 7911, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895654|A|*************|4777|*************/24|4777 2497 3356 1273 6774 6774 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895654|A|************|513|*************/24|513 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895654|A|************|513|*************/24|513 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895845|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895845|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895850|A|************|9177|***********/19|9177 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895853|A|*************|2914|***********/19|2914 1299 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895853|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895867|A|*********|7018|***********/19|7018 1239 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103895871|A|************|513|***********/19|513 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103896332|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|3007|3549:2291 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103896332|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2955|3549:2193 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103896332|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103896338|A|*************|2914|***********/19|2914 1299 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103896339|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103896341|A|************|9177|***********/19|9177 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103896349|A|*********|7018|***********/19|7018 1239 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103896378|A|************|513|***********/19|513 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/23|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/23|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/23|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/23|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/23|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/23|4777 2497 2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/23|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/23|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/23|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/23|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/23|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/18|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/19|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/19|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 13138 13138|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 13138, 13138]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 12735 12735 12735 12735|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 12735, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/19|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 13138 13138|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 13138, 13138]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/18|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/19|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 12735 12735 12735 12735|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 12735, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/20|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/19|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/20|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/20|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/18|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|*************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|**************|0|0|174:11102 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/19|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 13138 13138|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 13138, 13138]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 12735 12735 12735 12735|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 12735, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/19|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 13138 13138|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 13138, 13138]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 12735 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 12735, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/19|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/20|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/20|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/18|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/20|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/20|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|*************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/19|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 13138 13138|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 13138, 13138]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 12735 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 12735, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/18|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/19|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/20|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/20|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/19|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/20|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/20|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/18|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 12735 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 12735, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/19|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 13138 13138|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 13138, 13138]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/20|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/20|3333 12859 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|**********|0|0||NAG||
AS路径: [3333, 12859, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/18|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/19|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/20|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 12735 12735 12735 12735|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 12735, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/19|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 13138 13138|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 13138, 13138]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/23|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|************/24|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*************/24|513 8220 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735|IGP|************|0|0||NAG||
AS路径: [513, 8220, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/19|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 13138 13138|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 13138, 13138]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/20|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/19|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/20|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/20|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/18|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 12735 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 12735, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/19|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/20|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/20|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/18|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/19|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 13138 13138|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 13138, 13138]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/23|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/20|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 12735 12735 12735 12735|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 12735, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/19|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 13138 13138|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 13138, 13138]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/20|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/19|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/20|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/20|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/18|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 12735 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 12735, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/19|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 13138 13138|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 13138, 13138]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 12735 12735 12735 12735|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 12735, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/23|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 28967|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 28967]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/19|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/20|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/20|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/18|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/20|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|************/20|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|************/24|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 29189|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 29189]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|************/19|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|************/20|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|************/20|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|************/18|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 12859 6461 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735 15921 15921|IGP|**********|0|0||NAG||
AS路径: [3333, 12859, 6461, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735, 15921, 15921]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8386 24944|IGP|*************|0|318|2914:420 2914:2205 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8386, 24944]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8386 24944|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8386, 24944]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*********|7018|*************/24|7018 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8386 24944|IGP|*********|0|0|7018:2500|NAG||
AS路径: [7018, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8386, 24944]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8386 24944|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8386, 24944]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8386 24944|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8386, 24944]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|3741|*************/24|3741 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8386 24944|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8386, 24944]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|*************/24|4608 7474 7473 3356 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8386 24944|IGP|************|0|0||NAG||
AS路径: [4608, 7474, 7473, 3356, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8386, 24944]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|*************/24|4777 2516 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8386 24944|IGP|*************|0|267|2914:420 2914:2201 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8386, 24944]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 3356 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8386 24944|IGP|*************|0|13|2914:420 2914:2000 2914:3000 65504:3356|NAG||
AS路径: [2914, 3356, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8386, 24944]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|*************/24|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8386 24944|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8386, 24944]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 3356 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8386 24944|IGP|*************|0|26|2914:420 2914:2000 2914:3000 65504:3356|NAG||
AS路径: [2914, 3356, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8386, 24944]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 3356 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8386 24944|IGP|*************|0|235|2914:420 2914:2203 2914:3200 65504:3356|NAG||
AS路径: [2914, 3356, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8386, 24944]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|*************/24|4608 7474 7473 3356 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8386 24944|IGP|************|0|0||NAG||
AS路径: [4608, 7474, 7473, 3356, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8386, 24944]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|*************/24|4777 2516 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*********|7018|*************/24|7018 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8415 12735|IGP|*********|0|0|7018:2500|NAG||
AS路径: [7018, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8415, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|*************/24|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 5377 8415 12735|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 5377, 8415, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/24|13129 174 6774 9000 9121 13263 21081|IGP|**************|0|0|174:11102 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/24|13129 174 6774 9000 9121 13263 21081|IGP|**************|0|0|174:11102 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|**************|13129|***********/19|13129 174 6774 9000 9121 8707|IGP|**************|0|0|174:11102 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898353|A|**********|3333|***********/19|3333 3356 1273 6774 6774 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898357|A|************|513|*************/24|513 8220 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 8220, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898357|A|************|513|*************/24|513 3320 1239 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898357|A|************|513|***********/19|513 3320 1239 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 3320, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898365|A|*************|3741|***********/19|3741 7018 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898365|A|*************|3741|*************/24|3741 701 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898365|A|*************|3741|*************/24|3741 701 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898374|A|**************|13129|*************/24|13129 174 1299 6774 9000 9121 13263 21081|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898374|A|**************|13129|*************/24|13129 174 1299 6774 9000 9121 13263 21081|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898374|A|**************|13129|***********/19|13129 174 1299 6774 9000 9121 8707|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898400|A|************|4608|***********/19|4608 1221 4637 1273 6774 6774 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898419|A|*************|3741|***********/19|3741 701 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898419|A|*************|3741|*************/24|3741 701 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898419|A|*************|3741|*************/24|3741 701 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 701, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898427|A|************|4608|***********/19|4608 1221 4637 3356 1273 6774 6774 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898467|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898467|A|*************|2914|*************/24|2914 1299 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898467|A|**************|3549|*************/24|3549 1299 6774 9000 9121 13263 21081|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898467|A|**************|3549|*************/24|3549 1299 6774 9000 9121 13263 21081|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898467|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2869|3549:2021 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898467|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2869|3549:2021 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898467|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898467|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898468|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898468|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898469|A|*************|1103|*************/24|1103 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898469|A|*************|1103|*************/24|1103 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898475|A|*************|3741|*************/24|3741 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898475|A|*************|3741|*************/24|3741 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898478|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898478|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898484|A|**********|3333|*************/24|3333 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898484|A|**********|3333|*************/24|3333 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898485|A|************|9177|*************/24|9177 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898485|A|************|9177|*************/24|9177 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898488|A|**************|13129|*************/24|13129 6774 9000 9121 13263 21081|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898488|A|**************|13129|*************/24|13129 6774 9000 9121 13263 21081|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898491|A|************|513|*************/24|513 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898491|A|************|513|*************/24|513 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898506|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898506|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898521|A|*************|2914|***********/19|2914 1299 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898521|A|**************|3549|***********/19|3549 1299 6774 9000 9121 8707|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898523|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2869|3549:2021 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898523|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898523|A|*************|4777|*************/24|4777 2516 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898523|A|*************|4777|*************/24|4777 2516 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898524|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898527|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898527|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898532|A|*********|7018|***********/19|7018 1299 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898539|A|**********|3333|***********/19|3333 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898541|A|**************|13129|***********/19|13129 6774 9000 9121 8707|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898542|A|************|9177|***********/19|9177 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898544|A|************|513|***********/19|513 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898549|A|*************|1103|***********/19|1103 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898552|A|*************|4777|***********/19|4777 2516 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898560|A|*********|7018|***********/19|7018 1239 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898563|A|*************|3741|***********/19|3741 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103898610|A|************|4608|***********/19|4608 1221 4637 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*********/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*********/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|*********/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|*************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|*********/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|*************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*********/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*********/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|*********/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|*************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|*********/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|3741|************/24|3741 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|3741|************/24|3741 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|3741|*********/24|3741 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|3741|*************/24|3741 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*********/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*********/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*********/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*********/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|*********/24|9177 6730 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 6730, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|*************/24|9177 6730 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 6730, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*********/24|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|174:11401 174:20666 174:21100 13129:3030 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/24|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|174:11401 174:20666 174:21100 13129:3030 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|*********/24|4777 2497 2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|*************/24|4777 2497 2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|************/24|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|************/24|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|*********/24|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|*************/24|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|************|9177|***********/19|9177 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|2914|***********/19|2914 1299 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103902695|A|*********|7018|***********/19|7018 1239 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103902697|A|************|513|***********/19|513 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|*************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|*************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|*************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|*************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|*************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|*************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|3741|************/24|3741 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|3741|*************/24|3741 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|************/24|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|*************/24|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|*************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 6730 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 6730, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 6730 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 6730, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|*************/24|9177 6730 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 6730, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|*************/24|4777 2497 2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|*************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|*************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/24|3333 1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/24|3333 1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/24|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|174:10031 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 8437 3303 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 8437, 3303, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 8220 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 8220, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*************/24|513 8220 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 8220, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|*************/24|4777 2497 2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 286 8437 3303 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 8437, 3303, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 174 8437 3303 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|174:11405 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 8437, 3303, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|***********/16|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|1103|***********/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|1103|*************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|1103|*************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|1103|*************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|1103|*************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/19|1103 20965 8517 9121 13263 8466|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103903207|A|************|513|***********/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103903207|A|************|513|***********/16|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103903207|A|************|513|************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103903207|A|************|513|************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103903207|A|************|513|************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103903207|A|************|513|*************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103903207|A|************|513|*************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103903207|A|************|513|*************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103903207|A|************|513|*************/19|513 559 20965 8517 9121 13263 8466|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 13263, 8466]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904858|A|*************|2914|*************/24|2914 1299 6774 9000 9121 15924 20728|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904859|A|**************|3549|*************/24|3549 1299 6774 9000 9121 15924 20728|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904866|A|*************|1103|*************/24|1103 6774 9000 9121 15924 20728|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904866|A|*************|1103|************/24|1103 20965 8517 9121 8705 12908 12908|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 8705, 12908, 12908]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 8705), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904866|A|*************|1103|*************/24|1103 20965 8517 9121 8705 12908 12908|IGP|*************|0|0||NAG||
AS路径: [1103, 20965, 8517, 9121, 8705, 12908, 12908]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 8705), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904866|A|*************|1103|*************/24|1103 6774 9000 9121 8685 25230|IGP|*************|0|0||NAG||
AS路径: [1103, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904867|A|************|9177|*************/24|9177 3320 6774 9000 9121 8685 25230|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904867|A|************|9177|*************/24|9177 3320 6774 9000 9121 15924 20728|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904874|A|*********|7018|*************/24|7018 1299 6774 9000 9121 15924 20728|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1299, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904876|A|************|513|*************/24|513 3320 6774 9000 9121 15924 20728|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904876|A|************|513|*************/24|513 559 20965 8517 9121 8705 12908 12908|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 8705, 12908, 12908]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 8705), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904876|A|************|513|************/24|513 559 20965 8517 9121 8705 12908 12908|IGP|************|0|0||NAG||
AS路径: [513, 559, 20965, 8517, 9121, 8705, 12908, 12908]
发现的异常三元组及原因: [{'triplet': (8517, 9121, 8705), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904876|A|************|513|*************/24|513 3320 6774 9000 9121 8685 25230|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904876|A|**************|3549|*************/24|3549 1299 6774 9000 9121 15924 20728|IGP|**************|0|3155|3549:2352 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904878|A|**************|13129|*************/24|13129 6774 9000 9121 15924 20728|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904879|A|**********|3333|*************/24|3333 6774 9000 9121 15924 20728|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904880|A|**********|3333|*************/24|3333 6774 9000 9121 8685 25230|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904885|A|*************|3741|*************/24|3741 6774 9000 9121 8685 25230|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904885|A|*************|3741|*************/24|3741 6774 9000 9121 15924 20728|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904887|A|*************|2914|*************/24|2914 1299 6774 9000 9121 8685 25230|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1299|NAG||
AS路径: [2914, 1299, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904887|A|**************|3549|*************/24|3549 1299 6774 9000 9121 8685 25230|IGP|**************|0|3204|3549:2401 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904887|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 15924 20728|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904906|A|*************|4777|*************/24|4777 2497 3356 1273 6774 6774 6774 9000 9121 15924 20728|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904907|A|**************|3549|*************/24|3549 1299 6774 9000 9121 8685 25230|IGP|**************|0|3155|3549:2352 3549:30840|NAG||
AS路径: [3549, 1299, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904907|A|**************|13129|*************/24|13129 6774 9000 9121 8685 25230|IGP|**************|0|0|13129:2110 13129:3010|NAG||
AS路径: [13129, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904917|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 8685 25230|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904929|A|*********|7018|*************/24|7018 1299 6774 9000 9121 8685 25230|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1299, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904929|A|*********|7018|*************/24|7018 1299 6774 9000 9121 15924 20728|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1299, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904931|A|*************|4777|*************/24|4777 2497 3356 1273 6774 6774 6774 9000 9121 8685 25230|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904952|A|************|9177|*************/24|9177 3320 1273 6774 6774 6774 9000 9121 15924 20728|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 1273, 6774, 6774, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904952|A|************|9177|*************/24|9177 3320 1273 6774 6774 6774 9000 9121 8685 25230|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 1273, 6774, 6774, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904955|A|************|513|*************/24|513 3320 1299 6774 9000 9121 8685 25230|IGP|************|0|0||NAG||
AS路径: [513, 3320, 1299, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904955|A|************|513|*************/24|513 8220 6774 9000 9121 15924 20728|IGP|************|0|0||NAG||
AS路径: [513, 8220, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904960|A|**********|3333|*************/24|3333 3356 1273 6774 6774 6774 9000 9121 8685 25230|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904960|A|**********|3333|*************/24|3333 3356 1273 6774 6774 6774 9000 9121 15924 20728|IGP|**********|0|0||NAG||
AS路径: [3333, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904966|A|**************|13129|*************/24|13129 174 6774 9000 9121 15924 20728|IGP|**************|0|0|174:11102 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904980|A|************|513|*************/24|513 8220 6774 9000 9121 8685 25230|IGP|************|0|0||NAG||
AS路径: [513, 8220, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904989|A|*************|4777|*************/24|4777 2516 7911 1299 6774 9000 9121 15924 20728|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 7911, 1299, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103904989|A|*************|4777|*************/24|4777 2516 7911 1299 6774 9000 9121 8685 25230|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 7911, 1299, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103905002|A|************|4608|*************/24|4608 1221 4637 1273 6774 6774 6774 9000 9121 8685 25230|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 1273, 6774, 6774, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103905002|A|************|4608|*************/24|4608 1221 4637 1273 6774 6774 6774 9000 9121 15924 20728|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 1273, 6774, 6774, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103905031|A|************|4608|*************/24|4608 1221 4637 3356 1273 6774 6774 6774 9000 9121 8685 25230|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 8685, 25230]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8685), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103905032|A|************|4608|*************/24|4608 1221 4637 3356 1273 6774 6774 6774 9000 9121 15924 20728|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 3356, 1273, 6774, 6774, 6774, 9000, 9121, 15924, 20728]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 15924), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|************|513|*************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|267|2914:420 2914:2201 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|3741|*************/24|3741 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/24|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|174:11101 174:20666 174:21100 13129:3030 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/24|3333 286 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|*************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|*************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|*************/24|4777 2497 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|*************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/24|13129 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|5400:49 13129:2110 13129:3010|NAG||
AS路径: [13129, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|*************/24|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|*************/24|9177 6730 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [9177, 6730, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|*************/24|4777 2497 2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|*************/24|513 8220 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|************|0|0||NAG||
AS路径: [513, 8220, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|*************/24|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 702 701 6461 31654 15924|IGP|**************|0|0|174:10035 174:20666 174:21000 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 702, 701, 6461, 31654, 15924]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|291|2914:420 2914:2202 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|234|2914:420 2914:2203 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|9|2914:420 2914:2000 2914:3000|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|1103|************/24|1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**********|0|0||NAG||
AS路径: [3333, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|2914|************/24|2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|267|2914:420 2914:2201 2914:3200|NAG||
AS路径: [2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [513, 3320, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|3741|************/24|3741 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [3741, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 174 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**************|0|0|174:11102 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|4608|************/24|4608 1221 4637 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 1103 1273 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**********|0|0||NAG||
AS路径: [3333, 1103, 1273, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|*************|4777|************/24|4777 2497 2914 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|*************|0|0||NAG||
AS路径: [4777, 2497, 2914, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 8220 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [513, 8220, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|13129|************/24|13129 174 8437 3303 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**************|0|0|174:11405 174:20666 174:21100 13129:3010 16631:1000|NAG||
AS路径: [13129, 174, 8437, 3303, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|9177|************/24|9177 3320 8437 3303 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 8437, 3303, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**********|3333|************/24|3333 286 8437 3303 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|**********|0|0||NAG||
AS路径: [3333, 286, 8437, 3303, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|************|513|************/24|513 3320 8437 3303 5400 15854 9121 9121 9121 9121 9121 9121 22351 3356 701 702 5377 8415 12735 12735 12735|IGP|************|0|0||NAG||
AS路径: [513, 3320, 8437, 3303, 5400, 15854, 9121, 9121, 9121, 9121, 9121, 9121, 22351, 3356, 701, 702, 5377, 8415, 12735, 12735, 12735]
发现的异常三元组及原因: [{'triplet': (15854, 9121, 22351), 'reason': 'Leaked provider-learned route to another provider/peer'}]
---
路由: BGP4MP|**********|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2955|3549:2193 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2955|3549:2193 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|**********|3333|***********/19|3333 6774 9000 9121 8707|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/24|3333 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|**********|A|**********|3333|*************/24|3333 6774 9000 9121 13263 21081|IGP|**********|0|0||NAG||
AS路径: [3333, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906917|A|************|9177|***********/19|9177 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906917|A|************|9177|*************/24|9177 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906917|A|************|9177|*************/24|9177 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [9177, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906927|A|*************|3741|*************/24|3741 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906927|A|*************|3741|*************/24|3741 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906933|A|************|513|***********/19|513 3320 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906933|A|************|513|*************/24|513 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906933|A|************|513|*************/24|513 3320 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [513, 3320, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906936|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906936|A|*************|2914|*************/24|2914 1239 6774 9000 9121 13263 21081|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906936|A|*************|2914|***********/19|2914 1239 6774 9000 9121 8707|IGP|*************|0|9|2914:420 2914:2000 2914:3000 65504:1239|NAG||
AS路径: [2914, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906937|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906937|A|**************|3549|*************/24|3549 1239 6774 9000 9121 13263 21081|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906937|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906937|A|*********|7018|*************/24|7018 1239 6774 9000 9121 13263 21081|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906937|A|*********|7018|***********/19|7018 1239 6774 9000 9121 8707|IGP|*********|0|0|7018:5000|NAG||
AS路径: [7018, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906941|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2869|3549:2021 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906941|A|**************|3549|***********/19|3549 1239 6774 9000 9121 8707|IGP|**************|0|2614|3549:2141 3549:30840|NAG||
AS路径: [3549, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906954|A|*************|3741|***********/19|3741 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [3741, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906975|A|*************|4777|***********/19|4777 2516 1239 6774 9000 9121 8707|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906975|A|*************|4777|*************/24|4777 2516 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906975|A|*************|4777|*************/24|4777 2516 1239 6774 9000 9121 13263 21081|IGP|*************|0|0||NAG||
AS路径: [4777, 2516, 1239, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906976|A|************|4608|***********/19|4608 1221 4637 6774 9000 9121 8707|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 8707]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 8707), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906976|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
路由: BGP4MP|1103906976|A|************|4608|*************/24|4608 1221 4637 6774 9000 9121 13263 21081|IGP|************|0|0||NAG||
AS路径: [4608, 1221, 4637, 6774, 9000, 9121, 13263, 21081]
发现的异常三元组及原因: [{'triplet': (9000, 9121, 13263), 'reason': 'Leaked customer-learned route to another customer'}]
---
