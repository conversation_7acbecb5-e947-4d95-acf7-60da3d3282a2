import os
import pandas as pd
import numpy as np
import torch
from sklearn.preprocessing import StandardScaler
from dataset import SlidingWindowDataset
import joblib

def load_and_process_event_data(event_name, base_dir):
    event_path = os.path.join(base_dir, event_name)
    features_file = os.path.join(event_path, 'features', "fea.json")
    
    if not os.path.exists(features_file):
        print(f"Skipping {event_name}: Features file not found at {features_file}")
        return None, None
    
    try:
        # Read from fea.json, which is a standard JSON array
        features_df = pd.read_json(features_file)
        
        # The last column is the label, others are features
        labels = features_df.iloc[:, -1].values.astype(int)
        features = features_df.iloc[:, :-1].values.astype(np.float32)
        
        print(f"Loaded {event_name}: Features shape {features.shape}, Labels shape {labels.shape}")
        return features, labels
    except Exception as e:
        print(f"Error loading/processing {event_name} data: {e}. Skipping.")
        return None, None


if __name__ == "__main__":
    base_data_dir = '/data/Project_Liu/BGP-Baseline-main/ISP-Operated/data'
    output_dir = '/data/Project_Liu/BGP-Baseline-main/ISP-Operated/output'
    window_size = 10

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Use all events for training
    all_events = [d for d in os.listdir(base_data_dir) if os.path.isdir(os.path.join(base_data_dir, d))]
    train_event_names = all_events
    
    print(f"All {len(train_event_names)} events selected for training: {train_event_names}")

    # --- Process Training Data ---
    all_train_features = []
    all_train_labels = []
    print("\n--- Loading and processing training events ---")
    for event_name in train_event_names:
        features, labels = load_and_process_event_data(event_name, base_data_dir)
        if features is not None and labels is not None:
            all_train_features.append(features)
            all_train_labels.append(labels)

    if not all_train_features:
        print("No valid training data found. Exiting.")
        exit()

    all_train_features_np = np.concatenate(all_train_features, axis=0)
    all_train_labels_np = np.concatenate(all_train_labels, axis=0)
    print(f"Combined training data: Features shape {all_train_features_np.shape}, Labels shape {all_train_labels_np.shape}")

    # --- Scale Features ---
    print("\n--- Scaling features ---")
    scaler = StandardScaler()
    scaled_train_features = scaler.fit_transform(all_train_features_np)
    
    # Save the scaler for later use in prediction
    scaler_path = os.path.join(output_dir, 'scaler.pkl')
    joblib.dump(scaler, scaler_path)
    print(f"Scaler saved to {scaler_path}")

    print(f"Scaled training features shape: {scaled_train_features.shape}")

    # --- Create and Save Datasets ---
    print("\n--- Creating and saving training dataset ---")
    train_dataset = SlidingWindowDataset(
        data=scaled_train_features,
        window=window_size,
        y=all_train_labels_np
    )

    train_output_path = os.path.join(output_dir, 'train_data.pt')
    torch.save(train_dataset, train_output_path)

    print(f"Successfully saved training dataset to {train_output_path}")
    print("Data preparation complete.") 