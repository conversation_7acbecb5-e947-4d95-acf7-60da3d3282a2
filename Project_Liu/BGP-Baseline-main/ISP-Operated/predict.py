import torch
import pytorch_lightning as pl
import os
import time
import pandas as pd
import numpy as np
import joblib
import sys
import glob
import re
from collections import Counter
from classification import mymodel
from dataset import SlidingWindowDataset
from torch.utils.data import DataLoader

# --- 辅助函数 (从 compare_alarms.py 移植) ---
def get_date_from_filename(filename):
    """从文件名中提取 YYYY-MM-DD 格式的日期。"""
    match = re.search(r'(\d{4}-\d{2}-\d{2})', filename)
    return match.group(1) if match else None

def format_timestamp(date_str, minute_of_day):
    """将日期和天内分钟数格式化为 'YYYY-MM-DD HH:MM' 字符串。"""
    hour = int(minute_of_day) // 60
    minute = int(minute_of_day) % 60
    return f"{date_str} {hour:02d}:{minute:02d}"

if __name__ == "__main__":
    
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # --- 配置 ---
    PREDICTION_DATA_DIR = '/data/five_month/features'
    # 修正路径问题，确保所有路径都基于脚本自身位置
    script_dir = os.path.dirname(os.path.abspath(__file__))
    MODEL_OUTPUT_DIR = os.path.join(script_dir, 'model_output')
    RESULTS_FILE_PATH = os.path.join(script_dir, 'prediction_results.txt')
    SCALER_PATH = '/data/Project_Liu/BGP-Baseline-main/ISP-Operated/output/scaler.pkl'
    WINDOW_SIZE = 10 

    # --- 加载模型和Scaler ---
    model_path = os.path.join(MODEL_OUTPUT_DIR, 'last.ckpt')
    if not os.path.exists(model_path):
        print(f"Error: Model checkpoint not found at {model_path}.")
        sys.exit(1)
    
    print(f"Found model checkpoint: {model_path}")

    if not os.path.exists(SCALER_PATH):
        print(f"Error: Scaler file not found at {SCALER_PATH}.")
        sys.exit(1)
    
    print(f"Loading scaler from: {SCALER_PATH}")
    scaler = joblib.load(SCALER_PATH)

    # --- 加载预测文件 ---
    prediction_files = sorted(glob.glob(os.path.join(PREDICTION_DATA_DIR, "*.csv")))
    if not prediction_files:
        print(f"Error: No '.csv' files found in {PREDICTION_DATA_DIR}")
        sys.exit(1)
    print(f"Found {len(prediction_files)} prediction files to process.")

    # --- 加载模型 ---
    try:
        trained_model = mymodel.load_from_checkpoint(
            checkpoint_path=model_path,
            WINDOW_SIZE=WINDOW_SIZE,
            INPUT_SIZE=26, # 根据最终事实硬编码
            Hidden_SIZE=100,
            LSTM_layer_NUM=2,
            Num_class=2
        ).to(device).eval()
        print("Successfully loaded trained model!")
    except Exception as e:
        print(f"Error loading model from checkpoint: {e}")
        sys.exit(1)

    # --- 统一预测流程 ---
    print("\n--- Starting Prediction ---")
    
    results_to_save = [
        "ISP-Operated Model Prediction Report",
        "===================================="
    ]
    total_running_time = 0
    total_unique_alarms = set()

    # --- 恢复批次处理 ---
    batches = [
        ("Days 1-5", prediction_files[0:5]),
        ("Days 6-10", prediction_files[5:10]),
        ("Days 11-15", prediction_files[10:15]),
        ("Days 16-20", prediction_files[15:20]),
        ("Days 21-25", prediction_files[20:25]),
        ("Days 26-31", prediction_files[25:31])
    ]
    
    for batch_name, files_in_batch in batches:
        batch_header = f"\nProcessing batch: '{batch_name}'"
        print(batch_header)
        results_to_save.append(batch_header)
        
        batch_alarm_timestamps = set()
        batch_start_time = time.time()

        for i, file_path in enumerate(files_in_batch):
            filename = os.path.basename(file_path)
            date_str = get_date_from_filename(filename)
            if not date_str:
                print(f"Warning: Could not extract date from {filename}. Skipping.")
                continue

            print(f"  - Predicting on file ({i+1}/{len(files_in_batch)}): {filename}")
            
            try:
                df = pd.read_csv(file_path)
                if df.empty:
                    print(f"    - Warning: File {filename} is empty. Skipping.")
                    continue

                timestamps = df['index'].values
                features = df.iloc[:, 1:].values.astype(np.float32)

                if features.shape[0] < WINDOW_SIZE:
                    continue

                scaled_features = scaler.transform(features)
                dummy_y = np.zeros(len(scaled_features))
                dataset = SlidingWindowDataset(data=scaled_features, window=WINDOW_SIZE, y=dummy_y)
                
                if len(dataset) == 0:
                    continue

                loader = DataLoader(dataset, batch_size=128, shuffle=False)
                
                with torch.no_grad():
                    for j, (batch_x, _) in enumerate(loader):
                        batch_x = batch_x.to(device)
                        x_out = trained_model(batch_x) 
                        preds = x_out.argmax(-1)
                        
                        alarm_indices = (preds == 1).nonzero(as_tuple=True)[0]
                        for alarm_idx in alarm_indices:
                            real_idx = j * 128 + alarm_idx.item() + WINDOW_SIZE - 1
                            if real_idx < len(timestamps):
                                unique_ts = format_timestamp(date_str, timestamps[real_idx])
                                # 先添加到批次集合
                                batch_alarm_timestamps.add(unique_ts)

            except Exception as e:
                print(f"    - Error processing file {filename}: {e}")

        batch_end_time = time.time()
        batch_running_time = batch_end_time - batch_start_time
        total_running_time += batch_running_time
        
        # 更新总告警集合
        total_unique_alarms.update(batch_alarm_timestamps)
        
        # 报告批次结果
        result_line = f"--- Result for '{batch_name}': {len(batch_alarm_timestamps)} unique alarms detected. (Time: {batch_running_time:.2f}s) ---"
        print(result_line)
        results_to_save.append(result_line)

    completion_line = f"\nPrediction complete. Total processing time: {total_running_time:.2f} seconds."
    # 使用与 compare_alarms.py 一致的逻辑计算，并打印结果
    total_alarms_line = f"Total unique alarms for all days: {len(total_unique_alarms)}"
    
    print(completion_line)
    print(total_alarms_line)
    results_to_save.append(completion_line)
    results_to_save.append(total_alarms_line)

    # --- 保存结果 ---
    try:
        with open(RESULTS_FILE_PATH, 'w') as f:
            for line in results_to_save:
                f.write(line + '\n')
        print(f"\nPrediction results successfully saved to {RESULTS_FILE_PATH}")
    except Exception as e:
        print(f"\nError saving results to file: {e}")
    
