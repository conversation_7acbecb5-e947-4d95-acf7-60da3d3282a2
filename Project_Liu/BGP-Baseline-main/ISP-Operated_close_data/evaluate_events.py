import os
import sys
import time
import torch
import pandas as pd
import numpy as np
import joblib
import argparse
import pytorch_lightning as pl
from torch.utils.data import DataLoader
from sklearn.metrics import f1_score, precision_score, recall_score, confusion_matrix

from classification import mymodel
from dataset import SlidingWindowDataset

def find_optimal_threshold(model, device, df, scaler, window_size):
    """
    Evaluates a single event across a range of thresholds to find the best F1 score.
    """
    model.eval()
    
    true_labels = df.iloc[:, -1].values.astype(int)
    features = df.iloc[:, :-1].values.astype(np.float32)
    
    scaled_features = scaler.transform(features)
    
    dataset = SlidingWindowDataset(data=scaled_features, window=window_size, y=true_labels)
    loader = DataLoader(dataset, batch_size=100, shuffle=False)
    
    # Get model's probability predictions for the positive class (anomaly)
    all_probs = []
    with torch.no_grad():
        for features_batch, _ in loader:
            features_batch = features_batch.to(device)
            model_output, _ = model(features_batch)
            # Get probabilities for class '1'
            probs = model_output[:, 1].cpu().numpy()
            all_probs.extend(probs)
    
    all_probs = np.array(all_probs)
    corresponding_true_labels = true_labels[window_size - 1:]

    if len(all_probs) != len(corresponding_true_labels):
        min_len = min(len(all_probs), len(corresponding_true_labels))
        all_probs = all_probs[:min_len]
        corresponding_true_labels = corresponding_true_labels[:min_len]

    best_metrics = {
        'f1': -1.0,
        'threshold': 0.5,
        'recall': 0.0,
        'precision': 0.0,
        'alarms': 0,
        'false_alarms': 0
    }

    # Iterate over a range of thresholds to find the best one
    thresholds = np.arange(0.05, 1.0, 0.05)
    for threshold in thresholds:
        predictions = (all_probs >= threshold).astype(int)
        
        f1 = f1_score(corresponding_true_labels, predictions, zero_division=0)
        
        if f1 > best_metrics['f1']:
            best_metrics['f1'] = f1
            best_metrics['threshold'] = threshold
            best_metrics['recall'] = recall_score(corresponding_true_labels, predictions, zero_division=0)
            best_metrics['precision'] = precision_score(corresponding_true_labels, predictions, zero_division=0)
            
            try:
                _, fp, _, tp = confusion_matrix(corresponding_true_labels, predictions).ravel()
                best_metrics['alarms'] = int(tp + fp)
                best_metrics['false_alarms'] = int(fp)
            except ValueError:
                # This can happen if only one class is present in predictions
                best_metrics['alarms'] = np.sum(predictions)
                best_metrics['false_alarms'] = np.sum(predictions[corresponding_true_labels == 0])

    return (best_metrics['alarms'], best_metrics['false_alarms'], best_metrics['f1'],
            best_metrics['recall'], best_metrics['precision'], best_metrics['threshold'])


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Evaluate a specialized BGP event model.")
    parser.add_argument('--event_type', type=str, required=True, choices=['hijack', 'leak', 'outage'],
                        help="The type of event model to evaluate.")
    args = parser.parse_args()
    event_type = args.event_type

    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # --- Configuration based on event_type ---
    EVAL_DATA_DIR = '/data/data/anomaly-event-routedata'
    MODEL_DIR = f'./model_output_{event_type}/'
    DATA_DIR = f'./output_{event_type}/'
    RESULTS_FILE_PATH = os.path.join(DATA_DIR, f'evaluation_results_{event_type}.txt')
    SCALER_PATH = os.path.join(DATA_DIR, 'scaler.pkl')
    WINDOW_SIZE = 10 

    # --- Load Model and Scaler ---
    model_path = os.path.join(MODEL_DIR, 'last.ckpt')
    if not os.path.exists(model_path):
        print(f"Error: Model checkpoint not found at {model_path}")
        sys.exit(1)
        
    if not os.path.exists(SCALER_PATH):
        print(f"Error: Scaler file not found at {SCALER_PATH}")
        sys.exit(1)

    print(f"Loading model from: {model_path}")
    print(f"Loading scaler from: {SCALER_PATH}")
    scaler = joblib.load(SCALER_PATH)

    # --- Find events and determine model input size ---
    if not os.path.isdir(EVAL_DATA_DIR):
        print(f"Error: Evaluation data directory not found at {EVAL_DATA_DIR}")
        sys.exit(1)
    
    # Filter for events of the specified type
    all_events_found = sorted([d for d in os.listdir(EVAL_DATA_DIR) if os.path.isdir(os.path.join(EVAL_DATA_DIR, d))])
    events_to_evaluate = [e for e in all_events_found if e.startswith(f'{event_type}-')]
    print(f"Found {len(events_to_evaluate)} '{event_type}' events to evaluate.")
    
    # Determine INPUT_SIZE dynamically from the first event
    input_size = -1
    first_event_json_path = ""
    for event in events_to_evaluate:
        json_path = os.path.join(EVAL_DATA_DIR, event, 'features', 'fea.json')
        if os.path.exists(json_path):
            first_event_json_path = json_path
            break
    
    if not first_event_json_path:
        print("Error: Could not find any 'fea.json' in any event directory.")
        sys.exit(1)

    try:
        df_first = pd.read_json(first_event_json_path)
        input_size = df_first.shape[1] - 1 # All columns except the last 'label' one
        print(f"Detected INPUT_SIZE from data: {input_size}")
    except Exception as e:
        print(f"Error reading first event JSON to determine input size: {e}")
        sys.exit(1)

    # --- Load Model ---
    try:
        # We need to pass the same arguments as during training for the model to load correctly.
        # Even though class_weights is not used in evaluation, the model's state_dict expects it.
        # We can pass a placeholder tensor.
        num_classes = 2
        placeholder_weights = torch.ones(num_classes)

        trained_model = mymodel.load_from_checkpoint(
            checkpoint_path=model_path,
            WINDOW_SIZE=WINDOW_SIZE,
            INPUT_SIZE=input_size,
            Hidden_SIZE=100,
            LSTM_layer_NUM=2,
            Num_class=num_classes,
            dropout_rate=0.5,
            class_weights=placeholder_weights
        )
        print("Successfully loaded trained model!")
    except Exception as e:
        print(f"Error loading model from checkpoint: {e}")
        sys.exit(1)

    # --- Process each event ---
    results_to_save = []
    print("\n--- Starting Evaluation of Events (finding optimal threshold) ---")
    for event_name in events_to_evaluate:
        line = f"\n--- Processing Event: {event_name} ---"
        print(line)
        results_to_save.append(line)
        
        json_path = os.path.join(EVAL_DATA_DIR, event_name, 'features', 'fea.json')

        if not os.path.exists(json_path):
            line = f"Skipping: 'fea.json' not found for event {event_name}"
            print(line)
            results_to_save.append("  " + line)
            continue
        
        try:
            df_event = pd.read_json(json_path)
            if df_event.empty:
                line = "Skipping: JSON file is empty."
                print(line)
                results_to_save.append("  " + line)
                continue

            alarms, false_positives, f1, recall, precision, threshold = find_optimal_threshold(
                trained_model, device, df_event, scaler, WINDOW_SIZE
            )

            results_to_save.append(f"  Best F1 Score: {f1:.4f} (at threshold {threshold:.2f})")
            results_to_save.append(f"  Recall:        {recall:.4f}")
            results_to_save.append(f"  Precision:     {precision:.4f}")
            results_to_save.append(f"  报警次数 (Total Alarms): {alarms}")
            results_to_save.append(f"  误报次数 (False Alarms): {false_positives}")
            
            # Print results to console
            for line in results_to_save[-5:]: 
                print(line)

        except Exception as e:
            line = f"An error occurred while processing event {event_name}: {e}"
            print(line)
            results_to_save.append(line)
            
    print("\nEvaluation complete.")
    results_to_save.append("\nEvaluation complete.")

    # --- Save results to file ---
    try:
        with open(RESULTS_FILE_PATH, 'w') as f:
            for line in results_to_save:
                f.write(line + '\n')
        print(f"\nEvaluation results successfully saved to {RESULTS_FILE_PATH}")
    except Exception as e:
        print(f"\nError saving results to file: {e}") 