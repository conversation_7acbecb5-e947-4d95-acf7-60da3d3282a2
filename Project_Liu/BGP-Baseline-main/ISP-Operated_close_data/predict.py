import torch
import pytorch_lightning as pl
import os
import time
import pandas as pd
import numpy as np
import joblib
import sys
from collections import Counter
from classification import mymodel
from dataset import SlidingWindowDataset
from torch.utils.data import DataLoader

def predict_on_data(model, device, data, scaler, window_size):
    """
    Runs prediction on a given dataframe.
    """
    # Assuming all columns in the loaded CSV are features
    features = data.values.astype(np.float32)

    # Scale the features
    scaled_features = scaler.transform(features)

    # Create a dataset and dataloader
    # We provide a dummy 'y' array as it's required by the dataset but not used in prediction
    dummy_y = np.zeros(len(scaled_features))
    dataset = SlidingWindowDataset(data=scaled_features, window=window_size, y=dummy_y)
    
    # If dataset is too small to create a window, return 0 alarms
    if len(dataset) == 0:
        return 0

    loader = DataLoader(dataset, batch_size=128, shuffle=False)

    model.eval()
    model.to(device)
    
    total_alarms = 0
    with torch.no_grad():
        for batch_x, _ in loader:
            batch_x = batch_x.to(device)
            # mymodel's forward just needs the feature tensor x
            x_out = model(batch_x) 
            preds = x_out.argmax(-1)
            total_alarms += (preds == 1).sum().item()
            
    return total_alarms

if __name__ == "__main__":
    
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # --- Configuration ---
    PREDICTION_DATA_DIR = '/data/five_month/features'
    MODEL_OUTPUT_DIR = './model_output/'
    PREPARED_DATA_DIR = '/data/Project_Liu/BGP-Baseline-main/ISP-Operated/output/'
    RESULTS_FILE_PATH = os.path.join(PREPARED_DATA_DIR, 'prediction_results.txt')
    SCALER_PATH = os.path.join(PREPARED_DATA_DIR, 'scaler.pkl')
    WINDOW_SIZE = 10 # Must match the window size used in training

    # --- Wait for and load necessary files ---
    
    # Wait for the model checkpoint to be available
    model_path = None
    if os.path.exists(MODEL_OUTPUT_DIR):
        # Prefer 'last.ckpt' for the most recently saved model state
        last_ckpt_path = os.path.join(MODEL_OUTPUT_DIR, 'last.ckpt')
        if os.path.exists(last_ckpt_path):
            model_path = last_ckpt_path
        else:
            # Fallback to any other .ckpt file
            checkpoint_files = sorted([f for f in os.listdir(MODEL_OUTPUT_DIR) if f.endswith('.ckpt')])
            if checkpoint_files:
                model_path = os.path.join(MODEL_OUTPUT_DIR, checkpoint_files[-1]) # Get the latest one

    if not model_path:
        print(f"Error: Model checkpoint not found in {MODEL_OUTPUT_DIR}. Please run training first.")
        sys.exit(1)
        
    print(f"Found model checkpoint: {model_path}")

    # Wait for the scaler
    if not os.path.exists(SCALER_PATH):
        print(f"Error: Scaler file not found at {SCALER_PATH}. Please run data preparation first.")
        sys.exit(1)
    
    print(f"Loading scaler from: {SCALER_PATH}")
    scaler = joblib.load(SCALER_PATH)

    # --- Load Prediction Data Files ---
    if not os.path.isdir(PREDICTION_DATA_DIR):
        print(f"Error: Prediction data directory not found at {PREDICTION_DATA_DIR}")
        sys.exit(1)

    try:
        # It's crucial that files are sorted correctly (e.g., 'day_01.csv', 'day_02.csv', ...)
        prediction_files = sorted([os.path.join(PREDICTION_DATA_DIR, f) for f in os.listdir(PREDICTION_DATA_DIR) if f.endswith('.csv')])
        if not prediction_files:
            print(f"Error: No '.csv' files found in {PREDICTION_DATA_DIR}")
            sys.exit(1)
        print(f"Found {len(prediction_files)} prediction files to process.")
        print("Order of files being processed:", [os.path.basename(f) for f in prediction_files])
    except Exception as e:
        print(f"Error reading prediction files: {e}")
        sys.exit(1)

    # --- Determine INPUT_SIZE from the first data file ---
    try:
        first_df_raw = pd.read_csv(prediction_files[0])
        # Drop non-feature columns BEFORE determining the input size
        first_df_features = first_df_raw.drop(columns=['index'], errors='ignore')
        input_size = first_df_features.shape[1]
        print(f"Detected INPUT_SIZE from data: {input_size}")
    except Exception as e:
        print(f"Error reading first prediction file to determine input size: {e}")
        sys.exit(1)

    # --- Load Model ---
    try:
        # Load the model directly from the checkpoint
        trained_model = mymodel.load_from_checkpoint(
            checkpoint_path=model_path,
            WINDOW_SIZE=WINDOW_SIZE,
            INPUT_SIZE=input_size,
            Hidden_SIZE=100,      # Using values from classification.py
            LSTM_layer_NUM=2,     # Using values from classification.py
            Num_class=2           # Using values from classification.py
        )
        print("Successfully loaded trained model!")
    except Exception as e:
        print(f"Error loading model from checkpoint: {e}")
        sys.exit(1)

    # --- Batch Prediction ---
    # Batches are defined as 0-indexed slices [start, end)
    batches = [
        ("Days 1-5", prediction_files[0:5]),
        ("Days 6-10", prediction_files[5:10]),
        ("Days 11-15", prediction_files[10:15]),
        ("Days 16-20", prediction_files[15:20]),
        ("Days 21-25", prediction_files[20:25]),
        ("Days 26-31", prediction_files[25:31])
    ]

    print("\n--- Starting Batch Prediction ---")
    
    # To store results before writing to file
    results_to_save = []
    total_running_time = 0

    for batch_name, files_in_batch in batches:
        if not files_in_batch:
            line = f"\nBatch '{batch_name}': No files to process, skipping."
            print(line)
            results_to_save.append(line)
            continue
            
        batch_alarm_count = 0
        batch_header = f"\nProcessing batch: '{batch_name}'"
        print(batch_header)
        results_to_save.append(batch_header)
        
        batch_start_time = time.time()
        for i, file_path in enumerate(files_in_batch):
            try:
                file_processing_line = f"  - Predicting on file ({i+1}/{len(files_in_batch)}): {os.path.basename(file_path)}"
                print(file_processing_line)
                results_to_save.append(file_processing_line)
                
                df_raw = pd.read_csv(file_path)
                
                if df_raw.empty:
                    warning_line = f"    - Warning: File {os.path.basename(file_path)} is empty. Skipping."
                    print(warning_line)
                    results_to_save.append(warning_line)
                    continue

                # Drop the extra 'index' column to match training data
                df_features = df_raw.drop(columns=['index'], errors='ignore')

                alarms = predict_on_data(trained_model, device, df_features, scaler, WINDOW_SIZE)
                batch_alarm_count += alarms
            except Exception as e:
                error_line = f"    - Error processing file {os.path.basename(file_path)}: {e}"
                print(error_line)
                results_to_save.append(error_line)

        batch_end_time = time.time()
        batch_running_time = batch_end_time - batch_start_time
        total_running_time += batch_running_time
        
        result_line = f"--- Result for '{batch_name}': {batch_alarm_count} alarms detected. (Time: {batch_running_time:.2f}s) ---"
        print(result_line)
        results_to_save.append(result_line)

    completion_line = f"\nPrediction complete. Total processing time: {total_running_time:.2f} seconds."
    print(completion_line)
    results_to_save.append(completion_line)

    # --- Save results to file ---
    try:
        with open(RESULTS_FILE_PATH, 'w') as f:
            for line in results_to_save:
                f.write(line + '\n')
        print(f"\nPrediction results successfully saved to {RESULTS_FILE_PATH}")
    except Exception as e:
        print(f"\nError saving results to file: {e}")
    
