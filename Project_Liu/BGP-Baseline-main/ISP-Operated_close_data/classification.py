import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
import logging
import argparse
from pytorch_lightning.callbacks import ModelCheckpoint
from dataset import SlidingWindowDataset
from dataset import create_data_loaders
from sklearn.metrics import accuracy_score, recall_score, f1_score, confusion_matrix, precision_score, classification_report
from collections import Counter
import os
import time
import numpy as np
import pickle

def calculate_rates(y_true, y_pred):
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred, labels=[0, 1]).ravel()
    false_positive_rate = fp / (fp + tn)
    false_negative_rate = fn / (tp + fn)
    return false_positive_rate, false_negative_rate

class SelfAttention(nn.Module):
    def __init__(self, hidden_dim):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.projection = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(True),
            nn.Linear(64, 1)
        )

    def forward(self, encoder_outputs):
        # (B, L, H) -> (B , L, 1)
        energy = self.projection(encoder_outputs)
        weights = F.softmax(energy.squeeze(-1), dim=1)
        # (B, L, H) * (B, L, 1) -> (B, H)
        outputs = (encoder_outputs * weights.unsqueeze(-1)).sum(dim=1)
        return outputs, weights

class SA_LSTM(nn.Module):
    def __init__(self,WINDOW_SIZE,INPUT_SIZE,Hidden_SIZE,LSTM_layer_NUM, Num_class, dropout_rate=0.5):
        super(SA_LSTM, self).__init__()
        self.WINDOW_SIZE = WINDOW_SIZE
        self.INPUT_SIZE = INPUT_SIZE
        self.Hidden_SIZE = Hidden_SIZE
        self.LSTM_layer_NUM = LSTM_layer_NUM
        self.Num_class = Num_class
        
        self.BN = nn.BatchNorm1d(self.WINDOW_SIZE)
        self.lstm = nn.LSTM(input_size=INPUT_SIZE,
                            hidden_size=Hidden_SIZE,
                            num_layers=LSTM_layer_NUM,
                            batch_first=True,
                            )
        self.attention = SelfAttention(Hidden_SIZE)
        self.dropout = nn.Dropout(dropout_rate)
        self.out = nn.Sequential(nn.Linear(Hidden_SIZE, self.Num_class), nn.Softmax(dim=1))

    def forward(self, x):
        # print("The size of x:",x.size())
        x = self.BN(x)
        # x = torch.permute(x, (0,2,1))
        
        r_out, hidden = self.lstm(x, None)  # x(batch,time_step,input_size)
        r_out, attn_weights = self.attention(r_out)
        r_out = self.dropout(r_out) # Apply dropout
        out = self.out(r_out)
        
        return out, torch.mean(attn_weights, dim=-2)

class mymodel(pl.LightningModule):
    def __init__(self, WINDOW_SIZE, INPUT_SIZE, Hidden_SIZE, LSTM_layer_NUM, Num_class, dropout_rate=0.5, class_weights=None):
        super(mymodel, self).__init__()
        self.model = SA_LSTM(WINDOW_SIZE, INPUT_SIZE, Hidden_SIZE, LSTM_layer_NUM, Num_class, dropout_rate)
        # The weight tensor will be moved to the correct device automatically by PyTorch Lightning
        # because nn.CrossEntropyLoss registers it as a buffer.
        self.loss_func = nn.CrossEntropyLoss(weight=class_weights)
        self.validation_step_outputs = []
        self.train_step_outputs = []
        
    def forward(self, x):
        return self.model(x)

    def training_step(self, batch, batch_idx):
        x,y = batch
        y = y.long()
        
        x_out, _ = self.forward(x) # Unpack the tuple
        loss = self.loss_func(x_out, y)
        pred = x_out.argmax(-1)
        accuracy = (pred == y).sum() / pred.shape[0]

        self.log_dict({'train_loss':loss,'train_accuracy':accuracy},on_step=False, on_epoch=True, prog_bar=True, logger=True)
        self.train_step_outputs.append(loss)
        return loss
    
    def on_train_epoch_end(self):
        # The logging is now handled by self.log in training_step
        # We can optionally calculate and print the average loss.
        avg_loss = torch.stack(self.train_step_outputs).mean()
        print(f"Epoch {self.current_epoch + 1}: Avg. Train Loss: {avg_loss:.4f}")
        self.train_step_outputs.clear()

    def on_validation_epoch_end(self):
        # The logging is now handled by self.log in validation_step
        # We can optionally calculate and print the average loss.
        if not self.validation_step_outputs:
            return
        avg_loss = torch.stack(self.validation_step_outputs).mean()
        print(f"Epoch {self.current_epoch + 1}: Avg. Validation Loss: {avg_loss:.4f}")
        self.validation_step_outputs.clear()

    def validation_step(self, batch, batch_idx):
        x, y = batch
        y = y.long()
        x_out, _ = self.forward(x) # Unpack the tuple
        loss = self.loss_func(x_out, y) # Use the weighted loss function here too
        pred = x_out.argmax(-1)
        accuracy = (pred == y).sum() / pred.shape[0]

        self.log_dict({'val_loss':loss,'val_accuracy':accuracy},on_step=False, on_epoch=True, prog_bar=True, logger=True)
        self.validation_step_outputs.append(loss)
        return loss

    def configure_optimizers(self):
        optimizer = torch.optim.Adam(self.parameters(), lr=0.0001)
        return optimizer    

    def predict_step(self, batch, batch_idx):
        
        x, y = batch
        x_out = self.forward(x)
        pred = x_out.argmax(-1)
        return pred, y


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Train a specialized BGP event model.")
    parser.add_argument('--event_type', type=str, required=True, choices=['hijack', 'leak', 'outage'],
                        help="The type of event to train a model for.")
    args = parser.parse_args()
    event_type = args.event_type

    print(f"\n--- Training specialized model for '{event_type}' events ---")

    # Add a checkpoint callback
    checkpoint_callback = ModelCheckpoint(
        dirpath=f'./model_output_{event_type}/', # Save to a dedicated directory
        filename=f'{event_type}-model-{{epoch:02d}}-{{val_loss:.2f}}',
        monitor='val_loss',
        mode='min',
        save_top_k=1,
        save_last=True
    )

    # Initialize a logger
    logger = pl.loggers.TensorBoardLogger(f"tb_logs_{event_type}", name=f"{event_type}_model")

    # --- Start training ---
    print("Starting model training...")
    os.system(f"rm -rf ./tb_logs_{event_type}")
    time.sleep(1) # Brief pause

    # Load data from the event-specific path
    path = os.path.join(f'./output_{event_type}/', "train_data.pt")

    try:
        # Set weights_only=False to load the full dataset object
        train_dataset = torch.load(path, weights_only=False)
    except Exception as e:
        print(f"Error loading training dataset: {e}")
        # Fallback for older torch versions that do not support weights_only
        if "weights_only" in str(e):
            train_dataset = torch.load(path, pickle_module=pickle)
            print("Loaded dataset with pickle fallback.")
        else:
            raise e

    # --- Calculate Class Weights to handle data imbalance ---
    labels = train_dataset.y
    n_samples = len(labels)
    n_classes = 2
    n_samples_class_0 = np.sum(labels == 0)
    n_samples_class_1 = np.sum(labels == 1)

    # Calculate weights: total_samples / (n_classes * n_samples_per_class)
    # This gives higher weight to the minority class.
    weight_for_0 = n_samples / (n_classes * n_samples_class_0) if n_samples_class_0 > 0 else 0
    weight_for_1 = n_samples / (n_classes * n_samples_class_1) if n_samples_class_1 > 0 else 0
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    class_weights = torch.tensor([weight_for_0, weight_for_1], dtype=torch.float32).to(device)
    
    print(f"\n--- Handling Class Imbalance ---")
    print(f"Dataset has {n_samples_class_0} normal samples and {n_samples_class_1} anomaly samples.")
    print(f"Calculated class weights (for class 0 and 1): {class_weights.cpu().numpy()}")
    print(f"Weights will be applied to the loss function.\n")


    train_size = int(len(train_dataset)*0.8)
    validation_size = len(train_dataset) - train_size
    
    train_loader, val_loader, _ = create_data_loaders(train_dataset, batch_size=100)

    print(f"train_size: {train_size}")
    print(f"validation_size: {validation_size}")
    
    # Dynamically determine the number of features
    sample_features, _ = train_dataset[0]
    n_feature = sample_features.shape[1]
    print(f"Detected {n_feature} features.")
    
    window_size = 10
    num_class = 2

    my = mymodel(
        WINDOW_SIZE=window_size, 
        INPUT_SIZE=n_feature, 
        Hidden_SIZE=100, 
        LSTM_layer_NUM=2, 
        Num_class=num_class,
        dropout_rate=0.5,
        class_weights=class_weights
    )
    num_epochs = 50 # Lower epoch count for larger dataset
    # Using val_check_interval=1.0 to check after every epoch
    trainer = pl.Trainer(max_epochs = num_epochs, val_check_interval=1.0, log_every_n_steps=10, accelerator="gpu", callbacks=[checkpoint_callback], logger=logger, enable_progress_bar=True)
    
    print("Starting model training...")
    trainer.fit(my, train_loader, val_loader)
    print("Model training finished.")