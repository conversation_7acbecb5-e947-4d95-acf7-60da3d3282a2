import os
import sys
import torch
import joblib
import pandas as pd
import glob
import numpy as np
from collections import defaultdict
from itertools import chain
from matplotlib import pyplot as plt
from matplotlib_venn import venn3
import importlib.util 
import re # 导入正则表达式模块

# --- 动态设置项目路径 ---
# 将 BGP-Baseline-main 目录添加到 sys.path 以便导入各个子项目的模块
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
# **关键修改**：明确地将每个子项目目录添加到sys.path
sys.path.append(os.path.join(PROJECT_ROOT, 'ISP-Operated'))
sys.path.append(os.path.join(PROJECT_ROOT, 'MSLSTM_openworld'))
sys.path.append(os.path.join(PROJECT_ROOT, 'BGPviewer'))


# --- 辅助函数 ---
def load_module_from_path(module_name, file_path):
    """从文件路径动态加载模块的辅助函数。"""
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    if spec and spec.loader:
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return module
    raise ImportError(f"Could not load module {module_name} from {file_path}")

def get_date_from_filename(filename):
    """从文件名中提取 YYYY-MM-DD 格式的日期。"""
    match = re.search(r'(\d{4}-\d{2}-\d{2})', filename)
    return match.group(1) if match else None

def format_timestamp(date_str, minute_of_day):
    """将日期和天内分钟数格式化为 'YYYY-MM-DD HH:MM' 字符串。"""
    hour = int(minute_of_day) // 60
    minute = int(minute_of_day) % 60
    return f"{date_str} {hour:02d}:{minute:02d}"

# --- 模型预测函数 (已重构) ---
def get_isp_operated_alarms(prediction_files, device, scaler):
    """加载并运行 ISP-Operated 模型，返回唯一告警时间戳的集合。"""
    print("\n--- Processing Model: ISP-Operated ---")
    
    # 动态加载模块
    classification_path = os.path.join(PROJECT_ROOT, "ISP-Operated/classification.py")
    dataset_path = os.path.join(PROJECT_ROOT, "ISP-Operated/dataset.py")
    classification_module = load_module_from_path("isp_classification", classification_path)
    dataset_module = load_module_from_path("isp_dataset", dataset_path)
    mymodel = classification_module.mymodel
    SlidingWindowDataset = dataset_module.SlidingWindowDataset

    # 加载模型
    model_path = os.path.join(PROJECT_ROOT, "ISP-Operated/model_output/last.ckpt")
    model = mymodel.load_from_checkpoint(
        checkpoint_path=model_path, map_location=device,
        WINDOW_SIZE=10, INPUT_SIZE=26, Hidden_SIZE=100, LSTM_layer_NUM=2, Num_class=2
    ).eval()

    unique_alarm_timestamps = set()
    for file_path in prediction_files:
        filename = os.path.basename(file_path)
        date_str = get_date_from_filename(filename)
        if not date_str: continue

        print(f"  - ISP-Operated: Predicting on {filename}")
        df = pd.read_csv(file_path)
        timestamps = df['index'].values
        # 最终的最终修正：从第二列到最后一列，共26个特征
        features = df.iloc[:, 1:].values.astype(np.float32)

        if features.shape[0] < model.model.WINDOW_SIZE: continue

        scaled_features = scaler.transform(features)
        dataset = SlidingWindowDataset(data=scaled_features, y=np.zeros(len(scaled_features)), window=model.model.WINDOW_SIZE)
        loader = torch.utils.data.DataLoader(dataset, batch_size=128, shuffle=False)
        
        with torch.no_grad():
            for i, (x, _) in enumerate(loader):
                x = x.to(device)
                preds = model(x).argmax(dim=1)
                alarm_indices = (preds == 1).nonzero(as_tuple=True)[0]
                for alarm_idx in alarm_indices:
                    real_idx = i * 128 + alarm_idx.item() + model.model.WINDOW_SIZE - 1
                    if real_idx < len(timestamps):
                        unique_ts = format_timestamp(date_str, timestamps[real_idx])
                        unique_alarm_timestamps.add(unique_ts)
    return unique_alarm_timestamps

def get_mslstm_alarms(prediction_files, device, scaler):
    """加载并运行 MSLSTM_openworld 模型，返回唯一告警时间戳的集合。"""
    print("\n--- Processing Model: MSLSTM_openworld ---")
    
    classification_path = os.path.join(PROJECT_ROOT, "MSLSTM_openworld/sendXiaohui/classification.py")
    feature_extraction_path = os.path.join(PROJECT_ROOT, "MSLSTM_openworld/sendXiaohui/feature_extraction.py")
    classification_module = load_module_from_path("mslstm_classification", classification_path)
    feature_module = load_module_from_path("mslstm_feature", feature_extraction_path)
    GRUClassifier = classification_module.GRUClassifier
    SlidingWindowDataset = feature_module.SlidingWindowDataset

    model_path = os.path.join(PROJECT_ROOT, "MSLSTM_openworld/sendXiaohui/models/general_model.ckpt")
    model = GRUClassifier.load_from_checkpoint(checkpoint_path=model_path, map_location=device).eval()

    unique_alarm_timestamps = set()
    for file_path in prediction_files:
        filename = os.path.basename(file_path)
        date_str = get_date_from_filename(filename)
        if not date_str: continue

        print(f"  - MSLSTM: Predicting on {filename}")
        df = pd.read_csv(file_path)
        timestamps = df['index'].values
        # 最终的最终修正：从第二列到最后一列，共26个特征
        features = df.iloc[:, 1:].values.astype(np.float32)

        if features.shape[0] < model.hparams.window_size: continue
        
        scaled_features = scaler.transform(features)
        dataset = SlidingWindowDataset(data=scaled_features, y=np.zeros(len(scaled_features)), window=model.hparams.window_size)
        loader = torch.utils.data.DataLoader(dataset, batch_size=128, shuffle=False)
        
        with torch.no_grad():
            for i, (x, _) in enumerate(loader):
                x = x.to(device)
                preds = model(x).argmax(dim=1)
                alarm_indices = (preds == 1).nonzero(as_tuple=True)[0]
                for alarm_idx in alarm_indices:
                    real_idx = i * 128 + alarm_idx.item() + model.hparams.window_size - 1
                    if real_idx < len(timestamps):
                        unique_ts = format_timestamp(date_str, timestamps[real_idx])
                        unique_alarm_timestamps.add(unique_ts)
    return unique_alarm_timestamps

def get_bgpviewer_alarms(prediction_files, device, scaler):
    """加载并运行 BGPviewer 模型，返回唯一告警时间戳的集合。"""
    print("\n--- Processing Model: BGPviewer ---")
    
    compare_method_path = os.path.join(PROJECT_ROOT, "BGPviewer/compare_method.py")
    mydataset_path = os.path.join(PROJECT_ROOT, "BGPviewer/mydataset.py")
    compare_method_module = load_module_from_path("bgp_compare", compare_method_path)
    mydataset_module = load_module_from_path("bgp_dataset", mydataset_path)
    MTAD_GAT = compare_method_module.MTAD_GAT
    SlidingWindowDataset = mydataset_module.SlidingWindowDataset

    model_path = os.path.join(PROJECT_ROOT, "BGPviewer/model/last.ckpt")
    model = MTAD_GAT.load_from_checkpoint(
        checkpoint_path=model_path, map_location=device,
        n_features=26, window_size=10, dropout=0.5, hid_dim=100, num_classes=2,
        gru_hid_dim=130, n_layers=3, class_weights=[1.0, 1.0], device=device
    ).eval()

    unique_alarm_timestamps = set()
    for file_path in prediction_files:
        filename = os.path.basename(file_path)
        date_str = get_date_from_filename(filename)
        if not date_str: continue

        print(f"  - BGPviewer: Predicting on {filename}")
        df = pd.read_csv(file_path)
        timestamps = df['index'].values
        # 最终的最终修正：从第二列到最后一列，共26个特征
        features = df.iloc[:, 1:].values.astype(np.float32)

        if features.shape[0] < model.window_size: continue

        scaled_features = scaler.transform(features)
        dataset = SlidingWindowDataset(data=scaled_features, y=np.zeros(len(scaled_features)), window=model.window_size)
        loader = torch.utils.data.DataLoader(dataset, batch_size=512, shuffle=False)
        
        with torch.no_grad():
            for i, (x, _) in enumerate(loader):
                x = x.to(device)
                preds = model(x).argmax(dim=1)
                alarm_indices = (preds == 1).nonzero(as_tuple=True)[0]
                for alarm_idx in alarm_indices:
                    real_idx = i * 512 + alarm_idx.item() + model.window_size - 1
                    if real_idx < len(timestamps):
                        unique_ts = format_timestamp(date_str, timestamps[real_idx])
                        unique_alarm_timestamps.add(unique_ts)
    return unique_alarm_timestamps

# --- 主函数 ---
def main():
    """主函数，编排整个对比分析流程。"""
    # --- 1. 初始化环境和配置 ---
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
    SCALER_PATH = os.path.join(PROJECT_ROOT, "ISP-Operated/output/scaler.pkl")
    PREDICTION_DATA_DIR = '/data/five_month/features/'
    
    scaler = joblib.load(SCALER_PATH)
    prediction_files = sorted(glob.glob(os.path.join(PREDICTION_DATA_DIR, "*.csv")))
    if not prediction_files:
        raise FileNotFoundError(f"No prediction CSV files found in {PREDICTION_DATA_DIR}")

    # --- 2. 预加载所有唯一时间戳 (全局唯一格式) ---
    print("\n--- Collecting all timestamps from source files ---")
    all_timestamps_set = set()
    for file_path in prediction_files:
        filename = os.path.basename(file_path)
        date_str = get_date_from_filename(filename)
        if not date_str: continue
        # 真正最终的修正：标准读取，按正确的列名'index'提取
        df_ts = pd.read_csv(file_path)
        for minute_of_day in df_ts['index'].values:
            all_timestamps_set.add(format_timestamp(date_str, minute_of_day))
    
    all_timestamps = sorted(list(all_timestamps_set))
    print(f"Collected {len(all_timestamps)} total unique timestamps for the entire period.")

    # --- 3. 分别获取每个模型的告警 ---
    all_isp_alarms = get_isp_operated_alarms(prediction_files, device, scaler)
    all_mslstm_alarms = get_mslstm_alarms(prediction_files, device, scaler)
    all_bgpviewer_alarms = get_bgpviewer_alarms(prediction_files, device, scaler)
    
    # --- 4. 统计和验证 ---
    print("\n--- Analyzing Alarm Overlap (All Days) ---")
    print(f"Total unique alarms collected for ISP-Operated: {len(all_isp_alarms)}")
    print(f"Total unique alarms collected for MSLSTM_openworld: {len(all_mslstm_alarms)}")
    print(f"Total unique alarms collected for BGPviewer: {len(all_bgpviewer_alarms)}")

    # --- 5. 生成每分钟的详细告警报告 (包含正常时间点) ---
    print("\n--- Generating Per-Minute Alarm Report (including normal timestamps) ---")
    report_data = []
    for ts in all_timestamps:
        report_data.append({
            "timestamp": ts,
            "ISP-Operated": 1 if ts in all_isp_alarms else 0,
            "MSLSTM_openworld": 1 if ts in all_mslstm_alarms else 0,
            "BGPviewer": 1 if ts in all_bgpviewer_alarms else 0,
        })
    
    report_df = pd.DataFrame(report_data)
    # 修正最终报告的列名
    report_df.columns = ["timestamp", "ISP-Operated", "MSLSTM_openworld", "BGPviewer"]
    report_path = os.path.join(PROJECT_ROOT, 'per_minute_alarm_report.csv')
    report_df.to_csv(report_path, index=False)
    print(f"Per-minute alarm report saved to: {report_path}")

    # --- 6. 绘制维恩图 ---
    plt.figure(figsize=(12, 8))
    venn3(
        [all_isp_alarms, all_mslstm_alarms, all_bgpviewer_alarms],
        set_labels=('ISP-Operated', 'MSLSTM_openworld', 'BGPviewer')
    )
    plt.title('Alarm Overlap Analysis Across All Models (Full Month)')
    
    output_plot_path = os.path.join(PROJECT_ROOT, 'alarm_overlap_analysis.png')
    plt.savefig(output_plot_path)
    print(f"\nOverlap analysis plot saved to: {output_plot_path}")
    plt.show()

if __name__ == "__main__":
    # 在 main 函数外部定义 PROJECT_ROOT，以便全局可用
    PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
    main() 