import os
import torch
import pytorch_lightning as pl
import joblib
import numpy as np
import pandas as pd
import glob
import time
import sys
import re

from compare_method import MTAD_GAT
from mydataset import SlidingWindowDataset

# --- 辅助函数 (从 compare_alarms.py 移植) ---
def get_date_from_filename(filename):
    """从文件名中提取 YYYY-MM-DD 格式的日期。"""
    match = re.search(r'(\d{4}-\d{2}-\d{2})', filename)
    return match.group(1) if match else None

def format_timestamp(date_str, minute_of_day):
    """将日期和天内分钟数格式化为 'YYYY-MM-DD HH:MM' 字符串。"""
    hour = int(minute_of_day) // 60
    minute = int(minute_of_day) % 60
    return f"{date_str} {hour:02d}:{minute:02d}"

def predict_on_file(model, file_path, scaler, device):
    """对单个文件进行预测，并返回全局唯一告警时间戳的集合。"""
    model.eval()
    
    filename = os.path.basename(file_path)
    date_str = get_date_from_filename(filename)
    if not date_str:
        print(f"Warning: Could not extract date from {filename}. Skipping.")
        return set()

    # 核心逻辑修正：与 compare_alarms.py 完全一致
    df = pd.read_csv(file_path)
    timestamps = df['index'].values
    features = df.iloc[:, 1:].values.astype(np.float32)

    if features.shape[0] < model.window_size:
        return set()

    scaled_features = scaler.transform(features)
    dummy_labels = np.zeros(len(scaled_features))
    dataset = SlidingWindowDataset(data=scaled_features, y=dummy_labels, window=model.window_size)
    loader = torch.utils.data.DataLoader(dataset, batch_size=512, shuffle=False)
    
    alarm_timestamps = set()
    with torch.no_grad():
        for i, (x, _) in enumerate(loader):
            x = x.to(device)
            preds = model(x).argmax(dim=1)
            alarm_indices = (preds == 1).nonzero(as_tuple=True)[0]
            for alarm_idx in alarm_indices:
                real_idx = i * 512 + alarm_idx.item() + model.window_size - 1
                if real_idx < len(timestamps):
                    # 生成全局唯一的时间戳
                    unique_ts = format_timestamp(date_str, timestamps[real_idx])
                    alarm_timestamps.add(unique_ts)
                    
    return alarm_timestamps

if __name__ == "__main__":
    pl.seed_everything(42)

    # --- 配置 ---
    PREDICTION_DATA_DIR = '/data/five_month/features'
    MODEL_PATH = "/data/Project_Liu/BGP-Baseline-main/BGPviewer/model/last.ckpt"
    SCALER_PATH = '/data/Project_Liu/BGP-Baseline-main/ISP-Operated/output/scaler.pkl'
    RESULTS_FILE_PATH = 'prediction_results_bgpviewer_finetuned.txt'

    print(f"Loading fine-tuned model from: {MODEL_PATH}")
    print(f"Loading scaler from: {SCALER_PATH}")

    # --- 加载模型和Scaler ---
    if not os.path.exists(MODEL_PATH) or not os.path.exists(SCALER_PATH):
        print(f"Fatal: Model or Scaler not found.")
        sys.exit(1)

    scaler = joblib.load(SCALER_PATH)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    trained_model = MTAD_GAT.load_from_checkpoint(
        checkpoint_path=MODEL_PATH, map_location=device,
        n_features=26, window_size=10, dropout=0.5, hid_dim=100, num_classes=2,
        gru_hid_dim=130, n_layers=3, class_weights=[1.0, 1.0], device=device
    ).to(device)

    # --- 加载预测文件 ---
    prediction_files = sorted(glob.glob(os.path.join(PREDICTION_DATA_DIR, "*.csv")))
    if not prediction_files:
        print(f"Error: No prediction files found in {PREDICTION_DATA_DIR}")
        sys.exit(1)
    print(f"Found {len(prediction_files)} prediction files.")

    # --- 批量预测 (恢复批次处理) ---
    results_to_save = [
        "BGPviewer Model Prediction Report (Fine-tuned with SGDM)",
        "========================================"
    ]
    total_running_time = 0
    total_unique_alarms = set()

    batches = [
        ("Days 1-5", prediction_files[0:5]),
        ("Days 6-10", prediction_files[5:10]),
        ("Days 11-15", prediction_files[10:15]),
        ("Days 16-20", prediction_files[15:20]),
        ("Days 21-25", prediction_files[20:25]),
        ("Days 26-31", prediction_files[25:31])
    ]

    for batch_name, files_in_batch in batches:
        batch_header = f"\nProcessing batch: '{batch_name}'"
        print(batch_header)
        results_to_save.append(batch_header)
        
        batch_alarm_timestamps = set()
        batch_start_time = time.time()
        
        for i, file_path in enumerate(files_in_batch):
            print(f"  - Predicting on file ({i+1}/{len(files_in_batch)}): {os.path.basename(file_path)}")
            unique_alarms_in_file = predict_on_file(trained_model, file_path, scaler, device)
            batch_alarm_timestamps.update(unique_alarms_in_file)

        batch_end_time = time.time()
        batch_running_time = batch_end_time - batch_start_time
        total_running_time += batch_running_time
        
        total_unique_alarms.update(batch_alarm_timestamps)
        
        result_line = f"--- Result for '{batch_name}': {len(batch_alarm_timestamps)} unique alarms detected. (Time: {batch_running_time:.2f}s) ---"
        print(result_line)
        results_to_save.append(result_line)

    completion_line = f"\nPrediction complete. Total processing time: {total_running_time:.2f} seconds."
    # 恢复：使用脚本自己正确计算出的告警总数
    total_alarms_line = f"Total unique alarms for all days: {len(total_unique_alarms)}"
    print(completion_line)
    print(total_alarms_line)
    results_to_save.append(completion_line)
    results_to_save.append(total_alarms_line)

    with open(RESULTS_FILE_PATH, 'w') as f:
        for line in results_to_save:
            f.write(line + '\n')
    
    print(f"\nPrediction results saved to {RESULTS_FILE_PATH}")