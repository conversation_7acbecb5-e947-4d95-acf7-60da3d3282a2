# general
guiEnabled: true
systemVersion: latest
historic: false

# autostarter
checkInterval: 5

# redis
redisHost: redis
redisPort: 6379

# api
apiHost: postgrest
apiPort: 3000
configHost: configuration
configPort: 3000
databaseHost: database
restPort: 3000
nginxHost: nginx

# monitor-specific configs
risId: 8522

# database
dbHost: postgres
dbPort: 5432
dbVersion: 24
dbName: artemis_db
dbUser: artemis_user
dbPass: Art3m1s
dbSchema: public
dbAutoClean: false
dbBackup: true
dbHijackDormant: false

# frontend
webappHost: frontend
webappPort: 4200
adminPass: admin1234
adminEmail: <EMAIL>
sessionTimeout: 1800
inactivityTimeout: 900
limitWindow: 900000
limitRequests: 20
captchaWindow: 900000
captchaTries: 4
artemisWebBaseDir: ""
serviceAccountPath: ""

# mongodb
mongodbUser: admin
mongodbPass: pass
mongodbHost: mongodb
mongodbPort: 27017
mongodbName: artemis-web

# ldap
ldapEnabled: true
ldapHost: ldap
ldapPort: 10389
ldapProtocol: ldap
ldapBindDN: "cn=admin,dc=planetexpress,dc=com"
ldapSearchBase: "ou=people,dc=planetexpress,dc=com"
ldapSearchFilter: "(mail={{username}})"
ldapSearchAttributes: "mail,uid"
ldapGroupSearchBase: "ou=people,dc=planetexpress,dc=com"
ldapGroupSearchFilter: "(mail={{username}})"
ldapGroupSearchAttributes: "mail,uid"
ldapEmailFieldName: mail
ldapAdminGroup: admin_staff

# Google SSO
googleEnabled: false
googleClientID: ''
googleClientSecret: ''

# rabbitmq
rabbitmqHost: rabbitmq
rabbitmqPort: 5672
rabbitmqUser: guest
rabbitmqPass: guest
rabbitmqIoThreadPoolSize: 128

# hasura
hasuraHost: graphql
hasuraPort: 8080
hasuraGui: false

# custom log filter
hijackLogFilter: '[{"community_annotation":"critical"},{"community_annotation":"NA"}]'
# timeout (sec) since last seen BGP update for monitors
monTimeoutLastBgpUpdate: 3600
# fields to preserve in hijack logs
hijackLogFields: '["prefix","hijack_as","type","time_started","time_last","peers_seen","configured_prefix","timestamp_of_config","asns_inf","time_detected","key","community_annotation","rpki_status","end_tag","outdated_parent","hijack_url"]'
# percentage of monitor peers that have seen hijack updates, required to see corresponding withdrawals to declare a
# hijack as withdrawn
withdrawnHijackThreshold: 80
rpkiValidatorEnabled: false
rpkiValidatorHost: routinator
rpkiValidatorPort: 3323
testEnv: false
autoRecoverProcessState: true

# services
services:
  riperistap:
    image: inspiregroup/artemis-riperistap
    replicas: 1
    ports:
      - 3000
  bgpstreamlivetap:
    image: inspiregroup/artemis-bgpstreamlivetap
    replicas: 1
    ports:
      - 3000
  bgpstreamkafkatap:
    image: inspiregroup/artemis-bgpstreamkafkatap
    replicas: 1
    ports:
      - 3000
  bgpstreamhisttap:
    image: inspiregroup/artemis-bgpstreamhisttap
    replicas: 1
    ports:
      - 3000
  exabgptap:
    image: inspiregroup/artemis-exabgptap
    replicas: 1
    ports:
      - 3000
  autoignore:
    image: inspiregroup/artemis-autoignore
    replicas: 1
    ports:
      - 3000
  autostarter:
    image: inspiregroup/artemis-autostarter
    replicas: 1
    ports:
      - 3000
  configuration:
    image: inspiregroup/artemis-configuration
    replicas: 1
    ports:
      - 3000
  database:
    image: inspiregroup/artemis-database
    replicas: 1
    ports:
      - 3000
  detection:
    image: inspiregroup/artemis-detection
    replicas: 1
    ports:
      - 3000
  fileobserver:
    image: inspiregroup/artemis-fileobserver
    replicas: 1
    ports:
      - 3000
  mitigation:
    image: inspiregroup/artemis-mitigation
    replicas: 1
    ports:
      - 3000
  notifier:
    image: inspiregroup/artemis-notifier
    replicas: 1
    ports:
      - 3000
  prefixtree:
    image: inspiregroup/artemis-prefixtree
    replicas: 1
    ports:
      - 3000
  frontend:
    image: inspiregroup/artemis-frontend-web
    replicas: 1
    ports:
      - 4200
  redis:
    image: redis
    ports:
      - 6379
  postgrest:
    image: postgrest/postgrest:v10.0.0
    ports:
      - 3000
  postgres:
    image: timescale/timescaledb:2.8.1-pg14
    ports:
      - 5432
  rabbitmq:
    image: rabbitmq:3.9.20-management-alpine
    ports:
      - 5672
  graphql:
    image: hasura/graphql-engine:v2.10.2
    ports:
      - 8080
  pgamqpbridge:
    image: curiouzk0d3r/pg-amqp-bridge:latest
  mongodb:
    image: mongo:4.4.6-bionic
    ports:
      - 27017

# ingress
ingress:
  enabled: true
  # Used to create an Ingress record.
  host: artemis.com
  tlsEnabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
  graphql:
    annotations:
      nginx.ingress.kubernetes.io/rewrite-target: /v1alpha1/graphql
  frontend:
    annotations:

# pvc
pvc:
  storage: 1Gi
  # storageClassName: rook-block
  accessMode: ReadWriteOnce
  annotations:
    helm.sh/resource-policy: "keep"

# nodeselector
nodeSelector: {}

# probes
probes: {}
#  livenessProbe:
#    httpGet:
#      path: /health
#      port: 3000
#    failureThreshold: 2
#    periodSeconds: 5
#  startupProbe:
#    httpGet:
#      path: /health
#      port: 3000
#    failureThreshold: 30
#    periodSeconds: 10
