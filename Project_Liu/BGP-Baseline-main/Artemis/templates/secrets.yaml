apiVersion: v1
kind: Secret
metadata:
  name: secret
  labels:
{{ include "artemis.labels" . | indent 4 }}
type: Opaque
data:
  hasuraSecret: {{ required "A valid .Values.hasuraSecret entry required!" .Values.hasuraSecret | b64enc }}
  jwtSecret: {{ required "A valid .Values.jwtSecret entry required!" .Values.jwtSecret | b64enc }}
  csrfSecret: {{ required "A valid .Values.csrfSecret entry required!" .Values.csrfSecret | b64enc }}
  captchaSecret: {{ required "A valid .Values.captchaSecret entry required!" .Values.captchaSecret | b64enc }}
  apiKey: {{ required "A valid .Values.apiKey entry required!" .Values.apiKey | b64enc }}
  dbPass: {{ .Values.dbPass | default "Art3m1s" | b64enc }}
  mongodbPass: {{ .Values.mongodbPass | default "pass" | b64enc }}
  adminPass: {{ .Values.adminPass | default "admin1234" | b64enc }}
  rabbitmqPass: {{ .Values.rabbitmqPass | default "guest" | b64enc }}
  ldapBindSecret: {{ .Values.ldapBindSecret | default "GoodNewsEveryone" | b64enc }}
  hasuraJwt: {{ (printf "{\"type\":\"HS256\", \"key\":\"%s\" }" .Values.jwtSecret) | b64enc }}
---
{{- if or (not .Values.ingress.enabled) (.Values.ingress.tlsEnabled) -}}
apiVersion: v1
kind: Secret
metadata:
  name: secret-tls
  labels:
{{ include "artemis.labels" . | indent 4 }}
type: kubernetes.io/tls
data:
  tls.crt: {{ required "A valid .Values.certificate entry required!" .Values.certificate | b64enc }}
  tls.key: {{ required "A valid .Values.privKey entry required!" .Values.privKey | b64enc }}
{{- end -}}
