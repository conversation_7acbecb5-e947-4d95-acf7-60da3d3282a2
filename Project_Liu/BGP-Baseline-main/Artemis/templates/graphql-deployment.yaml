apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: graphql
{{ include "artemis.labels" . | indent 4 }}
  name: graphql
{{- with .Values.services.graphql }}
spec:
  selector:
    matchLabels:
      app: graphql
  replicas: 1
  strategy: {}
  template:
    metadata:
      labels:
        app: graphql
    spec:
      initContainers:
      - name: wait-for-service
        image: busybox
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'until nc -z {{ $.Values.dbHost }} {{ $.Values.dbPort }}; do echo waiting for services; sleep 10; done;']
      containers:
      - name: graphql
        image: {{ .image }}
        args:
        - graphql-engine
        - serve
        env:
        - name: HASURA_GRAPHQL_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: secret
              key: hasuraSecret
        - name: HASURA_GRAPHQL_DATABASE_URL
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: dbUri
        - name: HASURA_GRAPHQL_ENABLE_CONSOLE
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: hasuraGui
        - name: HASURA_GRAPHQL_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: secret
              key: hasuraJwt
      restartPolicy: Always
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
  {{- end }}
