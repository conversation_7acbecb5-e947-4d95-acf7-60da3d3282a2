apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: fileobserver
{{ include "artemis.labels" . | indent 4 }}
  name: fileobserver
{{- with .Values.services.fileobserver }}
spec:
  selector:
    matchLabels:
      app: fileobserver
  replicas: {{ .replicas | default 1 }}
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: fileobserver
    spec:
      containers:
      - name: fileobserver
        image: {{ .image }}:{{ $.Values.systemVersion }}
        imagePullPolicy: Always
        env:
        - name: REST_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: restPort
        {{- with $.Values.probes }}
{{ toYaml . | nindent 8 }}
        {{- end }}
        volumeMounts:
        - mountPath: /etc/artemis/
          name: fileobserver-configmap
      restartPolicy: Always
      volumes:
      - configMap:
          name: volumes
          items:
          - key: config.yaml
            path: config.yaml
          - key: logging.yaml
            path: logging.yaml
        name: fileobserver-configmap
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
  {{- end }}
