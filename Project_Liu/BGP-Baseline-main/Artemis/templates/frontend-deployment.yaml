apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: frontend
{{ include "artemis.labels" . | indent 4 }}
  name: frontend
{{- with .Values.services.frontend }}
spec:
  selector:
    matchLabels:
      app: frontend
  replicas: {{ .replicas | default 1 }}
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: {{  .image  }}:{{ $.Values.systemVersion }}
        imagePullPolicy: Always
        env:
        - name: API_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: apiHost
        - name: NGINX_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: nginxHost
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: secret
              key: apiKey
        - name: API_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: apiPort
        - name: CONFIG_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: configHost
        - name: CONFIG_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: configPort
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: databaseHost
        - name: DEFAULT_EMAIL
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: adminEmail
        - name: DEFAULT_PASS
          valueFrom:
            secretKeyRef:
              name: secret
              key: adminPass
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: secret
              key: jwtSecret
        - name: INACTIVITY_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: inactivityTimeout
        - name: LIMIT_WINDOW
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: limitWindow
        - name: LIMIT_REQUESTS
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: limitRequests
        - name: CAPTCHA_SECRET
          valueFrom:
            secretKeyRef:
              name: secret
              key: captchaSecret
        - name: CAPTCHA_WINDOW
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: captchaWindow
        - name: CAPTCHA_TRIES
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: captchaTries
        - name: ARTEMIS_WEB_BASE_DIR
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: artemisWebBaseDir
        - name: SESSION_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: sessionTimeout
        - name: RABBITMQ_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqHost
        - name: RABBITMQ_PASS
          valueFrom:
            secretKeyRef:
              name: secret
              key: rabbitmqPass
        - name: RABBITMQ_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqPort
        - name: RABBITMQ_USER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqUser
        - name: SYSTEM_VERSION
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: systemVersion
        - name: MONGODB_USER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: mongodbUser
        - name: MONGODB_PASS
          valueFrom:
            secretKeyRef:
              name: secret
              key: mongodbPass
        - name: MONGODB_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: mongodbHost
        - name: MONGODB_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: mongodbPort
        - name: MONGODB_NAME
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: mongodbName
        - name: CSRF_SECRET
          valueFrom:
            secretKeyRef:
              name: secret
              key: csrfSecret
        - name: TESTING
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: testEnv
        - name: LDAP_ENABLED
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: ldapEnabled
        - name: LDAP_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: ldapHost
        - name: LDAP_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: ldapPort
        - name: LDAP_PROTOCOL
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: ldapProtocol
        - name: LDAP_BIND_DN
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: ldapBindDN
        - name: LDAP_BIND_SECRET
          valueFrom:
            secretKeyRef:
              name: secret
              key: ldapBindSecret
        - name: LDAP_SEARCH_BASE
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: ldapSearchBase
        - name: LDAP_SEARCH_FILTER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: ldapSearchFilter
        - name: LDAP_SEARCH_ATTRIBUTES
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: ldapSearchAttributes
        - name: LDAP_GROUP_SEARCH_BASE
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: ldapGroupSearchBase
        - name: LDAP_GROUP_SEARCH_FILTER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: ldapGroupSearchFilter
        - name: LDAP_GROUP_SEARCH_ATTRIBUTES
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: ldapGroupSearchAttributes
        - name: LDAP_EMAIL_FIELDNAME
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: ldapEmailFieldName
        - name: LDAP_ADMIN_GROUP
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: ldapAdminGroup
      restartPolicy: Always
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
  {{- end }}
