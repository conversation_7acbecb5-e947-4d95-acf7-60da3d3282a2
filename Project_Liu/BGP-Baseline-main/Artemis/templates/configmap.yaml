apiVersion: v1
kind: ConfigMap
metadata:
  name: configmap
  labels:
{{ include "artemis.labels" . | indent 4 }}
data:
  guiEnabled: {{ .Values.guiEnabled | default "true" | quote }}
  systemVersion: {{ .Values.systemVersion | default "latest" | quote }}
  historic: {{ .Values.historic | default "false" | quote }}
  checkInterval: {{ .Values.checkInterval | default "5" | quote }}
  redisHost: {{ .Values.redisHost | default "redis" }}
  redisPort: {{ .Values.redisPort | default "6379" | quote }}
  apiHost: {{ .Values.apiHost | default "postgrest" }}
  apiPort: {{ .Values.apiPort | default "3000" | quote }}
  nginxHost: {{ .Values.nginxHost | default "nginx" }}
  configHost: {{ .Values.configHost | default "configuration" }}
  configPort: {{ .Values.configPort | default "3000" | quote }}
  databaseHost: {{ .Values.databaseHost | default "database" }}
  restPort: {{ .Values.restPort | default "3000" | quote }}
  risId: {{ .Values.risId | default "8522" | quote }}
  dbHost: {{ .Values.dbHost | default "postgres" }}
  dbPort: {{ .Values.dbPort | default "5432" | quote }}
  dbVersion: {{ .Values.dbVersion | default "24" | quote }}
  dbName: {{ .Values.dbName | default "artemis_db" | quote }}
  dbUser: {{ .Values.dbUser | default "artemis_user" | quote }}
  dbSchema: {{ .Values.dbSchema | default "public" | quote }}
  dbAutoClean: {{ .Values.dbAutoClean | default "false" | quote }}
  dbBackup: {{ .Values.dbBackup | default "true" | quote }}
  dbHijackDormant: {{ .Values.dbHijackDormant | default "false" | quote }}
  dbUri: postgres://{{ .Values.dbUser | default "artemis_user" }}:{{ .Values.dbPass | default "Art3m1s" }}@{{ .Values.dbHost | default "postgres"}}:{{ .Values.dbPort | default 5432 }}/{{ .Values.dbName | default "artemis_db" }}
  webappHost: {{ .Values.webappHost | default "frontend" }}
  webappPort: {{ .Values.webappPort | default "4200" | quote }}
  adminEmail: {{ .Values.adminEmail | default "<EMAIL>" | quote }}
  mongodbUser: {{ .Values.mongodbUser | default "admin" | quote }}
  mongodbHost: {{ .Values.mongodbHost | default "mongodb" | quote }}
  mongodbPort: {{ .Values.mongodbPort | default "27017" | quote }}
  mongodbName: {{ .Values.mongodbName | default "artemis-web" | quote }}
  ldapEnabled: {{ .Values.ldapEnabled | default "true" | quote }}
  ldapHost: {{ .Values.ldapHost | default "ldap" | quote }}
  ldapPort: {{ .Values.ldapPort | default "10389" | quote }}
  ldapProtocol: {{ .Values.ldapProtocol | default "ldap" | quote }}
  ldapBindDN: {{ .Values.ldapBindDN | default "cn=admin,dc=planetexpress,dc=com" | quote }}
  ldapSearchBase: {{ .Values.ldapSearchBase | default "ou=people,dc=planetexpress,dc=com" | quote }}
  ldapSearchFilter: {{ .Values.ldapSearchFilter | default "(mail={{username}})" | quote }}
  ldapSearchAttributes: {{ .Values.ldapSearchAttributes | default "mail,uid" | quote }}
  ldapGroupSearchBase: {{ .Values.ldapGroupSearchBase | default "ou=people,dc=planetexpress,dc=com" | quote }}
  ldapGroupSearchFilter: {{ .Values.ldapGroupSearchFilter | default "(mail={{username}})" | quote }}
  ldapGroupSearchAttributes: {{ .Values.ldapGroupSearchAttributes | default "mail,uid" | quote }}
  ldapEmailFieldName: {{ .Values.ldapEmailFieldName | default "mail" | quote }}
  ldapAdminGroup: {{ .Values.ldapAdminGroup | default "admin_staff" | quote }}
  rabbitmqHost: {{ .Values.rabbimtqHost | default "rabbitmq" }}
  rabbitmqPort: {{ .Values.rabbimtqPort | default "5672" | quote }}
  rabbitmqUser: {{ .Values.rabbitmqUser | default "guest" | quote }}
  rabbitmqUri: amqp://{{ .Values.rabbitmqUser | default "guest" }}:{{ .Values.rabbitmqPass | default "guest" }}@{{ .Values.rabbitmqHost | default "rabbitmq"}}:{{ .Values.rabbitmqPort | default 5672 }}//
  rabbitmqIoThreadPoolSize: {{ .Values.rabbitmqIoThreadPoolSize | default "128" | quote }}
  hasuraHost: {{ .Values.hasuraHost | default "graphql" }}
  hasuraPort: {{ .Values.hasuraPort | default "8080" | quote }}
  hasuraGui: {{ .Values.hasuraGui | default "false" | quote }}
  hijackLogFilter: {{ .Values.hijackLogFilter | default "" | quote }}
  monTimeoutLastBgpUpdate: {{ .Values.monTimeoutLastBgpUpdate | default "3600" | quote }}
  hijackLogFields: {{ .Values.hijackLogFields | default "" | quote }}
  artemisWebHost: {{ .Values.ingress.host | default "artemis.com" }}
  withdrawnHijackThreshold: {{ .Values.withdrawnHijackThreshold | default "80" | quote }}
  rpkiValidatorEnabled: {{ .Values.rpkiValidatorEnabled | default "false" | quote }}
  rpkiValidatorHost: {{ .Values.rpkiValidatorHost | default "routinator" | quote }}
  rpkiValidatorPort: {{ .Values.rpkiValidatorPort | default "3323" | quote }}
  testEnv: {{ .Values.testEnv | default "false" | quote }}
  autoRecoverProcessState: {{ .Values.autoRecoverProcessState | default "true" | quote }}
  sessionTimeout: {{ .Values.sessionTimeout | default "1800" | quote }}
  inactivityTimeout: {{ .Values.inactivityTimeout | default "900" | quote }}
  limitWindow: {{ .Values.limitWindow | default "900000" | quote }}
  limitRequests: {{ .Values.limitRequests | default "20" | quote }}
  captchaWindow: {{ .Values.captchaWindow | default "900000" | quote }}
  captchaTries: {{ .Values.captchaTries | default "4" | quote }}
  artemisWebBaseDir: {{ .Values.artemisWebBaseDir | default "" | quote }}
