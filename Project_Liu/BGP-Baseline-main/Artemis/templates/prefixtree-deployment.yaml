apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: prefixtree
{{ include "artemis.labels" . | indent 4 }}
  name: prefixtree
{{- with .Values.services.prefixtree }}
spec:
  selector:
    matchLabels:
      app: prefixtree
  replicas: {{ .replicas | default 1 }}
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: prefixtree
    spec:
      containers:
      - name: prefixtree
        image: {{ .image }}:{{ $.Values.systemVersion }}
        imagePullPolicy: Always
        env:
        - name: RABBITMQ_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqHost
        - name: RABBITMQ_PASS
          valueFrom:
            secretKeyRef:
              name: secret
              key: rabbitmqPass
        - name: RABBITMQ_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqPort
        - name: RABBITMQ_USER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqUser
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: redisHost
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: redisPort
        - name: REST_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: restPort
        {{- with $.Values.probes }}
{{ toYaml . | nindent 8 }}
        {{- end }}
        volumeMounts:
        - mountPath: /etc/artemis/logging.yaml
          name: prefixtree-configmap
          subPath: logging.yaml
      restartPolicy: Always
      volumes:
      - configMap:
          name: volumes
        name: prefixtree-configmap
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
