apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: database
{{ include "artemis.labels" . | indent 4 }}
  name: database
{{- with .Values.services.database }}
spec:
  selector:
    matchLabels:
      app: database
  replicas: {{ .replicas | default 1 }}
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: database
    spec:
      containers:
      - name: database
        image: {{ .image }}:{{ $.Values.systemVersion }}
        imagePullPolicy: Always
        env:
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: dbHost
        - name: DB_NAME
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: dbName
        - name: DB_PASS
          valueFrom:
            secretKeyRef:
              name: secret
              key: dbPass
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: dbPort
        - name: DB_USER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: dbUser
        - name: DB_VERSION
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: dbVersion
        - name: HASURA_GRAPHQL_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: secret
              key: hasuraSecret
        - name: HASURA_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: hasuraHost
        - name: HASURA_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: hasuraPort
        - name: HISTORIC
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: historic
        - name: RABBITMQ_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqHost
        - name: RABBITMQ_PASS
          valueFrom:
            secretKeyRef:
              name: secret
              key: rabbitmqPass
        - name: RABBITMQ_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqPort
        - name: RABBITMQ_USER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqUser
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: redisHost
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: redisPort
        - name: REST_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: restPort
        - name: WITHDRAWN_HIJACK_THRESHOLD
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: withdrawnHijackThreshold
        {{- with $.Values.probes }}
{{ toYaml . | nindent 8 }}
        {{- end }}
        volumeMounts:
        - mountPath: /etc/artemis/logging.yaml
          name: database-configmap
          subPath: logging.yaml
      restartPolicy: Always
      volumes:
      - configMap:
          name: volumes
        name: database-configmap
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
