apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: pg-amqp-bridge
{{ include "artemis.labels" . | indent 4 }}
  name: pg-amqp-bridge
{{- with .Values.services.pgamqpbridge }}
spec:
  selector:
    matchLabels:
      app: pg-amqp-bridge
  replicas: 1
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: pg-amqp-bridge
    spec:
      initContainers:
      - name: wait-for-service
        image: busybox
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'until nc -z {{ $.Values.rabbitmqHost }} {{ $.Values.rabbitmqPort}}; do echo waiting for rabbitmq; sleep 10; done;']
      containers:
      - name: pg-amqp-bridge
        image: {{ .image }}
        env:
        - name: AMQP_URI
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqUri
        - name: BRIDGE_CHANNELS
          value: events:amq.direct
        - name: POSTGRESQL_URI
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: dbUri
      restartPolicy: Always
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
