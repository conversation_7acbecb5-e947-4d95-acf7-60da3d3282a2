apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: detection
{{ include "artemis.labels" . | indent 4 }}
  name: detection
{{- with .Values.services.detection }}
spec:
  selector:
    matchLabels:
      app: detection
  replicas: {{ .replicas | default 1 }}
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: detection
    spec:
      containers:
      - name: detection
        image: {{ .image }}:{{ $.Values.systemVersion }}
        imagePullPolicy: Always
        env:
        - name: RABBITMQ_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqHost
        - name: RABBITMQ_PASS
          valueFrom:
            secretKeyRef:
              name: secret
              key: rabbitmqPass
        - name: RABBITMQ_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqPort
        - name: RABBITMQ_USER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqUser
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: redisHost
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: redisPort
        - name: REST_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: restPort
        - name: RPKI_VALIDATOR_ENABLED
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rpkiValidatorEnabled
        - name: RPKI_VALIDATOR_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rpkiValidatorHost
        - name: RPKI_VALIDATOR_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rpkiValidatorPort
        - name: TEST_ENV
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: testEnv
        {{- with $.Values.probes }}
{{ toYaml . | nindent 8 }}
        {{- end }}
        volumeMounts:
        - mountPath: /etc/artemis/logging.yaml
          name: detection-configmap
          subPath: logging.yaml
      restartPolicy: Always
      volumes:
      - configMap:
          name: volumes
        name: detection-configmap
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
