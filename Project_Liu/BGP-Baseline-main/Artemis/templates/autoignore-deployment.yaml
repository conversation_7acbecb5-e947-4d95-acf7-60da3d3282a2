apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: autoignore
{{ include "artemis.labels" . | indent 4 }}
  name: autoignore
{{- with .Values.services.autoignore }}
spec:
  selector:
    matchLabels:
      app: autoignore
  replicas: {{ .replicas | default 1 }}
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: autoignore
    spec:
      containers:
      - name: autoignore
        image: {{ .image }}:{{ $.Values.systemVersion }}
        imagePullPolicy: Always
        env:
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: dbHost
        - name: DB_NAME
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: dbName
        - name: DB_PASS
          valueFrom:
            secretKeyRef:
              name: secret
              key: dbPass
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: dbPort
        - name: DB_USER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: dbUser
        - name: RABBITMQ_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqHost
        - name: RABBITMQ_PASS
          valueFrom:
            secretKeyRef:
              name: secret
              key: rabbitmqPass
        - name: RABBITMQ_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqPort
        - name: RABBITMQ_USER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqUser
        - name: REST_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: restPort
        {{- with $.Values.probes }}
{{ toYaml . | nindent 8 }}
        {{- end }}
        volumeMounts:
        - mountPath: /etc/artemis/logging.yaml
          name: autoignore-configmap
          subPath: logging.yaml
      restartPolicy: Always
      volumes:
      - configMap:
          name: volumes
        name: autoignore-configmap
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
