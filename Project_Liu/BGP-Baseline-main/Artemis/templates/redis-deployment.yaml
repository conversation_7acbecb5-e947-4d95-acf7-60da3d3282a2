apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: redis
{{ include "artemis.labels" . | indent 4 }}
  name: redis
{{- with .Values.services.redis }}
spec:
  selector:
    matchLabels:
      app: redis
  replicas: 1
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: {{ .image }}:latest
        resources: {}
        command:
          - redis-server
        args:
          - /usr/local/etc/redis/redis.conf
        volumeMounts:
        - mountPath: /usr/local/etc/redis/redis.conf
          name: redis-configmap
          subPath: redis.conf
      restartPolicy: Always
      volumes:
      - configMap:
          name: volumes
        name: redis-configmap
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
  {{- end }}
