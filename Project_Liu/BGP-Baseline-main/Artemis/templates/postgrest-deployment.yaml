apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: postgrest
{{ include "artemis.labels" . | indent 4 }}
  name: postgrest
{{- with .Values.services.postgrest }}
spec:
  selector:
    matchLabels:
      app: postgrest
  replicas: 1
  strategy: {}
  template:
    metadata:
      labels:
        app: postgrest
    spec:
      initContainers:
      - name: wait-for-service
        image: busybox
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'until nc -z {{ $.Values.dbHost }} {{ $.Values.dbPort }}; do echo waiting for services; sleep 10; done;']
      containers:
      - name: postgrest
        image: {{ .image }}
        env:
        - name: PGRST_DB_ANON_ROLE
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: dbUser
        - name: PGRST_DB_SCHEMA
          value: public
        - name: PGRST_DB_URI
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: dbUri
      restartPolicy: Always
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
  {{- end }}
