1. Get the application URL by running these commands:
{{- if .Values.ingress.enabled }}
  http{{ if $.Values.ingress.tlsEnabled }}s{{ end }}://{{ .Values.ingress.host }}/
{{- else }}
  export POD_NAME=$(kubectl get pods -lapp=nginx,release={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}")
  echo "Visit https://127.0.0.1:8080 to use your application"
  kubectl port-forward $POD_NAME 8080:443
{{- end }}
