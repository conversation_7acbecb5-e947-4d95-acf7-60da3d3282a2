apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rabbitmq
{{ include "artemis.labels" . | indent 4 }}
  name: rabbitmq
{{- with .Values.services.rabbitmq }}
spec:
  selector:
    matchLabels:
      app: rabbitmq
  replicas: 1
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: rabbitmq
    spec:
      containers:
      - name: rabbitmq
        image: {{ .image }}
        env:
        - name: RABBITMQ_DEFAULT_USER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqUser
        - name: RABBITMQ_DEFAULT_PASS
          valueFrom:
            secretKeyRef:
              name: secret
              key: rabbitmqPass
        - name: RABBITMQ_IO_THREAD_POOL_SIZE
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqIoThreadPoolSize
        volumeMounts:
        - mountPath: /etc/rabbitmq/enabled_plugins
          name: rabbitmq-configmap0
          subPath: enabled_plugins
      restartPolicy: Always
      volumes:
      - configMap:
          name: volumes
        name: rabbitmq-configmap0
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
  {{- end }}
