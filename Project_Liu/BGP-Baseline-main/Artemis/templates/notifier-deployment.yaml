apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: notifier
{{ include "artemis.labels" . | indent 4 }}
  name: notifier
{{- with .Values.services.notifier }}
spec:
  selector:
    matchLabels:
      app: notifier
  replicas: {{ .replicas | default 1 }}
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: notifier
    spec:
      containers:
      - env:
        - name: ARTEMIS_WEB_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: artemisWebHost
        - name: RABBITMQ_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqHost
        - name: RABBITMQ_PASS
          valueFrom:
            secretKeyRef:
              name: secret
              key: rabbitmqPass
        - name: RABBITMQ_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqPort
        - name: RABBITMQ_USER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqUser
        - name: REST_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: restPort
        image: {{ .image }}:{{ $.Values.systemVersion }}
        imagePullPolicy: IfNotPresent
        name: notifier
        {{- with $.Values.probes }}
{{ toYaml . | nindent 8 }}
        {{- end }}
        volumeMounts:
        - mountPath: /etc/artemis/logging.yaml
          name: notifier-configmap
          subPath: logging.yaml
      restartPolicy: Always
      volumes:
      - configMap:
          name: volumes
        name: notifier-configmap
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
  {{- end }}
