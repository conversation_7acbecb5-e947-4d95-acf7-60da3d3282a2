apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: bgpstreamhisttap
{{ include "artemis.labels" . | indent 4 }}
  name: bgpstreamhisttap
{{- with .Values.services.bgpstreamhisttap }}
spec:
  selector:
    matchLabels:
      app: bgpstreamhisttap
  replicas: {{ .replicas | default 1 }}
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: bgpstreamhisttap
    spec:
      containers:
      - name: bgpstreamhisttap
        image: {{ .image }}:{{ $.Values.systemVersion }}
        imagePullPolicy: Always
        env:
        - name: HISTORIC
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: historic
        - name: RABBITMQ_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqHost
        - name: RABBITMQ_PASS
          valueFrom:
            secretKeyRef:
              name: secret
              key: rabbitmqPass
        - name: RABBITMQ_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqPort
        - name: RABBITMQ_USER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqUser
        - name: REST_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: restPort
        {{- with $.Values.probes }}
{{ toYaml . | nindent 8 }}
        {{- end }}
        volumeMounts:
        - mountPath: /etc/artemis/logging.yaml
          name: bgpstreamhisttap-configmap
          subPath: logging.yaml
      restartPolicy: Always
      volumes:
      - configMap:
          name: volumes
        name: bgpstreamhisttap-configmap
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
