apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: exabgptap
{{ include "artemis.labels" . | indent 4 }}
  name: exabgptap
{{- with .Values.services.exabgptap }}
spec:
  selector:
    matchLabels:
      app: exabgptap
  replicas: {{ .replicas | default 1 }}
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: exabgptap
    spec:
      containers:
      - name: exabgptap
        image: {{ .image }}:{{ $.Values.systemVersion }}
        imagePullPolicy: Always
        env:
        - name: MON_TIMEOUT_LAST_BGP_UPDATE
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: monTimeoutLastBgpUpdate
        - name: RABBITMQ_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqHost
        - name: RABBITMQ_PASS
          valueFrom:
            secretKeyRef:
              name: secret
              key: rabbitmqPass
        - name: RABBITMQ_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqPort
        - name: RABBITMQ_USER
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: rabbitmqUser
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: redisHost
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: redisPort
        - name: REST_PORT
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: restPort
        {{- with $.Values.probes }}
{{ toYaml . | nindent 8 }}
        {{- end }}
        volumeMounts:
        - mountPath: /etc/artemis/logging.yaml
          name: exabgptap-configmap
          subPath: logging.yaml
      restartPolicy: Always
      volumes:
      - configMap:
          name: volumes
        name: exabgptap-configmap
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
