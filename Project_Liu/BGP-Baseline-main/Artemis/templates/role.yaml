apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
    name: endpoints-reader
rules:
    - apiGroups: [""]
      resources: ["endpoints"]
      verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
    name: read-endpoints
subjects:
    - kind: ServiceAccount
      name: default
roleRef:
    kind: Role
    name: endpoints-reader
    apiGroup: rbac.authorization.k8s.io
