apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: mongodb
{{ include "artemis.labels" . | indent 4 }}
  name: mongodb
{{- with .Values.services.mongodb }}
spec:
  selector:
    matchLabels:
      app: mongodb
  replicas: {{ .replicas | default 1 }}
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
      - name: mongodb
        image: {{  .image  }}:4.4.6-bionic
        imagePullPolicy: Always
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            configMapKeyRef:
              name: configmap
              key: mongodbUser
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: secret
              key: mongodbPass
        volumeMounts:
        - mountPath: /data/db/
          name: frontend-pvc
          # subPath: mongo-data
      restartPolicy: Always
      volumes:
      - persistentVolumeClaim:
          claimName: frontend-pvc
        name: frontend-pvc
      {{- with $.Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | nindent 8 }}
      {{- end }}
  {{- end }}
