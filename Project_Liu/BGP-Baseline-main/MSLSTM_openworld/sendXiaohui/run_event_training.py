#!/usr/bin/env python3
import os
import json
import torch
import numpy as np
import pandas as pd
import joblib  # 用于加载 .pkl 文件
import logging
import shutil

from classification import GRUClassifier
from torch.utils.data import ConcatDataset, TensorDataset, DataLoader, random_split
from pytorch_lightning.callbacks import ModelCheckpoint
import pytorch_lightning as pl
from collections import Counter
from feature_extraction import SlidingWindowDataset

# 统一特征维度
EXPECTED_DIM = 26

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 全局配置
CONFIG = {
    "window_size": 10,
    "hid_dim": 100,      # 遵从指示，从 256 降至 100
    "n_layers": 2,
    "dropout": 0.5,      # 遵从指示，从 0.2 提升至 0.5
    "fore_out": 2,       # 二分类输出
    "batch_size": 128,
    "val_split": 0.2,
    "max_epochs": 200,   # 遵从指示，大幅增加训练轮数
}

def train_general_model(all_events: list, model_dir: str, scaler_path: str, config: dict):
    """
    使用所有事件数据，训练一个通用的二分类模型。
    返回: 最佳模型路径 或 None
    """
    try:
        # --- 1. 加载 Scaler ---
        if not os.path.exists(scaler_path):
            logger.error(f"Scaler file not found at {scaler_path}. Cannot proceed with training.")
            return None
        scaler = joblib.load(scaler_path)
        logger.info(f"Successfully loaded scaler from {scaler_path}")

        # --- 2. 组合和缩放数据集 ---
        datasets = []
        for event_dir in all_events:
            feature_file = os.path.join(event_dir, "features", "fea.json")
            if not os.path.exists(feature_file):
                logger.warning(f"Feature file not found for event {os.path.basename(event_dir)}, skipping.")
                continue
            
            # 直接从JSON加载数据，然后应用Scaler
            temp_dataset = SlidingWindowDataset.from_json(feature_file, window=config["window_size"])
            
            # 应用缩放
            # 从dataset中提取特征和标签
            features = np.array([item[0].numpy() for item in temp_dataset])
            labels = np.array([item[1] for item in temp_dataset])

            if features.shape[0] == 0:
                logger.warning(f"No data in {os.path.basename(event_dir)} after windowing, skipping.")
                continue
            
            # features shape: (num_samples, window_size, num_features)
            # Scaler expects (num_samples, num_features), so we need to reshape
            num_samples, window_size, num_features = features.shape
            features_reshaped = features.reshape(-1, num_features)
            scaled_features_reshaped = scaler.transform(features_reshaped)
            scaled_features = scaled_features_reshaped.reshape(num_samples, window_size, num_features)
            
            # 创建一个新的TensorDataset
            scaled_dataset = TensorDataset(torch.from_numpy(scaled_features).float(), torch.from_numpy(labels).long())
            datasets.append(scaled_dataset)
            logger.info(f"Loaded and scaled data from {os.path.basename(event_dir)}")


        if not datasets:
            logger.error("No valid event data found, cannot train model.")
            return None
        combined_dataset = ConcatDataset(datasets)

        # --- 3. 计算类别权重 ---
        all_labels = np.concatenate([d.tensors[1].numpy() for d in datasets])
        counter = Counter(all_labels)
        n_samples = len(all_labels)
        n_classes = len(counter)
        
        # 避免除以零
        weight_for_0 = n_samples / (n_classes * counter.get(0, 1))
        weight_for_1 = n_samples / (n_classes * counter.get(1, 1))
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        class_weights = torch.tensor([weight_for_0, weight_for_1], dtype=torch.float32).to(device)
        logger.info(f"Combined dataset class distribution: {dict(counter)}. Applying weights: {class_weights.cpu().numpy()}")
        
        # --- 4. 划分训练/验证集 ---
        val_size = int(config["val_split"] * len(combined_dataset))
        train_size = len(combined_dataset) - val_size
        train_dataset, val_dataset = random_split(combined_dataset, [train_size, val_size])
        
        train_loader = DataLoader(train_dataset, batch_size=config["batch_size"], shuffle=True, num_workers=0)
        val_loader = DataLoader(val_dataset, batch_size=config["batch_size"], shuffle=False, num_workers=0)
        logger.info(f"Training with {len(train_dataset)} samples, validating with {len(val_dataset)} samples.")

        # --- 5. 训练模型 ---
        os.makedirs(model_dir, exist_ok=True)
        monitor = "val_loss"
        checkpoint_callback = ModelCheckpoint(
            dirpath=model_dir,
            filename='general_model_best', # 固定文件名
            save_top_k=1,
            verbose=True,
            monitor=monitor,
            mode='min',
        )

        model = GRUClassifier(
            input_size=EXPECTED_DIM,
            hidden_size=config["hid_dim"],
            num_layers=config["n_layers"],
            output_size=config["fore_out"],
            class_weights=class_weights,
            learning_rate=1e-4, # 遵从指示，将学习率提升回 1e-4
            window_size=config["window_size"],
            dropout_rate=config["dropout"]
        )

        trainer = pl.Trainer(
            accelerator="gpu" if torch.cuda.is_available() else "cpu",
            devices=1,
            max_epochs=config["max_epochs"],
            callbacks=[checkpoint_callback], # 移除了 EarlyStopping
            logger=False,
            enable_progress_bar=True,
            enable_model_summary=True,
        )
        
        # 检查是否存在断点，如果存在则从断点继续训练
        ckpt_path = os.path.join(model_dir, "general_model.ckpt")
        if os.path.exists(ckpt_path):
            logger.info(f"Resuming training from checkpoint: {ckpt_path}")
            trainer.fit(model, train_loader, val_loader, ckpt_path=ckpt_path)
        else:
            logger.info("Starting training from scratch.")
            trainer.fit(model, train_loader, val_loader)


        best_model_path = checkpoint_callback.best_model_path
        if not best_model_path or not os.path.exists(best_model_path):
            logger.error("Training finished but could not find the best model checkpoint.")
            return None
            
        final_model_path = os.path.join(model_dir, "general_model.ckpt")
        shutil.move(best_model_path, final_model_path)
        logger.info(f"Training complete. Best model saved at {final_model_path}")

        return final_model_path

    except Exception as e:
        logger.error(f"Failed to train general model: {e}", exc_info=True)
        return None

def main():
    script_dir = os.path.dirname(__file__)
    
    # 定义数据源、scaler和模型输出路径
    data_source_dir = os.path.join(script_dir, "data")
    scaler_path = "/data/Project_Liu/BGP-Baseline-main/ISP-Operated/output/scaler.pkl"
    model_output_dir = os.path.join(script_dir, "models")
    
    logger.info("===== Starting General Model Training =====")
    
    # --- 1. 收集所有事件目录 ---
    if not os.path.isdir(data_source_dir):
        logger.error(f"Data source directory not found: {data_source_dir}")
        return

    all_event_dirs = sorted([os.path.join(data_source_dir, d) for d in os.listdir(data_source_dir) if os.path.isdir(os.path.join(data_source_dir, d))])
    
    if not all_event_dirs:
        logger.error(f"No event subdirectories found in {data_source_dir}")
        return
        
    logger.info(f"Found {len(all_event_dirs)} events to use for training.")

    # --- 2. 调用训练函数 ---
    trained_model_path = train_general_model(all_event_dirs, model_output_dir, scaler_path, CONFIG)

    if trained_model_path:
        logger.info(f"Successfully trained general model. Saved to: {trained_model_path}")
    else:
        logger.error("General model training failed.")

if __name__ == "__main__":
    main() 