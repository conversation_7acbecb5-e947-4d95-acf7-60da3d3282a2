#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import logging
import torch
import numpy as np
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import matplotlib.pyplot as plt
from classification import mymodel  # 导入您的模型类
from feature_extraction import SlidingWindowDataset, Multi_Scale_Wavelet0, clean_data
import pytorch_lightning as pl
from torch.utils.data import DataLoader
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- 全局设备配置 ---
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
logger.info(f"使用设备: {device}")

def main(args):
    """
    测试脚本，生成带有随机扰动的距离值
    """
    # 1. 加载特征数据
    try:
        labels_np, features_np = clean_data(args.features)
        logger.info(f"加载了 {len(features_np)} 个样本，其中异常样本 {sum(labels_np)} 个")
        
        # 应用小波变换
        wavelet_features, _ = Multi_Scale_Wavelet0(features_np, level=2)
        res_0 = wavelet_features[0]
        
        # 创建随机中心点
        center = torch.randn(1, 1)
        logger.info(f"随机中心点: {center.item():.8f}")
        
        # 创建随机扰动的特征
        features_tensor = torch.tensor(res_0, dtype=torch.float32)
        batch_size = features_tensor.shape[0]
        
        # 添加随机扰动
        noise_scale = 0.05  # 5%的扰动
        noise = torch.randn_like(features_tensor) * noise_scale
        features_tensor = features_tensor + noise
        
        # 计算到中心点的距离
        # 为了简化，我们只使用第一个特征维度
        distances = torch.sum((features_tensor[:, 0:1] - center) ** 2, dim=1)
        
        # 分别计算正常样本和异常样本的距离
        normal_mask = (torch.tensor(labels_np) == 0)
        anomaly_mask = (torch.tensor(labels_np) == 1)
        
        normal_distances = distances[normal_mask].numpy().tolist() if normal_mask.sum() > 0 else []
        anomaly_distances = distances[anomaly_mask].numpy().tolist() if anomaly_mask.sum() > 0 else []
        
        # 计算统计信息
        logger.info("\n===== 距离统计信息 =====")
        
        if normal_distances:
            logger.info("\n正常样本距离统计:")
            logger.info(f"样本数量: {len(normal_distances)}")
            logger.info(f"最小值: {min(normal_distances):.8f}")
            logger.info(f"最大值: {max(normal_distances):.8f}")
            logger.info(f"中位数: {np.median(normal_distances):.8f}")
            logger.info(f"平均值: {np.mean(normal_distances):.8f}")
            logger.info(f"标准差: {np.std(normal_distances):.8f}")
            
            # 输出百分位数
            percentiles = [25, 50, 75, 90, 95, 97.5, 99]
            for p in percentiles:
                logger.info(f"{p}%分位数: {np.percentile(normal_distances, p):.8f}")
        
        if anomaly_distances:
            logger.info("\n异常样本距离统计:")
            logger.info(f"样本数量: {len(anomaly_distances)}")
            logger.info(f"最小值: {min(anomaly_distances):.8f}")
            logger.info(f"最大值: {max(anomaly_distances):.8f}")
            logger.info(f"中位数: {np.median(anomaly_distances):.8f}")
            logger.info(f"平均值: {np.mean(anomaly_distances):.8f}")
            logger.info(f"标准差: {np.std(anomaly_distances):.8f}")
            
            # 输出前10个异常样本的距离
            logger.info("\n异常样本距离详情(前10个):")
            for i, dist in enumerate(anomaly_distances[:10]):
                logger.info(f"异常样本 {i+1}: {dist:.8f}")
        
        # 测试不同阈值
        thresholds = [0.001, 0.005, 0.01, 0.05, 0.1, 0.5]
        logger.info("\n不同阈值下的检测结果:")
        
        for threshold in thresholds:
            # 使用阈值预测
            preds = (distances > threshold).int().numpy()
            
            # 计算指标
            if sum(preds) > 0 and sum(labels_np) > 0:
                precision = precision_score(labels_np, preds)
                recall = recall_score(labels_np, preds)
                f1 = f1_score(labels_np, preds)
                accuracy = accuracy_score(labels_np, preds)
                
                logger.info(f"阈值 {threshold:.6f}: 准确率={accuracy:.4f}, 精确率={precision:.4f}, 召回率={recall:.4f}, F1分数={f1:.4f}, 报警次数={sum(preds)}")
            else:
                logger.info(f"阈值 {threshold:.6f}: 无法计算指标，报警次数={sum(preds)}")
        
    except Exception as e:
        logger.error(f"处理过程中出错: {e}", exc_info=True)
        return

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='测试距离计算')
    parser.add_argument('--features', type=str, required=True, help='特征文件路径')
    args = parser.parse_args()
    main(args) 