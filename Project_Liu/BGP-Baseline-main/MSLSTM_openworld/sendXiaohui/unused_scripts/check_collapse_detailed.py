#!/usr/bin/env python3
import os
import torch
import numpy as np
import logging
import json
import argparse
from classification import mymodel

# 配置日志，同时输出到控制台和文件
log_file = 'model_check_detailed.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_model_detailed(model_path, device="cuda" if torch.cuda.is_available() else "cpu"):
    """加载模型并详细检查状态字典"""
    try:
        logger.info(f"尝试加载模型: {model_path}")
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        
        # 从检查点获取模型参数
        in_dim = 26  # 默认特征维度
        hid_dim = 30  # 默认隐藏层维度
        n_layers = 2  # 默认层数
        dropout = 0.2  # 默认dropout
        
        # 如果检查点中包含这些参数，则使用检查点中的值
        if 'hyper_parameters' in checkpoint:
            hp = checkpoint['hyper_parameters']
            logger.info(f"检查点超参数: {hp}")
            if 'in_dim' in hp:
                in_dim = hp['in_dim']
            if 'hid_dim' in hp:
                hid_dim = hp['hid_dim']
            if 'n_layers' in hp:
                n_layers = hp['n_layers']
            if 'dropout' in hp:
                dropout = hp['dropout']
        
        # 创建模型实例
        model = mymodel(in_dim=in_dim, hid_dim=hid_dim, n_layers=n_layers, dropout=dropout)
        
        # 检查模型状态字典
        model_state_dict = model.state_dict()
        checkpoint_state_dict = checkpoint['state_dict']
        
        logger.info("===== 模型状态字典键 =====")
        for key in model_state_dict.keys():
            logger.info(f"模型键: {key}, 形状: {model_state_dict[key].shape}")
        
        logger.info("\n===== 检查点状态字典键 =====")
        for key in checkpoint_state_dict.keys():
            if key in model_state_dict:
                if model_state_dict[key].shape == checkpoint_state_dict[key].shape:
                    logger.info(f"匹配键: {key}, 形状: {checkpoint_state_dict[key].shape}")
                else:
                    logger.warning(f"形状不匹配键: {key}, 模型形状: {model_state_dict[key].shape}, 检查点形状: {checkpoint_state_dict[key].shape}")
            else:
                logger.warning(f"多余键: {key}, 形状: {checkpoint_state_dict[key].shape}")
        
        logger.info("\n===== 缺失的键 =====")
        for key in model_state_dict.keys():
            if key not in checkpoint_state_dict:
                logger.warning(f"缺失键: {key}, 模型形状: {model_state_dict[key].shape}")
        
        # 加载模型状态
        missing_keys, unexpected_keys = model.load_state_dict(checkpoint['state_dict'], strict=False)
        
        logger.info("\n===== 加载状态字典结果 =====")
        logger.info(f"缺失键: {missing_keys}")
        logger.info(f"多余键: {unexpected_keys}")
        
        # 检查中心点是否正确加载
        state_dict = checkpoint['state_dict']
        if 'center' in state_dict:
            center_value = state_dict['center']
            if isinstance(center_value, torch.Tensor):
                if center_value.numel() > 0:
                    model.center = center_value.to(device)
                    logger.info(f"从检查点加载中心点: {model.center.shape}")
                    logger.info(f"中心点值: {model.center.flatten().tolist()}")
        
        # 检查阈值是否正确加载
        if hasattr(model, 'anomaly_threshold') and model.anomaly_threshold is None:
            if 'hyper_parameters' in checkpoint and 'anomaly_threshold' in checkpoint['hyper_parameters']:
                model.anomaly_threshold = checkpoint['hyper_parameters']['anomaly_threshold']
                logger.info(f"从检查点超参数加载阈值: {model.anomaly_threshold}")
            elif 'anomaly_threshold' in state_dict:
                model.anomaly_threshold = state_dict['anomaly_threshold']
                logger.info(f"从检查点状态字典加载阈值: {model.anomaly_threshold}")
        
        model.to(device)
        model.eval()  # 设置为评估模式
        logger.info(f"成功加载模型: {model_path}")
        return model, device
    
    except Exception as e:
        logger.error(f"加载模型时出错: {e}")
        return None, device

def main():
    parser = argparse.ArgumentParser(description="详细检查模型状态字典")
    parser.add_argument("--model", type=str, required=True, help="模型文件路径")
    args = parser.parse_args()
    
    # 加载模型
    model, device = load_model_detailed(args.model)
    if model is None:
        return
    
    logger.info(f"详细检查完成，日志已保存到 {log_file}")

if __name__ == "__main__":
    main() 