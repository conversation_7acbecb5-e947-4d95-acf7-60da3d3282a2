#!/usr/bin/env python3
import json
import pandas as pd
import os
import numpy as np
import time
from datetime import datetime

def process_event(event_dir):
    """
    处理单个事件目录，生成对应的fea.json文件
    
    参数:
    - event_dir: 事件目录路径
    
    返回:
    - 成功则返回True，否则返回False
    """
    event_name = os.path.basename(event_dir)
    print(f"\n===== 处理事件: {event_name} =====")
    
    # 构建文件路径
    features_file = os.path.join(event_dir, "features", f"{event_name}_features.csv")
    labels_file = os.path.join(event_dir, "minute_labels.csv")
    output_file = os.path.join(event_dir, "features", "fea.json")
    
    # 检查文件是否存在
    if not os.path.exists(features_file):
        print(f"特征文件不存在: {features_file}")
        return False
    
    if not os.path.exists(labels_file):
        print(f"标签文件不存在: {labels_file}")
        return False
    
    # 检查output_file所在的目录是否存在，如果不存在则创建
    output_dir = os.path.dirname(output_file)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")
    
    # 读取特征和标签
    try:
        features_df = pd.read_csv(features_file)
        labels_df = pd.read_csv(labels_file)
        
        print(f"读取了 {len(features_df)} 行特征数据")
        print(f"读取了 {len(labels_df)} 行标签数据")
        
        # 检查特征数据是否包含标签列
        if 'label' in features_df.columns:
            print("特征文件已包含标签列")
        else:
            print("特征文件不包含标签列，将使用标签文件")
            
            # 如果特征数据没有标签列，则使用标签文件中的标签
            # 注意：这里假设特征和标签的行数相同，且一一对应
            if len(features_df) != len(labels_df):
                print(f"警告: 特征行数 ({len(features_df)}) 与标签行数 ({len(labels_df)}) 不匹配")
                
            # 将标签添加到特征数据中
            features_df['label'] = labels_df['label'].values[:len(features_df)]
        
        # 获取所有特征列（排除index和label）
        feature_columns = [col for col in features_df.columns if col not in ['index', 'label']]
        print(f"特征列数量: {len(feature_columns)}")
        
        # 合并特征和标签
        all_data = []
        for i in range(len(labels_df['label'])):
            # 确保特征和标签对齐
            if i < len(features_df):
                # 创建一个包含所有特征和标签的字典
                sample_data = {}
                # 将特征名与特征值对应起来
                for col_idx, col_name in enumerate(feature_columns):
                    sample_data[col_name] = features_df.iloc[i][col_name]
                
                # 确保label是整数
                try:
                    sample_data['label'] = int(labels_df['label'].iloc[i])
                except (ValueError, TypeError):
                    sample_data['label'] = 0  # 如果转换失败，默认为0
                
                all_data.append(sample_data)
        
        # 创建JSON数据
        json_data = []
        for i, item in enumerate(all_data):
            json_data.append(item)
        
        # 保存到JSON文件
        with open(output_file, 'w') as f:
            json.dump(json_data, f, indent=2)
        
        print(f"成功生成JSON文件: {output_file}")
        print(f"生成了 {len(json_data)} 个样本，每个样本有 {len(feature_columns)} 个特征维度")
        
        # 打印一个样本示例
        print("\n样本示例:")
        print(json.dumps(json_data[0], indent=2)[:500] + "...")  # 只显示前500个字符
        
        return True
        
    except Exception as e:
        print(f"处理事件时出错: {e}")
        return False

def main():
    # 只处理outage-20110311-Japan_Earthquake事件
    specific_event = "outage-20110311-Japan_Earthquake"
    
    base_dir = "/data/data/anomaly-event-routedata"
    event_dir = os.path.join(base_dir, specific_event)
    
    if os.path.exists(event_dir) and os.path.isdir(event_dir):
        print(f"\n\n======= 开始处理事件 {specific_event} =======")
        
        if process_event(event_dir):
            print(f"事件 {specific_event} 处理成功")
        else:
            print(f"事件 {specific_event} 处理失败")
    else:
        print(f"事件目录不存在: {event_dir}")
    
    print("\n\n===== 处理完成 =====")

if __name__ == "__main__":
    main() 