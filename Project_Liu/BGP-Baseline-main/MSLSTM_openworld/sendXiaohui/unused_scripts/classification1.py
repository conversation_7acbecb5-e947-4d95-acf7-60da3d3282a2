import os
import numpy as np
import torch
from torch import nn
import pytorch_lightning as pl
from feature_extraction import SlidingWindowDataset, create_data_loaders
import logging
from pytorch_lightning.callbacks import ModelCheckpoint
import torch.nn.functional as F
from typing import Optional
from sklearn.metrics import accuracy_score, recall_score, f1_score, confusion_matrix, precision_score, classification_report
import random


# ------------------ 加权 Focal Loss ------------------
def focal_loss(logits: torch.Tensor, targets: torch.Tensor, alpha: Optional[torch.Tensor] = None, gamma: float = 2.0) -> torch.Tensor:
    """Weighted Focal Loss (multi-class)。

    logits: (N, C)
    targets: (N,) int64
    alpha: tensor[C] or None
    gamma: focusing parameter
    """
    ce_loss = F.cross_entropy(logits, targets, reduction='none', weight=alpha)
    p_t = torch.exp(-ce_loss)
    loss = ((1 - p_t) ** gamma) * ce_loss
    return loss.mean()

class GRULayer(nn.Module):
    """Gated Recurrent Unit (GRU) Layer
    :param in_dim: number of input features
    :param hid_dim: hidden size of the GRU
    :param n_layers: number of layers in GRU
    :param dropout: dropout rate
    """
    def __init__(self, in_dim, hid_dim, n_layers, dropout):
        super(GRULayer, self).__init__()
        self.hid_dim = hid_dim
        self.n_layers = n_layers
        self.dropout = 0.0 if n_layers == 1 else dropout
        self.gru = nn.GRU(in_dim, hid_dim, num_layers=n_layers, batch_first=True, dropout=self.dropout)

    def forward(self, x):
        # The raw output from nn.GRU is (output_features, last_hidden_state)
        # The caller will decide what to do with them.
        out, h = self.gru(x)
        return out, h

class forcastModel(nn.Module):
    def __init__(self, in_dim, hid_dim, out_dim, dropout, n_layers):
        super(forcastModel, self).__init__()
        layers = [nn.Linear(in_dim, hid_dim)]
        for _ in range(n_layers - 1):
            layers.append(nn.Linear(hid_dim, hid_dim))
        layers.append(nn.Linear(hid_dim, out_dim))
        self.layers = nn.ModuleList(layers)
        self.dropout = nn.Dropout(dropout)
        self.relu = nn.ReLU()
    
    def forward(self, x):
        for l in range(len(self.layers)-1):
            x = self.relu(self.layers[l](x))
            x = self.dropout(x)
        return self.layers[-1](x)

class mymodel(pl.LightningModule):
    def __init__(self, in_dim, hid_dim, n_layers, dropout, fore_out, for_n_layer, for_hid_dim, focal_gamma=2.0, class_weights=None):
        # Call super().__init__() first, as is best practice.
        super(mymodel, self).__init__()

        self.n_layers = n_layers
        self.in_dim = in_dim
        self.hid_dim = hid_dim
        self.fore_out = fore_out
        self.dropout = dropout
        self.for_n_layer = for_n_layer
        self.for_hid_dim = for_hid_dim
        self.focal_gamma = focal_gamma

        # 异常检测模式：保存阈值和异常分数
        self.anomaly_threshold = None
        self.secondary_threshold = None  # 二级阈值
        self.anomaly_scores_history = []
        
        # 记录每个样本的分数历史，用于持续性判断
        self.sample_score_history = {}
        
        # 添加数据归一化器，用于记录训练数据的均值和标准差
        self.register_buffer('data_mean', None)
        self.register_buffer('data_std', None)
        self.normalize_data = True  # 控制是否对输入数据进行归一化
        
        # Save hyperparameters to the checkpoint, which is useful for loading models
        # and for visualization in loggers like TensorBoard.
        self.save_hyperparameters('in_dim', 'hid_dim', 'n_layers', 'dropout', 'fore_out', 'for_n_layer', 'for_hid_dim', 'focal_gamma', 'class_weights')

        # GRU层 - 提取时序特征
        self.gru = GRULayer(self.in_dim, self.hid_dim, self.n_layers, self.dropout)
        
        # 重构器 - 用于异常检测（通过重建输入判断异常）
        self.reconstructor = nn.Sequential(
            nn.Linear(self.hid_dim, self.for_hid_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.for_hid_dim, self.in_dim * 10)  # 重建整个序列
        )
        
        # 分类器 - 仍然保留但用于计算异常分数
        self.classifier = forcastModel(self.hid_dim, self.for_hid_dim, self.fore_out, self.dropout, self.n_layers)
        
        # Normalization layers -> 改用 LayerNorm 对异常更稳健
        self.norm1 = nn.LayerNorm(self.in_dim, eps=1e-5)
        self.norm2 = nn.LayerNorm(self.hid_dim, eps=1e-5)
        
        # 边界检测器 - 专门用于区分边界情况
        self.boundary_detector = nn.Sequential(
            nn.Linear(self.hid_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, 1),
            nn.Sigmoid()  # 输出0-1之间的置信度
        )
        
    def forward(self, x):
        # Ensure input is float
        x = x.float()
        
        # 保存原始输入，用于后续计算重建损失
        x_orig = x.clone()
        
        # 对输入数据进行归一化处理
        if self.normalize_data:
            # 首次运行时初始化均值和标准差
            if self.data_mean is None:
                # 计算每个特征维度的均值和标准差
                self.data_mean = x.mean(dim=(0, 1), keepdim=True)
                self.data_std = x.std(dim=(0, 1), keepdim=True) + 1e-6  # 添加小值避免除零
            
            # 使用记录的均值和标准差归一化输入数据
            x = (x - self.data_mean) / self.data_std

        # LayerNorm直接作用于最后一个维度，无需permute
        x_norm = self.norm1(x)
        
        # 获取GRU编码特征
        _, h_end = self.gru(x_norm)
        h_end = h_end[-1, :, :] # Shape: (batch, hidden_size)
        
        # 对特征进行标准化
        h_norm = self.norm2(h_end)
        
        # 生成重建序列
        reconstructed = self.reconstructor(h_norm)
        reconstructed = reconstructed.view(x.shape[0], 10, -1)  # 重塑为原始序列形状
        
        # 如果进行了数据归一化，则需要将重建结果还原回原始尺度
        if self.normalize_data and self.data_mean is not None:
            reconstructed = reconstructed * self.data_std + self.data_mean
        
        # 生成分类结果（用于计算异常分数）
        classification = self.classifier(h_norm)
        
        return h_norm, reconstructed, classification, x_orig
    
    def compute_anomaly_score(self, x_orig, x_reconstructed, classification, h_norm=None):
        """计算异常分数"""
        # 1. 重建误差（MSE）- 使用归一化后的误差
        # 先将原始输入和重建结果归一化到相同尺度
        if self.normalize_data and self.data_mean is not None:
            x_norm = (x_orig - self.data_mean) / self.data_std
            recon_norm = (x_reconstructed - self.data_mean) / self.data_std
            # 计算归一化后的误差 - 改为L1损失(MAE)
            recon_error = F.l1_loss(recon_norm, x_norm, reduction='none').mean(dim=(1, 2))
        else:
            # 如果没有归一化，则直接计算误差 - 改为L1损失(MAE)
            recon_error = F.l1_loss(x_reconstructed, x_orig, reduction='none').mean(dim=(1, 2))
        
        # 2. 分类不确定性（softmax概率差异）- 现在完全不使用
        softmax_probs = F.softmax(classification, dim=1)
        classification_uncertainty = 1.0 - torch.max(softmax_probs, dim=1)[0]
        
        # 3. 添加边界检测结果(如果可用)
        boundary_scores = None
        if h_norm is not None:
            boundary_scores = self.boundary_detector(h_norm).squeeze(-1)
            
            # 组合得分：主要依赖重建误差，边界检测器辅助判断
            # 当边界检测器给出高分时，增加异常分数
            # 大幅降低边界检测器影响 (从0.5降至0.1)
            anomaly_score = recon_error * (1.0 + 0.1 * boundary_scores)
        else:
            # 如果没有提供h_norm，则只使用重建误差
            anomaly_score = recon_error
            
        return anomaly_score, boundary_scores
        
    def training_step(self, batch, batch_idx):
        x, y = batch
        y = y.long()
        
        # 获取特征和重建
        h_norm, reconstructed, classification, x_orig = self.forward(x)
        
        # 计算异常分数
        anomaly_score, boundary_scores = self.compute_anomaly_score(x_orig, reconstructed, classification, h_norm)
        
        # 计算重建损失
        recon_loss = F.l1_loss(reconstructed, x_orig)
        
        # 分类损失 - 但不使用类别权重
        class_loss = F.cross_entropy(classification, y)
        
        # 添加边界检测损失 - 直接监督边界检测器的输出
        boundary_targets = y.float()  # 0=正常, 1=异常
        if boundary_scores is not None:
            boundary_loss = F.binary_cross_entropy(boundary_scores, boundary_targets)
        else:
            boundary_loss = torch.tensor(0.0, device=x.device)
        
        # --- 【核心修改】暂时完全禁用虚警惩罚 ---
        # 先将 false_alarm_penalty 设为0，观察训练是否能稳定
        false_alarm_penalty = torch.tensor(0.0, device=x.device)
        
        # 总损失 - 重建损失为主，分类损失和边界损失为辅，暂时移除虚警惩罚
        loss = recon_loss + 0.1 * class_loss + 0.2 * boundary_loss # + false_alarm_penalty
        
        # 记录正常样本的异常分数（用于后续设置阈值）
        normal_scores = anomaly_score[y == 0].detach().cpu().numpy()
        if len(normal_scores) > 0:
            self.anomaly_scores_history.extend(normal_scores.tolist())
            
        # 预测
        pred = (anomaly_score > 0.5).long()  # 暂时使用固定阈值，后续动态调整
        accuracy = (pred == y).float().mean()
        tp = ((pred == 1) & (y == 1)).sum().float()
        recall = tp / ((y == 1).sum().float() + 1e-8)
        precision = tp / ((pred == 1).sum().float() + 1e-8)
        
        # 日志记录
        self.log('loss/train', loss, on_step=True, on_epoch=True, prog_bar=True, logger=True)
        self.log('recon_loss/train', recon_loss, on_step=False, on_epoch=True, prog_bar=False, logger=True)
        self.log('class_loss/train', class_loss, on_step=False, on_epoch=True, prog_bar=False, logger=True)
        self.log('boundary_loss/train', boundary_loss, on_step=False, on_epoch=True, prog_bar=False, logger=True)
        self.log('false_alarm_penalty', false_alarm_penalty, on_step=False, on_epoch=True, prog_bar=False, logger=True)
        self.log('accuracy/train', accuracy, on_step=False, on_epoch=True, prog_bar=False, logger=True)
        self.log('recall/train', recall, on_step=False, on_epoch=True, prog_bar=True, logger=True)
        self.log('precision/train', precision, on_step=False, on_epoch=True, prog_bar=False, logger=True)
        
        return loss

    def validation_step(self, batch, batch_index):
        x, y = batch
        y = y.long()
        
        # 获取特征和重建
        h_norm, reconstructed, classification, x_orig = self.forward(x)
        
        # 计算异常分数
        anomaly_score, _ = self.compute_anomaly_score(x_orig, reconstructed, classification, h_norm)
        
        # 计算重建损失
        recon_loss = F.l1_loss(reconstructed, x_orig)
        
        # 分类损失 - 移除类别权重
        class_loss = F.cross_entropy(classification, y)
        
        # 总损失
        loss = recon_loss + 0.1 * class_loss
        
        # 设置动态阈值 - 每个验证epoch结束设置一次
        if self.anomaly_threshold is None and len(self.anomaly_scores_history) > 0:
            # 使用训练期间记录的正常样本异常分数的95%分位点作为阈值
            self.anomaly_threshold = np.percentile(self.anomaly_scores_history, 95)
            self.log('anomaly_threshold', self.anomaly_threshold, prog_bar=False, logger=True)
            
        threshold = self.anomaly_threshold if self.anomaly_threshold is not None else 0.5
        pred = (anomaly_score > threshold).long()
        
        accuracy = (pred == y).float().mean()
        tp = ((pred == 1) & (y == 1)).sum().float()
        recall = tp / ((y == 1).sum().float() + 1e-8)
        precision = tp / ((pred == 1).sum().float() + 1e-8)
        
        # 日志记录
        self.log("loss/val", loss, prog_bar=True, logger=True)
        self.log("recon_loss/val", recon_loss, prog_bar=False, logger=True)
        self.log("class_loss/val", class_loss, prog_bar=False, logger=True)
        self.log("accuracy/val", accuracy, prog_bar=False, logger=True)
        self.log("recall/val", recall, prog_bar=True, logger=True)
        self.log("precision/val", precision, prog_bar=False, logger=True)
        
        return loss

    def on_train_epoch_end(self):
        # 每个训练epoch结束时更新异常阈值
        if len(self.anomaly_scores_history) > 100:  # 确保有足够的样本
            # 使用更稳健的方法计算阈值，避免极端值影响
            # 先排序并去除极端值
            sorted_scores = np.sort(self.anomaly_scores_history)
            # 去除最高的1%极端值
            valid_scores = sorted_scores[:int(len(sorted_scores) * 0.99)]
            
            # 双阈值设置：
            # 1. 主阈值 - 使用更保守的设置
            self.anomaly_threshold = np.percentile(valid_scores, 99.0)
            # 2. 次级阈值 - 相对较低，用于持续性异常检测
            self.secondary_threshold = np.percentile(valid_scores, 95.0)
            
            self.log('anomaly_threshold', self.anomaly_threshold, prog_bar=False, logger=True)
            self.log('secondary_threshold', self.secondary_threshold, prog_bar=False, logger=True)
            
            # 只保留最近10000个分数，避免内存占用过大
            if len(self.anomaly_scores_history) > 10000:
                self.anomaly_scores_history = self.anomaly_scores_history[-10000:]

    def predict_step(self, batch, batch_idx):
        x, y = batch
        h_norm, reconstructed, classification, x_orig = self.forward(x)
        anomaly_score, _ = self.compute_anomaly_score(x_orig, reconstructed, classification, h_norm)
        
        # 主阈值检测 - 用于即时异常
        threshold = self.anomaly_threshold if self.anomaly_threshold is not None else 0.5
        # 次级阈值 - 用于持续性异常
        secondary_threshold = self.secondary_threshold if self.secondary_threshold is not None else 0.3
        
        # 阈值系数 - 预测时进一步提高阈值
        threshold_factor = 1.0  # 更激进的阈值系数 (从2.0增加到2.5)
        
        # 1. 主阈值检测 - 严格阈值即时判定
        main_detection = (anomaly_score > threshold * threshold_factor).long()
        
        # 2. 持续性异常检测 - 如果连续多次超过次级阈值
        persistent_detection = torch.zeros_like(main_detection)
        
        # 简化：在评估环节我们不考虑持续性检测
        # 实际部署时可以启用下面的逻辑
        
        # 组合两种检测结果 - 任一为1则判定为异常
        pred = torch.max(main_detection, persistent_detection)
        
        return pred, y, anomaly_score

    def configure_optimizers(self):
        optimizer = torch.optim.Adam(self.parameters(), lr=0.0001)  # 降低学习率，从0.001降至0.0001
        return optimizer

def train_model_on_event(event_dir, pt_dataset_path, config):
    """
    为单个事件训练模型。
    这个函数现在被 run_event_training.py 调用。
    """
    model_output_dir = os.path.join(event_dir, "model")
    os.makedirs(model_output_dir, exist_ok=True)

    try:
        dataset = torch.load(pt_dataset_path, weights_only=False)
        if len(dataset) < 20:
             logging.warning(f"事件 {os.path.basename(event_dir)} 的数据集太小 ({len(dataset)}个样本)，无法进行训练。")
             return None

        # --- 动态确定输入维度 ---
        # 从数据集中获取一个样本来确定特征维度
        # dataset[0] 返回 (x, y)，x的形状是 (window, features)
        sample_x, _ = dataset[0]
        in_dim = sample_x.shape[1]
        logging.info(f"动态确定的模型输入维度: {in_dim}")

        # --- 新增：动态计算类别权重 ---
        class_weights = None
        try:
            # dataset.y 存储了所有的标签
            labels = np.array(dataset.y)
            # 确保标签在预期范围内，以避免bincount出错
            valid_labels = labels[labels < config['fore_out']]
            
            if len(valid_labels) > 0:
                class_counts = np.bincount(valid_labels, minlength=config['fore_out'])
                # 避免除以零
                if class_counts[1] > 0:
                    # 权重是反比关系，给样本少的类别更高的权重
                    weight_for_class_1 = class_counts[0] / class_counts[1]
                    class_weights = torch.tensor([1.0, weight_for_class_1], dtype=torch.float)
                    logging.info(f"动态计算的类别权重 (正常:异常): {class_weights.numpy().tolist()}")
                else:
                    logging.warning("数据集中没有异常样本 (label=1)，将不使用类别权重。")
            else:
                logging.warning("数据集中没有有效的标签，将不使用类别权重。")

        except Exception as e:
            logging.error(f"计算类别权重时出错: {e}", exc_info=True)
            # 出错时继续，不使用权重
            class_weights = None
        # --- 结束新增 ---

        train_loader, val_loader, _ = create_data_loaders(
            dataset, 
            batch_size=config['batch_size'], 
            val_split=config['val_split']
        )

        checkpoint_callback = ModelCheckpoint(
            dirpath=model_output_dir,
            filename='best_model',
            monitor='loss/val',
            save_top_k=1,
            mode='min',
        )

        model = mymodel(
            in_dim=in_dim, # <--- 使用动态确定的维度
            hid_dim=config['hid_dim'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            fore_out=config['fore_out'],
            for_n_layer=config['for_n_layer'],
            for_hid_dim=config['for_hid_dim'],
            focal_gamma=config['focal_gamma'], # <--- 传入权重
            class_weights=class_weights # <--- 传入权重
        )

        trainer = pl.Trainer(
            max_epochs=config['max_epochs'],
            callbacks=[checkpoint_callback],
            logger=False,
            enable_progress_bar=True,
            accelerator="gpu" if torch.cuda.is_available() else "cpu",
            devices=1,
            default_root_dir=model_output_dir
        )
        
        trainer.fit(model, train_loader, val_loader)
        
        best_model_path = checkpoint_callback.best_model_path
        logging.info(f"模型训练完成。最佳模型已保存至: {best_model_path}")
        return best_model_path

    except Exception as e:
        logging.error(f"为事件 {os.path.basename(event_dir)} 训练模型时出错: {e}", exc_info=True)
        return None

if __name__ == "__main__":
    # Example usage with a dummy dataset
    path = "/data/Project_Liu/BGP-Baseline-main/MSLSTM/sendXiaohui/dataset/sampling_1.0.pt"  
    
    # It's better to let PyTorch Lightning handle the DataLoaders if possible,
    # but we'll stick to the existing structure for now.
    
    # Ensure the dataset exists, otherwise create a dummy one for testing
    if not os.path.exists(path):
        print(f"Dataset not found at {path}. Creating a dummy dataset for testing purposes.")
        dummy_data = torch.randn(100, 10, 26) # 100 samples, window 10, 26 features
        dummy_labels = torch.randint(0, 2, (100,))
        dummy_dataset = SlidingWindowDataset(dummy_data, window=10, y=dummy_labels)
        torch.save(dummy_dataset, path)

    train_dataset = torch.load(path, weights_only=False)

    # create_data_loaders is assumed to return only train and val loaders
    train_loader, val_loader, test_loader = create_data_loaders(train_dataset, batch_size=10, val_split=0.2, shuffle=True)
    
    # Re-define logger and checkpoint_callback for standalone execution
    checkpoint_callback = ModelCheckpoint(
        monitor='loss/val',
        filename='sample-mnist-{epoch:02d}-{val_loss:.2f}',
        save_top_k=1,
        mode='min',
        save_last=True
    )
    logger = pl.loggers.TensorBoardLogger(save_dir='./', version='my_name12', name='lightning_logs')
    
    # Model initialization
    model = mymodel(in_dim=26, hid_dim=30, n_layers=2, dropout=0.2, fore_out=2, for_n_layer=2, for_hid_dim=128)

    trainer = pl.Trainer(
        max_epochs=10,
        callbacks=[checkpoint_callback],
        logger=logger,
        enable_progress_bar=True,
        accelerator="gpu" if torch.cuda.is_available() else "cpu",
        devices=1
    )

    trainer.fit(model, train_loader, val_loader)
