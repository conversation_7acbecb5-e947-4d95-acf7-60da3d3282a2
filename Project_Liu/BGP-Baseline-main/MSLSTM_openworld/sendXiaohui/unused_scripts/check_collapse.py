#!/usr/bin/env python3
import os
import torch
import numpy as np
import logging
import json
import argparse
from classification import mymodel

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_model(model_path, device="cuda" if torch.cuda.is_available() else "cpu"):
    """加载模型"""
    try:
        logger.info(f"尝试加载模型: {model_path}")
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        
        # 从检查点获取模型参数
        in_dim = 26  # 默认特征维度
        hid_dim = 30  # 默认隐藏层维度
        n_layers = 2  # 默认层数
        dropout = 0.2  # 默认dropout
        
        # 如果检查点中包含这些参数，则使用检查点中的值
        if 'hyper_parameters' in checkpoint:
            hp = checkpoint['hyper_parameters']
            if 'in_dim' in hp:
                in_dim = hp['in_dim']
            if 'hid_dim' in hp:
                hid_dim = hp['hid_dim']
            if 'n_layers' in hp:
                n_layers = hp['n_layers']
            if 'dropout' in hp:
                dropout = hp['dropout']
        
        # 创建模型实例
        model = mymodel(in_dim=in_dim, hid_dim=hid_dim, n_layers=n_layers, dropout=dropout)
        
        # 加载模型状态
        model.load_state_dict(checkpoint['state_dict'], strict=False)
        
        # 检查中心点是否正确加载
        state_dict = checkpoint['state_dict']
        if 'center' in state_dict:
            center_value = state_dict['center']
            if isinstance(center_value, torch.Tensor):
                if center_value.numel() > 0:
                    model.center = center_value.to(device)
                    logger.info(f"从检查点加载中心点: {model.center.shape}")
        
        # 检查阈值是否正确加载
        if hasattr(model, 'anomaly_threshold') and model.anomaly_threshold is None:
            if 'hyper_parameters' in checkpoint and 'anomaly_threshold' in checkpoint['hyper_parameters']:
                model.anomaly_threshold = checkpoint['hyper_parameters']['anomaly_threshold']
                logger.info(f"从检查点超参数加载阈值: {model.anomaly_threshold}")
            elif 'anomaly_threshold' in state_dict:
                model.anomaly_threshold = state_dict['anomaly_threshold']
                logger.info(f"从检查点状态字典加载阈值: {model.anomaly_threshold}")
        
        model.to(device)
        model.eval()  # 设置为评估模式
        logger.info(f"成功加载模型: {model_path}")
        return model, device
    
    except Exception as e:
        logger.error(f"加载模型时出错: {e}")
        return None, device

def check_model_collapse(model, device):
    """检查模型是否存在坍缩现象"""
    # 创建10个随机样本，每个样本包含10个时间步，26个特征
    batch_size = 10
    seq_len = 10
    feat_dim = 26
    
    # 创建两批不同的随机输入
    x1 = torch.rand(batch_size, seq_len, feat_dim).to(device)
    x2 = torch.rand(batch_size, seq_len, feat_dim).to(device)
    
    # 确保x1和x2不同
    diff = torch.sum((x1 - x2) ** 2).item()
    logger.info(f"输入差异 (x1 vs x2): {diff:.6f}")
    
    # 获取模型输出
    with torch.no_grad():
        outputs1 = model(x1)
        outputs2 = model(x2)
    
    # 计算输出差异
    output_diff = torch.sum((outputs1 - outputs2) ** 2).item()
    logger.info(f"输出差异 (outputs1 vs outputs2): {output_diff:.6f}")
    
    # 计算到中心点的距离
    distances1 = torch.sum((outputs1 - model.center) ** 2, dim=1)
    distances2 = torch.sum((outputs2 - model.center) ** 2, dim=1)
    
    # 检查距离是否全部相同
    dist_diff = torch.sum((distances1 - distances2) ** 2).item()
    logger.info(f"距离差异 (distances1 vs distances2): {dist_diff:.6f}")
    
    # 计算每个批次内部的距离方差
    dist1_var = torch.var(distances1).item()
    dist2_var = torch.var(distances2).item()
    logger.info(f"批次1内部距离方差: {dist1_var:.6f}")
    logger.info(f"批次2内部距离方差: {dist2_var:.6f}")
    
    # 判断是否存在坍缩现象
    if output_diff < 1e-6 or dist_diff < 1e-6:
        logger.warning("检测到模型坍缩现象！不同输入产生相同或几乎相同的输出。")
        return True
    else:
        logger.info("模型未出现坍缩现象，不同输入产生不同的输出。")
        return False

def main():
    parser = argparse.ArgumentParser(description="检查模型是否存在坍缩现象")
    parser.add_argument("--model", type=str, required=True, help="模型文件路径")
    args = parser.parse_args()
    
    # 加载模型
    model, device = load_model(args.model)
    if model is None:
        return
    
    # 检查模型是否存在坍缩现象
    is_collapsed = check_model_collapse(model, device)
    
    # 输出最终结果
    if is_collapsed:
        logger.error("模型存在坍缩现象，需要修复！")
    else:
        logger.info("模型正常，未检测到坍缩现象。")

if __name__ == "__main__":
    main() 