#!/usr/bin/env python3
import os
import json
import subprocess
import logging

from run_event_training import evaluate_event_model, ensure_event_prepared, EXPECTED_DIM, CONFIG, get_dataset_path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_DIR = "/data/data/anomaly-event-routedata"
SCRIPT_DIR = os.path.dirname(__file__)
MODELS_DIR = os.path.join(SCRIPT_DIR, "models")
SUMMARY_CSV = os.path.join(SCRIPT_DIR, "metrics_summary_eval_only.csv")

# 扩展表头以包含新的阈值信息
header = "事件,所属类型,模型,总报警次数,虚警次数,是否检测到异常,F1分数,召回率,精确率,使用阈值,原始阈值,阈值系数\n"
with open(SUMMARY_CSV, "w") as f:
    f.write(header)

# 按类型分组事件
all_events = [os.path.join(BASE_DIR, d) for d in os.listdir(BASE_DIR) if os.path.isdir(os.path.join(BASE_DIR, d))]

events_by_type = {}
for e in all_events:
    t = os.path.basename(e).split("-")[0]
    events_by_type.setdefault(t, []).append(e)

for evt_type, evt_list in events_by_type.items():
    model_path = os.path.join(MODELS_DIR, f"{evt_type}_poolA", "best_model.ckpt")
    if not os.path.exists(model_path):
        logger.warning(f"模型 {model_path} 不存在，跳过类型 {evt_type}")
        continue

    logger.info(f"使用模型 {model_path} 评估 {evt_type} 的 {len(evt_list)} 个事件…")

    for evt in evt_list:
        # 准备 fea.json 与 dataset.pt（仅确保 fea.json）
        ensure_event_prepared(evt, os.path.join(SCRIPT_DIR, "dataset"), CONFIG)

        metrics = evaluate_event_model(evt, model_path)
        with open(SUMMARY_CSV, "a") as f:
            ev_name = os.path.basename(evt)
            if metrics:
                # 添加阈值信息(如果有)
                threshold = metrics.get("使用阈值", "N/A")
                raw_threshold = metrics.get("原始阈值", "N/A")
                threshold_factor = metrics.get("阈值系数", "N/A")
                
                f.write(f"{ev_name},{evt_type},poolA,{metrics['总报警次数']},{metrics['虚警次数']},{metrics['是否检测到异常']},{metrics['F1分数']},{metrics['召回率']},{metrics['精确率']},{threshold},{raw_threshold},{threshold_factor}\n")
            else:
                f.write(f"{ev_name},{evt_type},poolA,FAIL,FAIL,FAIL,0.0,0.0,0.0,N/A,N/A,N/A\n")

logger.info("评估完成，结果保存至 %s", SUMMARY_CSV) 