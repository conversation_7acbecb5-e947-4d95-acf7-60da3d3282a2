import numpy as np
import os
import pickle
import pywt
from sklearn.preprocessing import StandardScaler
import torch
from torch.utils.data import DataLoader, Dataset, SubsetRandomSampler
import time
from collections import Counter
import json
import sys
import logging

# 初始化 logger
logger = logging.getLogger(__name__)

np.set_printoptions(threshold=sys.maxsize)

def Multi_Scale_Wavelet(trainX, level, is_multi=True, wave_type='db1'):
    
    temp = [[] for i in range(level)]
    N = trainX.shape[0]
    if (is_multi == True) and (level > 1):
        for i in range(level):

            x = []
            for _feature in range(len(trainX[0])):
                coeffs = pywt.wavedec(trainX[:,_feature], wave_type, level=level)
                current_level = level  - i
                x.append(coeffs[i+1])

            temp[current_level - 1].extend(np.transpose(np.array(x)))

    else:
        for tab in range(level):
            current_level = level - tab
            temp[current_level - 1].extend(trainX)

    return  np.array(temp), trainX

def Multi_Scale_Wavelet0(trainX, level, is_multi=True, wave_type='db1'):
    temp = [[] for i in range(level)]
    N = trainX.shape[0]
    if (is_multi == True) and (level > 1):
        for i in range(level):
            x = []
            for _feature in range(len(trainX[0])):
                coeffs = pywt.wavedec(trainX[:,_feature], wave_type, level=level)
                current_level = level  - i
                for j in range(i+1,level+1):
                    coeffs[j] = None
                _rec = pywt.waverec(coeffs, wave_type)
                x.append(_rec[:N])

            temp[current_level - 1].extend(np.transpose(np.array(x)))

    else:
        for tab in range(level):
            current_level = level - tab
            temp[current_level - 1].extend(trainX)
    # print("ALA")
    print((np.array(temp)).shape)

    return  np.array(temp), trainX

class SlidingWindowDataset(Dataset):
    def __init__(self, data, window, y, target_dim=None, horizon=0):
        super(SlidingWindowDataset, self).__init__()
        self.data = data
        self.window = window
        self.target_dim = target_dim
        self.horizon = horizon
        self.y = y

    # def __getitem__(self, index):
    #     print(type(index), type(self.window))  # 检查类型
    #     #str int
    #     print(index)
    #     x = self.data[index : index + self.window]

    #     y = self.y[index + self.window + self.horizon]
    #     return x, y

  
    def __getitem__(self, index):
        # ---- 移除调试打印 ----

        # 跳过无效的 index
        if not isinstance(index, int):
             # 当 Lightning 保存/加载检查点时，它可能会传递一个字典
             # 我们应该忽略这种情况
            return torch.zeros(self.window, self.data.shape[-1]), torch.tensor(0)


        # 获取数据切片
        x = self.data[index : index + self.window]

        # 获取目标值
        y = self.y[index + self.window + self.horizon]

        return x, y


    def __len__(self):
        return len(self.data) - self.window

    @staticmethod
    def from_json(feature_file, window=10, horizon=0):
        """
        从特征文件创建滑动窗口数据集
        
        Args:
            feature_file (str): 特征文件路径
            window (int): 窗口大小
            horizon (int): 预测时间跨度
            
        Returns:
            SlidingWindowDataset: 创建的数据集
        """
        # 使用clean_data函数处理特征文件
        labels, features = clean_data(feature_file)
        
        # 确保数据长度足够
        if len(features) <= window:
            raise ValueError(f"数据点 ({len(features)}) 少于或等于窗口大小 ({window})，无法创建数据集")
        
        # 转换为torch张量
        data_tensor = torch.tensor(features, dtype=torch.float32)
        labels_tensor = torch.tensor(labels, dtype=torch.long)
        
        # 创建并返回数据集
        return SlidingWindowDataset(data_tensor, window, labels_tensor, horizon=horizon)


def create_data_loaders(train_dataset, batch_size, val_split=0.2, shuffle=True, test_dataset=None):
    train_loader, val_loader, test_loader = None, None, None
    if val_split == 0.0:
        print(f"train_size: {len(train_dataset)}")
        train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle)
    else:
        dataset_size = len(train_dataset)
        indices = list(range(dataset_size))
        split = int(np.floor(val_split * dataset_size))
        if shuffle:
            np.random.shuffle(indices)
        train_indices, val_indices = indices[split:], indices[:split]

        train_sampler = SubsetRandomSampler(train_indices)
        valid_sampler = SubsetRandomSampler(val_indices)

        train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, sampler=train_sampler, drop_last=True)
        val_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, sampler=valid_sampler, drop_last=True)

        print(f"train_size: {len(train_indices)}")
        print(f"validation_size: {len(val_indices)}")

    if test_dataset is not None:
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        print(f"test_size: {len(test_dataset)}")
    return train_loader, val_loader, test_loader


def standarize(data):
    # print(data[:10])
    # print(data.shape)
    # exit()
    res = (data - data.mean(axis=0)) / data.std(axis=0) 
    # print("std:",data.std(axis=0))
    
    # 使用 nan_to_num 将因除以零产生的 NaN/inf 替换为 0
    res = np.nan_to_num(res, nan=0.0, posinf=0.0, neginf=0.0)
    
    # res = data / np.sqrt(np.sum(data**2))
    # print("res:", res)
    return res 


#清洗数据，确保json文件中只用0 和 1 字符串，没有整型
def clean_data(feature_file):
    """
    从 fea.json 文件中加载数据，清洗并分离标签和特征。
    这个函数现在能正确处理作为JSON对象（字典）或列表存储的数据。
    """
    with open(feature_file, 'r') as f:
        data = json.load(f)

    res_fea = []
    res_label = []
    
    # FIX: 检查数据是字典还是列表，并相应地准备迭代
    if isinstance(data, dict):
        # 按键的数字顺序排序，以保持原始顺序
        items_to_process = [v for k, v in sorted(data.items(), key=lambda k: int(k[0]))]
    elif isinstance(data, list):
        items_to_process = data
    else:
        logger.error(f"不支持的数据格式: {type(data)} in file {feature_file}")
        return np.array([]), np.array([])

    for item in items_to_process:
        try:
            # 确保证据是一个字典并且包含'label'
            if not isinstance(item, dict) or 'label' not in item:
                continue

            # 分离标签
            label = int(item.pop('label'))
            res_label.append(label)

            # 剩下的都是特征（排除 'index' 字段）
            filtered_items = [(k, v) for k, v in item.items() if k != 'index']
            # 按键排序以确保每次特征顺序都一致
            sorted_features = sorted(filtered_items, key=lambda x: int(x[0]) if x[0].isdigit() else float('inf'))
            feature_values = [v for k, v in sorted_features]
            res_fea.append(feature_values)

        except (ValueError, TypeError) as e:
            # 跳过无法处理的条目
            print(f"警告: 跳过一个无效条目，错误: {e}, 条目: {item}")
            continue
        
    if not res_fea:
        return np.array([]), np.array([])

    return np.array(res_label), np.array(res_fea, dtype=np.float32)

def prepare_data_and_get_features(event_dir, output_pt_path, config):
    """
    为单个事件准备训练数据，从 fea.json 生成 dataset.pt。
    这个函数现在被 run_event_training.py 调用。
    """
    json_path = os.path.join(event_dir, "features", "fea.json")
    if not os.path.exists(json_path):
        logging.error(f"fea.json 文件不存在: {json_path}")
        return False

    try:
        labels, features = clean_data(json_path)
        if features.shape[0] == 0:
            logging.warning(f"事件 {os.path.basename(event_dir)} 清洗后没有有效数据。")
            return False

        wavelet_features, _ = Multi_Scale_Wavelet0(features, level=config['level'])
        res_0 = wavelet_features[0]

        # -------- 新增：统一特征维度为 26 --------
        EXPECTED_DIM = 26
        cur_dim = res_0.shape[1]
        if cur_dim < EXPECTED_DIM:
            pad = np.zeros((res_0.shape[0], EXPECTED_DIM - cur_dim), dtype=res_0.dtype)
            res_0 = np.concatenate([res_0, pad], axis=1)
            logging.info(f"特征维度 {cur_dim} < 26，已补零至 26")
        elif cur_dim > EXPECTED_DIM:
            res_0 = res_0[:, :EXPECTED_DIM]
            logging.info(f"特征维度 {cur_dim} > 26，已截断至 26")

        logging.info(f"诊断: 用于训练的特征维度 (res_0.shape): {res_0.shape}")
        
        dataset = SlidingWindowDataset(res_0, window=config['window_size'], y=labels)

        torch.save(dataset, output_pt_path)
        logging.info(f"成功生成 Pytorch 数据集: {output_pt_path}")
        return True

    except Exception as e:
        logging.error(f"为事件 {os.path.basename(event_dir)} 准备数据时出错: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    # 修改为从原始数据目录读取fea.json
    sampling_file = "/data/data/anomaly-event-routedata/hijack-20050507-Google_hijack/features/fea.json"
    
    with open(sampling_file, 'r') as f:
        data = json.load(f)
    
    # 使用 clean_data 函数清洗数据
    data, y = clean_data(sampling_file)

    # 数据是一个二维数组，y 是标签
    print("Cleaned data shape:", data.shape)
    print("Cleaned labels shape:", y.shape)

    # 接下来的处理和转换操作
    start_time = time.time()
    (res_0), _ = Multi_Scale_Wavelet0(data[:,:], level=2)[0]
    end_time = time.time()
    print("Used time:", end_time - start_time)

    # 创建数据集
    dataset = SlidingWindowDataset(res_0, window=10, y=y)

    # 保存数据集
    torch.save(dataset, '/data/Project_Liu/BGP-Baseline-main/MSLSTM/sendXiaohui/dataset/sampling_1.0.pt')
 