#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import torch
import numpy as np
import pandas as pd
import joblib
import sys
import logging
import glob
import re
from torch.utils.data import DataLoader

# --- 动态添加路径以导入外部模块 ---
script_dir = os.path.dirname(os.path.abspath(__file__))

from classification import GRUClassifier
from feature_extraction import SlidingWindowDataset

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- 辅助函数 (从 compare_alarms.py 移植) ---
def get_date_from_filename(filename):
    """从文件名中提取 YYYY-MM-DD 格式的日期。"""
    match = re.search(r'(\d{4}-\d{2}-\d{2})', filename)
    return match.group(1) if match else None

def format_timestamp(date_str, minute_of_day):
    """将日期和天内分钟数格式化为 'YYYY-MM-DD HH:MM' 字符串。"""
    hour = int(minute_of_day) // 60
    minute = int(minute_of_day) % 60
    return f"{date_str} {hour:02d}:{minute:02d}"

def main():
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")

    # --- 配置 ---
    # !! 最终修正：确保独立评估时也使用经过小波变换的新数据源 !!
    PREDICTION_DATA_DIR = '/data/five_month/features_wavelet'
    MODEL_PATH = os.path.join(script_dir, 'models', 'general_model.ckpt')
    SCALER_PATH = '/data/Project_Liu/BGP-Baseline-main/ISP-Operated/output/scaler.pkl'
    RESULTS_FILE_PATH = os.path.join(script_dir, 'prediction_results_general_model.txt')
    WINDOW_SIZE = 10 

    # --- 检查并加载必要的文件 ---
    if not os.path.exists(MODEL_PATH):
        logger.error(f"Fatal: General model not found at {MODEL_PATH}.")
        sys.exit(1)
    logger.info(f"Found model checkpoint: {MODEL_PATH}")

    if not os.path.exists(SCALER_PATH):
        logger.error(f"Fatal: Scaler file not found at {SCALER_PATH}.")
        sys.exit(1)
    logger.info(f"Loading scaler from: {SCALER_PATH}")
    scaler = joblib.load(SCALER_PATH)

    # --- 加载预测数据文件 ---
    prediction_files = sorted(glob.glob(os.path.join(PREDICTION_DATA_DIR, "*.csv")))
    if not prediction_files:
        logger.error(f"Fatal: No '.csv' files found in {PREDICTION_DATA_DIR}")
        sys.exit(1)
    logger.info(f"Found {len(prediction_files)} prediction files to process.")

    # --- 加载模型 ---
    try:
        trained_model = GRUClassifier.load_from_checkpoint(
            checkpoint_path=MODEL_PATH,
            map_location=device
        ).eval()
        logger.info("Successfully loaded trained general model!")
    except Exception as e:
        logger.error(f"Fatal: Error loading model from checkpoint: {e}", exc_info=True)
        sys.exit(1)

    # --- 统一预测流程 (恢复批次处理) ---
    logger.info("\n--- Starting Prediction with General Model ---")
    
    results_to_save = [
        "MSLSTM_openworld Model Prediction Report",
        "=========================================="
    ]
    total_running_time = 0
    total_unique_alarms = set()

    batches = [
        ("Days 1-5", prediction_files[0:5]),
        ("Days 6-10", prediction_files[5:10]),
        ("Days 11-15", prediction_files[10:15]),
        ("Days 16-20", prediction_files[15:20]),
        ("Days 21-25", prediction_files[20:25]),
        ("Days 26-31", prediction_files[25:31])
    ]

    for batch_name, files_in_batch in batches:
        batch_header = f"\nProcessing batch: '{batch_name}'"
        logger.info(batch_header)
        results_to_save.append(batch_header)
        
        batch_alarm_timestamps = set()
        batch_start_time = time.time()
        
        for i, file_path in enumerate(files_in_batch):
            filename = os.path.basename(file_path)
            date_str = get_date_from_filename(filename)
            if not date_str:
                logger.warning(f"Could not extract date from {filename}. Skipping.")
                continue
                
            logger.info(f"  - Predicting on file ({i+1}/{len(files_in_batch)}): {filename}")
            
            try:
                df = pd.read_csv(file_path)
                if df.empty:
                    logger.warning(f"    - Warning: File {filename} is empty. Skipping.")
                    continue

                timestamps = df['index'].values
                features = df.iloc[:, 1:].values.astype(np.float32)

                if features.shape[0] < WINDOW_SIZE:
                    continue

                scaled_features = scaler.transform(features)
                dummy_y = np.zeros(len(scaled_features))
                dataset = SlidingWindowDataset(data=scaled_features, window=WINDOW_SIZE, y=dummy_y)
                
                if len(dataset) == 0:
                    continue

                loader = DataLoader(dataset, batch_size=128, shuffle=False)
                
                with torch.no_grad():
                    for j, (batch_x, _) in enumerate(loader):
                        batch_x = batch_x.to(device)
                        probs = trained_model(batch_x) 
                        preds = probs.argmax(dim=-1)
                        
                        alarm_indices = (preds == 1).nonzero(as_tuple=True)[0]
                        for alarm_idx in alarm_indices:
                            real_idx = j * 128 + alarm_idx.item() + WINDOW_SIZE - 1
                            if real_idx < len(timestamps):
                                unique_ts = format_timestamp(date_str, timestamps[real_idx])
                                batch_alarm_timestamps.add(unique_ts)

            except Exception as e:
                logger.error(f"    - Error processing file {filename}: {e}", exc_info=True)

        batch_end_time = time.time()
        batch_running_time = batch_end_time - batch_start_time
        total_running_time += batch_running_time
        
        total_unique_alarms.update(batch_alarm_timestamps)
        
        result_line = f"--- Result for '{batch_name}': {len(batch_alarm_timestamps)} unique alarms detected. (Time: {batch_running_time:.2f}s) ---"
        logger.info(result_line)
        results_to_save.append(result_line)

    # Note: total_running_time is now the sum of batch times
    completion_line = f"\nPrediction complete. Total processing time: {total_running_time:.2f} seconds."
    total_alarms_line = f"Total unique alarms for all days: {len(total_unique_alarms)}"
    
    logger.info(completion_line)
    logger.info(total_alarms_line)
    results_to_save.append(completion_line)
    results_to_save.append(total_alarms_line)

    # --- 将结果保存到文件 ---
    try:
        with open(RESULTS_FILE_PATH, 'w') as f:
            for line in results_to_save:
                f.write(line + '\n')
        logger.info(f"\nPrediction results successfully saved to {RESULTS_FILE_PATH}")
    except Exception as e:
        logger.error(f"\nError saving results to file: {e}", exc_info=True)

if __name__ == '__main__':
    main()