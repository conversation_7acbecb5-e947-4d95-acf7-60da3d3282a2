{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Import python packages"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:28:20.117880Z", "start_time": "2019-12-28T17:28:18.438334Z"}}, "outputs": [], "source": ["from collections import defaultdict\n", "import pickle\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from collections import Counter\n", "import random\n", "\n", "np.random.seed(7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Define parameters and load bgp_routes and ToR datasets"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:28:43.899211Z", "start_time": "2019-12-28T17:28:27.494409Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3669655,) (3669655,)\n", "(580762, 2) (580762,)\n"]}], "source": ["ToR_MODEL_NAME = \"CAIDA_s1_ToR_Classification_NDToR_x_training_core\"\n", "\n", "TEST_SIZE = 0.2\n", "TOR_LABELS_DICT = {'P2P':0, 'C2P': 1,'P2C': 2}\n", "class_names = ['P2P', 'C2P', 'P2C']\n", "DATA_PATH = '../../Data/'\n", "MODELS_PATH = '../../Models/'\n", "RESULTS_PATH = '../../Results/'\n", "\n", "\n", "bgp_routes = np.load(DATA_PATH + \"bgp_routes_dataset.npy\")\n", "bgp_routes_labels = np.load(DATA_PATH + \"bgp_routes_labels.npy\")\n", "print(bgp_routes.shape, bgp_routes_labels.shape)\n", "\n", "DATA = \"caida_s1_tor\"\n", "tor_dataset = np.load(DATA_PATH + DATA + \"_dataset.npy\")\n", "tor_labels = np.load(DATA_PATH + DATA + \"_labels.npy\")\n", "\n", "print(tor_dataset.shape, tor_labels.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate training and test sets\n", "## Shauffle dataset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:28:44.101826Z", "start_time": "2019-12-28T17:28:43.956771Z"}}, "outputs": [], "source": ["from sklearn.utils import shuffle\n", "dataset, labels = shuffle(tor_dataset, tor_labels, random_state=7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate a balanced dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T13:08:27.960627Z", "start_time": "2018-11-17T13:08:27.890678Z"}}, "outputs": [], "source": ["# def generate_balanced_dataset(dataset, labels, labels_set):\n", "#     sets_dict = dict()\n", "#     for label in labels_set:\n", "#         sets_dict[label] = np.asarray([np.asarray(dataset[i]) for i in range(len(dataset)) if labels[i] == label])\n", "    \n", "#     min_set_len = min([len(label_set) for label_set in sets_dict.values()])\n", "    \n", "#     for label, label_set in sets_dict.items():\n", "#         sets_dict[label] = label_set[np.random.choice(label_set.shape[0], min_set_len, replace=False)]\n", "    \n", "#     dataset = np.concatenate((sets_dict.values()))\n", "#     labels = []\n", "#     for label, label_set in sets_dict.items():\n", "#         labels += [label]*len(label_set)\n", "#         print label, len(label_set)\n", "#     labels = np.asarray(labels)\n", "#     return shuffle(dataset, labels, random_state=7)\n", "\n", "# dataset, labels = generate_balanced_dataset(dataset, labels, (0,1,3))\n", "# print dataset.shape, labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train Test Split"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:28:44.335462Z", "start_time": "2019-12-28T17:28:44.160729Z"}}, "outputs": [], "source": ["x_training, x_test, y_training, y_test = train_test_split(dataset, labels, test_size=TEST_SIZE)\n", "\n", "del dataset, labels"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:28:45.027426Z", "start_time": "2019-12-28T17:28:44.424443Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(464609, 2) (464609,)\n", "(116153, 2) (116153,)\n", "0.7999989668745545\n", "Counter({0: 275382, 1: 94716, 2: 94511}) Counter({0: 68570, 2: 23894, 1: 23689})\n"]}], "source": ["print(x_training.shape, y_training.shape)\n", "print(x_test.shape, y_test.shape)\n", "\n", "print(1.0*len(x_training)/(len(x_test)+len(x_training)))\n", "\n", "from collections import Counter\n", "training_c = Counter(y_training)\n", "test_c = Counter(y_test)\n", "print(training_c, test_c)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:28:45.114353Z", "start_time": "2019-12-28T17:28:45.097817Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 20.386174180870366\n", "0 59.27177476114324\n", "2 20.342051057986392\n", "0 59.03420488493625\n", "2 20.571143233493753\n", "1 20.39465188157\n"]}], "source": ["for k,v in training_c.items():\n", "    print(k, 100.0*v/len(x_training))\n", "print\n", "for k,v in test_c.items():\n", "    print(k, 100.0*v/len(x_test))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Run ND-ToR Algo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load k_shell"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:28:45.241978Z", "start_time": "2019-12-28T17:28:45.176096Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["62523\n"]}], "source": ["with open(MODELS_PATH + 's1_k_shell.pickle', 'rb') as handle:\n", "    k_shell = pickle.load(handle)\n", "print(len(k_shell))"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["## Load CAIDA results and create CP-Core with NetworkX"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:09:02.332513Z", "start_time": "2019-12-28T16:08:58.992148Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["343952\n"]}], "source": ["caida_p2p = [tor for i, tor in enumerate(tor_dataset) if tor_labels[i] == 0]\n", "\n", "TIER1 = [\"174\", \"209\", \"286\", \"701\", \"1239\", \"1299\", \"2828\", \"2914\", \"3257\", \"3320\", \"3356\",\n", "         \"3491\", \"5511\", \"6453\", \"6461\", \"6762\", \"6830\", \"7018\", \"12956\"]\n", "# with open(caida_path, \"r\") as f:\n", "#     for line in f:\n", "#         as0, as1, label = [int(part) for part in line.split()[0].split('|')]\n", "#         if label == 0 and as0 in ASN_index_map and as1 in ASN_index_map:\n", "#             caida_p2p.append((ASN_index_map[as0], ASN_index_map[as1]))\n", "caida_p2p = [tor for i, tor in enumerate(tor_dataset) if tor_labels[i] == 0]\n", "print(len(caida_p2p))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:09:54.311120Z", "start_time": "2019-12-28T16:09:41.690522Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["12254\n", "12254 12254\n"]}], "source": ["import networkx as nx\n", "\n", "verteces = set()\n", "for pair in caida_p2p:\n", "    verteces.add(pair[0])\n", "    verteces.add(pair[1])\n", "print(len(verteces))\n", "\n", "vertex2ind = dict()\n", "ind2vertex = dict()\n", "for i, vertex in enumerate(verteces):\n", "    vertex2ind[vertex] = i\n", "    ind2vertex[i] = vertex\n", "\n", "print(len(vertex2ind), len(ind2vertex))\n", "\n", "g = nx.DiGraph()\n", "g.add_edges_from([(vertex2ind[pair[0]], vertex2ind[pair[1]]) for pair in caida_p2p])\n", "g.add_edges_from([(vertex2ind[pair[1]], vertex2ind[pair[0]]) for pair in caida_p2p])\n", "\n", "SCCs = [c for c in sorted(nx.strongly_connected_components(g),key=len, reverse=True)]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:10:33.810535Z", "start_time": "2019-12-28T16:10:33.771861Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["114\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "0\n", "11889\n", "11889\n"]}], "source": ["print(len(SCCs))\n", "for i, scc in enumerate(SCCs):\n", "    for as_tier1 in TIER1:\n", "        if vertex2ind[as_tier1] not in scc:\n", "            break\n", "        print(i)\n", "print(len(SCCs[0]))\n", "scc = SCCs[0]\n", "scc = set([ind2vertex[ind] for ind in scc])\n", "print(len(scc))"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T16:12:51.275044Z", "start_time": "2019-12-28T16:12:46.031280Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["343446\n", "numbr of edges 171723.0\n"]}], "source": ["cp_core = set()\n", "for pair in caida_p2p:\n", "    if pair[0] in scc and pair[1] in scc:\n", "        cp_core.add(tuple(pair))\n", "        cp_core.add(tuple((pair[1], pair[0])))\n", "print(len(cp_core))\n", "print(\"numbr of edges \" + str(len(cp_core)/2))"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["## Load CAIDA results and create CP-Core"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:25:50.178920Z", "start_time": "2019-01-05T10:25:49.237305Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["192088\n"]}], "source": ["caida_path = 'CAIDA_20181001.as-rel_cleaned.txt'\n", "caida_p2p = list()\n", "\n", "TIER1 = [7018, 209, 3356, 3549, 4323, 3320, 3257, 286, 6830, 2914, 5511, 3491, 1239, 6453, 6762, 12956, 701, 702, 703, 2828, 6461]\n", "TIER1 = [ASN_index_map[asn] for asn in TIER1]\n", "\n", "with open(caida_path, \"r\") as f:\n", "    for line in f:\n", "        as0, as1, label = [int(part) for part in line.split()[0].split('|')]\n", "        if label == 0 and as0 in ASN_index_map and as1 in ASN_index_map:\n", "            caida_p2p.append((ASN_index_map[as0], ASN_index_map[as1]))\n", "print(len(caida_p2p))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:32:15.437000Z", "start_time": "2019-01-05T10:32:15.379704Z"}, "code_folding": [3], "hidden": true}, "outputs": [], "source": ["#This class represents a directed graph using adjacency list representation \n", "class Graph: \n", "   \n", "    def __init__(self,vertices): \n", "        self.V = vertices #No. of vertices \n", "        self.graph = defaultdict(list) # default dictionary to store graph\n", "        self.scc_list = []\n", "   \n", "    # function to add an edge to graph \n", "    def addEdge(self,u,v): \n", "        self.graph[u].append(v) \n", "   \n", "    # A function used by DFS \n", "    def DFSUtil(self,v,visited): \n", "        # Mark the current node as visited and print it \n", "        visited[v]= True\n", "#         print v,\n", "        self.scc_list[-1].append(v)\n", "        #Recur for all the vertices adjacent to this vertex \n", "        for i in self.graph[v]: \n", "            if visited[i]==False: \n", "                self.D<PERSON><PERSON><PERSON>(i,visited) \n", "  \n", "  \n", "    def fillOrder(self,v,visited, stack): \n", "        # Mark the current node as visited  \n", "        visited[v]= True\n", "        #Recur for all the vertices adjacent to this vertex \n", "        for i in self.graph[v]: \n", "            if visited[i]==False: \n", "                self.fillOrder(i, visited, stack) \n", "        stack = stack.append(v) \n", "      \n", "  \n", "    # Function that returns reverse (or transpose) of this graph \n", "    def get<PERSON><PERSON><PERSON><PERSON>(self): \n", "        g = Graph(self.V) \n", "  \n", "        # Recur for all the vertices adjacent to this vertex \n", "        for i in self.graph: \n", "            for j in self.graph[i]: \n", "                g.add<PERSON>dge(j,i) \n", "        return g \n", "  \n", "   \n", "   \n", "    # The main function that finds and prints all strongly \n", "    # connected components \n", "    def printSCCs(self): \n", "          \n", "        stack = [] \n", "        # Mark all the vertices as not visited (For first DFS) \n", "        visited =[False]*(self.V) \n", "        # Fill vertices in stack according to their finishing \n", "        # times \n", "        for i in range(self.V): \n", "            if visited[i]==False: \n", "                self.fillOrder(i, visited, stack) \n", "  \n", "        # Create a reversed graph \n", "        gr = self.getTranspose() \n", "          \n", "        # Mark all the vertices as not visited (For second DFS) \n", "        visited =[False]*(self.V) \n", "  \n", "        # Now process all vertices in order defined by <PERSON><PERSON> \n", "        while stack: \n", "            i = stack.pop() \n", "            if visited[i]==False:\n", "                gr.scc_list.append([])\n", "                gr.<PERSON><PERSON><PERSON><PERSON>(i, visited) \n", "#                 print\"\"\n", "        \n", "        scc = gr.scc_list\n", "        return scc\n", "\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:45:11.810632Z", "start_time": "2019-01-05T10:45:11.347684Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["13083\n", "(13083, 13083)\n"]}], "source": ["verteces = set()\n", "for pair in caida_p2p:\n", "    verteces.add(pair[0])\n", "    verteces.add(pair[1])\n", "print(len(verteces))\n", "\n", "vertex2ind = dict()\n", "ind2vertex = dict()\n", "for i, vertex in enumerate(verteces):\n", "    vertex2ind[vertex] = i\n", "    ind2vertex[i] = vertex\n", "\n", "print(len(vertex2ind), len(ind2vertex))\n", "\n", "g = Graph(len(verteces))\n", "for pair in caida_p2p:\n", "    g.addEdge(vertex2ind[pair[0]], vertex2ind[pair[1]])\n", "    g.addEdge(vertex2ind[pair[1]], vertex2ind[pair[0]])\n", "\n", "SCCs = g.printSCCs()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:48:02.516359Z", "start_time": "2019-01-05T10:48:02.492207Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["135\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "134\n", "12618\n", "12618\n"]}], "source": ["print(len(SCCs))\n", "for i, scc in enumerate(SCCs):\n", "    for as_tier1 in TIER1:\n", "        if vertex2ind[as_tier1] not in scc:\n", "            break\n", "        print(i)\n", "print(len(SCCs[134]))\n", "scc = SCCs[134]\n", "scc = set([ind2vertex[ind] for ind in scc])\n", "print(len(scc))"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T10:48:39.959504Z", "start_time": "2019-01-05T10:48:39.759351Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["383512\n", "numbr of edges 191756\n"]}], "source": ["cp_core = set()\n", "for pair in caida_p2p:\n", "    if pair[0] in scc and pair[1] in scc:\n", "        cp_core.add(pair)\n", "        cp_core.add((pair[1], pair[0]))\n", "print(len(cp_core))\n", "print(\"numbr of edges \" + str(len(cp_core)/2))"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["## Create k_max-core"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:22:27.788461Z", "start_time": "2019-12-28T17:22:27.760421Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["21\n"]}], "source": ["k_max = max(k_shell.values())\n", "k_max_core = [vertex for vertex, k in k_shell.items() if k == k_max]\n", "print(len(k_max_core))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:22:45.563401Z", "start_time": "2019-12-28T17:22:33.935024Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["420\n"]}], "source": ["k_max_edges = set()\n", "for i in range(len(k_max_core)):\n", "    for j in range(i):\n", "        if (k_max_core[i], k_max_core[j]) in x_training or (k_max_core[i], k_max_core[j]) in x_test:\n", "            k_max_edges.add((k_max_core[i], k_max_core[j]))\n", "        if (k_max_core[j], k_max_core[i]) in x_training or (k_max_core[j], k_max_core[i]) in x_test:\n", "            k_max_edges.add((k_max_core[j], k_max_core[i]))\n", "            \n", "print(len(k_max_edges))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create x_training_core"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:28:55.209836Z", "start_time": "2019-12-28T17:28:51.597945Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["464609 57888\n"]}], "source": ["x_training_edges = set()\n", "x_training_vertecs = set()\n", "for pair in x_training:\n", "    x_training_edges.add((pair[0], pair[1]))\n", "    x_training_vertecs.add(pair[0])\n", "    x_training_vertecs.add(pair[1])\n", "print(len(x_training_edges), len(x_training_vertecs))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run NDTOR_CP"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:29:03.715060Z", "start_time": "2019-12-28T17:29:03.510984Z"}, "code_folding": [23, 29, 105, 127, 161]}, "outputs": [], "source": ["class NDTOR_CP:\n", "    def __init__(self, core_verteces, core_edges, routing_tables, core_labels=None, is_core=True, threshold=0.85, k_shell=None):\n", "        if is_core:\n", "            self.tor_dict = self.__intialize_tor_dict_with_core(core_edges)\n", "            print('Finished __intialize_tor_dict_with_core')\n", "            self.pahse2_routes = self.__split_path_through_core(routing_tables, core_verteces)\n", "        else:\n", "            self.tor_dict = self.__intialize_tor_dict_with_training_set(core_edges, core_labels)\n", "            print('Finished __intialize_tor_dict_with_training_set')\n", "            self.pahse2_routes = routing_tables\n", "        print('Finished __split_path_through_core with ' + str(len(self.pahse2_routes)) + ' remaining for phase 2')\n", "        self.unclassified_pairs = self.__pahse2(threshold)\n", "        print('Finished __pahse2 with ' + str(len(self.unclassified_pairs)) + \" unclassified pairs\")\n", "        self.__pahse3()\n", "        print('Finished __pahse3 with ' + str(len(self.unclassified_pairs)) + \" unclassified pairs\")\n", "        \n", "        if len(self.unclassified_pairs) > 0:\n", "            if k_shell is None:\n", "                self.k_shell = self.__compute_k_shells(routing_tables)\n", "            else:\n", "                self.k_shell = k_shell\n", "            print(\"Finished __compute_k_shells\")\n", "            self.__compare_k_shells()\n", "            print(\"Finished __compare_k_shells\")\n", "    \n", "    \n", "    def __intialize_tor_dict_with_training_set(self, core_edges, core_labels):\n", "        tor_dict = dict()\n", "        for i, edge in enumerate(core_edges):\n", "            tor_dict[edge] = core_labels[i]\n", "        return tor_dict\n", "    \n", "\n", "    def __intialize_tor_dict_with_core(self, core_edges):\n", "        tor_dict = dict()\n", "        for edge in core_edges:\n", "            tor_dict[edge] = 0 # 0 - p2p\n", "        return tor_dict\n", "        \n", "    def __split_path_through_core(self, routes, core_verteces):\n", "        pahse2_routes = list()\n", "        for path in routes:\n", "            core_inds = list()\n", "            for j, vertex in enumerate(path):\n", "                if vertex in core_verteces:\n", "                    core_inds.append(j)\n", "            if len(core_inds) == 0:\n", "                pahse2_routes.append(path)\n", "            else:\n", "                for i in range(core_inds[0]):\n", "                    if (path[i], path[i+1]) not in self.tor_dict:\n", "                        self.tor_dict[(path[i], path[i+1])] = 1 # 1 - c2p\n", "                        self.tor_dict[(path[i+1], path[i])] = 3\n", "                for i in range(core_inds[0], core_inds[-1]):\n", "                    if (path[i], path[i+1]) not in self.tor_dict:\n", "                        self.tor_dict[(path[i], path[i+1])] = 0 # 0 - p2p\n", "                        self.tor_dict[(path[i+1], path[i])] = 0 \n", "                for i in range(core_inds[-1], len(path)-1):\n", "                    if (path[i], path[i+1]) not in self.tor_dict:\n", "                        self.tor_dict[(path[i], path[i+1])] = 3 # 0 - p2c\n", "                        self.tor_dict[(path[i+1], path[i])] = 1\n", "        return pahse2_routes\n", "    \n", "    \n", "    def __pahse2(self, threshold):\n", "        votings_p2c = defaultdict(lambda:0)\n", "        votings_c2p = defaultdict(lambda:0)\n", "        voting_pairs = set()\n", "        \n", "        for path in self.pahse2_routes:\n", "            pairs = list(zip(path[:-1], path[1:]))\n", "            pairs_tor = []\n", "            for i, pair in enumerate(pairs):\n", "                if pair in self.tor_dict:\n", "                    pairs_tor.append(self.tor_dict[pair])\n", "                else:\n", "                    pairs_tor.append(-1)\n", "                    voting_pairs.add(pair)\n", "            for i in range(len(pairs)):\n", "                if pairs_tor[i] == -1:\n", "                    if i > 1 and pairs_tor[i-1] == 3: #p2c\n", "                        pairs_tor[i] = 3\n", "                        votings_p2c[pairs[i]] += 1\n", "                    if i + 1 < len(pairs) and pairs_tor[i+1] == 1: #c2p\n", "                        pairs_tor[i] = 1\n", "                        votings_c2p[pairs[i]] += 1\n", "        \n", "        unclassified_pairs = set()\n", "        for pair in voting_pairs:\n", "            if (votings_p2c[pair] + votings_c2p[pair]) > 0:\n", "                rank = (votings_p2c[pair]*1.0)/(votings_p2c[pair] + votings_c2p[pair])\n", "                if rank >= threshold:\n", "                    self.tor_dict[pair] = 3\n", "                elif rank <= (1 - threshold):\n", "                    self.tor_dict[pair] = 1\n", "                else:\n", "                    unclassified_pairs.add(pair)\n", "            else:\n", "                unclassified_pairs.add(pair)\n", "        return unclassified_pairs\n", "    \n", "    def __pahse3(self):\n", "        for path in self.pahse2_routes:\n", "            pairs = list(zip(path[:-1], path[1:]))\n", "            if len(pairs) > 2: \n", "                for i in range(1,len(pairs)-1):\n", "                    if pairs[i] not in self.tor_dict and pairs[i-1] in self.tor_dict and pairs[i+1] in self.tor_dict:\n", "                        if self.tor_dict[pairs[i-1]] == 1 and self.tor_dict[pairs[i+1]] == 3:\n", "                            self.tor_dict[pairs[i]] = 0\n", "                            self.unclassified_pairs.remove(pairs[i])\n", "                        elif self.tor_dict[pairs[i-1]] == 3 and self.tor_dict[pairs[i+1]] == 1:\n", "                            self.tor_dict[pairs[i]] = 0\n", "                            self.unclassified_pairs.remove(pairs[i])\n", "    \n", "    \n", "    def __get_k_shell(self, k, edges, k_shell):\n", "        neighbors = defaultdict(set)\n", "        k_shell_verteces = set()\n", "        for edge in edges:\n", "            neighbors[edge[0]].add(edge)\n", "            neighbors[edge[1]].add(edge)\n", "        for asn, asn_edges in neighbors.items():\n", "            if len(asn_edges) <= k:\n", "                k_shell[asn] = k\n", "                k_shell_verteces.add(asn)\n", "                \n", "        return neighbors, k_shell_verteces\n", "    \n", "    \n", "    def __get_graph_for_routes(self, P):\n", "        edges = []\n", "        for route in P:\n", "            for edge in zip(route[:-1], route[1:]):\n", "                edges.append(edge)\n", "        return set(edges)\n", "                \n", "    \n", "    def __remove_k_shell_edges(self, edges, k_shell_verteces, neighbors):\n", "        for vertex in k_shell_verteces:\n", "            edges = edges - neighbors[vertex]\n", "        return edges\n", "                \n", "                \n", "    def __compute_k_shells(self, routes):\n", "        k_shell = dict()\n", "        k = 1\n", "        edges = self.__get_graph_for_routes(routes)\n", "        \n", "        while len(edges) > 0:\n", "            print(\"K: \" + str(k) + \" Start Iteration on \" + str(len(edges)) + \" edges\")\n", "            neighbors, k_shell_verteces = self.__get_k_shell(k, edges, k_shell)\n", "            k += 1\n", "            edges = self.__remove_k_shell_edges(edges, k_shell_verteces, neighbors)\n", "            print(\"Number of remaining edges: \" + str(len(edges)))\n", "            print()\n", "            \n", "        return k_shell\n", "        \n", "        \n", "    def __compare_k_shells(self):\n", "        for pair in self.unclassified_pairs:\n", "            try:\n", "                as0_k = self.k_shell[pair[0]]\n", "            except:\n", "                as0_k = 0\n", "            try:\n", "                as1_k = self.k_shell[pair[1]]\n", "            except:\n", "                as0_k = 0\n", "            if as0_k == as1_k:\n", "                self.tor_dict[pair] = 0 # p2p\n", "            elif as0_k > as1_k:\n", "                self.tor_dict[pair] = 3 # p2c\n", "            else:\n", "                self.tor_dict[pair] = 1 # c2p\n", "    \n", "                \n", "    def tor_dict2dataset(self):\n", "        dataset = []\n", "        labels = []\n", "        for pair, label in self.tor_dict.items():\n", "            dataset.append(np.asarray(pair))\n", "            labels.append(label)\n", "        print(\"Finished __tor_dict2dataset\")\n", "        return np.asarray(dataset), np.asarray(labels)\n", "\n", "\n", "    def generate_labels_for_set(self, pairs):\n", "        labels = []\n", "        for pair in pairs:\n", "            if (pair[0], pair[1]) in self.tor_dict:\n", "                labels.append(self.tor_dict[(pair[0], pair[1])])\n", "            elif (pair[1], pair[0]) in self.tor_dict:\n", "                if self.tor_dict[(pair[1], pair[0])] == 0 or self.tor_dict[(pair[1], pair[0])] == 2:\n", "                    labels.append(self.tor_dict[(pair[1], pair[0])])\n", "                else:\n", "                    labels.append((self.tor_dict[(pair[1], pair[0])] + 2)%4)\n", "            else:\n", "                labels.append(-1)\n", "        return np.asarray(labels)\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:03:11.797765Z", "start_time": "2019-12-28T16:31:09.928989Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished __intialize_tor_dict_with_core\n", "Finished __split_path_through_core with 276 remaining for phase 2\n", "Finished __pahse2 with 142 unclassified pairs\n", "Finished __pahse3 with 142 unclassified pairs\n", "K: 1 Start Iteration on 147667 edges\n", "Number of remaining edges: 123786\n", "\n", "K: 2 Start Iteration on 123786 edges\n", "Number of remaining edges: 80747\n", "\n", "K: 3 Start Iteration on 80747 edges\n", "Number of remaining edges: 59956\n", "\n", "K: 4 Start Iteration on 59956 edges\n", "Number of remaining edges: 47881\n", "\n", "K: 5 Start Iteration on 47881 edges\n", "Number of remaining edges: 39960\n", "\n", "K: 6 Start Iteration on 39960 edges\n", "Number of remaining edges: 34330\n", "\n", "K: 7 Start Iteration on 34330 edges\n", "Number of remaining edges: 30048\n", "\n", "K: 8 Start Iteration on 30048 edges\n", "Number of remaining edges: 26555\n", "\n", "K: 9 Start Iteration on 26555 edges\n", "Number of remaining edges: 23538\n", "\n", "K: 10 Start Iteration on 23538 edges\n", "Number of remaining edges: 20722\n", "\n", "K: 11 Start Iteration on 20722 edges\n", "Number of remaining edges: 18171\n", "\n", "K: 12 Start Iteration on 18171 edges\n", "Number of remaining edges: 16102\n", "\n", "K: 13 Start Iteration on 16102 edges\n", "Number of remaining edges: 14291\n", "\n", "K: 14 Start Iteration on 14291 edges\n", "Number of remaining edges: 12517\n", "\n", "K: 15 Start Iteration on 12517 edges\n", "Number of remaining edges: 10872\n", "\n", "K: 16 Start Iteration on 10872 edges\n", "Number of remaining edges: 9525\n", "\n", "K: 17 Start Iteration on 9525 edges\n", "Number of remaining edges: 8561\n", "\n", "K: 18 Start Iteration on 8561 edges\n", "Number of remaining edges: 7541\n", "\n", "K: 19 Start Iteration on 7541 edges\n", "Number of remaining edges: 6955\n", "\n", "K: 20 Start Iteration on 6955 edges\n", "Number of remaining edges: 6518\n", "\n", "K: 21 Start Iteration on 6518 edges\n", "Number of remaining edges: 6108\n", "\n", "K: 22 Start Iteration on 6108 edges\n", "Number of remaining edges: 5780\n", "\n", "K: 23 Start Iteration on 5780 edges\n", "Number of remaining edges: 5393\n", "\n", "K: 24 Start Iteration on 5393 edges\n", "Number of remaining edges: 4987\n", "\n", "K: 25 Start Iteration on 4987 edges\n", "Number of remaining edges: 4614\n", "\n", "K: 26 Start Iteration on 4614 edges\n", "Number of remaining edges: 4356\n", "\n", "K: 27 Start Iteration on 4356 edges\n", "Number of remaining edges: 4142\n", "\n", "K: 28 Start Iteration on 4142 edges\n", "Number of remaining edges: 3835\n", "\n", "K: 29 Start Iteration on 3835 edges\n", "Number of remaining edges: 3662\n", "\n", "K: 30 Start Iteration on 3662 edges\n", "Number of remaining edges: 3485\n", "\n", "K: 31 Start Iteration on 3485 edges\n", "Number of remaining edges: 3213\n", "\n", "K: 32 Start Iteration on 3213 edges\n", "Number of remaining edges: 2938\n", "\n", "K: 33 Start Iteration on 2938 edges\n", "Number of remaining edges: 2648\n", "\n", "K: 34 Start Iteration on 2648 edges\n", "Number of remaining edges: 2327\n", "\n", "K: 35 Start Iteration on 2327 edges\n", "Number of remaining edges: 1926\n", "\n", "K: 36 Start Iteration on 1926 edges\n", "Number of remaining edges: 1664\n", "\n", "K: 37 Start Iteration on 1664 edges\n", "Number of remaining edges: 1262\n", "\n", "K: 38 Start Iteration on 1262 edges\n", "Number of remaining edges: 812\n", "\n", "K: 39 Start Iteration on 812 edges\n", "Number of remaining edges: 362\n", "\n", "K: 40 Start Iteration on 362 edges\n", "Number of remaining edges: 0\n", "\n", "Finished __compute_k_shells\n", "Finished __compare_k_shells\n"]}], "source": ["ndtor_cp = NDTOR_CP(scc, cp_core, bgp_routes,k_shell=k_shell)   # CP - Core"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:23:57.124142Z", "start_time": "2019-12-28T17:23:03.552400Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished __intialize_tor_dict_with_core\n", "Finished __split_path_through_core with 372339 remaining for phase 2\n", "Finished __pahse2 with 17250 unclassified pairs\n", "Finished __pahse3 with 14158 unclassified pairs\n", "Finished __compute_k_shells\n", "Finished __compare_k_shells\n"]}], "source": ["ndtor_k_max_core = NDTOR_CP(k_max_core, k_max_edges, bgp_routes, k_shell=k_shell)   # k_max_core - Core"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:31:02.633745Z", "start_time": "2019-12-28T17:29:26.409765Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished __intialize_tor_dict_with_training_set\n", "Finished __split_path_through_core with 3669655 remaining for phase 2\n", "Finished __pahse2 with 25145 unclassified pairs\n", "Finished __pahse3 with 23580 unclassified pairs\n", "Finished __compute_k_shells\n", "Finished __compare_k_shells\n"]}], "source": ["### Reverse to original labels\n", "core_labels = list()\n", "for label in y_training:\n", "    if label == 2:\n", "        core_labels.append(3)\n", "    else:\n", "        core_labels.append(label)\n", "\n", "ndtor_x_training_core = NDTOR_CP(x_training_vertecs, x_training_edges, bgp_routes, core_labels=core_labels, is_core=False, k_shell=k_shell)\n"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"ExecuteTime": {"end_time": "2019-01-11T19:45:58.174139Z", "start_time": "2019-01-11T19:45:55.731185Z"}}, "outputs": [], "source": ["# ToR_MODEL_NAME = \"Cleaned_Orig_3_ToR_Classification_NDToR_x_training_core\"\n", "\n", "# with open(MODEL<PERSON>_PATH + ToR_MODEL_NAME + '_tor_dict.pickle', 'wb') as handle:\n", "#     pickle.dump(ndtor_k_max_core.tor_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)\n", "\n", "# # with open(MODELS_PATH + 'k_shell.pickle', 'wb') as handle:\n", "# #     pickle.dump(ndtor_cp.k_shell, handle, protocol=pickle.HIGHEST_PROTOCOL)"]}, {"cell_type": "code", "execution_count": 48, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T13:45:11.237303Z", "start_time": "2019-01-05T13:45:11.213810Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["21\n"]}], "source": ["# k_max = max(ndtor_cp.k_shell.values())\n", "# k_max_core = [vertex for vertex, k in ndtor_cp.k_shell.items() if k == k_max]\n", "# print(len(k_max_core))"]}, {"cell_type": "code", "execution_count": 49, "metadata": {"ExecuteTime": {"end_time": "2019-01-05T13:46:40.776218Z", "start_time": "2019-01-05T13:46:40.758274Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3356,)\n", "(6939,)\n", "(1299,)\n", "(174,)\n", "(3257,)\n", "(2914,)\n", "(6453,)\n", "(209,)\n", "(1239,)\n", "(6762,)\n", "(9002,)\n", "(701,)\n", "(6461,)\n", "(4637,)\n", "(3491,)\n", "(286,)\n", "(37100,)\n", "(2497,)\n", "(3303,)\n", "(2516,)\n", "(1273,)\n"]}], "source": ["# index_ASN_map = {index: ASN for ASN, index in ASN_index_map.items()}\n", "\n", "# for ind in k_max_core:\n", "#     print(index_ASN_map[ind],)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save kshell"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:32:50.174001Z", "start_time": "2019-12-28T17:32:17.429691Z"}}, "outputs": [], "source": ["with open(MODELS_PATH + ToR_MODEL_NAME + '_tor_dict.pickle', 'wb') as handle:\n", "    pickle.dump(ndtor_x_training_core.tor_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)\n", "\n", "# with open(MODELS_PATH + ToR_MODEL_NAME + 's1_k_shell.pickle', 'wb') as handle:\n", "#     pickle.dump(ndtor_cp.k_shell, handle, protocol=pickle.HIGHEST_PROTOCOL)\n", "\n", "# with open(MODELS_PATH + 's1_k_shell.pickle', 'rb') as handle:\n", "#     k_shell = pickle.load(handle)\n", "# print(len(k_shell))"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2018-10-20T11:24:40.580036Z", "start_time": "2018-10-20T11:24:33.351927Z"}}, "source": ["# Final evaluation of the model\n", "## Evaluate accuracy over the test set"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:32:56.117758Z", "start_time": "2019-12-28T17:32:54.066759Z"}}, "outputs": [], "source": ["y_test_prediction = ndtor_x_training_core.generate_labels_for_set(x_test)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:33:01.108958Z", "start_time": "2019-12-28T17:33:01.029163Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0, 1, 3, -1}\n"]}], "source": ["print(set(y_test_prediction))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:33:05.820778Z", "start_time": "2019-12-28T17:33:05.224169Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["116153\n", "116153\n"]}], "source": ["print(len(y_test_prediction))\n", "y_test_prediction_new = []\n", "for i in range(len(y_test_prediction)):\n", "    if y_test_prediction[i] %2 == 0:\n", "        y_test_prediction_new.append(0)\n", "    elif y_test_prediction[i] == 3:\n", "        y_test_prediction_new.append(2)\n", "    elif y_test_prediction[i] == 1:\n", "        y_test_prediction_new.append(1)\n", "    else:\n", "        y_test_prediction_new.append(-1)\n", "\n", "y_test_prediction_new = np.asarray(y_test_prediction_new)\n", "print(len(y_test_prediction_new))\n", "y_test_prediction = y_test_prediction_new"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:33:10.792578Z", "start_time": "2019-12-28T17:33:10.707222Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0, 1, 2, -1}\n"]}], "source": ["print(set(y_test_prediction))"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:33:15.559734Z", "start_time": "2019-12-28T17:33:15.294353Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0, 1, 2}\n", "102819 102819\n"]}], "source": ["y_test = [y_test[i] for i, label in enumerate(y_test_prediction) if label!=-1]\n", "y_test_prediction = [label for i, label in enumerate(y_test_prediction) if label!=-1]\n", "print(set(y_test_prediction))\n", "print(len(y_test), len(y_test_prediction))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:33:19.833889Z", "start_time": "2019-12-28T17:33:19.705298Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 56.15%\n"]}], "source": ["from sklearn.metrics import accuracy_score\n", "test_scores = accuracy_score(y_test, y_test_prediction)\n", "print(\"Accuracy: %.2f%%\" % (test_scores*100))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:13.913361Z", "start_time": "2018-11-17T15:27:13.845207Z"}}, "outputs": [], "source": ["# x_test_cleaned = np.asarray([np.asarray(x_test[i]) for i in range(len(x_test)) if y_test_prediction[i] != -1])\n", "# y_test_cleaned = np.asarray([y_test[i] for i in range(len(y_test)) if y_test_prediction[i] != -1])\n", "# y_test_prediction_cleaned = np.asarray([y_test_prediction[i] for i in range(len(y_test_prediction)) if y_test_prediction[i] != -1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:15.867226Z", "start_time": "2018-11-17T15:27:15.861071Z"}}, "outputs": [], "source": ["# print(len(x_test_cleaned), len(y_test_cleaned), len(y_test_prediction_cleaned))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:17.061659Z", "start_time": "2018-11-17T15:27:17.052861Z"}}, "outputs": [], "source": ["# from sklearn.metrics import accuracy_score\n", "# test_scores = accuracy_score(y_test_cleaned, y_test_prediction_cleaned)\n", "# print(\"Accuracy: %.2f%%\" % (test_scores*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test if by learning (asn1, asn2) -> p2c then (asn2, asn1) -> c2p and vice versa"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2019-01-04T17:50:32.317308Z", "start_time": "2019-01-04T17:50:32.017509Z"}}, "outputs": [], "source": ["p2c = TOR_ORIG_LABELS_DICT['P2C']\n", "c2p = TOR_ORIG_LABELS_DICT['C2P']\n", "\n", "p2c_training = np.asarray([np.asarray(x_training[i]) for i in range(len(x_training)) if y_training[i] == p2c])\n", "p2c_training_oposite = np.asarray([np.asarray([pair[1], pair[0]]) for pair in p2c_training])\n", "p2c_training_labels = [p2c]*len(p2c_training)\n", "p2c_training_oposite_labels = [c2p]*len(p2c_training_oposite)\n", "print(p2c_training.shape, p2c_training_oposite.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2019-01-04T17:51:02.400638Z", "start_time": "2019-01-04T17:51:02.106675Z"}}, "outputs": [], "source": ["p2c_training_labels_prediction = generate_labels_for_set(caida_tor_dict, p2c_training)\n", "p2c_training_scores = accuracy_score(p2c_training_labels, p2c_training_labels_prediction)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_scores*100))\n", "\n", "p2c_training_oposite_labels_prediction = generate_labels_for_set(caida_tor_dict, p2c_training_oposite)\n", "p2c_training_oposite_scores = accuracy_score(p2c_training_oposite_labels, p2c_training_oposite_labels_prediction)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_oposite_scores*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot and save a confusion matrix for results over the test set"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define a function"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:33:25.319085Z", "start_time": "2019-12-28T17:33:24.639316Z"}}, "outputs": [], "source": ["%matplotlib inline\n", "\n", "import matplotlib\n", "import pylab as pl\n", "from sklearn.metrics import confusion_matrix\n", "import itertools\n", "import matplotlib.pyplot as plt\n", "import matplotlib.cm as cm\n", "\n", "def plot_confusion_matrix(cm, classes,\n", "                          normalize=False,\n", "                          title='Confusion matrix',\n", "                          fname='Confusion matrix',\n", "                          cmap=plt.cm.Blues):\n", "    \"\"\"\n", "    This function prints and plots the confusion matrix.\n", "    Normalization can be applied by setting `normalize=True`.\n", "    \"\"\"\n", "    if normalize:\n", "        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "        print(\"Normalized confusion matrix\")\n", "    else:\n", "        print('Confusion matrix, without normalization')\n", "\n", "#     print(cm)\n", "\n", "    plt.imshow(cm, interpolation='nearest', cmap=cmap)\n", "#     plt.title(title)\n", "    plt.colorbar()\n", "    tick_marks = np.arange(len(classes))\n", "    plt.xticks(tick_marks, classes, rotation=45)\n", "    plt.yticks(tick_marks, classes)\n", "\n", "    fmt = '.1f' if normalize else 'd'\n", "    thresh = cm.max() / 2.\n", "    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):\n", "        if normalize:\n", "            plt.text(j, i, format(cm[i, j]*100, fmt) + '%',\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")\n", "        else:\n", "            plt.text(j, i, format(cm[i, j], fmt),\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")    \n", "        \n", "    plt.tight_layout()\n", "    plt.ylabel('True Label')\n", "    plt.xlabel('Predicted Label')\n", "    plt.savefig(fname, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:33:33.679660Z", "start_time": "2019-12-28T17:33:30.067959Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Confusion matrix, without normalization\n", "Normalized confusion matrix\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Compute confusion matrix\n", "cnf_matrix = confusion_matrix(y_test, y_test_prediction)\n", "np.set_printoptions(precision=2)\n", "\n", "# Plot non-normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names,\n", "                      title='Confusion matrix, without normalization',\n", "                      fname=RESULTS_PATH + ToR_MODEL_NAME + \"_\" + 'Confusion_matrix_without_normalization')\n", "\n", "# Plot normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names, normalize=True,\n", "                      title='Normalized Confusion Msatrix',\n", "                      fname=RESULTS_PATH +ToR_MODEL_NAME + \"_\" + 'Normalized_confusion_matrix')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["# Export the model to a file"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-16T12:50:04.937659Z", "start_time": "2018-11-16T12:50:04.873865Z"}, "hidden": true}, "outputs": [], "source": ["model_json = pairs_model.to_json()\n", "with open(MODELS_PATH + ToR_MODEL_NAME + '.json', \"w\") as json_file:\n", "    json_file.write(model_json)\n", "pairs_model.save_weights(MODELS_PATH + ToR_MODEL_NAME + '.h5')\n", "print(\"Save Model\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Export results to a csv file (with original ASNs)\n", "## Define functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:53:31.858073Z", "start_time": "2018-10-20T15:53:31.831311Z"}}, "outputs": [], "source": ["def index2ASN(dataset_indexed, ASN_index_map):\n", "    dataset = []\n", "    index_ASN_map = {index: ASN for ASN, index in ASN_index_map.items()}\n", "    for row_indexed in dataset_indexed:\n", "        row = []\n", "        for index in row_indexed:\n", "            if index != 0:\n", "                row += [index_ASN_map[index]]\n", "        dataset.append(row)\n", "    \n", "    return dataset\n", "\n", "def index2ASN_labeled(dataset_indexed, labels_indexed, ASN_index_map):\n", "    dataset = []\n", "    index_ASN_map = {index: ASN for ASN, index in ASN_index_map.items()}\n", "    labels_colors_map = {0:'GREEN', 1:'RED'}\n", "    \n", "    for i, row_indexed in enumerate(dataset_indexed):\n", "        row = []\n", "        for index in row_indexed:\n", "            if index != 0:\n", "                row += [index_ASN_map[index]]\n", "        row += [labels_colors_map[labels_indexed[i]]]\n", "        dataset.append(row)\n", "    \n", "    return dataset\n", "\n", "import csv\n", "def export_csv(dataset, csv_name):\n", "    with open(csv_name + '.csv', 'wb') as csv_file:\n", "        csv_writer = csv.writer(csv_file)\n", "        for row in dataset:\n", "            csv_writer.writerow(row)"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["## Load a relevant dataset {all, misclassified, decided, undecided} and get model predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:35:46.412152Z", "start_time": "2018-10-20T15:30:04.317489Z"}, "hidden": true}, "outputs": [], "source": ["### misclassified from the entire dataset ###\n", "\n", "dataset = np.load(DATA_PATH + \"bgp_routes_indexed_dataset.npy\")\n", "labels = np.load(DATA_PATH + \"bgp_routes_labels.npy\")\n", "\n", "# remove UNDECIDED\n", "dataset = np.asarray([np.asarray(dataset[i]) for i in range(len(dataset)) if labels[i] != 2])\n", "labels = np.asarray([labels[i] for i in range(len(labels)) if labels[i] != 2])\n", "\n", "# pad sequences\n", "dataset = sequence.pad_sequences(dataset, maxlen=max_len)\n", "# Get Model Predictions\n", "predictions = model.predict_classes(dataset, verbose=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:54:32.435559Z", "start_time": "2018-10-20T15:54:28.652217Z"}, "hidden": true}, "outputs": [], "source": ["# Create misclassified dataset\n", "x_misclassified = np.asarray([route for i,route in enumerate(dataset) if labels[i] != predictions[i]])\n", "y_misclassified_prediction = np.asarray([label for i,label in enumerate(predictions) if labels[i] != predictions[i]])\n", "print len(x_misclassified), len(y_misclassified_prediction)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:54:39.339072Z", "start_time": "2018-10-20T15:54:39.169037Z"}}, "outputs": [], "source": ["dataset_misclassified = index2ASN_labeled(x_misclassified, y_misclassified_prediction, ASN_index_map)\n", "export_csv(dataset_misclassified, RESULTS_PATH + MODEL_NAME + \"_misclassified\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 2}