{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Import python packages"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:14:20.537892Z", "start_time": "2019-12-28T19:14:18.441654Z"}}, "outputs": [], "source": ["from run_ruan import Ruan\n", "\n", "import pickle\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from collections import Counter\n", "import random\n", "\n", "np.random.seed(7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Define parameters and load bgp_routes and ToR datasets"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:14:40.201768Z", "start_time": "2019-12-28T19:14:23.506384Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3669655,) (3669655,)\n", "(580762, 2) (580762,)\n"]}], "source": ["ToR_MODEL_NAME = \"CAIDA_s1_ToR_Classification_Ruan\"\n", "\n", "TEST_SIZE = 0.2\n", "TOR_LABELS_DICT = {'P2P':0, 'C2P': 1,'P2C': 2}\n", "class_names = ['P2P', 'C2P', 'P2C']\n", "DATA_PATH = '../../Data/'\n", "MODELS_PATH = '../../Models/'\n", "RESULTS_PATH = '../../Results/'\n", "\n", "\n", "bgp_routes = np.load(DATA_PATH + \"bgp_routes_dataset.npy\")\n", "bgp_routes_labels = np.load(DATA_PATH + \"bgp_routes_labels.npy\")\n", "print(bgp_routes.shape, bgp_routes_labels.shape)\n", "\n", "DATA = \"caida_s1_tor\"\n", "tor_dataset = np.load(DATA_PATH + DATA + \"_dataset.npy\")\n", "tor_labels = np.load(DATA_PATH + DATA + \"_labels.npy\")\n", "\n", "print(tor_dataset.shape, tor_labels.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate training and test sets\n", "## Shauffle dataset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:14:40.570339Z", "start_time": "2019-12-28T19:14:40.357557Z"}}, "outputs": [], "source": ["from sklearn.utils import shuffle\n", "dataset, labels = shuffle(tor_dataset, tor_labels, random_state=7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate a balanced dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T13:08:27.960627Z", "start_time": "2018-11-17T13:08:27.890678Z"}}, "outputs": [], "source": ["# def generate_balanced_dataset(dataset, labels, labels_set):\n", "#     sets_dict = dict()\n", "#     for label in labels_set:\n", "#         sets_dict[label] = np.asarray([np.asarray(dataset[i]) for i in range(len(dataset)) if labels[i] == label])\n", "    \n", "#     min_set_len = min([len(label_set) for label_set in sets_dict.values()])\n", "    \n", "#     for label, label_set in sets_dict.iteritems():\n", "#         sets_dict[label] = label_set[np.random.choice(label_set.shape[0], min_set_len, replace=False)]\n", "    \n", "#     dataset = np.concatenate((sets_dict.values()))\n", "#     labels = []\n", "#     for label, label_set in sets_dict.iteritems():\n", "#         labels += [label]*len(label_set)\n", "#         print label, len(label_set)\n", "#     labels = np.asarray(labels)\n", "#     return shuffle(dataset, labels, random_state=7)\n", "\n", "# dataset, labels = generate_balanced_dataset(dataset, labels, (0,1,3))\n", "# print dataset.shape, labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train Test Split"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:14:41.043047Z", "start_time": "2019-12-28T19:14:40.757954Z"}}, "outputs": [], "source": ["x_training, x_test, y_training, y_test = train_test_split(dataset, labels, test_size=TEST_SIZE)\n", "\n", "del dataset, labels"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:14:41.184530Z", "start_time": "2019-12-28T19:14:41.177940Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(464609, 2) (464609,)\n", "(116153, 2) (116153,)\n"]}], "source": ["print(x_training.shape, y_training.shape)\n", "print(x_test.shape, y_test.shape)\n", "\n", "# print 1.0*len(x_training)/(len(x_test)+len(x_training))\n", "\n", "# from collections import Counter\n", "# training_c = Counter(y_training)\n", "# test_c = Counter(y_test)\n", "# print(training_c, test_c)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T17:36:21.992316Z", "start_time": "2019-12-28T17:36:21.981763Z"}}, "outputs": [], "source": ["# for k,v in training_c.iteritems():\n", "#     print k, 100.0*v/len(x_training)\n", "# print\n", "# for k,v in test_c.iteritems():\n", "#     print k, 100.0*v/len(x_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Run <PERSON>"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:18:31.356612Z", "start_time": "2019-12-28T19:18:18.946860Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["62525\n"]}], "source": ["ASN_list = set()\n", "for route in bgp_routes:\n", "    for asn in route:\n", "        ASN_list.add(asn)\n", "ASN_list = list(ASN_list)\n", "print(len(ASN_list))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:44:03.143868Z", "start_time": "2019-12-28T19:18:33.485937Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finished __count_as_in_s1\n", "Finished __get_s1_tor\n", "Finished __get_S2\n", "Finished __get_pairs_list and creating graph\n", "Number of vertecs 62525\n", "0\n", "1000\n", "2000\n", "3000\n", "4000\n", "5000\n", "6000\n", "7000\n", "8000\n", "9000\n", "10000\n", "11000\n", "12000\n", "13000\n", "14000\n", "15000\n", "16000\n", "17000\n", "18000\n", "19000\n", "20000\n", "21000\n", "22000\n", "23000\n", "24000\n", "25000\n", "26000\n", "27000\n", "28000\n", "29000\n", "30000\n", "31000\n", "32000\n", "33000\n", "34000\n", "35000\n", "36000\n", "37000\n", "38000\n", "39000\n", "40000\n", "41000\n", "42000\n", "43000\n", "44000\n", "45000\n", "46000\n", "47000\n", "48000\n", "49000\n", "50000\n", "51000\n", "52000\n", "53000\n", "54000\n", "55000\n", "56000\n", "57000\n", "58000\n", "59000\n", "60000\n", "61000\n", "62000\n", "Finished __calcultae_distances_from_tier1\n", "0\n", "100000\n", "200000\n", "300000\n", "400000\n", "500000\n", "600000\n", "700000\n", "800000\n", "900000\n", "1000000\n", "1100000\n", "1200000\n", "1300000\n", "1400000\n", "1500000\n", "1600000\n", "1700000\n", "1800000\n", "1900000\n", "2000000\n", "2100000\n", "2200000\n", "2300000\n", "2400000\n", "2500000\n", "2600000\n", "2700000\n", "2800000\n", "2900000\n", "3000000\n", "3100000\n", "3200000\n", "3300000\n", "3400000\n", "3500000\n", "3600000\n", "Finished __count_as_in_s2\n", "Finished __get_s2_tor\n"]}], "source": ["ruan = Ruan(bgp_routes, ASN_list)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:44:04.056383Z", "start_time": "2019-12-28T19:44:03.544218Z"}}, "outputs": [], "source": ["with open(MODELS_PATH + ToR_MODEL_NAME + '_tor_dict.pickle', 'wb') as handle:\n", "        pickle.dump(ruan.tor_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:44:04.451639Z", "start_time": "2019-12-28T19:44:04.444791Z"}}, "outputs": [], "source": ["ruan_tor_dict = ruan.tor_dict"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON>ad Ruan Results"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2019-01-11T19:22:22.226968Z", "start_time": "2019-01-11T19:22:20.868396Z"}}, "outputs": [], "source": ["OLD_ToR_MODEL_NAME = \"Orig_ToR_Classification_Ruan\"\n", "with open(MODELS_PATH + OLD_ToR_MODEL_NAME + '_tor_dict.pickle', 'rb') as handle:\n", "    ruan_tor_dict = pickle.load(handle)"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2018-10-20T11:24:40.580036Z", "start_time": "2018-10-20T11:24:33.351927Z"}}, "source": ["# Final evaluation of the model\n", "## Evaluate accuracy over the test set"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:44:08.137886Z", "start_time": "2019-12-28T19:44:08.118503Z"}}, "outputs": [], "source": ["def generate_labels_for_set(tor_dict, pairs):\n", "    labels = []\n", "    for pair in pairs:\n", "        if (pair[0], pair[1]) in tor_dict:\n", "            labels.append(tor_dict[(pair[0], pair[1])])\n", "        elif (pair[1], pair[0]) in tor_dict:\n", "            if tor_dict[(pair[1], pair[0])] == 0 or tor_dict[(pair[1], pair[0])] == 2:\n", "                labels.append(tor_dict[(pair[1], pair[0])])\n", "            else:\n", "                labels.append((tor_dict[(pair[1], pair[0])] + 2)%4)\n", "        else:\n", "            labels.append(-1)\n", "    return np.asarray(labels)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:44:12.737819Z", "start_time": "2019-12-28T19:44:11.481843Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["116153\n", "116153\n"]}], "source": ["y_test_prediction = generate_labels_for_set(ruan_tor_dict, x_test)\n", "\n", "print(len(y_test_prediction))\n", "y_test_prediction_new = []\n", "for i in range(len(y_test_prediction)):\n", "    if y_test_prediction[i] %2 == 0:\n", "        y_test_prediction_new.append(0)\n", "    elif y_test_prediction[i] == 3:\n", "        y_test_prediction_new.append(2)\n", "    elif y_test_prediction[i] == 1:\n", "        y_test_prediction_new.append(1)\n", "    else:\n", "        y_test_prediction_new.append(-1)\n", "\n", "y_test_prediction_new = np.asarray(y_test_prediction_new)\n", "print(len(y_test_prediction_new))\n", "y_test_prediction = y_test_prediction_new"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:44:16.649768Z", "start_time": "2019-12-28T19:44:16.627345Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["49719\n"]}], "source": ["y_test_prediction = y_test_prediction[np.where(y_test_prediction!=-1)]\n", "y_test = y_test[np.where(y_test_prediction!=-1)]\n", "\n", "print(len(y_test_prediction))"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:46:41.194880Z", "start_time": "2019-12-28T19:46:41.167579Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 21.24%\n"]}], "source": ["from sklearn.metrics import accuracy_score\n", "test_scores = accuracy_score(y_test, y_test_prediction)\n", "print(\"Accuracy: %.2f%%\" % (test_scores*100))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:13.913361Z", "start_time": "2018-11-17T15:27:13.845207Z"}}, "outputs": [], "source": ["# x_test_cleaned = np.asarray([np.asarray(x_test[i]) for i in range(len(x_test)) if y_test_prediction[i] != -1])\n", "# y_test_cleaned = np.asarray([y_test[i] for i in range(len(y_test)) if y_test_prediction[i] != -1])\n", "# y_test_prediction_cleaned = np.asarray([y_test_prediction[i] for i in range(len(y_test_prediction)) if y_test_prediction[i] != -1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:15.867226Z", "start_time": "2018-11-17T15:27:15.861071Z"}}, "outputs": [], "source": ["# print(len(x_test_cleaned), len(y_test_cleaned), len(y_test_prediction_cleaned))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:27:17.061659Z", "start_time": "2018-11-17T15:27:17.052861Z"}}, "outputs": [], "source": ["# from sklearn.metrics import accuracy_score\n", "# test_scores = accuracy_score(y_test_cleaned, y_test_prediction_cleaned)\n", "# print(\"Accuracy: %.2f%%\" % (test_scores*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test if by learning (asn1, asn2) -> p2c then (asn2, asn1) -> c2p and vice versa"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:34:36.931489Z", "start_time": "2018-11-17T15:34:36.651143Z"}}, "outputs": [], "source": ["p2c = TOR_ORIG_LABELS_DICT['P2C']\n", "c2p = TOR_ORIG_LABELS_DICT['C2P']\n", "\n", "p2c_training = np.asarray([np.asarray(x_training[i]) for i in range(len(x_training)) if y_training[i] == p2c])\n", "p2c_training_oposite = np.asarray([np.asarray([pair[1], pair[0]]) for pair in p2c_training])\n", "p2c_training_labels = [p2c]*len(p2c_training)\n", "p2c_training_oposite_labels = [c2p]*len(p2c_training_oposite)\n", "print(p2c_training.shape, p2c_training_oposite.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-17T15:34:38.408003Z", "start_time": "2018-11-17T15:34:38.009050Z"}}, "outputs": [], "source": ["p2c_training_labels_prediction = gao.generate_labels_for_set(p2c_training)\n", "p2c_training_scores = accuracy_score(p2c_training_labels, p2c_training_labels_prediction)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_scores*100))\n", "\n", "p2c_training_oposite_labels_prediction = gao.generate_labels_for_set(p2c_training_oposite)\n", "p2c_training_oposite_scores = accuracy_score(p2c_training_oposite_labels, p2c_training_oposite_labels_prediction)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_oposite_scores*100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot and save a confusion matrix for results over the test set"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define a function"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:46:48.083434Z", "start_time": "2019-12-28T19:46:48.035690Z"}}, "outputs": [], "source": ["%matplotlib inline\n", "\n", "import matplotlib\n", "import pylab as pl\n", "from sklearn.metrics import confusion_matrix\n", "import itertools\n", "import matplotlib.pyplot as plt\n", "import matplotlib.cm as cm\n", "\n", "def plot_confusion_matrix(cm, classes,\n", "                          normalize=False,\n", "                          title='Confusion matrix',\n", "                          fname='Confusion matrix',\n", "                          cmap=plt.cm.Blues):\n", "    \"\"\"\n", "    This function prints and plots the confusion matrix.\n", "    Normalization can be applied by setting `normalize=True`.\n", "    \"\"\"\n", "    if normalize:\n", "        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "        print(\"Normalized confusion matrix\")\n", "    else:\n", "        print('Confusion matrix, without normalization')\n", "\n", "#     print(cm)\n", "\n", "    plt.imshow(cm, interpolation='nearest', cmap=cmap)\n", "#     plt.title(title)\n", "    plt.colorbar()\n", "    tick_marks = np.arange(len(classes))\n", "    plt.xticks(tick_marks, classes, rotation=45)\n", "    plt.yticks(tick_marks, classes)\n", "\n", "    fmt = '.1f' if normalize else 'd'\n", "    thresh = cm.max() / 2.\n", "    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):\n", "        if normalize:\n", "            plt.text(j, i, format(cm[i, j]*100, fmt) + '%',\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")\n", "        else:\n", "            plt.text(j, i, format(cm[i, j], fmt),\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")    \n", "        \n", "    plt.tight_layout()\n", "    plt.ylabel('True Label')\n", "    plt.xlabel('Predicted Label')\n", "    plt.savefig(fname, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2019-12-28T19:46:53.719086Z", "start_time": "2019-12-28T19:46:51.423701Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Confusion matrix, without normalization\n", "Normalized confusion matrix\n"]}, {"data": {"image/png": "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**********************************************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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Compute confusion matrix\n", "cnf_matrix = confusion_matrix(y_test, y_test_prediction)\n", "np.set_printoptions(precision=2)\n", "\n", "# Plot non-normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names,\n", "                      title='Confusion matrix, without normalization',\n", "                      fname=RESULTS_PATH + ToR_MODEL_NAME + \"_\" + 'Confusion_matrix_without_normalization')\n", "\n", "# Plot normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names, normalize=True,\n", "                      title='Normalized Confusion Msatrix',\n", "                      fname=RESULTS_PATH +ToR_MODEL_NAME + \"_\" + 'Normalized_confusion_matrix')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["# Export the model to a file"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-11-16T12:50:04.937659Z", "start_time": "2018-11-16T12:50:04.873865Z"}, "hidden": true}, "outputs": [], "source": ["model_json = pairs_model.to_json()\n", "with open(MODELS_PATH + ToR_MODEL_NAME + '.json', \"w\") as json_file:\n", "    json_file.write(model_json)\n", "pairs_model.save_weights(MODELS_PATH + ToR_MODEL_NAME + '.h5')\n", "print(\"Save Model\")"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["# Export results to a csv file (with original ASNs)\n", "## Define functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:53:31.858073Z", "start_time": "2018-10-20T15:53:31.831311Z"}, "hidden": true}, "outputs": [], "source": ["def index2ASN(dataset_indexed, ASN_index_map):\n", "    dataset = []\n", "    index_ASN_map = {index: ASN for ASN, index in ASN_index_map.iteritems()}\n", "    for row_indexed in dataset_indexed:\n", "        row = []\n", "        for index in row_indexed:\n", "            if index != 0:\n", "                row += [index_ASN_map[index]]\n", "        dataset.append(row)\n", "    \n", "    return dataset\n", "\n", "def index2ASN_labeled(dataset_indexed, labels_indexed, ASN_index_map):\n", "    dataset = []\n", "    index_ASN_map = {index: ASN for ASN, index in ASN_index_map.iteritems()}\n", "    labels_colors_map = {0:'GREEN', 1:'RED'}\n", "    \n", "    for i, row_indexed in enumerate(dataset_indexed):\n", "        row = []\n", "        for index in row_indexed:\n", "            if index != 0:\n", "                row += [index_ASN_map[index]]\n", "        row += [labels_colors_map[labels_indexed[i]]]\n", "        dataset.append(row)\n", "    \n", "    return dataset\n", "\n", "import csv\n", "def export_csv(dataset, csv_name):\n", "    with open(csv_name + '.csv', 'wb') as csv_file:\n", "        csv_writer = csv.writer(csv_file)\n", "        for row in dataset:\n", "            csv_writer.writerow(row)"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true, "hidden": true}, "source": ["## Load a relevant dataset {all, misclassified, decided, undecided} and get model predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:35:46.412152Z", "start_time": "2018-10-20T15:30:04.317489Z"}, "hidden": true}, "outputs": [], "source": ["### misclassified from the entire dataset ###\n", "\n", "dataset = np.load(DATA_PATH + \"bgp_routes_indexed_dataset.npy\")\n", "labels = np.load(DATA_PATH + \"bgp_routes_labels.npy\")\n", "\n", "# remove UNDECIDED\n", "dataset = np.asarray([np.asarray(dataset[i]) for i in range(len(dataset)) if labels[i] != 2])\n", "labels = np.asarray([labels[i] for i in range(len(labels)) if labels[i] != 2])\n", "\n", "# pad sequences\n", "dataset = sequence.pad_sequences(dataset, maxlen=max_len)\n", "# Get Model Predictions\n", "predictions = model.predict_classes(dataset, verbose=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:54:32.435559Z", "start_time": "2018-10-20T15:54:28.652217Z"}, "hidden": true}, "outputs": [], "source": ["# Create misclassified dataset\n", "x_misclassified = np.asarray([route for i,route in enumerate(dataset) if labels[i] != predictions[i]])\n", "y_misclassified_prediction = np.asarray([label for i,label in enumerate(predictions) if labels[i] != predictions[i]])\n", "print len(x_misclassified), len(y_misclassified_prediction)"]}, {"cell_type": "markdown", "metadata": {"hidden": true}, "source": ["## Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-10-20T15:54:39.339072Z", "start_time": "2018-10-20T15:54:39.169037Z"}, "hidden": true}, "outputs": [], "source": ["dataset_misclassified = index2ASN_labeled(x_misclassified, y_misclassified_prediction, ASN_index_map)\n", "export_csv(dataset_misclassified, RESULTS_PATH + MODEL_NAME + \"_misclassified\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 2}