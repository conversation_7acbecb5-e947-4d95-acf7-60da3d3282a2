{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Import python packages"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:54:44.763554Z", "start_time": "2025-03-01T03:54:44.757612Z"}}, "source": ["import pickle\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "\n", "from keras.models import Sequential\n", "from keras.layers import Dense, Reshape, Flatten\n", "from keras.layers import LSTM\n", "from keras.layers.convolutional import Convolution1D\n", "from keras.layers.convolutional import MaxPooling1D\n", "from keras.layers.embeddings import Embedding\n", "from keras.preprocessing import sequence\n", "from keras.callbacks import TensorBoard,ModelCheckpoint\n", "from keras.utils import to_categorical\n", "\n", "SEED = 7\n", "np.random.seed(SEED)"], "outputs": [], "execution_count": 60}, {"cell_type": "markdown", "metadata": {}, "source": ["# Define parameters and load dataset"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:54:45.940126Z", "start_time": "2025-03-01T03:54:45.914597Z"}}, "source": ["MODEL_NAME = \"CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32\"\n", "\n", "Word2Vec_MODEL_NAME = \"Word2Vec_No_Indexed_3_iters_5_negative_1_window\"  #################\n", "\n", "TEST_SIZE = 0.2\n", "TOR_LABELS_DICT = {'P2P':0, 'C2P': 1,'P2C': 2}\n", "class_names = ['P2P', 'C2P', 'P2C']\n", "num_classes = len(class_names)\n", "DATA_PATH = 'Data/'\n", "MODELS_PATH = 'Models/'\n", "RESULTS_PATH = 'Results/'\n", "\n", "DATA = \"caida_s1_tor\"\n", "dataset = np.load(DATA_PATH + DATA + \"_dataset.npy\")\n", "labels = np.load(DATA_PATH + DATA + \"_labels.npy\")\n", "\n", "print(dataset.shape, labels.shape)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(861704, 2) (861704,)\n"]}], "execution_count": 61}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:42:35.467595Z", "start_time": "2025-03-01T03:42:35.464850Z"}}, "source": ["print(dataset[0])"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['1' '11537']\n"]}], "execution_count": 52}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:54:48.534556Z", "start_time": "2025-03-01T03:54:48.172835Z"}}, "source": ["ASN_set = set()\n", "for tor in dataset:\n", "    ASN_set.add(tor[0])\n", "    ASN_set.add(tor[1])\n", "\n", "print(len(ASN_set))"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["60571\n"]}], "execution_count": 62}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:54:50.480310Z", "start_time": "2025-03-01T03:54:50.412769Z"}}, "source": ["from collections import Counter\n", "c_caida = Counter(labels)\n", "print(c_caida)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Counter({0: 619524, 2: 121090, 1: 121090})\n"]}], "execution_count": 63}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load Word2Vec embedding"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:42:42.108320Z", "start_time": "2025-03-01T03:42:41.900042Z"}}, "source": ["from gensim.models import Word2Vec\n", "word2vec_model = Word2Vec.load(MODELS_PATH + Word2Vec_MODEL_NAME + \".word2vec\")\n", "emdeddings = word2vec_model.wv.syn0\n", "total_ASNs, embedding_vecor_length = emdeddings.shape\n", "print(total_ASNs, embedding_vecor_length)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["60876 32\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/BGP2Vec/lib/python3.6/site-packages/ipykernel/__main__.py:3: DeprecationWarning: Call to deprecated `syn0` (Attribute will be removed in 4.0.0, use self.vectors instead).\n", "  app.launch_new_instance()\n"]}], "execution_count": 55}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate indexed data with fixed length"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:55:21.295896Z", "start_time": "2025-03-01T03:55:19.872337Z"}}, "source": ["def asn2idx(asn):\n", "    return word2vec_model.wv.vocab[asn].index\n", "def idx2asn(idx):\n", "    return word2vec_model.wv.index2word[idx]\n", "\n", "def dataset_asn2idx(dataset):\n", "    dataset_idx = np.zeros([len(dataset), 2], dtype=np.int32)\n", "\n", "    for i, pair in enumerate(dataset):\n", "            dataset_idx[i, 0] = asn2idx(pair[0])\n", "            dataset_idx[i, 1] = asn2idx(pair[1])\n", "    return dataset_idx\n", "\n", "def dataset_idx2asn(dataset_idx):\n", "    dataset_asn = np.zeros([len(dataset_idx), 2], dtype = np.dtype('U6'))\n", "\n", "    for i, pair in enumerate(dataset_idx):\n", "            dataset_asn[i, 0] = idx2asn(pair[0])\n", "            dataset_asn[i, 1] = idx2asn(pair[1])\n", "    return dataset_asn\n", "\n", "def dataset_asn2idx_skip_unknown(dataset):\n", "    \"\"\"\n", "    过滤并转换数据集，跳过包含未知ASN的对\n", "    Args:\n", "        dataset: [[asn1, asn2], ...]\n", "    Returns:\n", "        np.ndarray: 有效对的索引数组，形状 (N, 2)\n", "    \"\"\"\n", "    valid_pairs = []\n", "    skipped_count = 0\n", "    vocab = word2vec_model.wv.vocab  # Gensim 3.8.3的词汇表对象\n", "\n", "    for asn1, asn2 in dataset:\n", "        # 检查两个ASN是否均存在\n", "        if asn1 in vocab and asn2 in vocab:\n", "            idx1 = vocab[asn1].index\n", "            idx2 = vocab[asn2].index\n", "            valid_pairs.append([idx1, idx2])\n", "        else:\n", "            skipped_count += 1\n", "\n", "    print(f\"原始数据量: {len(dataset)}\")\n", "    print(f\"跳过无效对: {skipped_count}\")\n", "    print(f\"保留有效对: {len(valid_pairs)}\")\n", "    return np.array(valid_pairs, dtype=np.int32)\n", "\n", "dataset = dataset_asn2idx_skip_unknown(dataset)\n", "print(dataset.shape)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始数据量: 861704\n", "跳过无效对: 1756\n", "保留有效对: 859948\n", "(859948, 2)\n"]}], "execution_count": 64}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate training and test sets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Shuffle dataset"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:55:28.886497Z", "start_time": "2025-03-01T03:55:28.856032Z"}}, "source": ["from sklearn.utils import shuffle\n", "indices = np.arange(len(dataset))\n", "np.random.shuffle(indices)\n", "dataset = dataset[indices]\n", "labels = labels[indices]\n", "# dataset, labels = shuffle(dataset, labels, random_state=7)"], "outputs": [], "execution_count": 65}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train-Test split"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:55:31.309979Z", "start_time": "2025-03-01T03:55:31.274733Z"}}, "source": ["indices = np.arange(len(dataset))\n", "x_training, x_test, indices_training, indices_test = train_test_split(dataset, indices, test_size=TEST_SIZE)\n", "y_training = labels[indices_training]\n", "y_test = labels[indices_test]\n", "\n", "del dataset, labels"], "outputs": [], "execution_count": 66}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:55:32.942052Z", "start_time": "2025-03-01T03:55:32.875065Z"}}, "source": ["print(x_training.shape, y_training.shape)\n", "print(x_test.shape, y_test.shape)\n", "\n", "print(1.0*len(x_training)/(len(x_test)+len(x_training)))\n", "\n", "from collections import Counter\n", "training_c = Counter(y_training)\n", "test_c = Counter(y_test)\n", "print(training_c, test_c)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(687958, 2) (687958,)\n", "(171990, 2) (171990,)\n", "0.7999995348555959\n", "Counter({0: 495228, 2: 96404, 1: 96326}) Counter({0: 123874, 1: 24097, 2: 24019})\n"]}], "execution_count": 67}, {"cell_type": "markdown", "metadata": {}, "source": ["# Define and run NN model\n", "## Define model"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:55:35.934418Z", "start_time": "2025-03-01T03:55:35.837253Z"}}, "source": ["embedding_trainable = False\n", "\n", "input_length = 2\n", "\n", "model = Sequential()\n", "model.add(Embedding(total_ASNs, embedding_vecor_length, input_length=input_length,\n", "                    weights=[emdeddings], trainable=embedding_trainable))\n", "model.add(Convolution1D(filters=32, kernel_size=3, padding='same', activation='relu'))\n", "model.add(Reshape((model.output_shape[2],model.output_shape[1])))\n", "model.add(MaxPooling1D(pool_size=2))\n", "model.add(Convolution1D(filters=32, kernel_size=3, padding='same', activation='relu'))\n", "model.add(Reshape((model.output_shape[2],model.output_shape[1])))\n", "model.add(MaxPooling1D(pool_size=2))\n", "model.add(<PERSON><PERSON>())\n", "model.add(Dense(100, activation='relu'))\n", "model.add(Dense(num_classes, activation='softmax'))\n", "model.compile(loss='categorical_crossentropy', optimizer='adam', metrics=['accuracy'])\n", "print(model.summary())"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "embedding_2 (Embedding)      (None, 2, 32)             1948032   \n", "_________________________________________________________________\n", "conv1d_3 (Conv1D)            (None, 2, 32)             3104      \n", "_________________________________________________________________\n", "reshape_3 (Reshape)          (None, 32, 2)             0         \n", "_________________________________________________________________\n", "max_pooling1d_3 (MaxPooling1 (None, 16, 2)             0         \n", "_________________________________________________________________\n", "conv1d_4 (Conv1D)            (None, 16, 32)            224       \n", "_________________________________________________________________\n", "reshape_4 (Reshape)          (None, 32, 16)            0         \n", "_________________________________________________________________\n", "max_pooling1d_4 (MaxPooling1 (None, 16, 16)            0         \n", "_________________________________________________________________\n", "flatten_2 (<PERSON><PERSON>)          (None, 256)               0         \n", "_________________________________________________________________\n", "dense_3 (<PERSON><PERSON>)              (None, 100)               25700     \n", "_________________________________________________________________\n", "dense_4 (<PERSON><PERSON>)              (None, 3)                 303       \n", "=================================================================\n", "Total params: 1,977,363\n", "Trainable params: 29,331\n", "Non-trainable params: 1,948,032\n", "_________________________________________________________________\n", "None\n"]}], "execution_count": 68}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T02:39:51.620667Z", "start_time": "2025-03-01T02:39:51.592090Z"}}, "source": ["from keras.utils import plot_model\n", "plot_model(model, show_shapes=True,to_file=RESULTS_PATH + MODEL_NAME + \"_\" + 'model.png')"], "outputs": [{"ename": "Exception", "evalue": "\"dot\" not found in path.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m~/anaconda3/envs/BGP2Vec/lib/python3.6/site-packages/pydot.py\u001b[0m in \u001b[0;36mcreate\u001b[0;34m(self, prog, format)\u001b[0m\n\u001b[1;32m   1877\u001b[0m                 \u001b[0mshell\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mFalse\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1878\u001b[0;31m                 stderr=subprocess.PIPE, stdout=subprocess.PIPE)\n\u001b[0m\u001b[1;32m   1879\u001b[0m         \u001b[0;32mexcept\u001b[0m \u001b[0mOSError\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/BGP2Vec/lib/python3.6/subprocess.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, args, bufsize, executable, stdin, stdout, stderr, preexec_fn, close_fds, shell, cwd, env, universal_newlines, startupinfo, creationflags, restore_signals, start_new_session, pass_fds, encoding, errors)\u001b[0m\n\u001b[1;32m    708\u001b[0m                                 \u001b[0merrread\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0merrwrite\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 709\u001b[0;31m                                 restore_signals, start_new_session)\n\u001b[0m\u001b[1;32m    710\u001b[0m         \u001b[0;32mexcept\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/BGP2Vec/lib/python3.6/subprocess.py\u001b[0m in \u001b[0;36m_execute_child\u001b[0;34m(self, args, executable, preexec_fn, close_fds, pass_fds, cwd, env, startupinfo, creationflags, shell, p2cread, p2cwrite, c2pread, c2pwrite, errread, errwrite, restore_signals, start_new_session)\u001b[0m\n\u001b[1;32m   1343\u001b[0m                             \u001b[0merr_msg\u001b[0m \u001b[0;34m+=\u001b[0m \u001b[0;34m': '\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mrepr\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0merr_filename\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1344\u001b[0;31m                     \u001b[0;32mraise\u001b[0m \u001b[0mchild_exception_type\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0merrno_num\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0merr_msg\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0merr_filename\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1345\u001b[0m                 \u001b[0;32mraise\u001b[0m \u001b[0mchild_exception_type\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0merr_msg\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'dot': 'dot'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mException\u001b[0m                                 Traceback (most recent call last)", "\u001b[0;32m<ipython-input-12-c3db4ce0b85b>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0mkeras\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mutils\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mplot_model\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mplot_model\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmodel\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mshow_shapes\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mto_file\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mRESULTS_PATH\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mMODEL_NAME\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0;34m\"_\"\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0;34m'model.png'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m~/anaconda3/envs/BGP2Vec/lib/python3.6/site-packages/keras/utils/vis_utils.py\u001b[0m in \u001b[0;36mplot_model\u001b[0;34m(model, to_file, show_shapes, show_layer_names, rankdir)\u001b[0m\n\u001b[1;32m    130\u001b[0m             \u001b[0;34m'LR'\u001b[0m \u001b[0mcreates\u001b[0m \u001b[0ma\u001b[0m \u001b[0mhorizontal\u001b[0m \u001b[0mplot\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    131\u001b[0m     \"\"\"\n\u001b[0;32m--> 132\u001b[0;31m     \u001b[0mdot\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmodel_to_dot\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmodel\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mshow_shapes\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mshow_layer_names\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mrankdir\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    133\u001b[0m     \u001b[0m_\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mextension\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mos\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msplitext\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mto_file\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    134\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mextension\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/BGP2Vec/lib/python3.6/site-packages/keras/utils/vis_utils.py\u001b[0m in \u001b[0;36mmodel_to_dot\u001b[0;34m(model, show_shapes, show_layer_names, rankdir)\u001b[0m\n\u001b[1;32m     53\u001b[0m     \u001b[0;32mfrom\u001b[0m \u001b[0;34m.\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmodels\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mSequential\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     54\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 55\u001b[0;31m     \u001b[0m_check_pydot\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     56\u001b[0m     \u001b[0mdot\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpydot\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mDot\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     57\u001b[0m     \u001b[0mdot\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mset\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'rankdir'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mrankdir\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/BGP2Vec/lib/python3.6/site-packages/keras/utils/vis_utils.py\u001b[0m in \u001b[0;36m_check_pydot\u001b[0;34m()\u001b[0m\n\u001b[1;32m     24\u001b[0m         \u001b[0;31m# Attempt to create an image of a blank graph\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     25\u001b[0m         \u001b[0;31m# to check the pydot/graphviz installation.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 26\u001b[0;31m         \u001b[0mpydot\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mDot\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcreate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpydot\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mDot\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     27\u001b[0m     \u001b[0;32mexcept\u001b[0m \u001b[0mOSError\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     28\u001b[0m         raise OSError(\n", "\u001b[0;32m~/anaconda3/envs/BGP2Vec/lib/python3.6/site-packages/pydot.py\u001b[0m in \u001b[0;36mcreate\u001b[0;34m(self, prog, format)\u001b[0m\n\u001b[1;32m   1881\u001b[0m                 raise Exception(\n\u001b[1;32m   1882\u001b[0m                     '\"{prog}\" not found in path.'.format(\n\u001b[0;32m-> 1883\u001b[0;31m                         prog=prog))\n\u001b[0m\u001b[1;32m   1884\u001b[0m             \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1885\u001b[0m                 \u001b[0;32mraise\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mException\u001b[0m: \"dot\" not found in path."]}], "execution_count": 12}, {"cell_type": "markdown", "metadata": {}, "source": ["## convert class vectors to binary class matrices"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:55:39.613599Z", "start_time": "2025-03-01T03:55:39.605801Z"}}, "source": ["y_training_vector = to_categorical(y_training, num_classes)\n", "y_test_vector = to_categorical(y_test, num_classes)"], "outputs": [], "execution_count": 69}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use class_weight to deal with unbalanced dataset"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:55:41.459105Z", "start_time": "2025-03-01T03:55:41.456727Z"}}, "source": ["from sklearn.utils import class_weight\n", "\n", "# class_weights = class_weight.compute_class_weight('balanced', list(range(num_classes)), y_training)\n", "# print(class_weights)\n", "class_weights = None"], "outputs": [], "execution_count": 70}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fit model"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:55:43.411221Z", "start_time": "2025-03-01T03:55:43.407984Z"}}, "source": ["checkpointer_loss = ModelCheckpoint(filepath= MODELS_PATH + MODEL_NAME + '_loss.hdf5', verbose=1, save_best_only=True, save_weights_only=True)\n", "checkpointer_acc = ModelCheckpoint(monitor='val_acc', filepath= MODELS_PATH + MODEL_NAME + '_acc.hdf5', verbose=1, save_best_only=True, save_weights_only=True)\n", "\n", "callbacks = [checkpointer_loss,checkpointer_acc] #tensorboard"], "outputs": [], "execution_count": 71}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T03:55:45.071430Z", "start_time": "2025-03-01T03:55:45.059511Z"}}, "source": ["import math\n", "\n", "epochs = 40 #################################   3  ##########\n", "\n", "batch_size = 64\n", "\n", "steps_per_epoch = math.ceil(len(x_training)/batch_size)\n", "\n", "val_batch_size = 1024\n", "\n", "validation_steps = math.ceil(len(x_test)/val_batch_size)\n", "\n", "def val_generator(features, labels, val_batch_size):\n", "    index = 0\n", "    while True:\n", "        index += val_batch_size\n", "        batch_features, batch_labels = features[index-val_batch_size:index], labels[index-val_batch_size:index]\n", "        if index >= len(features):\n", "            index = 0\n", "        yield batch_features, batch_labels\n", "        \n", "def generator(features, labels, batch_size):\n", "    index = 0\n", "    while True:\n", "        index += batch_size\n", "        if index >= len(features):\n", "            batch_features = np.append(features[index-batch_size:len(features)], features[0:index-len(features)], axis=0)\n", "            batch_labels = np.append(labels[index-batch_size:len(features)], labels[0:index-len(features)], axis=0)\n", "            index -= len(features)\n", "            yield batch_features, batch_labels\n", "        else:\n", "            yield features[index-batch_size:index], labels[index-batch_size:index]"], "outputs": [], "execution_count": 72}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:45:18.208283Z", "start_time": "2025-03-01T03:55:48.401629Z"}}, "source": ["history = model.fit_generator(val_generator(x_training, y_training_vector, batch_size), steps_per_epoch=steps_per_epoch,\n", "                        epochs=epochs, callbacks=callbacks, class_weight=class_weights,\n", "                        validation_data=val_generator(x_test, y_test_vector,val_batch_size),\n", "                        validation_steps=validation_steps)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/40\n", "10750/10750 [==============================] - 230s 21ms/step - loss: 0.5826 - acc: 0.7899 - val_loss: 0.5588 - val_acc: 0.7968\n", "\n", "Epoch 00001: val_loss improved from inf to 0.55879, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00001: val_acc improved from -inf to 0.79685, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 2/40\n", "10750/10750 [==============================] - 164s 15ms/step - loss: 0.5490 - acc: 0.7992 - val_loss: 0.5434 - val_acc: 0.7999\n", "\n", "Epoch 00002: val_loss improved from 0.55879 to 0.54341, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00002: val_acc improved from 0.79685 to 0.79994, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 3/40\n", "10750/10750 [==============================] - 146s 14ms/step - loss: 0.5357 - acc: 0.8021 - val_loss: 0.5357 - val_acc: 0.8023\n", "\n", "Epoch 00003: val_loss improved from 0.54341 to 0.53568, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00003: val_acc improved from 0.79994 to 0.80226, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 4/40\n", "10750/10750 [==============================] - 143s 13ms/step - loss: 0.5271 - acc: 0.8040 - val_loss: 0.5287 - val_acc: 0.8036\n", "\n", "Epoch 00004: val_loss improved from 0.53568 to 0.52871, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00004: val_acc improved from 0.80226 to 0.80359, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 5/40\n", "10750/10750 [==============================] - 135s 13ms/step - loss: 0.5206 - acc: 0.8056 - val_loss: 0.5241 - val_acc: 0.8047\n", "\n", "Epoch 00005: val_loss improved from 0.52871 to 0.52409, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00005: val_acc improved from 0.80359 to 0.80468, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 6/40\n", "10750/10750 [==============================] - 136s 13ms/step - loss: 0.5158 - acc: 0.8065 - val_loss: 0.5212 - val_acc: 0.8053\n", "\n", "Epoch 00006: val_loss improved from 0.52409 to 0.52123, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00006: val_acc improved from 0.80468 to 0.80527, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 7/40\n", "10750/10750 [==============================] - 155s 14ms/step - loss: 0.5114 - acc: 0.8076 - val_loss: 0.5189 - val_acc: 0.8058\n", "\n", "Epoch 00007: val_loss improved from 0.52123 to 0.51890, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00007: val_acc improved from 0.80527 to 0.80581, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 8/40\n", "10750/10750 [==============================] - 158s 15ms/step - loss: 0.5080 - acc: 0.8085 - val_loss: 0.5176 - val_acc: 0.8059\n", "\n", "Epoch 00008: val_loss improved from 0.51890 to 0.51762, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00008: val_acc improved from 0.80581 to 0.80590, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 9/40\n", "10750/10750 [==============================] - 155s 14ms/step - loss: 0.5050 - acc: 0.8094 - val_loss: 0.5169 - val_acc: 0.8061\n", "\n", "Epoch 00009: val_loss improved from 0.51762 to 0.51693, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00009: val_acc improved from 0.80590 to 0.80612, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 10/40\n", "10750/10750 [==============================] - 136s 13ms/step - loss: 0.5026 - acc: 0.8098 - val_loss: 0.5151 - val_acc: 0.8066\n", "\n", "Epoch 00010: val_loss improved from 0.51693 to 0.51509, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00010: val_acc improved from 0.80612 to 0.80665, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 11/40\n", "10750/10750 [==============================] - 168s 16ms/step - loss: 0.5003 - acc: 0.8106 - val_loss: 0.5137 - val_acc: 0.8068\n", "\n", "Epoch 00011: val_loss improved from 0.51509 to 0.51368, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00011: val_acc improved from 0.80665 to 0.80680, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 12/40\n", "10750/10750 [==============================] - 196s 18ms/step - loss: 0.4982 - acc: 0.8111 - val_loss: 0.5125 - val_acc: 0.8075\n", "\n", "Epoch 00012: val_loss improved from 0.51368 to 0.51253, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00012: val_acc improved from 0.80680 to 0.80751, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 13/40\n", "10750/10750 [==============================] - 157s 15ms/step - loss: 0.4963 - acc: 0.8115 - val_loss: 0.5121 - val_acc: 0.8074\n", "\n", "Epoch 00013: val_loss improved from 0.51253 to 0.51214, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00013: val_acc did not improve from 0.80751\n", "Epoch 14/40\n", "10750/10750 [==============================] - 150s 14ms/step - loss: 0.4943 - acc: 0.8121 - val_loss: 0.5114 - val_acc: 0.8080\n", "\n", "Epoch 00014: val_loss improved from 0.51214 to 0.51137, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00014: val_acc improved from 0.80751 to 0.80801, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 15/40\n", "10750/10750 [==============================] - 149s 14ms/step - loss: 0.4927 - acc: 0.8128 - val_loss: 0.5106 - val_acc: 0.8082\n", "\n", "Epoch 00015: val_loss improved from 0.51137 to 0.51057, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00015: val_acc improved from 0.80801 to 0.80817, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 16/40\n", "10750/10750 [==============================] - 149s 14ms/step - loss: 0.4912 - acc: 0.8131 - val_loss: 0.5104 - val_acc: 0.8085\n", "\n", "Epoch 00016: val_loss improved from 0.51057 to 0.51035, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00016: val_acc improved from 0.80817 to 0.80852, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 17/40\n", "10750/10750 [==============================] - 149s 14ms/step - loss: 0.4900 - acc: 0.8131 - val_loss: 0.5099 - val_acc: 0.8082\n", "\n", "Epoch 00017: val_loss improved from 0.51035 to 0.50993, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00017: val_acc did not improve from 0.80852\n", "Epoch 18/40\n", "10750/10750 [==============================] - 169s 16ms/step - loss: 0.4886 - acc: 0.8136 - val_loss: 0.5105 - val_acc: 0.8083\n", "\n", "Epoch 00018: val_loss did not improve from 0.50993\n", "\n", "Epoch 00018: val_acc did not improve from 0.80852\n", "Epoch 19/40\n", "10750/10750 [==============================] - 197s 18ms/step - loss: 0.4875 - acc: 0.8139 - val_loss: 0.5104 - val_acc: 0.8079\n", "\n", "Epoch 00019: val_loss did not improve from 0.50993\n", "\n", "Epoch 00019: val_acc did not improve from 0.80852\n", "Epoch 20/40\n", "10750/10750 [==============================] - 182s 17ms/step - loss: 0.4862 - acc: 0.8141 - val_loss: 0.5098 - val_acc: 0.8081\n", "\n", "Epoch 00020: val_loss improved from 0.50993 to 0.50975, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00020: val_acc did not improve from 0.80852\n", "Epoch 21/40\n", "10750/10750 [==============================] - 184s 17ms/step - loss: 0.4851 - acc: 0.8146 - val_loss: 0.5094 - val_acc: 0.8079\n", "\n", "Epoch 00021: val_loss improved from 0.50975 to 0.50944, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00021: val_acc did not improve from 0.80852\n", "Epoch 22/40\n", "10750/10750 [==============================] - 180s 17ms/step - loss: 0.4841 - acc: 0.8146 - val_loss: 0.5101 - val_acc: 0.8080\n", "\n", "Epoch 00022: val_loss did not improve from 0.50944\n", "\n", "Epoch 00022: val_acc did not improve from 0.80852\n", "Epoch 23/40\n", "10750/10750 [==============================] - 165s 15ms/step - loss: 0.4832 - acc: 0.8149 - val_loss: 0.5090 - val_acc: 0.8080\n", "\n", "Epoch 00023: val_loss improved from 0.50944 to 0.50905, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00023: val_acc did not improve from 0.80852\n", "Epoch 24/40\n", "10750/10750 [==============================] - 160s 15ms/step - loss: 0.4825 - acc: 0.8151 - val_loss: 0.5093 - val_acc: 0.8078\n", "\n", "Epoch 00024: val_loss did not improve from 0.50905\n", "\n", "Epoch 00024: val_acc did not improve from 0.80852\n", "Epoch 25/40\n", "10750/10750 [==============================] - 155s 14ms/step - loss: 0.4817 - acc: 0.8153 - val_loss: 0.5095 - val_acc: 0.8085\n", "\n", "Epoch 00025: val_loss did not improve from 0.50905\n", "\n", "Epoch 00025: val_acc did not improve from 0.80852\n", "Epoch 26/40\n", "10750/10750 [==============================] - 171s 16ms/step - loss: 0.4812 - acc: 0.8153 - val_loss: 0.5087 - val_acc: 0.8080\n", "\n", "Epoch 00026: val_loss improved from 0.50905 to 0.50873, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00026: val_acc did not improve from 0.80852\n", "Epoch 27/40\n", "10750/10750 [==============================] - 152s 14ms/step - loss: 0.4803 - acc: 0.8157 - val_loss: 0.5085 - val_acc: 0.8083\n", "\n", "Epoch 00027: val_loss improved from 0.50873 to 0.50846, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00027: val_acc did not improve from 0.80852\n", "Epoch 28/40\n", "10750/10750 [==============================] - 162s 15ms/step - loss: 0.4796 - acc: 0.8159 - val_loss: 0.5090 - val_acc: 0.8078\n", "\n", "Epoch 00028: val_loss did not improve from 0.50846\n", "\n", "Epoch 00028: val_acc did not improve from 0.80852\n", "Epoch 29/40\n", "10750/10750 [==============================] - 178s 17ms/step - loss: 0.4790 - acc: 0.8162 - val_loss: 0.5090 - val_acc: 0.8080\n", "\n", "Epoch 00029: val_loss did not improve from 0.50846\n", "\n", "Epoch 00029: val_acc did not improve from 0.80852\n", "Epoch 30/40\n", "10750/10750 [==============================] - 188s 18ms/step - loss: 0.4783 - acc: 0.8161 - val_loss: 0.5088 - val_acc: 0.8085\n", "\n", "Epoch 00030: val_loss did not improve from 0.50846\n", "\n", "Epoch 00030: val_acc did not improve from 0.80852\n", "Epoch 31/40\n", "10750/10750 [==============================] - 176s 16ms/step - loss: 0.4777 - acc: 0.8164 - val_loss: 0.5088 - val_acc: 0.8081\n", "\n", "Epoch 00031: val_loss did not improve from 0.50846\n", "\n", "Epoch 00031: val_acc did not improve from 0.80852\n", "Epoch 32/40\n", "10750/10750 [==============================] - 181s 17ms/step - loss: 0.4772 - acc: 0.8166 - val_loss: 0.5089 - val_acc: 0.8080\n", "\n", "Epoch 00032: val_loss did not improve from 0.50846\n", "\n", "Epoch 00032: val_acc did not improve from 0.80852\n", "Epoch 33/40\n", "10750/10750 [==============================] - 181s 17ms/step - loss: 0.4765 - acc: 0.8168 - val_loss: 0.5094 - val_acc: 0.8077\n", "\n", "Epoch 00033: val_loss did not improve from 0.50846\n", "\n", "Epoch 00033: val_acc did not improve from 0.80852\n", "Epoch 34/40\n", "10750/10750 [==============================] - 168s 16ms/step - loss: 0.4760 - acc: 0.8171 - val_loss: 0.5090 - val_acc: 0.8089\n", "\n", "Epoch 00034: val_loss did not improve from 0.50846\n", "\n", "Epoch 00034: val_acc improved from 0.80852 to 0.80891, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_acc.hdf5\n", "Epoch 35/40\n", "10750/10750 [==============================] - 165s 15ms/step - loss: 0.4755 - acc: 0.8170 - val_loss: 0.5083 - val_acc: 0.8080\n", "\n", "Epoch 00035: val_loss improved from 0.50846 to 0.50831, saving model to Models/CAIDA_s1_ToR_Classification_with_NN_Based_Word2Vec_32_loss.hdf5\n", "\n", "Epoch 00035: val_acc did not improve from 0.80891\n", "Epoch 36/40\n", "10750/10750 [==============================] - 171s 16ms/step - loss: 0.4750 - acc: 0.8170 - val_loss: 0.5086 - val_acc: 0.8082\n", "\n", "Epoch 00036: val_loss did not improve from 0.50831\n", "\n", "Epoch 00036: val_acc did not improve from 0.80891\n", "Epoch 37/40\n", "10750/10750 [==============================] - 155s 14ms/step - loss: 0.4745 - acc: 0.8171 - val_loss: 0.5092 - val_acc: 0.8082\n", "\n", "Epoch 00037: val_loss did not improve from 0.50831\n", "\n", "Epoch 00037: val_acc did not improve from 0.80891\n", "Epoch 38/40\n", "10750/10750 [==============================] - 151s 14ms/step - loss: 0.4740 - acc: 0.8173 - val_loss: 0.5094 - val_acc: 0.8074\n", "\n", "Epoch 00038: val_loss did not improve from 0.50831\n", "\n", "Epoch 00038: val_acc did not improve from 0.80891\n", "Epoch 39/40\n", "10750/10750 [==============================] - 151s 14ms/step - loss: 0.4736 - acc: 0.8175 - val_loss: 0.5093 - val_acc: 0.8075\n", "\n", "Epoch 00039: val_loss did not improve from 0.50831\n", "\n", "Epoch 00039: val_acc did not improve from 0.80891\n", "Epoch 40/40\n", "10750/10750 [==============================] - 151s 14ms/step - loss: 0.4730 - acc: 0.8177 - val_loss: 0.5086 - val_acc: 0.8081\n", "\n", "Epoch 00040: val_loss did not improve from 0.50831\n", "\n", "Epoch 00040: val_acc did not improve from 0.80891\n"]}], "execution_count": 73}, {"cell_type": "markdown", "metadata": {}, "source": ["# Plot history accuracy"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:45:30.081541Z", "start_time": "2025-03-01T05:45:29.787874Z"}}, "source": ["import matplotlib.pyplot as plt\n", "import pickle\n", "%matplotlib inline\n", "\n", "def smooth(y, box_pts):\n", "    box = np.ones(box_pts)/box_pts\n", "    y_smooth = np.convolve(y, box, mode='same')\n", "    return y_smooth\n", "\n", "\n", "with open(RESULTS_PATH + MODEL_NAME +  \"_accuracy.pkl\", 'wb') as output:\n", "    pickle.dump(history.history, output, pickle.HIGHEST_PROTOCOL)\n", "\n", "# list all data in history\n", "print(history.history.keys())\n", "x = np.asarray(range(1,epochs + 1))\n", "# summarize history for accuracy\n", "plt.figure()\n", "plt.plot(x, smooth([y*100 for y in history.history['acc']],2))\n", "# plt.plot(x, [y*100 for y in history_history['val_acc']])\n", "plt.plot(x, smooth([y*100 for y in history.history['val_acc']],2))\n", "plt.ylabel('Accuracy (%)')\n", "plt.xlabel('Epochs')\n", "plt.ylim(70,100) ###########################\n", "plt.legend(['Training', 'Test'], loc='lower right')\n", "plt.grid()\n", "plt.savefig(RESULTS_PATH + MODEL_NAME +  \" accuracy history\", bbox_inches='tight')\n", "plt.show()"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['val_loss', 'val_acc', 'loss', 'acc'])\n"]}, {"data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAYgAAAEKCAYAAAAIO8L1AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nWEoQIiIS6/8DABNyXcqKfQYAAAAASUVORK5CYII=\n"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 74}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2018-10-20T11:24:40.580036Z", "start_time": "2018-10-20T11:24:33.351927Z"}}, "source": ["# Final evaluation of the model\n", "## Evaluate accuracy over the test set"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:45:33.912399Z", "start_time": "2025-03-01T05:45:33.860867Z"}}, "source": ["model.load_weights(MODELS_PATH + MODEL_NAME + '_acc.hdf5')"], "outputs": [], "execution_count": 75}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:45:36.748866Z", "start_time": "2025-03-01T05:45:35.330624Z"}}, "source": ["test_scores = model.evaluate(x_test, y_test_vector, batch_size=val_batch_size, verbose=1)\n", "    \n", "    \n", "print(\"Accuracy: %.2f%%\" % (test_scores[1]*100))"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["171990/171990 [==============================] - 1s 8us/step\n", "Accuracy: 80.89%\n"]}], "execution_count": 76}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:45:40.587785Z", "start_time": "2025-03-01T05:45:39.083553Z"}}, "source": ["y_test_prediction = model.predict_classes(x_test, batch_size=val_batch_size, verbose=1)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["171990/171990 [==============================] - 2s 9us/step\n"]}], "execution_count": 77}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:45:47.949211Z", "start_time": "2025-03-01T05:45:42.550765Z"}}, "source": ["y_training_prediction = model.predict_classes(x_training, batch_size=val_batch_size, verbose=1)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["687958/687958 [==============================] - 5s 8us/step\n"]}], "execution_count": 78}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test if by learning (asn1, asn2) -> p2c then (asn2, asn1) -> c2p and vice versa"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:45:51.778851Z", "start_time": "2025-03-01T05:45:51.530084Z"}}, "source": ["p2c = 2\n", "c2p = 1\n", "\n", "p2c_training = np.asarray([np.asarray(x_training[i]) for i in range(len(x_training)) if y_training[i] == p2c])\n", "p2c_training_oposite = np.asarray([np.asarray([pair[1], pair[0]]) for pair in p2c_training])\n", "\n", "p2c_training_label_vector = to_categorical([p2c]*len(p2c_training), num_classes)\n", "p2c_training_oposite_label_vector = to_categorical([c2p]*len(p2c_training_oposite), num_classes)\n", "\n", "print(p2c_training.shape, p2c_training_oposite.shape, p2c_training_oposite_label_vector.shape)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(96404, 2) (96404, 2) (96404, 3)\n"]}], "execution_count": 79}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:46:28.517549Z", "start_time": "2025-03-01T05:45:54.601114Z"}}, "source": ["p2c_training_scores = model.evaluate(p2c_training, p2c_training_label_vector, verbose=1)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_scores[1]*100))\n", "\n", "p2c_training_oposite_scores = model.evaluate(p2c_training_oposite, p2c_training_oposite_label_vector, verbose=1)\n", "print(\"Accuracy: %.2f%%\" % (p2c_training_oposite_scores[1]*100))"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["96404/96404 [==============================] - 17s 175us/step\n", "Accuracy: 45.22%\n", "96404/96404 [==============================] - 17s 177us/step\n", "Accuracy: 46.86%\n"]}], "execution_count": 80}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test if by learning (asn1, asn2) -> p2p then (asn2, asn1) -> p2p and vice versa"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:46:32.252547Z", "start_time": "2025-03-01T05:46:31.191203Z"}}, "source": ["p2p = 0\n", "\n", "p2p_training = np.asarray([np.asarray(x_training[i]) for i in range(len(x_training)) if y_training[i] == p2p])\n", "p2p_training_oposite = np.asarray([np.asarray([pair[1], pair[0]]) for pair in p2p_training])\n", "\n", "p2p_training_label_vector = to_categorical([p2p]*len(p2p_training), num_classes)\n", "p2p_training_oposite_label_vector = to_categorical([p2p]*len(p2p_training_oposite), num_classes)"], "outputs": [], "execution_count": 81}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:49:23.750404Z", "start_time": "2025-03-01T05:46:34.238691Z"}}, "source": ["p2p_training_scores = model.evaluate(p2p_training, p2p_training_label_vector, verbose=1)\n", "print(\"Accuracy: %.2f%%\" % (p2p_training_scores[1]*100))\n", "\n", "p2p_training_oposite_scores = model.evaluate(p2p_training_oposite, p2p_training_oposite_label_vector, verbose=1)\n", "print(\"Accuracy: %.2f%%\" % (p2p_training_oposite_scores[1]*100))"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["495228/495228 [==============================] - 86s 173us/step\n", "Accuracy: 95.45%\n", "495228/495228 [==============================] - 84s 169us/step\n", "Accuracy: 95.37%\n"]}], "execution_count": 82}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot and save a confusion matrix for results over the test set"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define a function"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:49:33.702550Z", "start_time": "2025-03-01T05:49:33.674324Z"}}, "source": ["%matplotlib inline\n", "\n", "import matplotlib\n", "import pylab as pl\n", "from sklearn.metrics import confusion_matrix\n", "import itertools\n", "import matplotlib.pyplot as plt\n", "import matplotlib.cm as cm\n", "\n", "def plot_confusion_matrix(cm, classes,\n", "                          normalize=False,\n", "                          title='Confusion matrix',\n", "                          fname='Confusion matrix',\n", "                          cmap=plt.cm.Blues):\n", "    \"\"\"\n", "    This function prints and plots the confusion matrix.\n", "    Normalization can be applied by setting `normalize=True`.\n", "    \"\"\"\n", "    if normalize:\n", "        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "        print(\"Normalized confusion matrix\")\n", "        plt.imshow([[100*j for j in i] for i in cm], interpolation='nearest', cmap=cmap)\n", "        cbar = plt.colorbar()\n", "        cbar.ax.set_yticklabels(['0%','20%','40%','60%','80%','100%'])\n", "    else:\n", "        print('Confusion matrix, without normalization')\n", "        plt.imshow(cm, interpolation='nearest', cmap=cmap)\n", "        plt.colorbar()\n", "    \n", "    tick_marks = np.arange(len(classes))\n", "    plt.xticks(tick_marks, classes, rotation=45)\n", "    plt.yticks(tick_marks, classes)\n", "\n", "    fmt = '.1f' if normalize else 'd'\n", "    thresh = cm.max() / 2.\n", "    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):\n", "        if normalize:\n", "            plt.text(j, i, format(cm[i, j]*100, fmt) + '%',\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")\n", "        else:\n", "            plt.text(j, i, format(cm[i, j], fmt),\n", "                 horizontalalignment=\"center\",\n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")    \n", "        \n", "    plt.tight_layout()\n", "    plt.ylabel('True Label')\n", "    plt.xlabel('Predicted Label')\n", "    plt.savefig(fname, bbox_inches='tight')"], "outputs": [], "execution_count": 83}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:49:37.527030Z", "start_time": "2025-03-01T05:49:37.221087Z"}}, "source": ["# Compute confusion matrix\n", "cnf_matrix = confusion_matrix(y_test, y_test_prediction)\n", "np.set_printoptions(precision=2)\n", "\n", "# Plot non-normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names,\n", "                      title='Confusion matrix, without normalization',\n", "                      fname=RESULTS_PATH + MODEL_NAME + \"_\" + 'Confusion_matrix_without_normalization')\n", "\n", "# Plot normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names, normalize=True,\n", "                      title='Normalized Confusion Msatrix',\n", "                      fname=RESULTS_PATH +MODEL_NAME + \"_\" + 'Normalized_confusion_matrix')\n", "\n", "plt.show()"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Confusion matrix, without normalization\n", "Normalized confusion matrix\n"]}, {"data": {"text/plain": ["<Figure size 432x288 with 2 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAW8AAAEmCAYAAACtaxGwAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 432x288 with 2 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAV8AAAEmCAYAAADFmJOIAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 84}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot cm for training set"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:49:41.313429Z", "start_time": "2025-03-01T05:49:40.814142Z"}}, "source": ["# Compute confusion matrix\n", "cnf_matrix = confusion_matrix(y_training, y_training_prediction)\n", "np.set_printoptions(precision=2)\n", "\n", "# Plot non-normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names,\n", "                      title='Confusion matrix, without normalization',\n", "                      fname=RESULTS_PATH + MODEL_NAME + \"_\" + 'training_Confusion_matrix_without_normalization')\n", "\n", "# Plot normalized confusion matrix\n", "plt.figure()\n", "plot_confusion_matrix(cnf_matrix, classes=class_names, normalize=True,\n", "                      title='Normalized Confusion Matrix',\n", "                      fname=RESULTS_PATH +MODEL_NAME + \"_\" + 'training_Normalized_confusion_matrix')\n", "\n", "plt.show()"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Confusion matrix, without normalization\n", "Normalized confusion matrix\n"]}, {"data": {"text/plain": ["<Figure size 432x288 with 2 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAW8AAAEmCAYAAACtaxGwAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\ngyx4G2OMB1nwNsYYD7LgbYwxHvT/gyUy8mutHZMAAAAASUVORK5CYII=\n"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 432x288 with 2 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAV8AAAEmCAYAAADFmJOIAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 85}, {"cell_type": "markdown", "metadata": {}, "source": ["# Export the model to a file"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:49:48.937796Z", "start_time": "2025-03-01T05:49:48.893840Z"}}, "source": ["model_json = model.to_json()\n", "with open(MODELS_PATH + MODEL_NAME + '.json', \"w\") as json_file:\n", "    json_file.write(model_json)\n", "model.save_weights(MODELS_PATH + MODEL_NAME + '.h5')\n", "print(\"Save Model\")"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Save Model\n"]}], "execution_count": 86}, {"cell_type": "markdown", "metadata": {}, "source": ["# Export results to a csv file (with original ASNs)\n", "## Define functions"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:50:02.592923Z", "start_time": "2025-03-01T05:50:02.531508Z"}}, "source": ["def dataset_idx2asn(dataset, indexes=None):\n", "    dataset_asn = []\n", "    if indexes is not None:\n", "        dataset = np.take(dataset, indexes, axis=0)\n", "\n", "    for i, route in enumerate(dataset):\n", "        route_asn = []\n", "        for idx in route:\n", "            route_asn.append(idx2asn(idx))\n", "        dataset_asn.append(route_asn) \n", "    return dataset_asn\n", "\n", "def dataset_idx2asn_labels(dataset, labels, class_names=class_names, indexes=None):\n", "    dataset_asn = []\n", "    if indexes is not None:\n", "        dataset = np.take(dataset, indexes, axis=0)\n", "        labels = np.take(labels, indexes, axis=0)\n", "    \n", "    for i, route in enumerate(dataset):\n", "        route_asn = []\n", "        for idx in route:\n", "            route_asn.append(idx2asn(idx))\n", "        route_asn.append(class_names[labels[i]])\n", "        dataset_asn.append(route_asn) \n", "    return dataset_asn\n", "\n", "\n", "def dataset_idx2asn_labels_predictions(dataset, labels, predictions, class_names=class_names, indexes=None):\n", "    dataset_asn = []\n", "    if indexes is not None:\n", "        dataset = np.take(dataset, indexes, axis=0)\n", "        labels = np.take(labels, indexes, axis=0)\n", "        predictions = np.take(predictions, indexes, axis=0)\n", "    \n", "    for i, route in enumerate(dataset):\n", "        route_asn = []\n", "        for idx in route:\n", "            route_asn.append(idx2asn(idx))\n", "        route_asn.append(class_names[labels[i]])\n", "        route_asn.append(class_names[predictions[i]])\n", "        dataset_asn.append(route_asn) \n", "    return dataset_asn\n", "\n", "def dataset_idx2asn_labels_predictions_prob(dataset, labels, predictions, prob, class_names=class_names, indexes=None):\n", "    dataset_asn = []\n", "    if indexes is not None:\n", "        dataset = np.take(dataset, indexes, axis=0)\n", "        labels = np.take(labels, indexes, axis=0)\n", "        predictions = np.take(predictions, indexes, axis=0)\n", "        prob = np.take(prob, indexes, axis=0)\n", "    \n", "    for i, route in enumerate(dataset):\n", "        route_asn = []\n", "        for idx in route:\n", "            route_asn.append(idx2asn(idx))\n", "        route_asn.append(class_names[labels[i]])\n", "        route_asn.append(class_names[predictions[i]])\n", "        for p in prob[i]:\n", "            route_asn.append(round(p,4))\n", "        \n", "        dataset_asn.append(route_asn) \n", "    return dataset_asn\n", "\n", "import csv\n", "def export_csv(dataset, csv_name, header_type='with_gt'):\n", "    header_types = {\n", "        'reg': [\"AS1\", \"AS2\", \"Label\", \"Prediction\", \"P2P_Prob\", \"C2P_Prob\", \"P2C_Prob\"],\n", "        'with_gt': [\"AS1\", \"AS2\", \"Label\", 'GT', \"Prediction\", \"P2P_Prob\", \"C2P_Prob\", \"P2C_Prob\"]\n", "    }\n", "    \n", "    with open(csv_name + '.csv', 'w') as csv_file:\n", "        csv_writer = csv.writer(csv_file)\n", "        if header_type is not None:\n", "            csv_writer.writerow(header_types[header_type])\n", "        for row in dataset:\n", "            csv_writer.writerow(row)"], "outputs": [], "execution_count": 87}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load a relevant dataset {all, misclassified, decided, undecided} and get model predictions"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:50:09.533741Z", "start_time": "2025-03-01T05:50:08.099689Z"}}, "source": ["y_test_prob = model.predict_proba(x_test, batch_size=val_batch_size, verbose=1)\n", "# y_training_prob = model.predict_proba(x_training, batch_size=val_batch_size, verbose=1)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["171990/171990 [==============================] - 1s 8us/step\n"]}], "execution_count": 88}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:50:12.108416Z", "start_time": "2025-03-01T05:50:11.970468Z"}}, "source": ["# # Create test misclassified dataset\n", "x_test_misclassified = np.asarray([route for i,route in enumerate(x_test) if y_test[i] != y_test_prediction[i]])\n", "y_test_misclassified_prediction = np.asarray([label for i,label in enumerate(y_test_prediction) if y_test[i] != y_test_prediction[i]])\n", "y_test_misclassified = np.asarray([label for i,label in enumerate(y_test) if y_test[i] != y_test_prediction[i]])\n", "y_test_misclassified_prob = np.asarray([prob for i,prob in enumerate(y_test_prob) if y_test[i] != y_test_prediction[i]])\n", "\n", "print(len(x_test_misclassified), len(y_test_misclassified_prediction), len(y_test_misclassified))\n", "print(y_test_misclassified_prob.shape)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["32865 32865 32865\n", "(32865, 3)\n"]}], "execution_count": 89}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:50:15.162422Z", "start_time": "2025-03-01T05:50:14.869346Z"}}, "source": ["dataset_test_misclassified = dataset_idx2asn_labels_predictions_prob(x_test_misclassified, y_test_misclassified,\n", "                                        y_test_misclassified_prediction, y_test_misclassified_prob)\n", "export_csv(dataset_test_misclassified, RESULTS_PATH + MODEL_NAME + \"_test_misclassified\")"], "outputs": [], "execution_count": 90}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:50:17.033064Z", "start_time": "2025-03-01T05:50:17.022617Z"}}, "source": ["y_test_misclassified_prob_max = np.array([y_test_misclassified_prob[i][pred] for i, pred in enumerate(y_test_misclassified_prediction)])\n", "print(y_test_misclassified_prob_max[:10])"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.94 0.65 0.69 0.67 0.61 0.91 0.68 0.66 0.97 0.68]\n"]}], "execution_count": 91}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-01T05:50:20.823766Z", "start_time": "2025-03-01T05:50:20.790176Z"}}, "source": ["indexes = np.array(list(range(len(x_test_misclassified))))\n", "indexes = [x for _, x in sorted(zip(y_test_misclassified_prob_max,indexes), reverse=True)]\n", "print(indexes[:100])\n", "print(y_test_misclassified_prob_max[indexes[0]])"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[31785, 19361, 4145, 15133, 28591, 15351, 17368, 5707, 6392, 12956, 20258, 25595, 24878, 2045, 22104, 8033, 17478, 23644, 8998, 23392, 17871, 16853, 18542, 17916, 3200, 23549, 31399, 32506, 30320, 30499, 13002, 17540, 9007, 30506, 27102, 11666, 32298, 10838, 2043, 9647, 8356, 18204, 30068, 12520, 16815, 159, 30773, 5540, 29052, 13744, 1168, 12523, 18043, 487, 12046, 21284, 11380, 29853, 16549, 32498, 22958, 2704, 27756, 14367, 33, 13912, 8541, 25619, 11467, 26941, 20384, 24531, 3922, 16284, 7449, 5555, 24384, 21262, 13170, 28087, 19832, 24529, 25236, 24312, 14619, 32672, 14588, 11841, 9953, 18767, 11174, 26925, 1141, 8095, 23366, 3633, 27206, 933, 1409, 5652]\n", "0.99939215\n"]}], "execution_count": 92}, {"cell_type": "markdown", "metadata": {}, "source": ["## Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2019-05-04T15:44:11.090069Z", "start_time": "2019-05-04T15:44:11.008017Z"}}, "outputs": [], "source": ["dataset_misclassified = dataset_idx2asn_and_labels(x_test_misclassified, y_test_misclassified_prediction)\n", "export_csv(dataset_misclassified, RESULTS_PATH + MODEL_NAME + \"_misclassified\")"]}], "metadata": {"hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 2}