{"cells": [{"cell_type": "markdown", "metadata": {"id": "C0QLeiS8rTZg"}, "source": ["# Define parameters and load ASN index map and bgp_routes"]}, {"cell_type": "code", "metadata": {"id": "lVtoAjF_rTZh", "ExecuteTime": {"end_time": "2025-03-01T03:41:24.510445Z", "start_time": "2025-03-01T03:41:24.507494Z"}}, "source": ["import numpy as np\n", "\n", "TOR_ORIG_LABELS_DICT = {'P2P':0, 'C2P': 1,'Siblings': 2, 'P2C': 3}\n", "TOR_CSV_LABELS_DICT = {'P2P':0,'P2C': -1}\n", "DATA_PATH = ''"], "outputs": [], "execution_count": 22}, {"cell_type": "markdown", "metadata": {"id": "OSJ51XmUrTZi"}, "source": ["# Create a list of all pairs and inverse_pairs in the bgp_routes dataset"]}, {"cell_type": "markdown", "metadata": {"id": "forZEqYmrTZi"}, "source": ["# Import tors.csv and generate ToR dataset"]}, {"cell_type": "code", "metadata": {"id": "u0xhH1CArTZi", "outputId": "a9d21547-abf8-407d-e999-ac6924bf12d3", "colab": {"base_uri": "https://localhost:8080/"}, "ExecuteTime": {"end_time": "2025-03-01T03:41:27.295447Z", "start_time": "2025-03-01T03:41:25.854399Z"}}, "source": ["import csv\n", "\n", "ToR_CSV = '20180301.as-rel2.txt'\n", "\n", "tor_dataset = []\n", "tor_labels = []\n", "\n", "with open(DATA_PATH + ToR_CSV, 'r') as csv_file:\n", "    reader = csv.reader(csv_file,delimiter='|')\n", "    for i, row in enumerate(reader):\n", "      if row[0][0] != '#' and int(row[2]) in TOR_CSV_LABELS_DICT.values():\n", "        tor_dataset.append(np.asarray(row[:2]))\n", "        tor_dataset.append(np.asarray(row[1::-1]))\n", "        tor_labels += [int(row[2])%3, abs(int(row[2]))]\n", "\n", "print(len(tor_dataset), len(tor_labels))"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["861704 861704\n"]}], "execution_count": 23}, {"cell_type": "markdown", "metadata": {"id": "kWmj8OumrTZi"}, "source": ["## Optional: Remove all pairs that are not in the Routeview"]}, {"cell_type": "markdown", "metadata": {"id": "lOCO8dXzrTZi"}, "source": ["## Count number of Tor of each kind"]}, {"cell_type": "code", "metadata": {"id": "IBJekecWrTZj", "outputId": "c2b2d39e-0fad-4935-caeb-724ab03506ac", "colab": {"base_uri": "https://localhost:8080/"}, "ExecuteTime": {"end_time": "2025-03-01T03:41:30.339285Z", "start_time": "2025-03-01T03:41:30.322939Z"}}, "source": ["from collections import Counter\n", "\n", "c = Counter(tor_labels)\n", "\n", "print(c)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Counter({0: 619524, 2: 121090, 1: 121090})\n"]}], "execution_count": 24}, {"cell_type": "markdown", "metadata": {"id": "Z0zpfQRLrTZj"}, "source": ["# Export ToRs to np files"]}, {"cell_type": "code", "metadata": {"id": "1a98n4SprTZj", "ExecuteTime": {"end_time": "2025-03-01T03:41:33.229319Z", "start_time": "2025-03-01T03:41:33.008325Z"}}, "source": ["def export_dataset(dataset_dict, data_path):\n", "    # with open(file_path + \".pkl\", 'wb') as outfile:\n", "    #     pickle.dump(dataset_list, outfile, pickle.HIGHEST_PROTOCOL)\n", "    for name, array in dataset_dict.items():\n", "        np.save(data_path + \"_\" + name, array)\n", "\n", "dataset_dict = dict()\n", "\n", "dataset_dict[\"dataset\"] = np.asarray(tor_dataset)\n", "dataset_dict[\"labels\"] = np.asarray(tor_labels)\n", "\n", "DATA = 'caida_s1_tor'\n", "export_dataset(dataset_dict, DATA_PATH + DATA)"], "outputs": [], "execution_count": 25}, {"cell_type": "code", "source": ["dataset = np.load(DATA_PATH + DATA + \"_dataset.npy\")\n", "labels = np.load(DATA_PATH + DATA + \"_labels.npy\")\n", "\n", "print(dataset.shape, labels.shape)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2TxDxziMzxXL", "outputId": "d0dce8ad-6ed4-4e3c-9679-bb01a34a3c8a", "ExecuteTime": {"end_time": "2025-03-01T03:41:35.143069Z", "start_time": "2025-03-01T03:41:35.062498Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(861704, 2) (861704,)\n"]}], "execution_count": 26}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "position": {"height": "144px", "left": "1311px", "right": "20px", "top": "152px", "width": "350px"}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}