#!/usr/bin/env python3
"""
使用完整的特征提取逻辑为leak-20181112-Nigeria_MainOne_Cable_leak事件提取26个特征
基于Project_Liu/BGP-Baseline-main/BGPviewer/feature_extraction.py
"""

import time
import os
import numpy as np
import editdistance
from collections import defaultdict
from math import log
import sys
import json
import csv

# 添加路径
sys.path.append('/data/Project_Liu/data_process_GAT')
from Data_generator import data_generator_wlabel
from Routes_vp import Routes

def s2t(seconds: int) -> str:
    """将时间戳转换为字符串格式"""
    utcTime = time.gmtime(seconds)
    strTime = time.strftime("%Y-%m-%d %H:%M:%S", utcTime)
    return strTime

def t2s(str_time: str) -> int:
    """将字符串时间转换为时间戳"""
    time_format = '%Y-%m-%d %H:%M:%S'
    time_int = int(time.mktime(time.strptime(str_time, time_format)))
    return time_int

def entropy(list1: list):
    """计算熵"""
    if not list1:
        return 0
    set_ = set(list1)
    counts = [list1.count(i) for i in set_]
    total = len(list1)
    result = sum([-(i / total) * log(i / total, 2) for i in counts])
    return result

class Feature:
    """提取BGP统计特征"""
    
    def __init__(self):
        self.features = {}
    
    def init(self):
        """初始化特征字典"""
        self.features = {
            'index': 0,
            'A_num': 0,
            'W_num': 0,
            'num_new_A': 0,
            'max_A_AS': 0,
            'min_A_AS': 0,
            'ave_A_AS': 0,
            'max_A_prefix': 0,
            'ave_A_prefix': 0,
            'min_A_prefix': 0,
            'max_length': 0,
            'min_length': 0,
            'ave_length': 0,
            'num_A_prefix': 0,
            'num_W_prefix': 0,
            'num_longer': 0,
            'num_shorter': 0,
            'num_ori_change': 0,
            'label': None,
            'editDis_entropy': 0,
            'length_entropy': 0,
            'max_editDis': 0,
            'min_editDis': 0,
            'num_dup_A': 0,
            'num_dup_W': 0,
            'ave_arrival_interval': 0,
            'num_new_A_afterW': 0,
            'num_A_AS': 0
        }

    def extract_features(self, ind, updates_message, routes):
        """提取特征"""
        updates, self.features['label'] = updates_message
        self.features['index'] = ind
        
        if len(updates) < 3:
            return self.features

        A_num = 0
        W_num = 0
        A_per_as = {}
        A_per_prefix = {}
        W_per_prefix = {}
        path_len = []
        
        edist_list = []
        interval_list = []
        first_flag = True

        for update in updates:
            if first_flag:
                first_flag = False
                if '.' in update[1]:
                    cur_time_ = int(float(update[1]))
                else:
                    cur_time_ = int(update[1])

            pre_time_ = cur_time_
            if '.' in update[1]:
                cur_time_ = int(float(update[1]))
            else:
                cur_time_ = int(update[1])
            
            interval_list.append(cur_time_ - pre_time_)
            
            prefix_ = update[5]
            peer_as_ = update[4]
            op_ = update[2]

            # 如果为宣告消息
            if op_ == 'A':
                A_num += 1
                as_path = update[6].split(' ')
                ori_as_ = as_path[-1]
                
                if (not routes.get(prefix_)) or (routes[prefix_][peer_as_] == None):
                    self.features['num_new_A'] += 1
                elif routes[prefix_][peer_as_] == 'w' + str(ind):
                    self.features['num_new_A_afterW'] += 1
                    self.features['num_new_A'] += 1
                else:
                    if routes[prefix_][peer_as_] != None:
                        as_path_prev = routes[prefix_][peer_as_].split(' ')
                        if as_path_prev == as_path:
                            self.features['num_dup_A'] += 1
                        if ori_as_ != as_path_prev[-1]:
                            self.features['num_ori_change'] += 1
                        if len(as_path_prev) < len(as_path):
                            self.features['num_longer'] += 1
                        if len(as_path_prev) > len(as_path):
                            self.features['num_shorter'] += 1
                        edist = editdistance.eval(as_path_prev, as_path)        
                        edist_list.append(edist)
                
                if ori_as_ not in A_per_as:
                    A_per_as[ori_as_] = 0
                A_per_as[ori_as_] += 1

                if prefix_ not in A_per_prefix:
                    A_per_prefix[prefix_] = 0
                A_per_prefix[prefix_] += 1
                path_len.append(len(as_path))
                routes[prefix_][peer_as_] = ' '.join(as_path)

            # 如果为撤销消息
            elif op_ == 'W':
                W_num += 1
                if prefix_ not in W_per_prefix:
                    W_per_prefix[prefix_] = 0
                W_per_prefix[prefix_] += 1
                if routes[prefix_][peer_as_] == 'w' + str(ind):
                    self.features['num_dup_W'] += 1
                routes[prefix_][peer_as_] = 'w{}'.format(ind)

        # 计算统计特征
        A_per_as_values = list(A_per_as.values())
        self.features["ave_arrival_interval"] = np.average(interval_list) if interval_list else 0
        self.features['length_entropy'] = entropy(path_len)
        
        try:
            self.features['editDis_entropy'] = entropy(edist_list)
            self.features['min_editDis'] = min(edist_list)
            self.features['max_editDis'] = max(edist_list)
        except:
            self.features['editDis_entropy'] = 0
            self.features['min_editDis'] = 0
            self.features['max_editDis'] = 0
            
        if A_per_as_values == []:
            self.features['max_A_AS'] = 0
            self.features['min_A_AS'] = 0
            self.features['ave_A_AS'] = 0
            self.features['num_A_AS'] = 0
        else:
            self.features['max_A_AS'] = max(A_per_as_values)
            self.features['min_A_AS'] = min(A_per_as_values)
            self.features['ave_A_AS'] = np.average(A_per_as_values)
            self.features['num_A_AS'] = len(A_per_as_values)

        A_per_prefix_values = list(A_per_prefix.values())
        if A_per_prefix_values == []:
            self.features['max_A_prefix'] = 0
            self.features['min_A_prefix'] = 0
            self.features['ave_A_prefix'] = 0
            self.features['num_A_prefix'] = 0
        else:
            self.features['max_A_prefix'] = max(A_per_prefix_values)
            self.features['min_A_prefix'] = min(A_per_prefix_values)
            self.features['ave_A_prefix'] = np.average(A_per_prefix_values)
            self.features['num_A_prefix'] = len(A_per_prefix_values)
            
        if path_len == []:
            self.features['max_length'] = 0
            self.features['min_length'] = 0
            self.features['ave_length'] = 0
        else:
            self.features['max_length'] = max(path_len)
            self.features['min_length'] = min(path_len)
            self.features['ave_length'] = np.average(path_len)

        W_per_prefix_values = list(W_per_prefix.values())
        self.features['num_W_prefix'] = len(W_per_prefix_values)

        self.features['A_num'] = A_num
        self.features['W_num'] = W_num

        return self.features

def main():
    # 事件配置
    event_name = "leak-20181112-Nigeria_MainOne_Cable_leak"
    base_dir = "/data/data/anomaly-event-routedata"
    event_dir = os.path.join(base_dir, event_name)
    
    # 输入输出路径
    data_dir = os.path.join(event_dir, "data")
    minute_labels_file = os.path.join(event_dir, "minute_labels.csv")
    
    print(f"处理事件: {event_name}")
    print(f"数据目录: {data_dir}")
    print(f"标签文件: {minute_labels_file}")
    
    # 读取时间标签获取时间范围
    minute_labels_dict = {}
    with open(minute_labels_file, 'r') as f:
        lines = f.readlines()
        for line in lines[1:]:  # 跳过表头
            parts = line.strip().split(',')
            timestamp = int(parts[0])
            label = int(parts[1])
            minute_labels_dict[timestamp] = label
    
    # 解析时间范围（减去60秒得到窗口开始时间）
    start_time_sec = int(lines[1].strip().split(',')[0]) - 60
    end_time_sec = int(lines[-1].strip().split(',')[0]) - 60
    
    start_time_str = s2t(start_time_sec)
    end_time_str = s2t(end_time_sec)
    
    print(f"时间范围: {start_time_str} 到 {end_time_str}")
    
    # 获取BGP数据文件列表
    bgp_files = []
    for filename in sorted(os.listdir(data_dir)):
        if filename.startswith('rrc00_updates') and filename.endswith('.txt'):
            bgp_files.append(os.path.join(data_dir, filename))
    
    print(f"找到 {len(bgp_files)} 个BGP数据文件")
    
    # 使用data_generator_wlabel生成时间窗口
    Period = 1  # 1分钟窗口
    
    print("开始生成时间窗口...")
    
    # 生成数据切片
    data_slices_gen = data_generator_wlabel(
        bgp_files, 
        Period,
        start_time_str, 
        end_time_str,
        start_time_str,  # 异常开始时间
        end_time_str     # 异常结束时间
    )
    
    # 初始化路由表（使用空的路由表，因为我们没有priming数据）
    print("初始化路由表...")
    routes = defaultdict(lambda: defaultdict(lambda: None))
    
    # 提取特征
    print("开始提取特征...")
    F1 = Feature()
    features_list = []
    window_index = 0
    
    # 获取时间戳列表用于标签对应
    timestamps = sorted(minute_labels_dict.keys())
    
    for data_slice in data_slices_gen:
        print(f"处理窗口 {window_index}")
        
        F1.init()
        
        # 使用minute_labels.csv中的正确标签
        if window_index < len(timestamps):
            timestamp = timestamps[window_index]
            label = minute_labels_dict[timestamp]
        else:
            label = 0
        
        # 修改data_slice的标签
        updates_slice, _ = data_slice
        modified_data_slice = (updates_slice, label)
        
        # 提取特征
        features = F1.extract_features(window_index, modified_data_slice, routes)
        features_list.append(features)
        window_index += 1
    
    print(f"共处理了 {len(features_list)} 个时间窗口")
    
    # 保存为JSON格式（与其他事件格式一致 - 数组格式）
    json_output_file = os.path.join(event_dir, "features", "fea.json")
    os.makedirs(os.path.dirname(json_output_file), exist_ok=True)

    # 转换为数组格式，移除index字段，确保label字段在最后（与其他事件一致）
    json_data = []
    for features in features_list:
        # 创建副本并重新排序字段
        feature_copy = features.copy()
        feature_copy.pop('index', None)  # 移除index字段

        # 提取label字段并重新构建有序字典，label放在最后
        label_value = feature_copy.pop('label', None)
        ordered_features = {}

        # 按照其他事件的字段顺序添加特征
        field_order = [
            'A_num', 'W_num', 'num_new_A', 'max_A_AS', 'min_A_AS', 'ave_A_AS',
            'max_A_prefix', 'ave_A_prefix', 'min_A_prefix', 'max_length',
            'min_length', 'ave_length', 'num_A_prefix', 'num_W_prefix',
            'num_longer', 'num_shorter', 'num_ori_change', 'editDis_entropy',
            'length_entropy', 'max_editDis', 'min_editDis', 'num_dup_A',
            'num_dup_W', 'ave_arrival_interval', 'num_new_A_afterW', 'num_A_AS'
        ]

        # 按顺序添加字段
        for field in field_order:
            if field in feature_copy:
                ordered_features[field] = feature_copy[field]

        # 最后添加label字段
        if label_value is not None:
            ordered_features['label'] = label_value

        json_data.append(ordered_features)

    with open(json_output_file, 'w') as f:
        json.dump(json_data, f, indent=2)
    
    print(f"特征已保存到: {json_output_file}")
    print(f"特征维度: {len(features_list[0])} 个特征")
    print(f"时间窗口数: {len(features_list)}")
    
    # 验证标签分布
    labels = [f['label'] for f in features_list]
    print(f"标签分布: 正常={labels.count(0)}, 异常={labels.count(1)}")

if __name__ == "__main__":
    main()
