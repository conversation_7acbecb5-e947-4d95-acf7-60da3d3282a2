import time
import calendar

# --- 时间转换辅助函数 ---
def s2t(seconds:int) -> str:
    utcTime = time.gmtime(int(seconds))
    strTime = time.strftime("%Y-%m-%d %H:%M:%S", utcTime)
    return strTime

def t2s(str_time:str) -> int:
    time_format = '%Y-%m-%d %H:%M:%S'
    time_tuple = time.strptime(str_time, time_format)
    time_int = int(calendar.timegm(time_tuple))
    return time_int

# --- 最终版、带标签的数据生成器 (会为沉默期生成空数据) ---
def data_generator_wlabel(files, Period, start_time:str, end_time:str, anomaly_start_time:str, anomaly_end_time:str):
    if not files:
        print("The updates files list is empty, please check!")
        return

    interval = Period * 60
    left_time = t2s(start_time)
    right_time = left_time + interval
    end_time_s = t2s(end_time)

    anomaly_start_time_s = t2s(anomaly_start_time)
    anomaly_end_time_s = t2s(anomaly_end_time)

    updates_in_window = []

    for file in files:
        with open(file, 'r') as f:
            for l in f:
                if not l.strip():
                    continue

                line = l.strip().split('|')
                if len(line) < 6:
                    continue

                current_time = int(float(line[1]))
                prefix_ = line[5]

                if '.' not in prefix_ or current_time < left_time:
                    continue
                
                if current_time > end_time_s:
                    break

                # 如果当前记录已经超出了当前窗口
                if current_time >= right_time:
                    # 首先，提交上一个收集的窗口 (无论是否为空)
                    is_anomaly = not (right_time <= anomaly_start_time_s or left_time >= anomaly_end_time_s)
                    yield (updates_in_window, '1' if is_anomaly else '0')
                    
                    # 然后，滑动窗口，并为所有中间的“沉默”窗口提交空列表
                    left_time = right_time
                    right_time += interval
                    while current_time >= right_time:
                        # 这是一个沉默窗口，提交一个空列表
                        is_anomaly = not (right_time <= anomaly_start_time_s or left_time >= anomaly_end_time_s)
                        yield ([], '1' if is_anomaly else '0')
                        left_time = right_time
                        right_time += interval
                    
                    # 为新窗口重置列表
                    updates_in_window = []

                # 将当前记录添加到它所属的窗口中
                updates_in_window.append(line)
        
        if 'current_time' in locals() and current_time > end_time_s:
            break

    # 循环结束后，提交最后一个窗口里可能遗留的数据
    is_anomaly = not (right_time <= anomaly_start_time_s or left_time >= anomaly_end_time_s)
    yield (updates_in_window, '1' if is_anomaly else '0')
    
    # 最后，填充从当前窗口到总结束时间之间的所有可能的沉默窗口
    left_time = right_time
    right_time += interval
    while left_time < end_time_s:
        is_anomaly = not (right_time <= anomaly_start_time_s or left_time >= anomaly_end_time_s)
        yield ([], '1' if is_anomaly else '0')
        left_time = right_time
        right_time += interval

# --- 最终版、无标签的数据生成器 (逻辑同步修改) ---
def data_generator_wolabel(files, Period, start_time:str, end_time:str):
    if not files:
        print("The updates files list is empty, please check!")
        return

    interval = Period * 60
    left_time = t2s(start_time)
    right_time = left_time + interval
    end_time_s = t2s(end_time)

    updates_in_window = []

    for file in files:
        with open(file, 'r') as f:
            for l in f:
                if not l.strip():
                    continue

                line = l.strip().split('|')
                if len(line) < 6:
                    continue

                current_time = int(float(line[1]))
                prefix_ = line[5]

                if '.' not in prefix_ or current_time < left_time:
                    continue
                
                if current_time > end_time_s:
                    break
                
                if current_time >= right_time:
                    yield updates_in_window
                    
                    left_time = right_time
                    right_time += interval
                    while current_time >= right_time:
                        yield [] # 为沉默窗口提交空列表
                        left_time = right_time
                        right_time += interval
                    
                    updates_in_window = []

                updates_in_window.append(line)
        
        if 'current_time' in locals() and current_time > end_time_s:
            break
            
    # 循环结束后，提交最后一个窗口
    yield updates_in_window

    # 填充到总结束时间
    left_time = right_time
    while left_time < end_time_s:
        yield []
        left_time += interval