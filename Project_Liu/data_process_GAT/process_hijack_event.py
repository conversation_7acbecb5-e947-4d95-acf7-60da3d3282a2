import time
import os
import numpy as np
import editdistance
from collections import defaultdict
from math import log
import sys
from tqdm import tqdm
import argparse
from datetime import datetime
sys.path.append('/home/<USER>/Feature_extraction/')
from Routes_vp import Routes
import json
import csv
# from Data_generator import data_generator_wlabel
from Data_generator import data_generator_wlabel
# second time transfer to '%Y-%m-%d %H:%M:%S'.
def s2t(seconds:int) -> str:
    utcTime = time.gmtime(seconds)
    strTime = time.strftime("%Y-%m-%d %H:%M:%S",utcTime)
    return strTime

# str time transfer to second time.
def t2s(str_time:str) -> int:
    time_format = '%Y-%m-%d %H:%M:%S'
    time_int = int(time.mktime(time.strptime(str_time, time_format)))
    return time_int

def entropy(list1:list):
    set_ = set(list1)
    counts = [list1.count(i) for i in set_]
    total = len(list1)
    result = sum([-(i / total) * log(i / total, 2) for i in counts])
    return result
                        
class Feature:
    
    '''
    Extract the statistical features from updates slices;
    '''

    def __init__(self):
        self.features = {}
    
    def init(self):
        self.features = {
            'index': 0, # the no. of instances.
            'A_num': 0,  # the number of Announcement.
            'W_num': 0,  # the number of Withdraw.
            'num_new_A': 0,  # the number of new annountment.
            'max_A_AS': 0,  # the largest number of annountment by AS.
            'min_A_AS': 0,  # the least number of announcement by AS.
            'ave_A_AS': 0,  # the average number of announcement by AS.
            'max_A_prefix': 0,  # the largest number of annountment by prefix.
            'ave_A_prefix': 0,  # the average number of announcement by prefix.
            'min_A_prefix': 0,  # the min number of announcement by prefix.
            'max_length': 0,  # the max length of as path.
            'min_length': 0,  # the min length of as path.
            'ave_length': 0,  # the average length of as path.
            'num_A_prefix': 0,  # the number of prefix announcing A updates.
            'num_W_prefix':0,  # the number of prefix announcing withdraw.
            'num_longer': 0,  # the number of longer path of announcement.
            'num_shorter': 0,  # the number of shorter path of announcement.
            'num_ori_change': 0,  # ori_as changes.
            'label': None,  # label 0 represents normal event; label 1 represents anomaly events.
            'editDis_entropy': 0,  # the entropy of editDis.
            'length_entropy': 0,  # entropy of as path length.
            'max_editDis': 0,  # the maximum edit distance.
            'min_editDis': 0,  # the min edit distance.
            'num_dup_A': 0,  # the number of duplicate announcements.
            "num_dup_W": 0,  # the number of duplicate withdraw.
            "ave_arrival_interval": 0,  # the average of arrival interval.
            'num_new_A_afterW': 0,  # the number of new announcement after withdraw.
            'num_A_AS': 0
        }
        

    def extract_features(self, ind, updates_message, routes):
        
        updates, self.features['label'] = updates_message
        self.features['index'] = ind
        extraction_time = 0
        if len(updates) < 3:
            return self.features, extraction_time

        A_num = 0
        W_num = 0
        A_per_as = {}
        A_per_prefix = {}
        W_per_prefix = {}
        path_len = []
        
        edist_list = []
        interval_list = []
        first_flag = True

        starttime = time.time()
        for update in updates:
            if first_flag:
                first_flag = False
                if '.' in update[1]:
                    cur_time_ = int(float(update[1]))
                else:
                    cur_time_ = int(update[1])

            pre_time_ = cur_time_
            if '.' in update[1]:
                cur_time_ = int(float(update[1]))
            else:
                cur_time_ = int(update[1])
            
            op_ = update[2]
            prefix_ = update[5]
            peer_as_ = update[4]
            
            interval = cur_time_ - pre_time_
            interval_list.append(interval)

            # 如果为宣告消息
            if op_ == 'A':
                A_num += 1
                as_path = update[6].split(' ')
                ori_as_ = as_path[-1]

                # Fix: Initialize the prefix in the routes dictionary if it's not present.
                if prefix_ not in routes:
                    routes[prefix_] = defaultdict(lambda: None)

                if (not routes.get(prefix_)) or (routes[prefix_][peer_as_] == None):
                    self.features['num_new_A'] += 1
                elif routes[prefix_][peer_as_] == 'w' + str(ind):
                    self.features['num_new_A_afterW'] += 1
                    # print('the num new A after W:',self.features['num_new_A_afterW'])
                    self.features['num_new_A'] += 1
                else:
                    if routes[prefix_][peer_as_] != None:
                        as_path_prev = routes[prefix_][peer_as_].split(' ')
                        if as_path_prev == as_path:
                            self.features['num_dup_A'] += 1
                        if ori_as_ != as_path_prev[-1]:
                            self.features['num_ori_change'] += 1
                        if len(as_path_prev) < len(as_path):
                            self.features['num_longer'] += 1
                        if len(as_path_prev) > len(as_path):
                            self.features['num_shorter'] += 1
                        edist = editdistance.eval(as_path_prev, as_path)        
                        edist_list.append(edist)
                
                if ori_as_ not in A_per_as:
                    A_per_as[ori_as_] = 0

                A_per_as[ori_as_] += 1

                if prefix_ not in A_per_prefix:
                    A_per_prefix[prefix_] = 0
                A_per_prefix[prefix_] += 1
                path_len.append(len(as_path))
                routes[prefix_][peer_as_] = ' '.join(as_path)

            # 如果为撤销消息,提取特征                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
            elif op_ == 'W':
                W_num += 1

                # Fix: Initialize the prefix in the routes dictionary if it's not present.
                if prefix_ not in routes:
                    routes[prefix_] = defaultdict(lambda: None)

                if prefix_ not in W_per_prefix:
                    W_per_prefix[prefix_] = 0
                W_per_prefix[prefix_] += 1
                if routes[prefix_][peer_as_] == 'w' + str(ind):
                    self.features['num_dup_W'] += 1
                routes[prefix_][peer_as_] = 'w{}'.format(ind)

        A_per_as_values = list(A_per_as.values())
        self.features["ave_arrival_interval"] = np.average(interval_list)
        self.features['length_entropy'] = entropy(path_len)
        
        try:
            self.features['editDis_entropy'] = entropy(edist_list)
            self.features['min_editDis'] = min(edist_list)
            self.features['max_editDis'] = max(edist_list)
        except:
            if len(edist_list) < 2:
                self.features['editDis_entropy'] = 0
                self.features['min_editDis'] = 0
                self.features['max_editDis'] = 0
        if A_per_as_values == []:
            self.features['max_A_AS'] = 0
            self.features['min_A_AS'] = 0
            self.features['ave_A_AS'] = 0
            self.features['num_A_AS'] = 0
        else:
            self.features['max_A_AS'] = max(A_per_as_values)
            self.features['min_A_AS'] = min(A_per_as_values)
            self.features['ave_A_AS'] = np.average(A_per_as_values)
            self.features['num_A_AS'] = len(A_per_as_values)

        A_per_prefix_values = list(A_per_prefix.values())
        if A_per_prefix_values == []:
            
            self.features['max_A_prefix'] = 0
            self.features['min_A_prefix'] = 0
            self.features['ave_A_prefix'] = 0
            self.features['num_A_prefix'] = 0
        else:
            self.features['max_A_prefix'] = max(A_per_prefix_values)
            self.features['min_A_prefix'] = min(A_per_prefix_values)
            self.features['ave_A_prefix'] = np.average(A_per_prefix_values)
            self.features['num_A_prefix'] = len(A_per_as_values)
        if path_len == []:
            self.features['max_length'] = 0
            self.features['min_length'] = 0
            self.features['ave_length'] = 0
        else:
            self.features['max_length'] = max(path_len)
            self.features['min_length'] = min(path_len)
            self.features['ave_length'] = np.average(path_len)

        W_per_prefix_values = list(W_per_prefix.values())
        self.features['num_W_prefix'] = len(W_per_prefix_values)

        self.features['A_num'] = A_num
        self.features['W_num'] = W_num
        extract_time = time.time() - starttime
        print('The extraction time:', extract_time)

        return (self.features, extract_time)


if __name__ == "__main__":

    # ==============================================================================
    # --- Interactive Menu Based on Available Update Dates ---
    # This script scans the updates directory to find all available dates,
    # lets the user choose one, and then processes it against a fixed RIB file.
    # ==============================================================================

    # --- Static & Base Paths ---
    rib_path = "/data/five_month/RIB/rrc00_bview.20250501.0000"
    updates_dir = "/data/five_month/updates_txt/"
    output_base_dir = "/data/five_month/features/"

    # 1. Discover available dates from the updates directory
    try:
        all_files = os.listdir(updates_dir)
        # Extract unique dates (e.g., '20250501') from filenames like 'rrc00_updates.20250501.0105.txt'
        unique_dates_nodash = sorted(list(set([f.split('.')[1] for f in all_files if f.startswith('rrc00_updates.')])))
    except FileNotFoundError:
        print(f"Error: Updates directory not found at '{updates_dir}'")
        sys.exit(1)

    if not unique_dates_nodash:
        print(f"Error: No update files found in '{updates_dir}' with the 'rrc00_updates.*.txt' pattern.")
        sys.exit(1)

    # 2. Display an interactive menu to the user
    print("--- Discovered Available Dates in Updates ---")
    date_map = {}
    for i, date_str in enumerate(unique_dates_nodash, 1):
        try:
            date_obj = datetime.strptime(date_str, '%Y%m%d')
            formatted_date = date_obj.strftime('%Y-%m-%d')
            date_map[i] = {'nodash': date_str, 'dashed': formatted_date}
            print(f"{i}: Process updates for date {formatted_date}")
        except ValueError:
            print(f"Warning: Found files with malformed date part '{date_str}'. Skipping.")
            continue
    print("-------------------------------------------")

    if not date_map:
        print("Error: No processable dates found. Exiting.")
        sys.exit(1)

    # 3. Get and validate user's choice
    try:
        choice = input(f"Please enter the number of the date to process (1-{len(date_map)}): ")
        choice_index = int(choice)
        if choice_index not in date_map:
            print("Error: Your choice is out of the valid range.")
            sys.exit(1)
    except (ValueError, IndexError):
        print("Error: Invalid input. Please enter a number from the list.")
        sys.exit(1)

    # 4. Set up paths and files based on user selection
    selected_date_info = date_map[choice_index]
    selected_date_nodash = selected_date_info['nodash']
    date_str_dashed = selected_date_info['dashed']

    # Filter the list of update files to only include those for the selected date
    updates_files_for_date = sorted([
        os.path.join(updates_dir, f) for f in all_files if f.split('.')[1] == selected_date_nodash
    ])
    
    # Define output path
    output_filename = f"features_{date_str_dashed}.csv"
    full_output_path = os.path.join(output_base_dir, output_filename)

    # Time windows are set for the entire selected day
    event_time = (f"{date_str_dashed} 00:00:00", f"{date_str_dashed} 23:59:59")
    anomaly_time = event_time

    # --- Pre-computation Checks ---
    if not os.path.exists(rib_path):
        print(f"Error: The fixed RIB file was not found at '{rib_path}'")
        sys.exit(1)
    
    if not updates_files_for_date:
        print(f"Error: Could not find any update files for the selected date {date_str_dashed}.")
        sys.exit(1)

    os.makedirs(output_base_dir, exist_ok=True)

    # ==============================================================================
    # --- Main Processing Logic ---
    # ==============================================================================

    print("\\n" + "="*80)
    print(f"--- Starting feature extraction for {date_str_dashed} ---")
    print(f"Using Fixed RIB file: {rib_path}")
    print(f"Processing {len(updates_files_for_date)} update files from: {updates_dir}")
    print(f"Output will be saved to: {full_output_path}")
    print("="*80 + "\\n")
    
    Period = 1
    
    updates_files = updates_files_for_date # Use the filtered list

    # Pass the list of files to the data generator
    print("Preparing data generator...")
    data_slices_gen = data_generator_wlabel(updates_files, Period=Period,
                                        start_time = event_time[0],
                                        end_time= event_time[1], 
                                        anomaly_start_time = anomaly_time[0],
                                        anomaly_end_time= anomaly_time[1])
    # The line converting the generator to a list has been removed to prevent OOM errors.
    # data_slices = list(data_slices_gen) 
    # print(f"Generated {len(data_slices)} data slices.")

    print('Loading the routes...')
    r = Routes(rib_path) # Use the fixed RIB path
    r.collect_routes()
    r1 = r.get_route

    F1 = Feature()
    ind = 0
    features = []
    extraction_time = []
    print("Starting feature extraction...")
    start_process_time = time.time()
    # tqdm will now process the generator directly without a total count.
    for u in tqdm(data_slices_gen, desc="Processing Slices"):
        F1.init()
        # Correctly unpack the tuple returned by extract_features
        feature_dict, ext_time = F1.extract_features(ind, u, r1)
        features.append(feature_dict)
        extraction_time.append(ext_time)
        ind += 1
    
    total_process_time = time.time() - start_process_time
    print(f"Feature extraction for {ind} slices completed in {total_process_time:.2f} seconds.")

    if extraction_time:
        print(f"Feature extraction average time consumes: {np.average(extraction_time)}.")
    else:
        print("No features were extracted.")

    if features:
        with open(full_output_path, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=features[0].keys())
            writer.writeheader()
            writer.writerows(features)
    
    print(f"Processing complete.")

    # Explicitly delete objects to match the original script's behavior
    del r, r1, F1 