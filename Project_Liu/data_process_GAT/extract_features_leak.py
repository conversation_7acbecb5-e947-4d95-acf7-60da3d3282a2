#!/usr/bin/env python3
"""
专门为leak-20181112-Nigeria_MainOne_Cable_leak事件提取特征的脚本
避免Routes_vp.py中的错误，直接使用Data_generator进行特征提取
"""

import sys
import os
import csv
import time
import numpy as np
from datetime import datetime

# 添加路径
sys.path.append('/data/Project_Liu/data_process_GAT')
from Data_generator import data_generator_wlabel

def s2t(seconds: int) -> str:
    """将时间戳转换为字符串格式"""
    utcTime = time.gmtime(seconds)
    strTime = time.strftime("%Y-%m-%d %H:%M:%S", utcTime)
    return strTime

def t2s(str_time: str) -> int:
    """将字符串时间转换为时间戳"""
    time_format = '%Y-%m-%d %H:%M:%S'
    time_int = int(time.mktime(time.strptime(str_time, time_format)))
    return time_int

def extract_basic_features(updates_slice):
    """
    提取基础特征，避免使用Routes_vp
    """
    features = {}

    # 初始化计数器
    A_num = 0
    W_num = 0
    path_lengths = []
    prefixes = set()
    peer_ases = set()

    for update in updates_slice:
        # update可能是列表或字符串，需要处理
        if isinstance(update, list):
            parts = update
        else:
            parts = update.strip().split('|')

        if len(parts) < 7:
            continue

        msg_type = parts[2]
        peer_as = parts[4]
        prefix = parts[5]
        as_path = parts[6]
        
        # 统计消息类型
        if msg_type == 'A':
            A_num += 1
            if as_path and '{' not in as_path:
                path_length = len(as_path.split())
                path_lengths.append(path_length)
        elif msg_type == 'W':
            W_num += 1
            
        prefixes.add(prefix)
        peer_ases.add(peer_as)
    
    # 计算特征
    features['A_num'] = A_num
    features['W_num'] = W_num
    features['num_prefixes'] = len(prefixes)
    features['num_peer_ases'] = len(peer_ases)
    
    if path_lengths:
        features['max_path_length'] = max(path_lengths)
        features['min_path_length'] = min(path_lengths)
        features['avg_path_length'] = np.mean(path_lengths)
    else:
        features['max_path_length'] = 0
        features['min_path_length'] = 0
        features['avg_path_length'] = 0
    
    # 添加更多基础特征以匹配原始格式
    features['num_updates'] = A_num + W_num
    features['A_W_ratio'] = A_num / (W_num + 1)  # 避免除零
    
    return features

def main():
    # 事件配置
    event_name = "leak-20181112-Nigeria_MainOne_Cable_leak"
    base_dir = "/data/data/anomaly-event-routedata"
    event_dir = os.path.join(base_dir, event_name)
    
    # 输入输出路径
    data_dir = os.path.join(event_dir, "data")
    minute_labels_file = os.path.join(event_dir, "minute_labels.csv")
    output_file = os.path.join(event_dir, "features", f"{event_name}_features.csv")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    print(f"处理事件: {event_name}")
    print(f"数据目录: {data_dir}")
    print(f"标签文件: {minute_labels_file}")
    print(f"输出文件: {output_file}")
    
    # 读取时间标签获取时间范围和标签
    minute_labels_dict = {}
    with open(minute_labels_file, 'r') as f:
        lines = f.readlines()
        for line in lines[1:]:  # 跳过表头
            parts = line.strip().split(',')
            timestamp = int(parts[0])
            label = int(parts[1])
            minute_labels_dict[timestamp] = label

    # 解析时间范围（减去60秒得到窗口开始时间）
    start_time_sec = int(lines[1].strip().split(',')[0]) - 60
    end_time_sec = int(lines[-1].strip().split(',')[0]) - 60

    start_time_str = s2t(start_time_sec)
    end_time_str = s2t(end_time_sec)
    
    print(f"时间范围: {start_time_str} 到 {end_time_str}")
    
    # 获取BGP数据文件列表
    bgp_files = []
    for filename in sorted(os.listdir(data_dir)):
        if filename.startswith('rrc00_updates') and filename.endswith('.txt'):
            bgp_files.append(os.path.join(data_dir, filename))
    
    print(f"找到 {len(bgp_files)} 个BGP数据文件")
    
    # 使用data_generator_wlabel生成时间窗口
    Period = 1  # 1分钟窗口
    
    print("开始生成时间窗口和提取特征...")
    
    # 生成数据切片
    # data_generator_wlabel(files, Period, start_time, end_time, anomaly_start_time, anomaly_end_time)
    data_slices_gen = data_generator_wlabel(
        bgp_files,
        Period,
        start_time_str,
        end_time_str,
        start_time_str,  # 异常开始时间，这里设为整个时间范围
        end_time_str     # 异常结束时间
    )
    
    # 提取特征
    features_list = []
    window_index = 0

    # 获取时间戳列表用于标签对应
    timestamps = sorted(minute_labels_dict.keys())

    for data_slice in data_slices_gen:
        print(f"处理窗口 {window_index}")

        # data_slice是一个元组 (updates_in_window, label)
        updates_slice, _ = data_slice  # 忽略data_generator的标签

        # 提取基础特征
        features = extract_basic_features(updates_slice)
        features['window_index'] = window_index

        # 使用minute_labels.csv中的正确标签
        if window_index < len(timestamps):
            timestamp = timestamps[window_index]
            features['label'] = minute_labels_dict[timestamp]
        else:
            features['label'] = 0  # 默认为正常

        features_list.append(features)
        window_index += 1
    
    print(f"共处理了 {len(features_list)} 个时间窗口")
    
    # 保存特征到CSV文件
    if features_list:
        with open(output_file, 'w', newline='') as f:
            fieldnames = features_list[0].keys()
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(features_list)
        
        print(f"特征已保存到: {output_file}")
        print(f"特征维度: {len(fieldnames)} 个特征")
        print(f"时间窗口数: {len(features_list)}")
    else:
        print("错误: 没有提取到任何特征")

if __name__ == "__main__":
    main()
