import time
import os
import numpy as np
import editdistance
from collections import defaultdict
from math import log
import sys
import csv
sys.path.append('/home/<USER>/Feature_extraction/')
from Routes_vp import Routes
from Data_generator import data_generator_wlabel

# --- 辅助函数 ---
def s2t(seconds:int) -> str:
    """将秒级时间戳转换为 'YYYY-MM-DD HH:MM:S' 格式的字符串"""
    utcTime = time.gmtime(int(seconds))
    strTime = time.strftime("%Y-%m-%d %H:%M:%S", utcTime)
    return strTime

def t2s(str_time:str) -> int:
    """将字符串时间转换为秒级时间戳"""
    time_format = '%Y-%m-%d %H:%M:%S'
    time_int = int(time.mktime(time.strptime(str_time, time_format)))
    return time_int
    
def entropy(list1:list):
    """计算列表的香农熵"""
    set_ = set(list1)
    counts = [list1.count(i) for i in set_]
    total = len(list1)
    if total == 0: return 0
    result = sum([-(i / total) * log(i / total, 2) for i in counts if i > 0])
    return result

# --- 特征提取类 ---
class Feature:
    """从BGP更新数据切片中提取统计特征"""
    def __init__(self):
        self.features = {}
    
    def init(self):
        """初始化所有特征值为0或None"""
        self.features = {
            'index': 0, 'A_num': 0, 'W_num': 0, 'num_new_A': 0, 'max_A_AS': 0,
            'min_A_AS': 0, 'ave_A_AS': 0, 'max_A_prefix': 0, 'ave_A_prefix': 0,
            'min_A_prefix': 0, 'max_length': 0, 'min_length': 0, 'ave_length': 0,
            'num_A_prefix': 0, 'num_W_prefix':0, 'num_longer': 0, 'num_shorter': 0,
            'num_ori_change': 0, 'editDis_entropy': 0, 'length_entropy': 0,
            'max_editDis': 0, 'min_editDis': 0, 'num_dup_A': 0, "num_dup_W": 0,
            "ave_arrival_interval": 0, 'num_new_A_afterW': 0, 'num_A_AS': 0
        }

    def extract_features(self, ind, updates_message, routes):
        """核心特征提取函数"""
        self.init()
        # 接收数据，并智能处理可能存在的标签，但我们最终会忽略它
        updates = updates_message[0] if isinstance(updates_message, tuple) and len(updates_message) > 1 else updates_message
        self.features['index'] = ind
        extraction_time = 0
        if len(updates) < 3:
            return self.features, extraction_time
        
        A_num, W_num = 0, 0
        A_per_as, A_per_prefix, W_per_prefix = {}, {}, {}
        path_len, edist_list, interval_list = [], [], []

        starttime = time.time()
        cur_time_ = int(float(updates[0][1])) if updates else 0

        for update in updates:
            pre_time_ = cur_time_
            cur_time_ = int(float(update[1]))
            interval_list.append(cur_time_ - pre_time_)
            op_, prefix_, peer_as_ = update[2], update[5], update[4]

            if op_ == 'A':
                A_num += 1
                as_path = update[6].split(' ')
                ori_as_ = as_path[-1]
                prev_route = routes.get(prefix_, {}).get(peer_as_)
                if prev_route is None:
                    self.features['num_new_A'] += 1
                elif isinstance(prev_route, str) and prev_route.startswith('w'):
                    self.features['num_new_A_afterW'] += 1
                    self.features['num_new_A'] += 1
                else:
                    as_path_prev = prev_route.split(' ')
                    if as_path_prev == as_path: self.features['num_dup_A'] += 1
                    if ori_as_ != as_path_prev[-1]: self.features['num_ori_change'] += 1
                    if len(as_path_prev) < len(as_path): self.features['num_longer'] += 1
                    if len(as_path_prev) > len(as_path): self.features['num_shorter'] += 1
                    edist_list.append(editdistance.eval(as_path_prev, as_path))
                
                A_per_as[ori_as_] = A_per_as.get(ori_as_, 0) + 1
                A_per_prefix[prefix_] = A_per_prefix.get(prefix_, 0) + 1
                path_len.append(len(as_path))
                routes[prefix_][peer_as_] = ' '.join(as_path)
            
            elif op_ == 'W':
                W_num += 1
                W_per_prefix[prefix_] = W_per_prefix.get(prefix_, 0) + 1
                if routes.get(prefix_, {}).get(peer_as_) == f'w{ind}':
                    self.features['num_dup_W'] += 1
                routes[prefix_][peer_as_] = f'w{ind}'

        # --- 已补全的完整特征计算逻辑 ---
        if interval_list:
            self.features["ave_arrival_interval"] = np.average(interval_list)
        
        self.features['length_entropy'] = entropy(path_len)
        if edist_list:
            self.features['editDis_entropy'] = entropy(edist_list)
            self.features['min_editDis'] = min(edist_list)
            self.features['max_editDis'] = max(edist_list)

        A_per_as_values = list(A_per_as.values())
        if A_per_as_values:
            self.features['max_A_AS'] = max(A_per_as_values)
            self.features['min_A_AS'] = min(A_per_as_values)
            self.features['ave_A_AS'] = np.average(A_per_as_values)
            self.features['num_A_AS'] = len(A_per_as_values)

        A_per_prefix_values = list(A_per_prefix.values())
        if A_per_prefix_values:
            self.features['max_A_prefix'] = max(A_per_prefix_values)
            self.features['min_A_prefix'] = min(A_per_prefix_values)
            self.features['ave_A_prefix'] = np.average(A_per_prefix_values)
            self.features['num_A_prefix'] = len(A_per_prefix_values)

        if path_len:
            self.features['max_length'] = max(path_len)
            self.features['min_length'] = min(path_len)
            self.features['ave_length'] = np.average(path_len)

        self.features['num_W_prefix'] = len(W_per_prefix)
        self.features['A_num'] = A_num
        self.features['W_num'] = W_num
        
        extract_time = time.time() - starttime
        return (self.features, extract_time)

# --- 主程序入口 ---
if __name__ == "__main__":

    Period = 1
    
    # 定义根目录，方便管理
    ANOMALY_DATA_ROOT = "/data/data/anomaly-event-routedata"
    RIB_DATA_ROOT = "/data/data/node_event/RIB"

    # 1. 自动发现所有事件
    try:
        # 获取所有文件夹并排序，保证每次运行顺序一致
        event_names = sorted([d for d in os.listdir(ANOMALY_DATA_ROOT) if os.path.isdir(os.path.join(ANOMALY_DATA_ROOT, d))])
    except FileNotFoundError:
        print(f"[致命错误] 事件数据根目录未找到: {ANOMALY_DATA_ROOT}")
        sys.exit()

    if not event_names:
        print(f"[致命错误] 在 {ANOMALY_DATA_ROOT} 中未发现任何事件文件夹。")
        sys.exit()

    # 2. 打印带编号的事件菜单
    print("--- 发现以下可用事件 ---")
    for i, name in enumerate(event_names, 1):
        print(f"{i}: {name}")
    print("-" * 28)

    # 3. 自动选择leak-20181112-Nigeria_MainOne_Cable_leak事件
    target_event = "leak-20181112-Nigeria_MainOne_Cable_leak"
    if target_event in event_names:
        choice_index = event_names.index(target_event)
        print(f"自动选择事件: {target_event}")
    else:
        print(f"错误：未找到目标事件 {target_event}")
        print("可用事件:", event_names)
        sys.exit()

    # 4. 根据选择，处理单个事件
    event_name = event_names[choice_index]
    
    print("\n" + "="*80)
    print(f"--- 您已选择处理事件: {event_name} ---")
    print("="*80)

    config = {'event_name': event_name}
    
    try:
        # 自动解析 event_time
        labels_file = os.path.join(ANOMALY_DATA_ROOT, event_name, "minute_labels.csv")
        with open(labels_file, 'r') as f:
            lines = f.readlines()
        
        if len(lines) < 2:
            raise ValueError(f"{labels_file} 行数不足，无法确定时间范围。")
            
        start_time_sec = int(lines[1].strip().split(',')[0]) - 60
        end_time_sec = int(lines[-1].strip().split(',')[0]) - 60
        config['event_time'] = (s2t(start_time_sec), s2t(end_time_sec))
        print(f"自动解析到 event_time: {config['event_time']}")

        # 自动发现 priming_path
        rib_dir = os.path.join(RIB_DATA_ROOT, event_name)
        parsed_files = [f for f in os.listdir(rib_dir) if f.endswith("_parsed.txt")]
        if len(parsed_files) != 1:
            raise ValueError(f"在 {rib_dir} 中找到 {len(parsed_files)} 个 '_parsed.txt' 文件，无法确定唯一的RIB文件。")
        config['priming_path'] = os.path.join(rib_dir, parsed_files[0])
        print(f"自动发现 priming_path: {config['priming_path']}")
        
        # 自动生成其他路径
        base_path = os.path.join(ANOMALY_DATA_ROOT, event_name)
        config['data_path'] = os.path.join(base_path, "data")
        config['output_path'] = os.path.join(base_path, "features")
        config['output_filename'] = f"{event_name}_features.csv"

        # 开始核心处理流程
        print(f"读取数据: {config['data_path']}")
        updates_files = sorted([os.path.join(config['data_path'], i) for i in os.listdir(config['data_path'])])
        
        # 为 data_generator_wlabel 传入必需的占位参数
        data_slices = data_generator_wlabel(
            updates_files,
            Period=Period,
            start_time=config['event_time'][0],
            end_time=config['event_time'][1],
            anomaly_start_time=config['event_time'][0],
            anomaly_end_time=config['event_time'][1]
        )

        print(f"加载初始路由表: {config['priming_path']}")
        r = Routes(config['priming_path'])
        r.collect_routes()
        r1 = r.get_route
        
        F1 = Feature()
        ind = 0
        features = []
        extraction_time = []
        print("开始提取无标签特征...")
        start_process_time = time.time()
        for u in data_slices:
            res, ext_time = F1.extract_features(ind, u, r1)
            extraction_time.append(ext_time)
            features.append(res)
            ind += 1
        
        total_process_time = time.time() - start_process_time
        print(f"为 {ind} 个时间片提取特征完成，总耗时: {total_process_time:.2f} 秒。")

        if features:
            output_path = config['output_path']
            if not os.path.exists(output_path): os.makedirs(output_path)
            w_path = os.path.join(output_path, config['output_filename'])
            print(f"保存无标签特征到: {w_path}")
            with open(w_path, 'w', newline='', encoding='utf-8') as f:
                # 健壮性检查，防止features为空列表时出错
                if features and features[0]:
                    writer = csv.DictWriter(f, fieldnames=features[0].keys())
                    writer.writeheader()
                    writer.writerows(features)
        
        print(f"--- 事件 {event_name} 处理完毕 ---")

    except (FileNotFoundError, ValueError) as e:
        print(f"[错误] 自动配置或处理事件 '{event_name}' 失败: {e}")
    except Exception as e:
        print(f"[错误] 处理事件 '{event_name}' 时发生未知错误: {e}")

    print("\n--- 程序运行结束 ---")