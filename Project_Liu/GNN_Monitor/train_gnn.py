# ==============================================================================
#  GNN Monitor - 训练脚本
# ==============================================================================
#
#  运行此脚本前，请确保BGP2Vec知识库 (bgp2vec.model) 已经生成。
#
#  功能：
#  1. 加载BGP2Vec模型作为节点特征查找表。
#  2. 加载原始BGP数据和标签。
#  3. 使用BGP2Vec向量作为初始特征，构建图数据集。
#  4. 训练GNN模型以进行异常分类。
#  5. 保存训练好的GNN模型 (realtime_gnn.pth)，用于在线推理。
#

import torch
import torch.optim as optim
from torch.nn import BCEWithLogitsLoss
from torch_geometric.loader import DataLoader
from gensim.models import Word2Vec

# 从其他模块导入
from models import BGP_GNN_Classifier
from utils import prepare_dataset, load_labels_from_csv, load_updates_from_folder
import config

def main():
    """主训练函数"""
    
    # --- 1. 加载BGP2Vec知识库 ---
    print("--- 步骤1: 加载BGP2Vec知识库... ---")
    bgp2vec_model_path = 'Project_Liu/BGP2Vec/bgp2vec.model'
    try:
        bgp2vec_model = Word2Vec.load(bgp2vec_model_path)
    except FileNotFoundError:
        print(f"错误: BGP2Vec模型 {bgp2vec_model_path} 未找到。")
        print("请先成功运行 'Project_Liu/BGP2Vec/train_bgp2vec.py'。")
        return
    print("知识库加载成功。")

    # --- 2. 加载和准备数据 ---
    print("\n--- 步骤2: 加载原始数据并构建图数据集... ---")
    event_folder = '/data/data/anomaly-event-routedata/leak-20041224-TTNet_in_Turkey_leak'
    label_file = f'{event_folder}/minute_labels.csv'
    update_folder = f'{event_folder}/data'
    
    label_data = load_labels_from_csv(label_file)
    update_lines = load_updates_from_folder(update_folder)
    
    dataset_with_mappings = prepare_dataset(
        update_lines, 
        label_data, 
        bgp2vec_model, 
        vector_size=config.INPUT_DIM
    )
    
    if not dataset_with_mappings:
        print("数据集中没有可训练的图，程序退出。")
        return
    
    print(f"成功创建了 {len(dataset_with_mappings)} 个图快照。")

    graphs = [item['graph'] for item in dataset_with_mappings]

    # --- 3. 初始化模型、优化器和损失函数 ---
    print("\n--- 步骤3: 初始化GNN模型及训练组件... ---")
    
    model = BGP_GNN_Classifier(
        input_dim=config.INPUT_DIM, 
        hidden_dim=config.HIDDEN_DIM, 
        output_dim=config.OUTPUT_DIM, 
        dropout_rate=config.DROPOUT_RATE
    )
    optimizer = optim.Adam(model.parameters(), lr=config.LEARNING_RATE)
    criterion = BCEWithLogitsLoss()

    print("模型结构:")
    print(model)

    # --- 4. 创建DataLoader并执行训练 ---
    print("\n--- 步骤4: 使用DataLoader打包数据并开始训练... ---")
    config.BATCH_SIZE = 32 if len(graphs) > 32 else len(graphs)
    if config.BATCH_SIZE == 0:
        print("错误：数据集为空，无法设置BATCH_SIZE。")
        return
        
    loader = DataLoader(graphs, batch_size=config.BATCH_SIZE, shuffle=True)

    model.train()
    for epoch in range(config.EPOCHS):
        epoch_loss = 0
        for batch_data in loader:
            optimizer.zero_grad()
            out, _ = model(batch_data.x, batch_data.edge_index, batch_data.batch)
            loss = criterion(out.squeeze(), batch_data.y)
            loss.backward()
            optimizer.step()
            epoch_loss += loss.item()

        print(f"Epoch {epoch+1:02d}, Loss: {epoch_loss/len(loader):.4f}")

    print("\n--- 训练完成 ---")
    
    # --- 5. 保存最终的GNN模型 ---
    model_save_path = 'Project_Liu/GNN_Monitor/realtime_gnn.pth'
    torch.save(model.state_dict(), model_save_path)
    print(f"\n用于实时监测的GNN模型已保存到: {model_save_path}")

if __name__ == '__main__':
    main() 