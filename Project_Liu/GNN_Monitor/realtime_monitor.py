# ==============================================================================
#  GNN Monitor - 实时监测与向量提取脚本
# ==============================================================================
#
#  功能：
#  1. 加载离线训练好的BGP2Vec知识库和GNN模型。
#  2. 模拟接收一批实时BGP更新数据。
#  3. 使用"离线训练，在线推理"架构，为这批数据构建图并提取高质量的上下文向量。
#

import torch
from gensim.models import Word2Vec

# 从其他模块导入
from models import BGP_GNN_Classifier
from utils import parse_bgp_line
import config
from torch_geometric.data import Data

# --- 全局变量，模拟只加载一次模型 ---
BGP2VEC_MODEL = None
GNN_MODEL = None
UNK_TOKEN = '[UNK]'

def load_models():
    """加载所有离线训练好的模型。在实际服务中，这个函数只在启动时调用一次。"""
    global BGP2VEC_MODEL, GNN_MODEL
    
    if BGP2VEC_MODEL is None:
        print("--- 加载BGP2Vec知识库... ---")
        try:
            BGP2VEC_MODEL = Word2Vec.load('Project_Liu/BGP2Vec/bgp2vec.model')
            print("BGP2Vec知识库加载成功。")
        except FileNotFoundError:
            print("错误: BGP2Vec模型未找到。")
            return False

    if GNN_MODEL is None:
        print("--- 加载GNN模型... ---")
        try:
            GNN_MODEL = BGP_GNN_Classifier(
                input_dim=config.INPUT_DIM,
                hidden_dim=config.HIDDEN_DIM,
                output_dim=config.OUTPUT_DIM,
                dropout_rate=config.DROPOUT_RATE
            )
            GNN_MODEL.load_state_dict(torch.load('Project_Liu/GNN_Monitor/realtime_gnn.pth'))
            GNN_MODEL.eval() # 切换到评估模式
            print("GNN模型加载成功。")
        except FileNotFoundError:
            print("错误: GNN模型未找到。")
            return False
            
    return True

def get_contextual_vectors(bgp_updates):
    """
    接收一批BGP更新，返回其中所有AS的高质量上下文向量。
    这是实时监测的核心函数。
    """
    if BGP2VEC_MODEL is None or GNN_MODEL is None:
        print("错误: 模型尚未加载。请先调用 load_models()。")
        return None, None

    # 1. 构建图
    edges = set()
    all_asns = set()
    for line in bgp_updates:
        update = parse_bgp_line(line)
        if not update: continue
        
        path = update['as_path']
        all_asns.update(path)
        for i in range(len(path) - 1):
            u, v = path[i], path[i+1]
            if u and v: edges.add(tuple(sorted((u, v))))

    if not all_asns:
        return {}
    
    sorted_asns = sorted(list(all_asns))
    asn_to_idx = {asn: i for i, asn in enumerate(sorted_asns)}
    idx_to_asn = {i: asn for asn, i in asn_to_idx.items()}
    num_nodes = len(sorted_asns)

    # 2. 特征查找 (核心实时逻辑)
    x = torch.zeros((num_nodes, config.INPUT_DIM), dtype=torch.float)
    for asn, idx in asn_to_idx.items():
        try:
            x[idx, :] = torch.tensor(BGP2VEC_MODEL.wv[asn])
        except KeyError:
            x[idx, :] = torch.tensor(BGP2VEC_MODEL.wv[UNK_TOKEN])

    edge_list = [[asn_to_idx[u], asn_to_idx[v]] for u, v in edges if u in asn_to_idx and v in asn_to_idx]
    edge_index = torch.tensor(edge_list, dtype=torch.long).t().contiguous() if edge_list else torch.empty((2, 0), dtype=torch.long)
    
    graph_data = Data(x=x, edge_index=edge_index)

    # 3. GNN推理 (精炼向量)
    with torch.no_grad():
        batch_vector = torch.zeros(graph_data.num_nodes, dtype=torch.long)
        _, refined_embeddings = GNN_MODEL(graph_data.x, graph_data.edge_index, batch_vector)

    # 4. 格式化输出
    final_vectors = {idx_to_asn[i]: vec for i, vec in enumerate(refined_embeddings)}
    
    return final_vectors

def main():
    """主执行与演示函数"""
    
    # --- 1. 加载所有模型 (在真实服务中，这只在启动时执行一次) ---
    if not load_models():
        return

    # --- 2. 模拟实时收到的BGP数据 ---
    #    这些数据可以包含新的、训练时从未见过的AS
    #    修正了格式，确保有足够多的 '|' 分隔字段
    realtime_bgp_updates = [
        "BGP4MP|1632996000|A|202.12.28.1|701|208.65.153.0/24|701 1239 6453 99999|EGP|...|NAG||", # 99999 是一个全新的AS
        "BGP4MP|1632996000|A|192.0.2.1|3356|198.51.100.0/24|3356 1299 88888 6453|EGP|...|NAG||", # 88888 也是全新的
    ]
    print(f"\n--- 模拟收到 {len(realtime_bgp_updates)} 条实时BGP更新... ---")

    # --- 3. 获取高质量上下文向量 ---
    contextual_vectors = get_contextual_vectors(realtime_bgp_updates)

    if contextual_vectors:
        print("\n--- 成功提取经过GNN精炼的上下文向量 ---")
        for asn, vector in contextual_vectors.items():
            # 检查这个AS是否是全新的
            is_new = asn not in BGP2VEC_MODEL.wv
            note = "(来自[UNK]初始向量)" if is_new else ""
            print(f"  ASN: {asn:<10} {note:<25} -> Vector: {vector[:5]}...")

if __name__ == '__main__':
    main() 