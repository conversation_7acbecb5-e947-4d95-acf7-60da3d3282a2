# ==============================================================================
#  全局AS向量导出脚本
# ==============================================================================
#
#  运行此脚本前，请确保已经成功运行 train.py 并生成了 bgp_gnn_model.pth 模型文件。
#
#  功能：
#  1. 加载已训练的GNN模型。
#  2. 遍历数据集中的所有图快照。
#  3. 为每个图中的每个AS提取其上下文向量。
#  4. 对每个AS在所有图中出现过的向量进行平均，生成一个全局向量。
#  5. 将所有AS的全局向量保存到一个文件 (global_as_vectors.pt) 中。
#

import torch
from collections import defaultdict

# 从其他模块导入
from models import BGP_GNN_Classifier
from utils import prepare_dataset, load_labels_from_csv, load_updates_from_folder
import config

def export_global_vectors():
    """主执行函数"""

    # --- 1. 加载模型 ---
    print("--- 步骤1: 加载已训练的GNN模型... ---")
    model = BGP_GNN_Classifier(
        input_dim=config.INPUT_DIM,
        hidden_dim=config.HIDDEN_DIM,
        output_dim=config.OUTPUT_DIM,
        dropout_rate=config.DROPOUT_RATE
    )
    model_path = 'bgp_gnn_model.pth'
    try:
        model.load_state_dict(torch.load(model_path))
    except FileNotFoundError:
        print(f"错误: 模型文件 {model_path} 不存在。请先运行 train.py 进行训练。")
        return
    
    model.eval() # 切换到评估模式
    print("模型加载成功。")

    # --- 2. 加载完整数据集 ---
    print("\n--- 步骤2: 加载完整数据集... ---")
    event_folder = '/data/data/anomaly-event-routedata/leak-20041224-TTNet_in_Turkey_leak'
    label_file = f'{event_folder}/minute_labels.csv'
    update_folder = f'{event_folder}/data'
    
    label_data = load_labels_from_csv(label_file)
    update_lines = load_updates_from_folder(update_folder)
    dataset_with_mappings = prepare_dataset(update_lines, label_data)

    if not dataset_with_mappings:
        print("数据为空，无法导出向量。")
        return
    print(f"数据集加载成功，包含 {len(dataset_with_mappings)} 个图快照。")

    # --- 3. 遍历所有图，聚合向量 ---
    print("\n--- 步骤3: 开始遍历所有图并聚合AS向量... ---")
    all_as_vectors = defaultdict(list)

    with torch.no_grad():
        for i, item in enumerate(dataset_with_mappings):
            graph = item['graph']
            mapping = item['mapping']
            idx_to_asn = {idx: asn for asn, idx in mapping.items()}

            batch_vector = torch.zeros(graph.num_nodes, dtype=torch.long)
            _, node_embeddings = model(graph.x, graph.edge_index, batch_vector)

            for node_idx, embedding in enumerate(node_embeddings):
                asn = idx_to_asn[node_idx]
                all_as_vectors[asn].append(embedding)
            
            if (i + 1) % 50 == 0:
                print(f"  已处理 {i + 1}/{len(dataset_with_mappings)} 个图...")

    print("向量聚合完成。")

    # --- 4. 计算全局平均向量 ---
    print("\n--- 步骤4: 计算全局平均向量... ---")
    global_vectors = {}
    for asn, vectors in all_as_vectors.items():
        if vectors:
            # 将向量列表堆叠成一个张量，然后沿维度0求均值
            global_vectors[asn] = torch.stack(vectors).mean(dim=0)
    
    total_unique_asns = len(global_vectors)
    print(f"成功为 {total_unique_asns} 个独立AS计算了全局向量。")

    # --- 5. 保存全局向量表 ---
    output_path = 'global_as_vectors.pt'
    torch.save(global_vectors, output_path)
    print(f"\n全局向量知识库已保存到: {output_path}")

if __name__ == '__main__':
    export_global_vectors() 