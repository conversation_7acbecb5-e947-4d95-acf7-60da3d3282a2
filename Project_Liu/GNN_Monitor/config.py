# ==============================================================================
#  项目配置文件
# ==============================================================================

# --- 模型超参数 ---
INPUT_DIM = 64      # 节点特征维度，必须与BGP2Vec的VECTOR_SIZE一致
HIDDEN_DIM = 64     # GNN隐层维度
OUTPUT_DIM = 1      # 输出维度 (二分类)
DROPOUT_RATE = 0.42 # Dropout比率

# --- 训练超参数 ---
LEARNING_RATE = 0.001 # 优化器学习率
EPOCHS = 10           # 训练轮数
BATCH_SIZE = 1        # 批处理大小 (由于模拟数据量小，设为1；真实场景可增大)

# --- 数据路径 (未来可扩展使用) ---
# LABEL_FILE_PATH = 'data/labels.csv'
# UPDATE_FILE_PATH = 'data/updates.mrt' 