import torch
from torch_geometric.data import Data
from collections import defaultdict
import csv
import os

UNK_TOKEN = '[UNK]' # 确保与BGP2Vec训练脚本中的一致

def load_labels_from_csv(file_path):
    """从CSV文件加载标签数据，返回 {timestamp: label} 字典。"""
    label_data = {}
    print(f"正在从 {file_path} 加载标签...")
    with open(file_path, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            try:
                # 假设CSV有 'timestamp' 和 'label' 列
                label_data[int(row['timestamp'])] = int(row['label'])
            except (KeyError, ValueError) as e:
                print(f"警告: 解析CSV行时出错: {row}, 错误: {e}")
    print(f"成功加载 {len(label_data)} 条标签。")
    return label_data

def load_updates_from_folder(folder_path):
    """从指定文件夹加载所有txt格式的BGP update数据。"""
    update_lines = []
    print(f"正在从 {folder_path} 加载BGP updates...")
    if not os.path.isdir(folder_path):
        print(f"错误: 文件夹不存在: {folder_path}")
        return []
    
    for filename in sorted(os.listdir(folder_path)):
        if filename.endswith(".txt"):
            file_path = os.path.join(folder_path, filename)
            with open(file_path, 'r', errors='ignore') as f:
                update_lines.extend(f.readlines())
    print(f"成功加载 {len(update_lines)} 行update数据。")
    return update_lines

def parse_bgp_line(line):
    """解析单行BGP4MP格式的数据。"""
    parts = line.strip().split('|')
    if len(parts) < 7:
        return None
    
    try:
        timestamp = int(parts[1])
        msg_type = parts[2]
        as_path_str = parts[6]
    except (ValueError, IndexError):
        return None
    
    if msg_type == 'A' and as_path_str:
        as_path = as_path_str.split(' ')
        # 移除AS_SET中的花括号
        as_path = [asn.replace('{', '').replace('}', '') for asn in as_path]
        return {'timestamp': timestamp, 'type': 'announcement', 'as_path': as_path}
    return None

def create_labeled_graph(updates_in_window, label, bgp2vec_model, vector_size):
    """
    根据BGP update、标签和BGP2Vec模型，创建带标签的图数据对象和节点映射。
    节点特征将从BGP2Vec模型中查找。
    """
    node_features = defaultdict(lambda: {'sent': 0, 'received': 0})
    edges = set()
    all_asns = set()

    for line in updates_in_window:
        update = parse_bgp_line(line)
        if not update:
            continue

        as_path = update['as_path']
        # 去重连续重复的ASN
        cleaned_path = [asn for i, asn in enumerate(as_path) if i == 0 or asn != as_path[i-1]]

        for asn in cleaned_path:
            node_features[asn]['sent'] += 1
            all_asns.add(asn)
        for asn in cleaned_path[1:]:
            node_features[asn]['received'] += 1
            
        for i in range(len(cleaned_path) - 1):
            u, v = cleaned_path[i], cleaned_path[i+1]
            if u and v: # 确保ASN不为空
                edge = tuple(sorted((u, v)))
                edges.add(edge)

    if not all_asns:
        return None, None

    sorted_asns = sorted(list(all_asns))
    asn_to_idx = {asn: i for i, asn in enumerate(sorted_asns)}
    num_nodes = len(sorted_asns)

    x = torch.zeros((num_nodes, vector_size), dtype=torch.float)
    for asn, idx in asn_to_idx.items():
        try:
            # 从BGP2Vec模型中查找向量
            x[idx, :] = torch.tensor(bgp2vec_model.wv[asn])
        except KeyError:
            # 如果找不到 (这是一个在BGP2Vec训练时未见过的AS)，则使用 [UNK] 向量
            x[idx, :] = torch.tensor(bgp2vec_model.wv[UNK_TOKEN])

    edge_list = []
    for u, v in edges:
        if u in asn_to_idx and v in asn_to_idx:
            u_idx, v_idx = asn_to_idx[u], asn_to_idx[v]
            edge_list.append([u_idx, v_idx])
            edge_list.append([v_idx, u_idx])
    
    edge_index = torch.tensor(edge_list, dtype=torch.long).t().contiguous() if edge_list else torch.empty((2, 0), dtype=torch.long)
    
    graph_snapshot = Data(x=x, edge_index=edge_index, y=torch.tensor([label], dtype=torch.float))
    
    return graph_snapshot, asn_to_idx

def prepare_dataset(update_lines, label_data, bgp2vec_model, vector_size):
    """从原始数据行和标签字典准备图数据集。"""
    dataset_with_mappings = []
    updates_by_time = defaultdict(list)
    for line in update_lines:
        try:
            timestamp = int(line.strip().split('|')[1])
            updates_by_time[timestamp].append(line)
        except (IndexError, ValueError):
            continue

    for timestamp, updates in updates_by_time.items():
        if timestamp in label_data:
            label = label_data[timestamp]
            graph, mapping = create_labeled_graph(updates, label, bgp2vec_model, vector_size)
            if graph:
                dataset_with_mappings.append({'graph': graph, 'mapping': mapping})
    return dataset_with_mappings 