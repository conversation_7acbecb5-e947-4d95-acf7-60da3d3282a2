# ==============================================================================
#  BGP Anomaly Detection with GNN - 主训练脚本
# ==============================================================================

import torch
import torch.optim as optim
from torch.nn import BCEWithLogitsLoss
from torch_geometric.loader import DataLoader

# 从其他模块导入
from models import BGP_GNN_Classifier
from utils import prepare_dataset, load_labels_from_csv, load_updates_from_folder
import config

def main():
    """主训练函数"""
    
    # --- 1. 加载和准备数据 ---
    
    # --- 使用真实数据 ---
    event_folder = '/data/data/anomaly-event-routedata/leak-20041224-TTNet_in_Turkey_leak'
    label_file = f'{event_folder}/minute_labels.csv'
    update_folder = f'{event_folder}/data'
    
    label_data = load_labels_from_csv(label_file)
    update_lines = load_updates_from_folder(update_folder)

    # --- 使用模拟数据 (注释掉真实数据加载部分即可切换) ---
    # label_data = {
    #     1503609900: 0, # 正常
    #     1503609901: 1, # 异常
    # }
    # update_lines = [
    #     "BGP4MP|1503609900|A|2a01:2a8::3|1836|2407:4600::/32|1836 13030 1299 6453 24482 24538 45295|EGP|...|NAG||",
    #     "BGP4MP|1503609900|A|146.228.1.3|1836|192.141.188.0/24|1836 3356 3549 28210 262664 262664 264873 267494|IGP|...|NAG||",
    #     "BGP4MP|1503609901|A|193.0.0.56|3333|2804:2e40::/32|3333 3257 12956 18881 61763 262593|IGP|...|NAG||",
    #     "BGP4MP|1503609901|A|193.0.0.56|3333|2804:2e40::/32|3333 1257 12956 18881 61763 262593|IGP|...|NAG||", # 模拟劫持事件
    #     "BGP4MP|1503609901|A|2001:8e0:0:ffff::9|8758|2804:2e78::/32|8758 8220 6939 28329 28213|IGP|...|NAG||",
    # ]

    print("\n--- 步骤1: 正在从原始数据构建图数据集... ---")
    dataset_with_mappings = prepare_dataset(update_lines, label_data)
    
    if not dataset_with_mappings:
        print("数据集中没有可训练的图，程序退出。")
        return
    
    print(f"成功创建了 {len(dataset_with_mappings)} 个图快照。")

    # 提取所有图对象用于DataLoader
    graphs = [item['graph'] for item in dataset_with_mappings]

    # --- 2. 初始化模型、优化器和损失函数 ---
    print("\n--- 步骤2: 初始化GNN模型及训练组件... ---")
    
    model = BGP_GNN_Classifier(
        input_dim=config.INPUT_DIM, 
        hidden_dim=config.HIDDEN_DIM, 
        output_dim=config.OUTPUT_DIM, 
        dropout_rate=config.DROPOUT_RATE
    )
    optimizer = optim.Adam(model.parameters(), lr=config.LEARNING_RATE)
    criterion = BCEWithLogitsLoss()

    print("模型结构:")
    print(model)

    # --- 3. 创建DataLoader并执行训练 ---
    print("\n--- 步骤3: 使用DataLoader打包数据并开始训练... ---")
    # 对于真实数据，可以适当增大学习批次
    config.BATCH_SIZE = 32 if len(graphs) > 32 else len(graphs)
    if config.BATCH_SIZE == 0:
        print("错误：数据集为空，无法设置BATCH_SIZE。")
        return
        
    loader = DataLoader(graphs, batch_size=config.BATCH_SIZE, shuffle=True)

    model.train() # 将模型设置为训练模式
    for epoch in range(config.EPOCHS):
        epoch_loss = 0
        for batch_data in loader:
            optimizer.zero_grad()
            # 模型现在返回分类输出和节点嵌入，训练时我们只用分类输出计算损失
            out, _ = model(batch_data.x, batch_data.edge_index, batch_data.batch)
            loss = criterion(out.squeeze(), batch_data.y)
            loss.backward()
            optimizer.step()
            epoch_loss += loss.item()

        print(f"Epoch {epoch+1:02d}, Loss: {epoch_loss/len(loader):.4f}")

    print("\n--- 训练完成 ---")
    
    # --- 4. 演示如何获取节点向量 ---
    print("\n--- 步骤4: 使用训练好的模型提取节点向量 (以第100个图为例)... ---")
    model.eval() # 将模型设置为评估模式

    # --- 为了更好地演示，我们检查一个更靠后的图，例如第100个 ---
    graph_index_to_inspect = 99

    # 以数据集中的第100个图为例
    if len(dataset_with_mappings) > graph_index_to_inspect:
        item_to_inspect = dataset_with_mappings[graph_index_to_inspect]
        sample_graph = item_to_inspect['graph']
        asn_mapping = item_to_inspect['mapping']
        
        # 创建一个反向映射，从索引找回ASN
        idx_to_asn = {idx: asn for asn, idx in asn_mapping.items()}

        print(f"正在分析第 {graph_index_to_inspect + 1} 个图 (标签: {sample_graph.y.item()})，它包含 {sample_graph.num_nodes} 个节点 (AS)。")

        # 对于单个图的推理，需要创建一个全零的batch向量
        batch_vector = torch.zeros(sample_graph.num_nodes, dtype=torch.long)
        
        with torch.no_grad():
            _, node_embeddings = model(sample_graph.x, sample_graph.edge_index, batch_vector)

        print(f"成功为图中的 {node_embeddings.shape[0]} 个AS提取了向量 (维度: {node_embeddings.shape[1]})。")
        print("AS编号及其对应的向量 (展示前5个):")
        for i in range(min(5, node_embeddings.shape[0])):
            asn = idx_to_asn[i]
            vector = node_embeddings[i]
            print(f"  ASN: {asn:<10} -> Vector: {vector[:5]}...")


    # 保存训练好的模型以备后用
    model_save_path = 'bgp_gnn_model.pth'
    torch.save(model.state_dict(), model_save_path)
    print(f"\n模型已保存到: {model_save_path}")

if __name__ == '__main__':
    main() 