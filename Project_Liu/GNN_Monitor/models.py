import torch
import torch.nn.functional as F
from torch.nn import Sequential, Linear, ReLU, BatchNorm1d, Dropout
from torch_geometric.nn import GINConv, global_mean_pool

class BGP_GNN_Classifier(torch.nn.Module):
    """
    根据论文《Unveiling the Potential of GNNs for BGP Anomaly Detection》描述构建的GNN模型。
    """
    def __init__(self, input_dim, hidden_dim, output_dim, dropout_rate):
        super(BGP_GNN_Classifier, self).__init__()
        
        # 为GINConv层定义MLP (遵循论文描述)
        mlp1 = Sequential(Linear(input_dim, hidden_dim), ReLU(), Linear(hidden_dim, hidden_dim))
        mlp2 = Sequential(Linear(hidden_dim, hidden_dim), ReLU(), Linear(hidden_dim, hidden_dim))
        mlp3 = Sequential(Linear(hidden_dim, hidden_dim), ReLU(), Linear(hidden_dim, hidden_dim))

        # 定义GNN核心层 (3个GIN层 + 2个归一化层)
        self.conv1 = GINConv(nn=mlp1)
        self.bn1 = BatchNorm1d(hidden_dim)

        self.conv2 = GINConv(nn=mlp2)
        self.bn2 = BatchNorm1d(hidden_dim)

        self.conv3 = GINConv(nn=mlp3)

        # 定义分类器部分
        self.dropout = Dropout(p=dropout_rate)
        self.fc = Linear(hidden_dim, output_dim)

    def forward(self, x, edge_index, batch):
        # GNN消息传递过程
        x = self.conv1(x, edge_index)
        x = self.bn1(x)
        x = F.relu(x)
        x = self.dropout(x)

        x = self.conv2(x, edge_index)
        x = self.bn2(x)
        x = F.relu(x)
        x = self.dropout(x)

        x = self.dropout(x)

        node_embeddings = self.conv3(x, edge_index)
        node_embeddings = F.relu(node_embeddings)
        node_embeddings = self.dropout(node_embeddings)
        
        # 全局池化和分类
        graph_embedding = global_mean_pool(node_embeddings, batch)
        out = self.fc(graph_embedding)
        
        return out, node_embeddings 