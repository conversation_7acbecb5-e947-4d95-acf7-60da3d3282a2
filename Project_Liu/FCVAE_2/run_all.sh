
# python train.py --data_dir ./data/AIOPS  --window 128  --condition_emb_dim 16  --condition_mode 2  --save_file ./result.txt  --gpu 0 --kernel_size 16 --stride 16 --dropout_rate 0
# python train.py --data_dir ./data/NAB  --window 32  --condition_emb_dim 8  --condition_mode 2  --save_file ./result.txt  --gpu 0 --kernel_size 12 --stride 4 --dropout_rate 0
# python train.py --data_dir ./data/WSD --window 128  --condition_emb_dim 16  --condition_mode 2  --save_file ./result.txt  --gpu 0 --kernel_size 8 --stride 8 --dropout_rate 0
# python train.py --data_dir ./data/Yahoo  --window 48  --condition_emb_dim 64  --condition_mode 2  --save_file ./result.txt  --gpu 0 --kernel_size 24 --stride 8 --dropout_rate 0.05
