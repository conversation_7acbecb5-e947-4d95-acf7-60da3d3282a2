import torch
import numpy as np
import matplotlib.pyplot as plt
from model import MyVAE
import argparse
from pytorch_lightning import Trainer
from tqdm import tqdm
import os
from get_f1_score import best_f1, delay_f1, best_f1_without_pointadjust
from sklearn.metrics import f1_score, precision_score, recall_score

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def load_model(checkpoint_path):
    print(f"加载CVAE模型: {checkpoint_path}")
    model = MyVAE.load_from_checkpoint(checkpoint_path=checkpoint_path)
    model.eval()
    return model

def get_reconstruction(model, dataloader, save_dir):
    print("数据集大小:", len(dataloader.dataset))
    print("批次大小:", dataloader.batch_size)
    print("总批次数:", len(dataloader))
    
    # 检查是否存在保存的重建结果
    reconstructions_path = os.path.join(save_dir, 'reconstructions.npy')
    originals_path = os.path.join(save_dir, 'originals.npy')
    labels_path = os.path.join(save_dir, 'labels.npy')
    
    if os.path.exists(reconstructions_path) and os.path.exists(originals_path) and os.path.exists(labels_path):
        print("加载已保存的重建结果...")
        reconstructions = np.load(reconstructions_path)
        originals = np.load(originals_path)
        labels = np.load(labels_path)
        return reconstructions, originals, labels
    
    print("计算重建结果...")
    try:
        reconstructions = []
        originals = []
        labels = []
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(dataloader, desc='FCVAE重建进度')):
                x = batch[0].cuda()
                y = batch[1]
                
                # 创建两个不同的mask
                # 1. 预测mask：只有最后一个时间步是0
                pred_mask = torch.ones_like(x).cuda()
                pred_mask[:, :, -1] = 0
                
                # 2. 重构mask：全部设为0，测试完整重构能力
                recon_mask = torch.zeros_like(x).cuda()
                
                # 获取两种结果
                x_pred, _ = model(x, mode='test', mask=pred_mask)  # 预测结果
                x_recon, _ = model(x, mode='test', mask=recon_mask)  # 重构结果
                
                # 合并结果：使用预测结果的最后一个时间步，其他使用重构结果
                x_combined = x_recon.clone()
                x_combined[:, :, -1] = x_pred[:, :, -1]
                
                # 计算重构误差
                recon_error = torch.mean((x_recon - x) ** 2).item()
                
                reconstructions.append(x_combined.cpu().numpy())
                originals.append(x.cpu().numpy())
                labels.append(y.cpu().numpy())
                
                if batch_idx % 50 == 0:
                    print(f"\n批次 {batch_idx}:")
                    print(f"最后一个时间步的异常点数量: {torch.sum(y[:, -1] == 1)}")
                    print(f"平均重构误差: {recon_error:.6f}")
                
                # 清理GPU内存
                del x_pred, x_recon, x_combined
                del x
                del pred_mask, recon_mask
                torch.cuda.empty_cache()
        
        reconstructions = np.concatenate(reconstructions, axis=0)
        originals = np.concatenate(originals, axis=0)
        labels = np.concatenate(labels, axis=0)
        
        print("\n最终数据统计:")
        print(f"重建数据形状: {reconstructions.shape}")
        print(f"原始数据形状: {originals.shape}")
        print(f"标签形状: {labels.shape}")
        print(f"最后一个时间步的异常点总数: {np.sum(labels[:, -1] == 1)}")
        
        # 保存重建结果
        print("保存重建结果...")
        os.makedirs(save_dir, exist_ok=True)
        np.save(reconstructions_path, reconstructions)
        np.save(originals_path, originals)
        np.save(labels_path, labels)
        
        return reconstructions, originals, labels
    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        raise e

def plot_anomaly_detection(original, reconstructed, labels, score, save_path):
    try:
        # 创建多个子图，每个子图显示不同的样本
        num_samples = 4  # 显示4个样本
        plt.figure(figsize=(20, 15))
        
        # 随机选择样本，确保样本之间间隔足够大
        total_samples = original.shape[0]
        min_gap = 100  # 样本之间的最小间隔
        
        # 随机选择第一个样本
        start_idx = np.random.randint(0, total_samples - min_gap * num_samples)
        selected_indices = [start_idx + i * min_gap for i in range(num_samples)]
        
        print(f"\n选择的样本索引: {selected_indices}")
        
        # 计算整体的重建误差和阈值
        reconstruction_errors = np.mean((original - reconstructed) ** 2, axis=1)  # (N, 48)
        threshold = np.percentile(reconstruction_errors, 95)  # 使用95百分位数作为阈值
        predicted_anomalies = (reconstruction_errors > threshold).astype(int)
        
        # 计算F1分数
        true_labels = labels  # (N, 48)
        f1_scores = []
        precisions = []
        recalls = []
        
        for t in range(labels.shape[1]):
            f1 = f1_score(true_labels[:, t], predicted_anomalies[:, t])
            precision = precision_score(true_labels[:, t], predicted_anomalies[:, t])
            recall = recall_score(true_labels[:, t], predicted_anomalies[:, t])
            f1_scores.append(f1)
            precisions.append(precision)
            recalls.append(recall)
        
        print("\n异常检测性能指标:")
        print(f"平均F1分数: {np.mean(f1_scores):.4f}")
        print(f"平均精确率: {np.mean(precisions):.4f}")
        print(f"平均召回率: {np.mean(recalls):.4f}")
        print(f"最后时间步F1分数: {f1_scores[-1]:.4f}")
        
        for i, start_idx in enumerate(selected_indices):
            # 创建子图
            plt.subplot(num_samples, 1, i+1)
            
            # 获取连续的时间窗口数据
            end_idx = min(start_idx + min_gap, total_samples)
            time_steps = np.array(range(start_idx, end_idx))
            
            # 获取每个窗口的数据
            actual_values = original[time_steps, 0, -1]  # 实际值
            predicted_values = reconstructed[time_steps, 0, -1]  # 预测值
            window_labels = labels[time_steps, -1]  # 真实标签
            window_predictions = predicted_anomalies[time_steps, -1]  # 预测的异常
            
            # 绘制实际值和预测值
            plt.plot(time_steps, actual_values, 'b-', label='实际值', linewidth=2)
            plt.plot(time_steps, predicted_values, 'r--', label='FCVAE预测值', linewidth=2)
            
            # 标记真实异常点
            true_anomaly_indices = np.where(window_labels == 1)[0]
            if len(true_anomaly_indices) > 0:
                plt.scatter(time_steps[true_anomaly_indices], actual_values[true_anomaly_indices], 
                          color='red', s=100, label='真实异常点', zorder=5)
            
            # 标记检测出的异常点
            detected_anomaly_indices = np.where(window_predictions == 1)[0]
            if len(detected_anomaly_indices) > 0:
                plt.scatter(time_steps[detected_anomaly_indices], predicted_values[detected_anomaly_indices], 
                          color='yellow', edgecolor='black', s=150, label='检测出的异常点', zorder=4)
            
            # 计算并显示这段时间窗口的统计信息
            window_f1 = f1_score(window_labels, window_predictions)
            window_precision = precision_score(window_labels, window_predictions)
            window_recall = recall_score(window_labels, window_predictions)
            
            plt.title(f'时间窗口 {start_idx}-{end_idx} 的预测结果\n'
                     f'F1分数: {window_f1:.3f}, 精确率: {window_precision:.3f}, 召回率: {window_recall:.3f}')
            
            plt.xlabel('时间步')
            plt.ylabel('值')
            plt.grid(True)
            plt.legend()
        
        plt.tight_layout()
        
        # 确保保存目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path)
        plt.close()
        
        # 保存详细的评估结果
        results_file = os.path.join(os.path.dirname(save_path), 'evaluation_results.txt')
        with open(results_file, 'w') as f:
            f.write("异常检测评估结果\n")
            f.write("=" * 50 + "\n")
            f.write(f"使用的阈值: {threshold:.6f}\n")
            f.write(f"总样本数: {total_samples}\n")
            f.write(f"真实异常点数量: {np.sum(true_labels)}\n")
            f.write(f"检测出的异常点数量: {np.sum(predicted_anomalies)}\n")
            f.write("\n时间序列整体性能:\n")
            f.write(f"平均F1分数: {np.mean(f1_scores):.4f}\n")
            f.write(f"平均精确率: {np.mean(precisions):.4f}\n")
            f.write(f"平均召回率: {np.mean(recalls):.4f}\n")
            f.write("\n最后时间步性能:\n")
            f.write(f"F1分数: {f1_scores[-1]:.4f}\n")
            f.write(f"精确率: {precisions[-1]:.4f}\n")
            f.write(f"召回率: {recalls[-1]:.4f}\n")
            
    except Exception as e:
        print(f"绘图时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        raise e

def calculate_metrics(original, reconstructed, labels):
    # 打印数据形状
    print("\n数据形状信息:")
    print(f"原始数据形状: {original.shape}")
    print(f"重建数据形状: {reconstructed.shape}")
    print(f"标签数据形状: {labels.shape}")
    
    # 计算重建误差
    reconstruction_error = np.mean((original - reconstructed) ** 2, axis=1)  # 形状: (263438, 64)
    score = -reconstruction_error  # 转换为异常分数
    
    # 对每个样本取最大重建误差作为异常分数
    score = np.max(score, axis=1)  # 形状: (263438,)
    
    # 确保标签是一维数组
    labels_flat = labels[:, -1].flatten()  # 形状: (263438,)
    print(f"展平后的标签形状: {labels_flat.shape}")
    print(f"异常分数形状: {score.shape}")
    
    # 确保score和labels_flat具有相同的长度
    if len(score) != len(labels_flat):
        print(f"警告：异常分数长度 ({len(score)}) 与标签长度 ({len(labels_flat)}) 不匹配")
        # 如果长度不匹配，截取相同长度
        min_len = min(len(score), len(labels_flat))
        score = score[:min_len]
        labels_flat = labels_flat[:min_len]
    
    # 计算最佳F1分数
    best_f1_score, best_precision, best_recall, best_predict = best_f1(score, labels_flat)
    
    # 计算延迟F1分数
    delay_f1_score, delay_precision, delay_recall, delay_predict = delay_f1(score, labels_flat)
    
    # 计算无点调整的F1分数
    best_f1_score_no_adj, best_precision_no_adj, best_recall_no_adj, _ = best_f1_without_pointadjust(score, labels_flat)
    
    print(f"\n异常检测评估指标:")
    print(f"最佳F1分数: {best_f1_score:.4f}")
    print(f"最佳精确率: {best_precision:.4f}")
    print(f"最佳召回率: {best_recall:.4f}")
    print(f"\n延迟F1分数: {delay_f1_score:.4f}")
    print(f"延迟精确率: {delay_precision:.4f}")
    print(f"延迟召回率: {delay_recall:.4f}")
    print(f"\n无点调整F1分数: {best_f1_score_no_adj:.4f}")
    print(f"无点调整精确率: {best_precision_no_adj:.4f}")
    print(f"无点调整召回率: {best_recall_no_adj:.4f}")
    
    return score

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--ckpt_path', type=str, required=True, help='CVAE模型检查点路径')
    parser.add_argument('--save_dir', type=str, default='./comparison_results', help='结果保存目录')
    args = parser.parse_args()
    
    print("开始执行主函数...")
    print(f"检查点路径: {args.ckpt_path}")
    print(f"保存目录: {args.save_dir}")
    
    try:
        # 加载CVAE模型
        print("\n正在加载模型...")
        model = load_model(args.ckpt_path)
        trainer = Trainer(accelerator="gpu", devices=1)
        
        # 获取测试数据
        print("\n正在准备数据加载器...")
        test_loader = model.mydataloader("test")
        print(f"\n测试数据集信息:")
        print(f"数据集大小: {len(test_loader.dataset)}")
        print(f"批次大小: {test_loader.batch_size}")
        print(f"总批次数: {len(test_loader)}")
        
        # 获取重建结果
        print("\n开始获取重建结果...")
        reconstructions, originals, labels = get_reconstruction(model, test_loader, args.save_dir)
        print(f"\n重建数据形状: {reconstructions.shape}")
        
        # 绘制异常检测结果
        print("\n开始绘制结果...")
        plot_anomaly_detection(
            originals, 
            reconstructions,
            labels,
            None,  # 不再需要score
            f'{args.save_dir}/anomaly_detection.png'
        )
        print("\n执行完成！")
        
    except Exception as e:
        print(f"\n执行过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        
if __name__ == "__main__":
    main() 