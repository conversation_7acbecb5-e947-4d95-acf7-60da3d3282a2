import torch
import torch.utils.data
import logging
import numpy as np
import pandas as pd
import os
import datapreprocess


class UniDataset(torch.utils.data.Dataset):
    def __init__(
        self,
        use_label,
        window,
        data_dir,
        data_name,
        mode,
        sliding_window_size,
        data_pre_mode=0,
    ):
        self.window = window
        self.data_dir = data_dir
        self.data_name = data_name
        file_list = os.listdir(data_dir)
        value_all = []
        label_all = []
        missing_all = []
        self.len = 0
        self.sample_num = 0
        for file in file_list:
            file_path = os.path.join(data_dir, file)
            df = pd.read_csv(file_path)
            df_train = df[: int(0.35 * len(df))]
            df_train = df_train.bfill() #就是使用下一个能用的值去填充上一个缺失的值
            train_value = np.asarray(df_train["value"])
            train_label = np.asarray(df_train["label"])
            train_value = train_value[np.where(train_label == 0)[0]]#只保留label为0的值 这样是会为了只使用正常值进行训练
            train_max = train_value.max()
            train_min = train_value.min()
            if mode == "train":
                df = df[: int(0.35 * len(df))]
            elif mode == "valid":
                df = df[int(0.35 * len(df)) : int(0.5 * len(df))]
            elif mode == "test":
                df = df[int(0.5 * len(df)) :]
            timestamp, missing, (value, label) = datapreprocess.complete_timestamp(
                df["timestamp"], (df["value"], df["label"])
            )#将时间戳缺失部分和缺失值和value和label进行补全
            value = value.astype(float)#将value转换为float类型
            missing2 = np.isnan(value)#判断value中是否存在缺失值
            missing = np.logical_or(missing, missing2).astype(int)#将missing和missing2进行或运算 并转换为int类型
            label = label.astype(float)#将label转换为float类型
            label[np.where(missing == 1)[0]] = np.nan#将缺失值的label设置为nan
            value[np.where(missing == 1)[0]] = np.nan#将缺失值的value设置为nan
            df2 = pd.DataFrame()#创建一个DataFrame
            df2["timestamp"] = timestamp#将timestamp添加到df2中
            df2["value"] = value#将value添加到df2中
            df2["label"] = label#将label添加到df2中
            df2["missing"] = missing.astype(int)#将missing转换为int类型并添加到df2中
            df2 = df2.bfill()#使用下一个能用的值去填充上一个缺失的值
            df2 = df2.fillna(0)#将缺失值填充为0，防止第一个值为nan
            df2["label"] = df2["label"].astype(int)#将label转换为int类型
            if data_pre_mode == 0:#如果data_pre_mode为0 则进行标准化
                df2["value"], *_ = datapreprocess.standardize_kpi(df2["value"])
            else:
                v = np.asarray(df2["value"])#将value转换为numpy数组   
                v = 2 * (v - train_min) / (train_max - train_min) - 1#进行标准化
                df2["value"] = v#将标准化后的value添加到df2中
            timestamp, values, labels = (
                np.asarray(df2["timestamp"]),
                np.clip(np.asarray(df2["value"]), -40, 40),
                np.asarray(df2["label"]),
            )
            values[np.where(missing == 1)[0]] = 0
            if (mode == "train" or mode == "valid") and use_label == 1:
                values[np.where(labels == 1)[0]] = 0
            elif (mode == "train" or mode == "valid") and use_label == 0:
                labels[:] = 0
            else:
                pass
            values = np.convolve(
                values,
                np.ones((sliding_window_size,)) / sliding_window_size,
                mode="valid",
            )#对values进行卷积运算 计算每个窗口的平均值
            timestamp = timestamp[sliding_window_size - 1 :]
            labels = labels[sliding_window_size - 1 :]
            missing = missing[sliding_window_size - 1 :]#将窗口进行匹配
            value_all.append(values)#这里的value_all是一个列表，列表中每个元素是一个numpy数组，每个数组的长度为window
            label_all.append(labels)
            missing_all.append(missing)
            self.sample_num += max(len(values) - window + 1, 0)#window是最终的窗口大小，sliding_window_size是滑动窗口的大小
        self.samples, self.labels, self.miss_label = self.__getsamples(
            value_all, label_all, missing_all
        )

    def __getsamples(self, values, labels, missing):
        X = torch.zeros((self.sample_num, 1, self.window))
        Y = torch.zeros((self.sample_num, self.window))
        Z = torch.zeros((self.sample_num, self.window))
        i = 0
        for cnt in range(len(values)):#这里的valudes是valuesall，是一个列表，列表中每个元素是一个numpy数组，每个数组的长度为window
            v = values[cnt]
            l = labels[cnt]
            m = missing[cnt]
            for j in range(len(v) - self.window + 1):
                X[i, 0, :] = torch.from_numpy(v[j : j + self.window])
                Y[i, :] = torch.from_numpy(np.asarray(l[j : j + self.window]))
                Z[i, :] = torch.from_numpy(np.asarray(m[j : j + self.window]))
                i += 1
        return (X, Y, Z)

    def __len__(self):
        return self.sample_num

    def __getitem__(self, idx):
        sample = [self.samples[idx, :, :], self.labels[idx, :], self.miss_label[idx, :]]
        return sample
