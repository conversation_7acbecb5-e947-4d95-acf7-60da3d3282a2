#!/usr/bin/env python3
"""
CloudTrie Hijack Detector - Main Entry Point
主执行脚本，完整重现CloudTrie-Code的处理流程
"""

import sys
import time
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from config import Config
from core.ip_trie import IPTrie
from builders.ip_trie_builder import IPTrieBuilder
from builders.detect_trie_builder import DetectTrieBuilder
from detector.hijack_inspector import HijackInspector
from utils.data_loader import DataLoader
from utils.gpu_utils import GPUAccelerator

class CloudTrieHijackDetector:
    """CloudTrie劫持检测器主类"""
    
    def __init__(self):
        self.config = Config()
        self.data_loader = DataLoader()
        self.gpu_accelerator = GPUAccelerator() if Config.USE_GPU else None
        self.ip_trie_builder = IPTrieBuilder()
        self.detect_trie_builder = DetectTrieBuilder()
        self.hijack_inspector = HijackInspector()
    
    def run_full_pipeline(self):
        """运行完整的检测流水线"""
        print("开始CloudTrie劫持检测完整流水线...")
        
        # 步骤1: 验证配置和路径
        print("\n步骤1: 验证配置...")
        Config.print_config()
        if not Config.validate_paths():
            print("路径验证失败，请检查配置")
            return False
        
        Config.create_output_dirs()
        
        # 步骤2: 构建IPTrie
        print("\n步骤2: 构建IPTrie...")
        start_time = time.time()
        self.build_ip_trie()
        print(f"IPTrie构建完成，耗时: {time.time() - start_time:.2f}秒")
        
        # 步骤3: 构建DetectTrie
        print("\n步骤3: 构建DetectTrie...")
        start_time = time.time()
        self.build_detect_trie()
        print(f"DetectTrie构建完成，耗时: {time.time() - start_time:.2f}秒")
        
        # 步骤4: 执行劫持检测
        print("\n步骤4: 执行劫持检测...")
        start_time = time.time()
        self.detect_hijacks()
        print(f"劫持检测完成，耗时: {time.time() - start_time:.2f}秒")
        
        print("\n完整流水线执行完成！")
        return True
    
    def build_ip_trie(self):
        """构建IPTrie - 完全按照CloudTrie-Code逻辑"""
        print("开始构建IPTrie...")
        
        self.ip_trie_builder.process_folder(
            rib_folder=Path(Config.RIB_DIR),
            roa_folder=Path(Config.RPKI_DIR),
            irr_folder=Path(Config.IRR_DIR),
            output_dir=Config.IPTRIE_OUTPUT_DIR,
            workers=Config.WORKERS
        )
        
        # 验证结果
        try:
            first_trie_path = Path(Config.IPTRIE_OUTPUT_DIR) / "trie_group_1.dat"
            if first_trie_path.exists():
                trie = IPTrie.load_from_file(str(first_trie_path))
                prefix_count = self.ip_trie_builder.count_prefixes(trie.root)
                print(f"IPTrie构建成功，第一个Trie包含 {prefix_count} 个前缀")
            else:
                print("警告：未找到IPTrie文件")
        except Exception as e:
            print(f"IPTrie验证失败: {e}")
    
    def build_detect_trie(self):
        """构建DetectTrie - 完全按照CloudTrie-Code逻辑"""
        print("开始构建DetectTrie...")
        
        detect_trie = self.detect_trie_builder.build_detect_trie(Config.IPTRIE_OUTPUT_DIR)
        detect_trie.save(Config.DETECTTRIE_OUTPUT_PATH)
        
        print(f"DetectTrie保存到: {Config.DETECTTRIE_OUTPUT_PATH}")
    
    def detect_hijacks(self):
        """执行劫持检测 - 完全按照CloudTrie-Code逻辑，输出txt格式"""
        print("开始劫持检测...")
        
        self.hijack_inspector.detect_prefix_hijacking(
            updates_dir=Config.UPDATES_DIR,
            trie_path=Config.DETECTTRIE_OUTPUT_PATH,
            output_dir=Config.HIJACK_OUTPUT_DIR
        )
        
        print(f"劫持检测结果保存到: {Config.HIJACK_OUTPUT_DIR}")
    
    def run_step(self, step_name):
        """运行单个步骤"""
        if step_name == "iptrie":
            self.build_ip_trie()
        elif step_name == "detecttrie":
            self.build_detect_trie()
        elif step_name == "hijack":
            self.detect_hijacks()
        else:
            print(f"未知步骤: {step_name}")
            print("可用步骤: iptrie, detecttrie, hijack")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CloudTrie Hijack Detector")
    parser.add_argument(
        "--step", 
        choices=["all", "iptrie", "detecttrie", "hijack"],
        default="all",
        help="要执行的步骤 (默认: all)"
    )
    parser.add_argument(
        "--config-check",
        action="store_true",
        help="仅检查配置，不执行任何步骤"
    )
    
    args = parser.parse_args()
    
    detector = CloudTrieHijackDetector()
    
    if args.config_check:
        print("配置检查模式...")
        Config.print_config()
        success = Config.validate_paths()
        sys.exit(0 if success else 1)
    
    if args.step == "all":
        success = detector.run_full_pipeline()
        sys.exit(0 if success else 1)
    else:
        detector.run_step(args.step)

if __name__ == "__main__":
    main()
