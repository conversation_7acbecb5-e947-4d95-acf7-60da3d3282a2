# CloudTrie Hijack Detector

完美重现CloudTrie-Code的BGP前缀劫持检测系统，专注于hijack事件检测，保持原有处理逻辑的完整性和流畅性。

## 项目特点

- **完全重现**：100%保持CloudTrie-Code的核心算法和处理逻辑
- **仅两处修改**：云滴数量改为100，添加GPU加速支持
- **模块化设计**：重组为清晰的模块结构，便于维护
- **批处理优化**：保持原有的并行处理能力
- **txt格式输出**：劫持检测结果输出为txt格式

## 项目结构

```
cloudtrie_hijack_detector/
├── core/                   # 核心数据结构模块
│   ├── __init__.py
│   ├── ip_trie.py         # IPTrie实现（完全复制）
│   ├── detect_trie.py     # DetectTrie实现（完全复制）
│   └── uncertainty_calculator.py  # 不确定度计算（云滴数量改为100）
├── builders/              # 构建器模块
│   ├── __init__.py
│   ├── ip_trie_builder.py # IPTrie构建器
│   └── detect_trie_builder.py # DetectTrie构建器
├── detector/              # 检测器模块
│   ├── __init__.py
│   └── hijack_inspector.py # 劫持检测器（输出txt格式）
├── utils/                 # 工具模块
│   ├── __init__.py
│   ├── data_loader.py     # 数据加载工具
│   └── gpu_utils.py       # GPU加速工具（新增）
├── config.py              # 配置文件
├── main.py                # 主执行脚本
├── requirements.txt       # 依赖包列表
└── README.md             # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
cd cloudtrie_hijack_detector
pip install -r requirements.txt
```

### 2. 配置路径

编辑 `config.py` 文件，设置正确的数据路径：

```python
# 数据源路径
RIB_DIR = "/rrc00-24"
RPKI_DIR = "/roa-24" 
IRR_DIR = "/IRR"
UPDATES_DIR = "/rrc00Updates-202412-txt/22"

# 输出路径
IPTRIE_OUTPUT_DIR = "/rrc00-24/trie"
DETECTTRIE_OUTPUT_PATH = "/DetectTrie/detect.dat"
HIJACK_OUTPUT_DIR = "/Hijacked_report/hijack/22"
```

### 3. 运行检测

#### 运行完整流水线
```bash
python main.py --step all
```

#### 分步执行
```bash
# 1. 构建IPTrie
python main.py --step iptrie

# 2. 构建DetectTrie
python main.py --step detecttrie

# 3. 执行劫持检测
python main.py --step hijack
```

#### 检查配置
```bash
python main.py --config-check
```

## 处理流程

### 1. 构建IP前缀Trie（IPTrie）
- 整合RIB、RPKI、IRR数据源
- 建立可信的前缀-AS关系基线
- 支持并行处理，按时间分组

### 2. 计算不确定度
- 使用云模型计算前缀-AS对的可信度
- **修改**：云滴数量从1000改为100
- **新增**：GPU加速支持

### 3. 构建检测Trie（DetectTrie）
- 基于低不确定度的P/O对构建检测树
- 筛选可信的前缀-AS关系

### 4. 劫持检测
- 基于DetectTrie识别潜在劫持事件
- 多重验证：IRR白名单、商业关系、API验证
- **修改**：输出格式改为txt

## 输出格式

劫持检测结果保存为 `hijack.txt`，格式如下：
```
Prefix          Victim_AS       Hijacker_AS     Start_Time      End_Time
***********/24  12345          67890           1640995200      1640995200
```

## GPU加速

如需启用GPU加速，请：

1. 安装CuPy：
```bash
pip install cupy-cuda11x  # 根据CUDA版本选择
```

2. 在config.py中启用GPU：
```python
USE_GPU = True
```

## 性能特点

- **并行处理**：支持多进程并行构建和检测
- **内存优化**：使用__slots__优化节点内存占用
- **批处理**：分批处理大量文件，提高效率
- **缓存机制**：API查询结果缓存，减少重复请求

## 注意事项

1. 确保所有数据路径存在且可访问
2. BGP工具（bgpdump）需要正确安装
3. CAIDA AS关系数据需要定期更新
4. 大规模数据处理需要足够的内存和存储空间

## 与原版差异

本项目与CloudTrie-Code的唯一差异：
1. **云滴数量**：从1000改为100
2. **GPU支持**：新增GPU加速功能
3. **输出格式**：劫持检测结果改为txt格式
4. **模块化**：重组为模块化结构，但保持所有逻辑不变

所有核心算法、数据结构、处理逻辑完全保持一致。
