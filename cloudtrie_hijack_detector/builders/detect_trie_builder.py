import glob
import pickle
import numpy as np
from collections import defaultdict
from ..core.ip_trie import IPTrie, TrieNode, default_list, default_peer_dict, default_source_dict
from ..core.detect_trie import DetectTrie, DetectTrieNode

class DetectTrieBuilder:
    """DetectTrie构建器 - 完全复制CloudTrie-Code的build-DetectTrie.py逻辑"""
    
    # 常量定义
    THRESHOLD = 0.1 
    ANYCAST_PREFIXES = {'0.0.0.0/24', '*******/24', '*******/24'}
    WINDOW_SIZE = 5    # 时间窗口长度
    SOURCE_WEIGHTS = {
        'RPKI': 0.9,
        'IRR': 0.7,
        'RIB': 0.6
    }
    
    def __init__(self):
        pass

    def calculate_confidence(self, asn, sources, peer_count):
        """修正后的置信度计算"""
        # 时间持续性（分数据源计算）
        time_weights = np.array([np.exp(-i/2) for i in range(5)])
        rib_vector = sources.get('RIB', [0]*5)  
        time_score = np.dot(rib_vector, time_weights) * self.SOURCE_WEIGHTS['RIB']
        
        # 空间一致性（观测点平方根平滑）
        space_score = np.sqrt(peer_count) * 0.3
        
        # 隶属度（最高权重）
        member_score = max(self.SOURCE_WEIGHTS[src] for src in sources.keys()) * 0.2
        if 'RPKI' in sources or 'IRR' in sources:
            return 0.9
        else:
            return 0.5*time_score + 0.3*space_score + member_score

    def binary_to_ip(self, binary_str):
        """安全处理二进制前缀输入"""
        if not isinstance(binary_str, str):
            raise TypeError(f"Expected string, got {type(binary_str)}")
        
        cidr_len = len(binary_str)
        padded = binary_str.ljust(32, '0')
        octets = [str(int(padded[i:i+8], 2)) for i in range(0, 32, 8)]
        return f"{'.'.join(octets)}/{cidr_len}"

    def process_single_trie(self, trie_path):
        """处理单个IPTrie文件"""
        ip_trie = IPTrie.load_from_file(trie_path)
        po_pairs = []
        
        # 遍历Trie收集所有P/O对
        node_stack = [(ip_trie.root, "")]
        while node_stack:
            node, prefix = node_stack.pop()
            
            if node.is_end_of_prefix:
                ip = self.binary_to_ip(prefix)
                if ip in self.ANYCAST_PREFIXES:
                    continue
                    
                for asn, sources in node.sources.items():
                    peer_count = len(node.peers.get(asn, []))
                    
                    # 计算置信度
                    confidence = self.calculate_confidence(asn, sources, peer_count)

                    
                    if confidence >= self.THRESHOLD:
                        po_pairs.append((prefix, asn))
            
            # 继续遍历子节点
            for bit in ['1', '0']:  # 优先处理右子树
                child = node.children[bit]
                if child:
                    node_stack.append((child, prefix+bit))
        
        return po_pairs

    def build_detect_trie(self, trie_dir):
        """构建全局检测树"""
        detect_trie = DetectTrie()
        trie_files = glob.glob(f"{trie_dir}/trie_*.dat")
        
        for filepath in trie_files:
            print(f"Processing {filepath}...")
            po_pairs = self.process_single_trie(filepath)
            detect_trie.batch_insert(po_pairs)
        
        return detect_trie
