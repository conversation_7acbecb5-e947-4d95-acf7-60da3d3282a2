"""
CloudTrie Hijack Detector - Configuration
配置文件，定义所有路径和参数
"""

import os
from pathlib import Path

class Config:
    """配置类 - 完全按照CloudTrie-Code的配置要求"""
    
    # 数据源路径
    RIB_DIR = "/rrc00-24"
    RPKI_DIR = "/roa-24" 
    IRR_DIR = "/IRR"
    UPDATES_DIR = "/rrc00Updates-202412-txt/22"
    
    # 输出路径
    IPTRIE_OUTPUT_DIR = "/rrc00-24/trie"
    DETECTTRIE_OUTPUT_PATH = "/DetectTrie/detect.dat"
    HIJACK_OUTPUT_DIR = "/Hijacked_report/hijack/22"
    
    # CAIDA AS关系数据路径
    CAIDA_RELATIONSHIPS_DIR = "/caida_as_rel/serial-1/"
    
    # BGP工具路径
    BGPDUMP_PATH = "/bgpd"
    
    # 处理参数
    WORKERS = 4  # 并行处理进程数
    BATCH_SIZE = 10  # 每批处理的文件数
    
    # 检测参数（完全按照原始设置）
    CONFIDENCE_THRESHOLD = 0.1
    ANYCAST_PREFIXES = {'0.0.0.0/24', '*******/24', '*******/24'}
    WINDOW_SIZE = 5  # 时间窗口长度
    SOURCE_WEIGHTS = {
        'RPKI': 0.9,
        'IRR': 0.7,
        'RIB': 0.6
    }
    
    # 云滴参数（修改为100）
    NUM_CLOUD_DROPS = 100
    
    # 缓存和验证参数
    CACHE_TTL = 3600  # 缓存生存时间（秒）
    REQUEST_INTERVAL = 1  # API请求间隔（秒）
    
    # GPU设置
    USE_GPU = True  # 是否使用GPU加速
    
    @classmethod
    def validate_paths(cls):
        """验证所有路径是否存在"""
        paths_to_check = [
            cls.RIB_DIR,
            cls.RPKI_DIR,
            cls.IRR_DIR,
            cls.UPDATES_DIR,
            cls.CAIDA_RELATIONSHIPS_DIR,
            cls.BGPDUMP_PATH
        ]
        
        missing_paths = []
        for path in paths_to_check:
            if not os.path.exists(path):
                missing_paths.append(path)
        
        if missing_paths:
            print(f"警告：以下路径不存在: {missing_paths}")
            return False
        
        print("所有路径验证通过")
        return True
    
    @classmethod
    def create_output_dirs(cls):
        """创建输出目录"""
        output_dirs = [
            cls.IPTRIE_OUTPUT_DIR,
            os.path.dirname(cls.DETECTTRIE_OUTPUT_PATH),
            cls.HIJACK_OUTPUT_DIR
        ]
        
        for dir_path in output_dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            print(f"创建输出目录: {dir_path}")
    
    @classmethod
    def print_config(cls):
        """打印当前配置"""
        print("=" * 50)
        print("CloudTrie Hijack Detector 配置")
        print("=" * 50)
        print(f"RIB目录: {cls.RIB_DIR}")
        print(f"RPKI目录: {cls.RPKI_DIR}")
        print(f"IRR目录: {cls.IRR_DIR}")
        print(f"更新文件目录: {cls.UPDATES_DIR}")
        print(f"IPTrie输出目录: {cls.IPTRIE_OUTPUT_DIR}")
        print(f"DetectTrie输出路径: {cls.DETECTTRIE_OUTPUT_PATH}")
        print(f"劫持检测输出目录: {cls.HIJACK_OUTPUT_DIR}")
        print(f"并行进程数: {cls.WORKERS}")
        print(f"云滴数量: {cls.NUM_CLOUD_DROPS}")
        print(f"使用GPU: {cls.USE_GPU}")
        print("=" * 50)
