"""
GPU加速工具模块
为不确定度计算提供GPU加速支持
"""

import numpy as np

try:
    import cupy as cp
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False
    cp = None

class GPUAccelerator:
    """GPU加速器类"""
    
    def __init__(self):
        self.gpu_available = GPU_AVAILABLE
        if self.gpu_available:
            print("GPU加速已启用")
        else:
            print("GPU不可用，使用CPU计算")
    
    def to_gpu(self, array):
        """将numpy数组转换为GPU数组"""
        if self.gpu_available:
            return cp.asarray(array)
        return array
    
    def to_cpu(self, array):
        """将GPU数组转换为numpy数组"""
        if self.gpu_available and hasattr(array, 'get'):
            return array.get()
        return array
    
    def generate_cloud_drops_gpu(self, ex, en, he, num_drops=100):
        """GPU加速的云滴生成"""
        if not self.gpu_available:
            return self._generate_cloud_drops_cpu(ex, en, he, num_drops)
        
        # 使用GPU计算
        ex_gpu = cp.asarray(ex)
        en_gpu = cp.asarray(en)
        he_gpu = cp.asarray(he)
        
        drops = []
        for _ in range(num_drops):
            # 生成非负熵值
            en_time = cp.abs(cp.random.normal(en_gpu[0], he_gpu[0]))
            en_space = cp.abs(cp.random.normal(en_gpu[1], he_gpu[1]))
            
            # 生成云滴坐标
            x = cp.random.normal(ex_gpu[0], cp.maximum(en_time, 1e-6))
            y = cp.random.normal(ex_gpu[1], cp.maximum(en_space, 1e-6))
            
            # 计算隶属度
            mu = 1 - cp.exp(-(
                (x - ex_gpu[0])**2 / (2 * (en_time**2 + 1e-6)) + 
                (y - ex_gpu[1])**2 / (2 * (en_space**2 + 1e-6))
            ))
            mu = cp.clip(mu, 0, 1)
            drops.append((x, y, mu))
        
        # 转换回CPU
        result = []
        for x, y, mu in drops:
            result.append((float(x.get()), float(y.get()), float(mu.get())))
        
        return np.array(result)
    
    def _generate_cloud_drops_cpu(self, ex, en, he, num_drops=100):
        """CPU版本的云滴生成（备用）"""
        drops = []
        for _ in range(num_drops):
            # 生成非负熵值
            en_time = abs(np.random.normal(en[0], he[0]))
            en_space = abs(np.random.normal(en[1], he[1]))
            
            # 生成云滴坐标
            x = np.random.normal(ex[0], max(en_time, 1e-6))
            y = np.random.normal(ex[1], max(en_space, 1e-6))
            
            # 计算隶属度
            mu = 1 - np.exp(-(
                (x - ex[0])**2 / (2 * (en_time**2 + 1e-6)) + 
                (y - ex[1])**2 / (2 * (en_space**2 + 1e-6))
            ))
            mu = np.clip(mu, 0, 1)
            drops.append((x, y, mu))
        
        return np.array(drops)
