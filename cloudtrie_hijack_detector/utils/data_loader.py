"""
数据加载工具模块
处理各种数据源的加载和预处理
"""

import os
import pandas as pd
import glob
from pathlib import Path

class DataLoader:
    """数据加载器类"""
    
    def __init__(self):
        pass
    
    def load_rib_files(self, rib_dir):
        """加载RIB文件"""
        rib_files = glob.glob(os.path.join(rib_dir, "*.csv"))
        rib_data = []
        
        for file_path in rib_files:
            try:
                df = pd.read_csv(file_path)
                rib_data.append(df)
                print(f"已加载RIB文件: {file_path}")
            except Exception as e:
                print(f"加载RIB文件失败 {file_path}: {e}")
        
        return rib_data
    
    def load_rpki_files(self, rpki_dir):
        """加载RPKI文件"""
        rpki_files = glob.glob(os.path.join(rpki_dir, "*.csv"))
        rpki_data = []
        
        for file_path in rpki_files:
            try:
                df = pd.read_csv(file_path)
                rpki_data.append(df)
                print(f"已加载RPKI文件: {file_path}")
            except Exception as e:
                print(f"加载RPKI文件失败 {file_path}: {e}")
        
        return rpki_data
    
    def load_irr_files(self, irr_dir):
        """加载IRR文件"""
        irr_files = glob.glob(os.path.join(irr_dir, "*.irr"))
        irr_data = {}
        
        for file_path in irr_files:
            try:
                with open(file_path, 'r') as file:
                    current_prefix = None
                    for line in file:
                        line = line.strip()
                        if line.startswith('route:'):
                            current_prefix = line.split()[1]
                            irr_data[current_prefix] = {'maintained_by': [], 'org': []}
                        elif line.startswith('mnt-by:') and current_prefix:
                            maintainer = line.split()[1]
                            irr_data[current_prefix]['maintained_by'].append(maintainer)
                        elif line.startswith('org:') and current_prefix:
                            org = line.split()[1]
                            irr_data[current_prefix]['org'].append(org)
                print(f"已加载IRR文件: {file_path}")
            except Exception as e:
                print(f"加载IRR文件失败 {file_path}: {e}")
        
        return irr_data
    
    def load_update_files(self, updates_dir):
        """加载BGP更新文件"""
        update_files = []
        
        # 支持多种文件格式
        patterns = ["*.csv", "*.txt", "*.updates"]
        
        for pattern in patterns:
            files = glob.glob(os.path.join(updates_dir, pattern))
            update_files.extend(files)
        
        update_files.sort()  # 按文件名排序
        print(f"找到 {len(update_files)} 个更新文件")
        
        return update_files
    
    def validate_data_paths(self, config):
        """验证数据路径是否存在"""
        paths_to_check = [
            config.RIB_DIR,
            config.RPKI_DIR, 
            config.IRR_DIR,
            config.UPDATES_DIR
        ]
        
        missing_paths = []
        for path in paths_to_check:
            if not os.path.exists(path):
                missing_paths.append(path)
        
        if missing_paths:
            print(f"警告：以下路径不存在: {missing_paths}")
            return False
        
        print("所有数据路径验证通过")
        return True
