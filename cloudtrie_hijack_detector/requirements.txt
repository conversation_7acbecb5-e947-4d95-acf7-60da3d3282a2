# CloudTrie Hijack Detector Dependencies
# 完全按照CloudTrie-Code的依赖要求

# 核心数据处理
numpy>=1.21.0
pandas>=1.3.0

# 网络和IP处理
ipaddress
requests>=2.25.0
urllib3>=1.26.0

# 日期时间处理
python-dateutil>=2.8.0
pytz>=2021.1

# 字符编码检测
chardet>=4.0.0

# 日志处理
logging

# 并发处理
concurrent.futures

# 路径处理
pathlib

# 可选：GPU加速支持
# cupy-cuda11x>=9.0.0  # 根据CUDA版本选择，如果需要GPU加速请取消注释

# 可选：性能分析
# memory-profiler>=0.58.0
# psutil>=5.8.0

# 开发和测试依赖（可选）
# pytest>=6.0.0
# pytest-cov>=2.12.0
# black>=21.0.0
# flake8>=3.9.0
