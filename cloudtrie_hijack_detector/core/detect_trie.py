from collections import defaultdict
import pickle

class DetectTrieNode:
    """DetectTrie节点优化结构"""
    __slots__ = ['children', 'source_as_set']  # 内存优化
    
    def __init__(self):
        self.children = {'0': None, '1': None}
        self.source_as_set = set()  # 存储合法AS集合

class DetectTrie:
    """高效前缀检测树"""
    def __init__(self):
        self.root = DetectTrieNode()
        self.conflict_log = defaultdict(set)  # 记录冲突事件

    def insert(self, binary_prefix: str, source_as: int):
        """插入二进制格式前缀（带冲突检测）"""
        node = self.root
        for bit in binary_prefix:
            if not node.children[bit]:
                node.children[bit] = DetectTrieNode()
            node = node.children[bit]

        if node.source_as_set:
            if source_as not in node.source_as_set:
                self.log_conflict(binary_prefix, node.source_as_set, source_as)
        else:
            node.source_as_set.add(source_as)

    def log_conflict(self, prefix, existing_as, new_as):
        """记录前缀冲突事件"""
        self.conflict_log[prefix].update(existing_as)
        self.conflict_log[prefix].add(new_as)

    def batch_insert(self, po_pairs):
        """批量插入P/O对"""
        for prefix, asn in po_pairs:
            self.insert(prefix, asn)
    
    def search(self, binary_prefix: str) -> set:
        """搜索二进制前缀对应的合法AS集合"""
        node = self.root
        for bit in binary_prefix:
            if node.children[bit] is None:
                return None  # 前缀不存在
            node = node.children[bit]
        return node.source_as_set if node.source_as_set else None

    def search_with_fallback(self, binary_prefix: str) -> tuple:
        """搜索二进制前缀，支持回溯到父前缀

        Returns:
            tuple: (as_set, matched_prefix_length, is_exact_match)
            - as_set: 匹配到的AS集合
            - matched_prefix_length: 匹配的前缀长度
            - is_exact_match: 是否为精确匹配
        """
        # 首先尝试精确匹配
        exact_match = self.search(binary_prefix)
        if exact_match:
            return exact_match, len(binary_prefix), True

        # 精确匹配失败，开始回溯查找父前缀
        for prefix_len in range(len(binary_prefix) - 1, 0, -1):
            parent_prefix = binary_prefix[:prefix_len]
            parent_as_set = self.search(parent_prefix)

            if parent_as_set:
                return parent_as_set, prefix_len, False

        # 没有找到任何匹配
        return None, 0, False

    def save(self, filepath):
        with open(filepath, 'wb') as f:
            pickle.dump(self.root, f)

    @classmethod
    def load(cls, filepath):
        trie = cls()
        with open(filepath, 'rb') as f:
            trie.root = pickle.load(f)
        return trie

def ip_prefix_to_binary(prefix):
    ip_part, length = prefix.split('/')
    length = int(length)
    parts = list(map(int, ip_part.split('.')))
    binary_str = ''.join([format(part, '08b') for part in parts])
    return binary_str[:length]
