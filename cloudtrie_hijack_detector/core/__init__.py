"""
CloudTrie Hijack Detector - Core Module
完全重现CloudTrie-Code的核心数据结构和算法
"""

from .ip_trie import IPTrie, TrieNode, default_list, default_source_dict, default_peer_dict
from .detect_trie import DetectTrie, DetectTrieNode
from .uncertainty_calculator import calculate_prefix_uncertainty, generate_t_s_cloud_drops, generate_t_m_cloud_drops, generate_s_m_cloud_drops

__all__ = [
    'IPTrie', 'TrieNode', 'default_list', 'default_source_dict', 'default_peer_dict',
    'DetectTrie', 'DetectTrieNode',
    'calculate_prefix_uncertainty', 'generate_t_s_cloud_drops', 'generate_t_m_cloud_drops', 'generate_s_m_cloud_drops'
]
