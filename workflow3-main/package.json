{"name": "workflow3", "version": "1.0.6", "description": "MCP Server for three-stage workflow prompt injection", "main": "dist/index.js", "type": "module", "bin": {"workflow3": "dist/index.js"}, "scripts": {"build": "tsc && npm run copy-templates", "copy-templates": "cp -r src/templates dist/", "start": "node dist/index.js", "dev": "tsx src/index.ts", "prepare": "npm run build", "release": "npm version patch && npm publish && git push"}, "keywords": ["mcp", "workflow", "prompt", "cursor", "ai"], "author": "geekoe", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/geekoe/workflow3.git"}, "homepage": "https://github.com/geekoe/workflow3#readme", "bugs": {"url": "https://github.com/geekoe/workflow3/issues"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "tsx": "^4.0.0"}, "files": ["dist/**/*", "README.md", "package.json"], "engines": {"node": ">=18.0.0"}}