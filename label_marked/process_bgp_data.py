import pandas as pd
import datetime
import ipaddress
import glob
import gzip
import re
import os
import sys
import multiprocessing
from functools import partial
from tqdm import tqdm
import argparse

def parse_event_info(file_path):
    """
    解析 anomaly-event-info.csv 文件并返回一个包含事件信息的 DataFrame。
    将时间字符串转换为 datetime 对象，并处理多值字段。
    """
    df = pd.read_csv(file_path)

    # 转换时间列为 datetime 对象，并添加 UTC 时区信息
    df['start_time_dt'] = pd.to_datetime(df['start_time'], format='%Y/%m/%d %H:%M', errors='coerce')
    df['end_time_dt'] = pd.to_datetime(df['end_time'], format='%Y/%m/%d %H:%M', errors='coerce')
    
    # 为日期时间添加 UTC 时区信息
    df['start_time_dt'] = df['start_time_dt'].dt.tz_localize('UTC')
    df['end_time_dt'] = df['end_time_dt'].dt.tz_localize('UTC')

    # 处理 'unknown' 的 end_time
    # 对于 'unknown' 的结束时间，可以将其设置为一个遥远的未来时间，或者根据具体情况处理
    # 这里我们将其设置为 start_time + 1 day，或者 NaN 如果 start_time 也无效
    df.loc[df['end_time'].astype(str).str.lower() == 'unknown', 'end_time_dt'] = \
        df['start_time_dt'] + pd.Timedelta(days=1)

    # 处理可能包含多个值的 AS 号和前缀
    for col in ['prefix', 'hijacked_prefix', 'hijack_as', 'vicitim_as', 'outage_as', 'leak_as']:
        # Ensure the column is treated as string before applying split, and handle NaN
        df[col] = df[col].astype(str).apply(
            lambda x: [item.strip() for item in x.split(',') if item.strip()] if x.strip() != 'nan' else []
        )
    return df

def process_bgp_file(bgp_file, event_info_df):
    """
    处理单个 BGP 数据文件，并返回分钟标签和异常路由。

    参数:
    - bgp_file: BGP 数据文件路径
    - event_info_df: 包含异常事件信息的 DataFrame

    返回:
    - minute_labels: 该文件的分钟标签字典 {timestamp_in_minutes: label}
    - abnormal_routes: 该文件的异常路由列表 [(raw_bgp_message, reason), ...]
    """
    minute_labels = {}
    abnormal_routes = []

    # 获取事件的开始和结束时间 (Unix timestamp)
    # 因为这个函数被调用时，event_info_df 只包含一个事件的信息
    event_details = event_info_df.iloc[0]
    if pd.isna(event_details['start_time_dt']) or pd.isna(event_details['end_time_dt']):
        # 如果时间信息不完整，无法处理，直接返回空结果
        return minute_labels, abnormal_routes

    start_ts = int(event_details['start_time_dt'].timestamp())
    end_ts = int(event_details['end_time_dt'].timestamp())

    # 初始化滑动窗口变量（与Data_generator.py保持一致）
    interval = 60  # 1分钟窗口
    current_window_updates = []
    window_has_anomaly = False

    # 根据文件扩展名选择不同的打开方式
    if bgp_file.endswith('.gz'):
        open_func = lambda f: gzip.open(f, 'rt', encoding='utf-8', errors='replace')
    else:
        open_func = lambda f: open(f, 'r', encoding='utf-8', errors='replace')
    
    # 获取文件中的第一个和最后一个时间戳来确定数据范围
    first_timestamp = None
    last_timestamp = None

    # 先扫描文件获取时间范围
    try:
        with open_func(bgp_file) as f:
            for line in f:
                parts = line.strip().split('|')
                if len(parts) >= 2 and parts[0].startswith('BGP4MP'):
                    timestamp_unix = int(parts[1])
                    if first_timestamp is None:
                        first_timestamp = timestamp_unix
                    last_timestamp = timestamp_unix
    except Exception as e:
        print(f"扫描文件 {bgp_file} 时出错: {e}")
        return minute_labels, abnormal_routes

    if first_timestamp is None:
        return minute_labels, abnormal_routes

    # 初始化滑动窗口（与Data_generator.py一致）
    # 从第一个时间戳的分钟边界开始
    left_time = (first_timestamp // 60) * 60  # 向下取整到分钟边界
    right_time = left_time + interval
    data_end_time = last_timestamp

    try:
        with open_func(bgp_file) as f:
            for line in f:
                parts = line.strip().split('|')
                # BGP4MP|timestamp|type|router_ip|peer_as|prefix|as_path|...
                if len(parts) < 7 or not parts[0].startswith('BGP4MP'): # 确保是有效的 BGP 记录
                    continue

                try:
                    timestamp_unix = int(parts[1])

                    # 如果当前记录已经超出了当前窗口
                    while timestamp_unix >= right_time:
                        # 提交当前窗口的结果
                        minute_labels[right_time] = 1 if window_has_anomaly else 0

                        # 滑动到下一个窗口
                        left_time = right_time
                        right_time += interval
                        window_has_anomaly = False

                        # 如果已经超过数据范围，停止创建新窗口
                        if left_time > data_end_time:
                            break

                    # 如果数据不在官方事件时间窗口内，则跳过异常检查
                    if not (start_ts <= timestamp_unix < end_ts):
                        continue

                    # 提取 AS 路径，如果是 Withdrawal 消息，可能没有 AS 路径
                    as_path_str = parts[6] if len(parts) > 6 and parts[2] == 'A' else ""

                    # AS 路径处理：移除 AS Sets (如 {123,456}) 和重复的 AS (AS Prepending)
                    # 简化处理：只提取数字，忽略 { }。
                    as_path_list = []
                    as_numbers_in_path = re.findall(r'\d+', as_path_str)
                    for asn_str in as_numbers_in_path:
                        try:
                            as_path_list.append(int(asn_str))
                        except ValueError:
                            pass # Skip non-numeric parts
                    
                    # 规则 B (内容规则) 检查
                    is_abnormal_content = False
                    matched_reason = []

                    for _, event in event_info_df.iterrows():
                        event_type = event['event_type']
                        event_name = event['event_name']
                        
                        # 针对劫持(hijack)事件的特殊处理
                        if event_type == 'hijack':
                            # 获取劫持者AS和被劫持前缀以及受害者AS
                            hijack_as_list = event['hijack_as']
                            hijacked_prefix_list = event['hijacked_prefix']
                            victim_as_list = event['vicitim_as']
                            
                            # 检查是否有前缀信息
                            has_prefix_info = any(prefix for prefix in hijacked_prefix_list if prefix)
                            
                            # 如果有前缀信息，执行前缀+AS匹配
                            if has_prefix_info:
                                try:
                                    # 检查前缀匹配和起源AS
                                    current_bgp_prefix = ipaddress.ip_network(parts[5], strict=False)
                                    # 只检查被劫持的前缀
                                    for hijacked_prefix in hijacked_prefix_list:
                                        if hijacked_prefix:
                                            try:
                                                event_prefix = ipaddress.ip_network(hijacked_prefix, strict=False)
                                                # 检查前缀是否重叠
                                                if current_bgp_prefix.overlaps(event_prefix):
                                                    # 检查AS路径，判断是否由劫持者AS宣告
                                                    if as_path_list and len(as_path_list) > 0:
                                                        origin_as = as_path_list[-1]  # 起源AS是路径中的最后一个AS（不是第一个）
                                                        # 检查起源AS是否为劫持者AS
                                                        for hijack_as_str in hijack_as_list:
                                                            if hijack_as_str:
                                                                try:
                                                                    # 修复数据类型问题，确保正确转换AS号
                                                                    if isinstance(hijack_as_str, str):
                                                                        hijack_as = int(float(hijack_as_str)) if '.' in hijack_as_str else int(hijack_as_str)
                                                                    else:
                                                                        hijack_as = int(hijack_as_str)
                                                                    
                                                                    print(f"DEBUG: 比较起源AS {origin_as} 与劫持者AS {hijack_as}")
                                                                    if origin_as == hijack_as:
                                                                        is_abnormal_content = True
                                                                        victim_as_str = ','.join([str(x) for x in victim_as_list if x])
                                                                        reason = f"劫持检测: 前缀{hijacked_prefix}被AS{origin_as}劫持(应属于AS{victim_as_str}) (事件: {event_name})"
                                                                        matched_reason.append(reason)
                                                                        # 添加调试信息
                                                                        print(f"找到劫持路由! 前缀: {parts[5]}, 起源AS: {origin_as}, 原因: {reason}")
                                                                except ValueError:
                                                                    pass
                                            except ValueError:
                                                pass  # 忽略无效的前缀格式
                                except ValueError:
                                    pass # 忽略无效的当前BGP前缀格式
                            # 如果没有前缀信息，仅检查AS路径中是否包含劫持者AS
                            else:
                                # 检查AS路径，判断是否包含劫持者AS
                                if as_path_list and len(as_path_list) > 0:
                                    origin_as = as_path_list[-1]  # 起源AS是路径中的最后一个AS
                                    # 检查起源AS是否为劫持者AS
                                    for hijack_as_str in hijack_as_list:
                                        if hijack_as_str:
                                            try:
                                                # 修复数据类型问题，确保正确转换AS号
                                                if isinstance(hijack_as_str, str):
                                                    hijack_as = int(float(hijack_as_str)) if '.' in hijack_as_str else int(hijack_as_str)
                                                else:
                                                    hijack_as = int(hijack_as_str)
                                                
                                                print(f"DEBUG: 无前缀劫持检测 - 比较起源AS {origin_as} 与劫持者AS {hijack_as}")
                                                if origin_as == hijack_as:
                                                    is_abnormal_content = True
                                                    victim_as_str = ','.join([str(x) for x in victim_as_list if x])
                                                    reason = f"劫持检测(无前缀): AS{origin_as}宣告的前缀{parts[5]}可能是劫持(应属于AS{victim_as_str}) (事件: {event_name})"
                                                    matched_reason.append(reason)
                                                    # 添加调试信息
                                                    print(f"找到可能的劫持路由! 前缀: {parts[5]}, 起源AS: {origin_as}, 原因: {reason}")
                                            except ValueError:
                                                pass
                        # 针对路由泄露(leak)事件的简化处理
                        elif event_type == 'leak':
                            # 获取泄露AS
                            leak_as_list = event['leak_as']
                            
                            # 检查AS路径中是否包含泄露AS
                            if as_path_list and len(as_path_list) > 0:
                                for leak_as_str in leak_as_list:
                                    if leak_as_str:
                                        try:
                                            # 修复数据类型问题
                                            if isinstance(leak_as_str, str):
                                                leak_as = int(float(leak_as_str)) if '.' in leak_as_str else int(leak_as_str)
                                            else:
                                                leak_as = int(leak_as_str)
                                            
                                            # 检查AS路径中是否包含泄露AS
                                            if leak_as in as_path_list:
                                                is_abnormal_content = True
                                                reason = f"路由泄露检测: AS路径中包含泄露AS{leak_as} (事件: {event_name})"
                                                matched_reason.append(reason)
                                                print(f"找到路由泄露! 前缀: {parts[5]}, AS路径: {as_path_list}, 原因: {reason}")
                                        except ValueError:
                                            pass
                        # 针对网络中断(outage)事件的简化处理
                        elif event_type == 'outage':
                            outage_as_list = event['outage_as']
                            
                            # 对宣告消息(A)，检查AS路径是否包含中断AS
                            if parts[2] == 'A' and as_path_list:
                                for outage_as_str in outage_as_list:
                                    if outage_as_str:
                                        try:
                                            outage_as = int(float(outage_as_str)) if '.' in outage_as_str else int(outage_as_str)
                                            if outage_as in as_path_list:
                                                is_abnormal_content = True
                                                reason = f"网络中断检测: AS路径中包含中断AS {outage_as} (事件: {event_name})"
                                                matched_reason.append(reason)
                                        except ValueError:
                                            pass
                            
                            # 对撤销消息(W)，检查发送方(peer_as)是否为中断AS
                            elif parts[2] == 'W':
                                try:
                                    peer_as = int(parts[4])
                                    for outage_as_str in outage_as_list:
                                        if outage_as_str:
                                            try:
                                                outage_as = int(float(outage_as_str)) if '.' in outage_as_str else int(outage_as_str)
                                                if peer_as == outage_as:
                                                    is_abnormal_content = True
                                                    reason = f"网络中断检测: 来自中断AS {peer_as} 的路由撤销 (事件: {event_name})"
                                                    matched_reason.append(reason)
                                            except ValueError:
                                                pass
                                except (ValueError, IndexError):
                                    pass # peer_as 可能无效
                        else:
                            # 其他事件类型的通用逻辑
                            # 检查前缀匹配
                            try:
                                current_bgp_prefix = ipaddress.ip_network(parts[5], strict=False)
                                for prefix_col in ['prefix', 'hijacked_prefix']:
                                    for event_prefix_str in event[prefix_col]:
                                        if event_prefix_str:
                                            try:
                                                event_defined_prefix = ipaddress.ip_network(event_prefix_str, strict=False)
                                                if current_bgp_prefix.overlaps(event_defined_prefix):
                                                    is_abnormal_content = True
                                                    matched_reason.append(f"前缀匹配: {event_prefix_str} (事件: {event_name}, 类型: {event_type})")
                                            except ValueError:
                                                pass # 忽略无效的前缀格式
                            except ValueError:
                                pass # 忽略无效的当前 BGP 前缀格式

                    if is_abnormal_content:
                        window_has_anomaly = True  # 标记当前窗口有异常
                        # 确保不重复添加异常路由，可以通过一个集合来跟踪已添加的原始消息
                        # 但由于需求是保存所有"异常路由"的原始记录，即使重复也无妨，只要原因明确
                        abnormal_routes.append((line.strip(), ", ".join(list(set(matched_reason))))) # 保存异常路由和去重后的原因

                except (ValueError, IndexError, TypeError) as e:
                    # 某些行可能格式不正确，跳过
                    # print(f"解析错误，跳过行: {line.strip()} 错误: {e}")
                    continue
    except Exception as e:
        print(f"处理文件 {bgp_file} 时出错: {e}")

    # 处理最后一个窗口（如果有数据的话）
    if 'right_time' in locals() and right_time <= data_end_time + interval:
        minute_labels[right_time] = 1 if window_has_anomaly else 0

        # 填充到数据结束时间的所有剩余窗口
        left_time = right_time
        right_time += interval
        while left_time < data_end_time:
            minute_labels[right_time] = 0  # 剩余窗口默认为正常
            left_time = right_time
            right_time += interval

    return minute_labels, abnormal_routes

def merge_results(results):
    """
    合并多个进程的处理结果。
    
    参数:
    - results: 每个文件处理结果的列表 [(minute_labels, abnormal_routes), ...]
    
    返回:
    - merged_minute_labels: 合并后的分钟标签字典 {timestamp_in_minutes: label}
    - merged_abnormal_routes: 合并后的异常路由列表 [(raw_bgp_message, reason), ...]
    """
    merged_minute_labels = {}
    merged_abnormal_routes = []
    
    for minute_labels, abnormal_routes in results:
        # 合并分钟标签，如果有冲突，优先使用标记为异常的标签
        for timestamp, label in minute_labels.items():
            if timestamp not in merged_minute_labels or label == 1:
                merged_minute_labels[timestamp] = label
        
        # 合并异常路由
        merged_abnormal_routes.extend(abnormal_routes)
    
    return merged_minute_labels, merged_abnormal_routes

def process_bgp_data(event_info_df, bgp_data_dir, event_name=None, num_processes=None):
    """
    处理指定事件或所有事件的BGP数据。
    """
    if event_name:
        # 如果指定了 event_name，则只处理该事件
        events_to_process_df = event_info_df[event_info_df['event_name'] == event_name].copy()
        if events_to_process_df.empty:
            print(f"错误: 找不到指定的事件 '{event_name}'。请检查事件名称是否正确。")
            return None, None, None
    else:
        # 否则处理所有事件
        events_to_process_df = event_info_df.copy()

    all_minute_labels = {}
    all_abnormal_routes = []
    all_results = []
    
    # 如果未指定进程数，则使用 CPU 核心数
    if num_processes is None:
        num_processes = multiprocessing.cpu_count()
    
    print(f"使用 {num_processes} 个进程进行并行处理")
    
    # 遍历需要处理的事件
    for _, event in tqdm(events_to_process_df.iterrows(), total=events_to_process_df.shape[0], desc="处理事件"):
        current_event_name = event['event_name']
        event_dir = os.path.join(bgp_data_dir, current_event_name)
        data_dir = os.path.join(event_dir, 'data')
        
        # 获取该事件的所有 BGP 数据文件 - 同时支持.txt和.gz格式
        bgp_files = glob.glob(os.path.join(data_dir, 'rrc*.txt')) + glob.glob(os.path.join(data_dir, 'rrc*.gz'))
        
        # 对文件进行排序，确保按时间顺序处理
        bgp_files.sort()
        
        print(f"找到 {len(bgp_files)} 个数据文件")
        if len(bgp_files) > 0:
            print(f"文件格式示例: {bgp_files[0]}")
        
        if not bgp_files:
            continue
        
        # 打印事件信息以便调试
        print("事件信息:")
        for _, event in events_to_process_df.iterrows():
            print(f"  事件类型: {event['event_type']}")
            print(f"  事件名称: {event['event_name']}")
            if event['event_type'] == 'hijack':
                print(f"  劫持AS: {event['hijack_as']}")
                print(f"  被劫持前缀: {event['hijacked_prefix']}")
                print(f"  受害者AS: {event['vicitim_as']}")
        
        # 使用 functools.partial 包装 process_bgp_file，并传入当前事件的信息
        # 这确保了每个文件都只根据与其相关的事件信息进行分析
        current_event_df = pd.DataFrame([event]).reset_index(drop=True)
        
        # 使用 with Pool 来确保进程池在使用后被正确关闭
        with multiprocessing.Pool(processes=num_processes) as pool:
            # 创建一个只包含当前处理事件信息的 DataFrame
            process_func = partial(process_bgp_file, event_info_df=current_event_df)
            
            # 使用 imap_unordered 来获取一个迭代器，这可以更快地开始处理
            results = list(tqdm(pool.imap_unordered(process_func, bgp_files), total=len(bgp_files), desc=f"分析 {current_event_name}"))

        # 合并该事件的所有结果
        merged_labels, merged_routes = merge_results(results)
        
        print(f"处理完成，找到 {len(merged_routes)} 条异常路由")
        if merged_routes:
            print("前5条异常路由示例:")
            for i, (route, reason) in enumerate(merged_routes[:5]):
                print(f"  {i+1}. 路由: {route[:100]}...")  # 只显示前100个字符
                print(f"     原因: {reason}")
                print("---")
        
        # 将分钟标签转换为 sorted list of (timestamp, label)
        sorted_minute_labels = sorted(merged_labels.items())
        
        # 保存结果到事件目录
        save_results(sorted_minute_labels, merged_routes, event_dir)
        print(f"结果已保存到 {event_dir}")
        
        # 如果只处理一个事件，则返回结果
        if event_name:
            return sorted_minute_labels, merged_routes, event_dir
        else:
            # 否则收集所有事件的结果
            all_minute_labels.update(merged_labels)
            all_abnormal_routes.extend(merged_routes)
            all_results.append((current_event_name, len(merged_labels), len(merged_routes)))
    
    # 如果处理多个事件，打印汇总信息
    if not event_name:
        print("\n所有事件处理结果汇总:")
        print("事件名称\t时间窗口数\t异常路由数")
        for event_name, num_labels, num_routes in all_results:
            print(f"{event_name}\t{num_labels}\t{num_routes}")
    
    return all_minute_labels, all_abnormal_routes, None

def save_results(minute_labels, abnormal_routes, output_dir=None):
    """
    保存处理结果到文件。
    
    参数:
    - minute_labels: 时间窗口标签列表 [(timestamp, label), ...]
    - abnormal_routes: 异常路由列表 [(raw_message, reason), ...]
    - output_dir: 输出目录，如果为 None，则保存到当前目录
    """
    # 确定输出目录
    if output_dir:
        # 确保目录存在
        os.makedirs(output_dir, exist_ok=True)
        label_output_file = os.path.join(output_dir, "minute_labels.csv")
        abnormal_route_output_file = os.path.join(output_dir, "abnormal_routes.txt")
        raw_abnormal_route_file = os.path.join(output_dir, "raw_abnormal_routes.txt")
    else:
        label_output_file = "minute_labels.csv"
        abnormal_route_output_file = "abnormal_routes.txt"
        raw_abnormal_route_file = "raw_abnormal_routes.txt"
    
    # 保存时间标签，现在ts已经是Unix时间戳
    with open(label_output_file, 'w') as f:
        f.write("timestamp,label\n")
        for ts, label in minute_labels:
            # ts现在已经是Unix时间戳（整数）
            f.write(f"{ts},{label}\n")
    print(f"时间标签已保存到 {label_output_file}")

    # 保存带解释的异常路由
    with open(abnormal_route_output_file, 'w') as f:
        for route, reason in abnormal_routes:
            f.write(f"原始消息: {route}\n原因: {reason}\n---\n")
    print(f"带解释的异常路由已保存到 {abnormal_route_output_file}")
    
    # 保存只包含原始记录的异常路由（无解释）
    with open(raw_abnormal_route_file, 'w') as f:
        for route, _ in abnormal_routes:
            f.write(f"{route}\n")
    print(f"原始异常路由已保存到 {raw_abnormal_route_file}")

def main():
    """
    主函数：解析命令行参数，并启动 BGP 数据处理流程。
    """
    parser = argparse.ArgumentParser(description="处理BGP数据，基于事件信息进行分钟级打标和异常路由识别。")
    parser.add_argument('--event_info_file', type=str, default='data/anomaly-event-info.csv',
                        help='包含异常事件信息的CSV文件路径。')
    parser.add_argument('--bgp_data_dir', type=str, default='data/anomaly-event-routedata',
                        help='BGP原始数据存放的根目录。')
    parser.add_argument('--event_name', type=str, default=None,
                        help='如果指定，则只处理这一个事件。')
    parser.add_argument('--num_processes', type=int, default=None,
                        help='用于并行处理的进程数，默认为CPU核心数。')

    args = parser.parse_args()

    # 解析事件信息
    event_info_df = parse_event_info(args.event_info_file)

    # 处理BGP数据
    process_bgp_data(event_info_df, args.bgp_data_dir, args.event_name, args.num_processes)

if __name__ == '__main__':
    main()



#此文件用于处理从各个地方下载的data数据，对于hijack和outage事件可以一步到位，对于leak事件这需要使用leak_process_Three_tuple.py文件进行进一出处理