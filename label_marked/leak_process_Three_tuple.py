import os
import pandas as pd
import datetime
import ipaddress
import glob
import gzip
import re
import sys
import json
import bz2
import numpy as np
import shutil
from tqdm import tqdm
from collections import defaultdict, Counter

def parse_event_info(file_path):
    """
    解析 anomaly-event-info.csv 文件并返回一个包含事件信息的 DataFrame。
    将时间字符串转换为 datetime 对象，并处理多值字段。
    """
    df = pd.read_csv(file_path)

    # 转换时间列为 datetime 对象，并添加 UTC 时区信息
    df['start_time_dt'] = pd.to_datetime(df['start_time'], format='%Y/%m/%d %H:%M', errors='coerce')
    df['end_time_dt'] = pd.to_datetime(df['end_time'], format='%Y/%m/%d %H:%M', errors='coerce')
    
    # 为日期时间添加 UTC 时区信息
    df['start_time_dt'] = df['start_time_dt'].dt.tz_localize('UTC')
    df['end_time_dt'] = df['end_time_dt'].dt.tz_localize('UTC')

    # 处理 'unknown' 的 end_time
    df.loc[df['end_time'].astype(str).str.lower() == 'unknown', 'end_time_dt'] = \
        df['start_time_dt'] + pd.Timedelta(days=1)

    # 处理可能包含多个值的 AS 号和前缀
    for col in ['prefix', 'hijacked_prefix', 'hijack_as', 'vicitim_as', 'outage_as', 'leak_as']:
        # 确保列被视为字符串并处理 NaN
        df[col] = df[col].astype(str).apply(
            lambda x: [item.strip() for item in x.split(',') if item.strip()] if x.strip() != 'nan' else []
        )
    return df

def load_as_relationships(as_rel_file):
    """
    加载AS关系数据，并确保扩充双向关系
    
    参数:
    - as_rel_file: AS关系文件路径（as-rel2.txt或as-rel.txt）
    
    返回:
    - as_relationships: 字典，键为(as1, as2)元组，值为关系类型（-1:provider-to-customer, 0:peer-to-peer, 1:customer-to-provider）
    """
    print(f"加载AS关系数据: {as_rel_file}")
    as_relationships = {}
    
    # 检查文件是否为bz2压缩文件
    if as_rel_file.endswith('.bz2'):
        open_func = lambda f: bz2.open(f, 'rt', encoding='utf-8', errors='replace')
    else:
        open_func = lambda f: open(f, 'r', encoding='utf-8', errors='replace')
    
    # 记录原始关系数量
    original_relations_count = 0
    
    try:
        with open_func(as_rel_file) as f:
            for line in f:
                if line.startswith('#'):
                    continue
                parts = line.strip().split('|')
                # 至少需要3部分: as1, as2, rel_type
                if len(parts) < 3:
                    continue
                
                try:
                    # 只取前三部分，忽略可能存在的第四部分（来源信息）
                    as1 = int(parts[0])
                    as2 = int(parts[1])
                    rel_type = int(parts[2])
                    
                    # 存储原始关系
                    as_relationships[(as1, as2)] = rel_type
                    original_relations_count += 1
                except ValueError:
                    continue
    except Exception as e:
        print(f"加载AS关系数据时出错: {e}")
    
    print(f"从文件加载了 {original_relations_count} 条原始AS关系")
    
    # 扩充反向关系
    expanded_relations = {}
    for (as1, as2), rel_type in as_relationships.items():
        # 如果反向关系尚未定义，则根据原始关系推导
        if (as2, as1) not in as_relationships:
            if rel_type == -1:  # provider-to-customer
                expanded_relations[(as2, as1)] = 1  # customer-to-provider
            elif rel_type == 1:  # customer-to-provider
                expanded_relations[(as2, as1)] = -1  # provider-to-customer
            elif rel_type == 0:  # peer-to-peer
                expanded_relations[(as2, as1)] = 0  # peer-to-peer仍然是peer-to-peer
    
    # 将扩充的关系添加到原始关系中
    as_relationships.update(expanded_relations)
    
    # 计算每种关系类型的数量，用于验证
    p2c_count = sum(1 for rel in as_relationships.values() if rel == -1)
    c2p_count = sum(1 for rel in as_relationships.values() if rel == 1)
    p2p_count = sum(1 for rel in as_relationships.values() if rel == 0)
    
    print(f"扩充后的AS关系总数: {len(as_relationships)}")
    print(f"  - Provider-to-Customer (-1) 关系数量: {p2c_count}")
    print(f"  - Customer-to-Provider (1) 关系数量: {c2p_count}")
    print(f"  - Peer-to-Peer (0) 关系数量: {p2p_count}")
    print(f"  - 扩充的关系数量: {len(expanded_relations)}")
    
    # 验证扩充是否正确
    for (as1, as2), rel_type in list(as_relationships.items())[:5]:
        if (as2, as1) in as_relationships:
            rev_rel = as_relationships[(as2, as1)]
            print(f"示例关系: AS{as1}-AS{as2}={rel_type}, 反向关系: AS{as2}-AS{as1}={rev_rel}")
    
    return as_relationships

def parse_as_path(as_path_str):
    """
    解析AS路径字符串，返回AS号列表
    
    参数:
    - as_path_str: AS路径字符串
    
    返回:
    - as_path: AS号列表
    """
    as_path = []
    # 使用正则表达式匹配AS号（包括在{}中的AS集合）
    as_numbers = re.findall(r'\d+', as_path_str)
    for asn_str in as_numbers:
        try:
            as_path.append(int(asn_str))
        except ValueError:
            pass
    return as_path

def has_private_as(as_path):
    """
    检查AS路径中是否包含私有AS号。
    返回: (True/False, list_of_private_as)
    """
    private_asns = []
    found = False
    for asn in as_path:
        if (64512 <= asn <= 65534) or (4200000000 <= asn <= 4294967294):
            private_asns.append(asn)
            found = True
    return found, private_asns

def update_minute_labels(minute_labels_file, analyzed_routes, event_info):
    """
    根据新的三元组分析结果更新分钟标签。
    """
    minute_labels_df = pd.read_csv(minute_labels_file)
    original_labels = dict(zip(minute_labels_df['timestamp'], minute_labels_df['label']))
    
    start_time = event_info['start_time_dt']
    end_time = event_info['end_time_dt']
    is_end_time_unknown = str(event_info['end_time']).lower() == 'unknown'
    
    updated_labels = {ts: 0 for ts in original_labels.keys()}
    
    # 按分钟统计恶意路由
    malicious_counts = defaultdict(int)
    for route in analyzed_routes:
        if route['is_malicious']:
            minute_ts = (route['timestamp'] // 60) * 60
            malicious_counts[minute_ts] += 1
    
    # 更新标签
    for ts in original_labels.keys():
        ts_dt = datetime.datetime.fromtimestamp(ts, tz=datetime.timezone.utc)
        has_malicious_route = malicious_counts.get(ts, 0) > 0
        
        # 修正后的逻辑：只根据是否存在恶意路由来标记。
        # 时间窗口的过滤已由前置脚本完成。
        if has_malicious_route:
            updated_labels[ts] = 1
    
    print(f"更新后标签中异常时间点数量: {sum(1 for label in updated_labels.values() if label == 1)}")
    return updated_labels

def is_triplet_a_leak(as_left, leaker, as_right, as_relationships):
    """
    根据AS间的商业关系，判断一个观测到的三元组是否构成路由泄漏。
    三元组格式: (as_left, leaker, as_right)，表示路由传播方向 as_right -> leaker -> as_left
    返回: (is_leak, reason, unknown_pairs_list)
    """
    rel_upstream = as_relationships.get((leaker, as_right))
    rel_downstream = as_relationships.get((leaker, as_left))

    is_leak = False
    reason = None
    
    # 只有当两个关系都明确时，才能进行泄漏判断
    if rel_upstream is not None and rel_downstream is not None:
        # 场景1：从Provider(c2p, 1)或Peer(p2p, 0)学到的路由，不应该宣告给另一个Provider或Peer
        if rel_upstream == 1:  # 从Provider学到
            if rel_downstream == 1 or rel_downstream == 0:
                is_leak, reason = True, "Leaked provider-learned route to another provider/peer"
        elif rel_upstream == 0:  # 从Peer学到
            if rel_downstream == 1 or rel_downstream == 0:
                is_leak, reason = True, "Leaked peer-learned route to a provider/peer"
        
        # 场景2：从Customer(p2c, -1)学到的路由，不应该宣告给另一个Customer
        elif rel_upstream == -1 and rel_downstream == -1:
            is_leak, reason = True, "Leaked customer-learned route to another customer"

    # 无论是否能判断泄漏，都独立记录未知的关系对
    unknown_pairs = []
    if rel_upstream is None:
        unknown_pairs.append((leaker, as_right))
    if rel_downstream is None:
        unknown_pairs.append((leaker, as_left))
            
    return is_leak, reason, unknown_pairs

def analyze_routes_for_leaks(raw_abnormal_routes_file, leaker_as_list, as_relationships):
    """
    动态分析BGP路由，通过检查路径中每个围绕leaker的三元组来识别泄漏。
    """
    print(f"开始动态分析BGP路由以寻找泄漏...")
    analyzed_routes = []
    unknown_relationships_set = set()
    
    leaker_as_set = set()
    for item in leaker_as_list:
        # 使用正则表达式从 'AS1234' 或 '1234' 这样的字符串中提取数字
        numbers = re.findall(r'\d+', str(item))
        for num_str in numbers:
            leaker_as_set.add(int(num_str))

    if not leaker_as_set:
        print(f"错误：未能从leaker_as列表 {leaker_as_list} 中提取有效的AS号。")
        return None, None

    try:
        with open(raw_abnormal_routes_file, 'r', encoding='utf-8') as f:
            for line in tqdm(f, desc="分析BGP路由"):
                try:
                    parts = line.strip().split('|')
                    if len(parts) < 7 or not parts[0].startswith('BGP4MP'):
                        continue
                    
                    timestamp_unix = int(parts[1])
                    as_path_str = parts[6]
                    as_path = parse_as_path(as_path_str)
                    
                    is_malicious = False
                    found_triplets = []
                    private_as_reasons = []

                    # 检查1：路径中是否包含私有AS
                    contains_private, private_asns = has_private_as(as_path)
                    if contains_private:
                        is_malicious = True
                        private_as_reasons.append(f"Private AS detected: {private_asns}")
                    
                    # 无论是否包含私有AS，都继续进行三元组泄漏检查
                    cleaned_path = []
                    if as_path:
                        cleaned_path.append(as_path[0])
                        for i in range(1, len(as_path)):
                            if as_path[i] != as_path[i-1]:
                                cleaned_path.append(as_path[i])

                    # 在清理后的路径中寻找leaker
                    for i, asn in enumerate(cleaned_path):
                        if asn in leaker_as_set:
                            # 确保leaker不是路径的起点或终点，这样它才有左右邻居
                            if i > 0 and i < len(cleaned_path) - 1:
                                # BGP路径是从左到右接收的，但传播是反向的
                                # A B C D -> D传给C，C传给B，B传给A
                                # 所以在我们的路径列表中，`as_right`是上游，`as_left`是下游
                                # 三元组(as_left, leaker, as_right)代表 `as_right -> leaker -> as_left`
                                as_left = cleaned_path[i-1]
                                leaker = cleaned_path[i]
                                as_right = cleaned_path[i+1]
                                
                                triplet = (as_left, leaker, as_right)

                                is_leak, reason, unknown_pairs = is_triplet_a_leak(as_left, leaker, as_right, as_relationships)
                                
                                # 独立检查1：是否是泄漏？
                                if is_leak:
                                    is_malicious = True
                                    found_triplets.append({"triplet": triplet, "reason": reason})
                                
                                # 独立检查2：是否有未知关系？
                                if unknown_pairs:
                                    for pair in unknown_pairs:
                                        unknown_relationships_set.add(pair)

                    route_info = {
                        'raw_route': line.strip(),
                        'timestamp': timestamp_unix,
                        'as_path': as_path,
                        'is_malicious': is_malicious,
                        'found_triplets': found_triplets,
                        'private_as_reasons': private_as_reasons
                    }
                    analyzed_routes.append(route_info)

                except Exception as e:
                    continue
    except Exception as e:
        print(f"读取异常路由文件时出错: {e}")
    
    malicious_route_count = sum(1 for r in analyzed_routes if r['is_malicious'])
    print(f"分析完成。共找到 {malicious_route_count} 条判定为恶意的泄漏路由。")
    print(f"发现 {len(unknown_relationships_set)} 对未知的AS关系。")
    return analyzed_routes, unknown_relationships_set

def process_leak_event(event_name, event_info_df, bgp_data_dir, node_event_dir, output_dir):
    """
    处理单个leak事件（使用三元组推断法）
    """
    print(f"处理事件: {event_name}")
    
    event_row = event_info_df[event_info_df['event_name'] == event_name]
    if event_row.empty:
        print(f"错误：在事件信息中找不到事件 {event_name}")
        return
    event_info = event_row.iloc[0].to_dict()

    # ... [文件路径和AS关系加载逻辑保持不变] ...
    
    # --- 新的AS关系加载与合并逻辑 ---
    as_relationships = {}
    
    rib_dir = os.path.join(node_event_dir, "RIB", event_name)
    if not os.path.isdir(rib_dir):
        print(f"警告：找不到事件 '{event_name}' 对应的RIB目录，跳过此事件。路径: {rib_dir}")
        return
    
    as_rel_patterns = [
        os.path.join(rib_dir, "*.as-rel2.txt"),
        os.path.join(rib_dir, "*.as-rel2.txt.bz2"),
        os.path.join(rib_dir, "*.as-rel.txt"),
        os.path.join(rib_dir, "*.as-rel.txt.bz2")
    ]
    
    all_as_rel_files = []
    for pattern in as_rel_patterns:
        all_as_rel_files.extend(glob.glob(pattern))

    # --- 更智能的文件选择逻辑 ---
    before_file = None
    after_file = None
    
    try:
        event_date_str = event_name.split('-')[1]
        event_date = datetime.datetime.strptime(event_date_str, '%Y%m%d')

        latest_before_date = None
        earliest_after_date = None

        for f in all_as_rel_files:
            try:
                file_date_str = os.path.basename(f).split('.')[0]
                file_date = datetime.datetime.strptime(file_date_str, '%Y%m%d')
                
                if file_date <= event_date:
                    if latest_before_date is None or file_date > latest_before_date:
                        latest_before_date = file_date
                        before_file = f
                else:  # file_date > event_date
                    if earliest_after_date is None or file_date < earliest_after_date:
                        earliest_after_date = file_date
                        after_file = f
            except (IndexError, ValueError):
                continue # 忽略不符合日期格式的文件名
    except (IndexError, ValueError):
        print(f"警告：无法从事件名称 {event_name} 中解析日期。将尝试使用默认文件加载逻辑。")
        if all_as_rel_files:
             # 回退到简单逻辑：按名称排序并选择最新的一个
            all_as_rel_files.sort(reverse=True)
            before_file = all_as_rel_files[0]

    if after_file:
        print(f"加载 '后一个月' 的关系 (作为补充): {os.path.basename(after_file)}")
        as_relationships.update(load_as_relationships(after_file))
        
    if before_file:
        print(f"加载 '事件前' 的关系 (作为主要): {os.path.basename(before_file)}")
        as_relationships.update(load_as_relationships(before_file))
    
    if not as_relationships:
        print(f"错误：未能从找到的文件中加载任何AS关系数据。")
        return
    
    print(f"合并后的关系总数: {len(as_relationships)}")
    # --- 结束新逻辑 ---

    # --- 实现新的三元组推断流程 ---
    leaker_as_list = event_info.get('leak_as', [])
    if not leaker_as_list:
        print(f"事件 {event_name} 未定义 leaker_as，跳过。")
        return

    # 在BGP历史数据中动态验证
    raw_abnormal_routes_file = os.path.join(bgp_data_dir, event_name, "raw_abnormal_routes.txt")
    minute_labels_file = os.path.join(bgp_data_dir, event_name, "minute_labels.csv")

    analyzed_routes, unknown_rels = analyze_routes_for_leaks(raw_abnormal_routes_file, leaker_as_list, as_relationships)
    
    # 增加失败安全检查
    if analyzed_routes is None:
        print(f"由于无法确定Leaker AS或分析失败，中止对事件 {event_name} 的处理。")
        return

    # 更新分钟标签
    updated_labels = update_minute_labels(minute_labels_file, analyzed_routes, event_info)
    
    # 保存处理结果
    save_triplet_results(event_name, analyzed_routes, updated_labels, unknown_rels, output_dir)
    
    # ... [保存原始文件副本的逻辑] ...
    original_files_dir = os.path.join(output_dir, "original_files")
    os.makedirs(original_files_dir, exist_ok=True)
    
    bgp_event_dir = os.path.join(bgp_data_dir, event_name)
    shutil.copy2(os.path.join(bgp_event_dir, "raw_abnormal_routes.txt"), os.path.join(original_files_dir, "raw_abnormal_routes.txt"))
    shutil.copy2(os.path.join(bgp_event_dir, "minute_labels.csv"), os.path.join(original_files_dir, "minute_labels.csv"))
    
    print(f"事件 {event_name} 处理完成")

def delete_existing_results(output_dir):
    """
    删除已存在的处理结果文件
    
    参数:
    - output_dir: 输出目录路径
    """
    if os.path.exists(output_dir):
        print(f"删除已存在的处理结果: {output_dir}")
        shutil.rmtree(output_dir)
    os.makedirs(output_dir, exist_ok=True)
    print(f"创建新的输出目录: {output_dir}")

def save_triplet_results(event_name, analyzed_routes, updated_labels, unknown_relationships, output_dir):
    """
    保存基于三元组推断法的处理结果。
    """
    print(f"保存处理结果到: {output_dir}")
    os.makedirs(output_dir, exist_ok=True)

    # 保存更新后的分钟标签
    updated_labels_file = os.path.join(output_dir, f"{event_name}_updated_minute_labels.csv")
    pd.DataFrame([
        {"timestamp": ts, "label": label}
        for ts, label in sorted(updated_labels.items())
    ]).to_csv(updated_labels_file, index=False)

    # 保存包含恶意三元组的路由详情
    malicious_routes_file = os.path.join(output_dir, f"{event_name}_malicious_routes.txt")
    with open(malicious_routes_file, 'w', encoding='utf-8') as f:
        f.write("根据BGP数据动态发现的恶意泄漏路由详情:\n\n")
        for route in analyzed_routes:
            if route['is_malicious']:
                f.write(f"路由: {route['raw_route']}\n")
                f.write(f"AS路径: {route['as_path']}\n")
                if route['found_triplets']:
                    f.write(f"发现的异常三元组及原因: {route['found_triplets']}\n")
                if route['private_as_reasons']:
                    f.write(f"发现的私有AS泄漏: {route['private_as_reasons']}\n")
                f.write("---\n")
    
    # 新增：保存只包含原始恶意路由的文件
    raw_malicious_routes_file = os.path.join(output_dir, f"{event_name}_raw_violations.txt")
    with open(raw_malicious_routes_file, 'w', encoding='utf-8') as f:
        for route in analyzed_routes:
            if route['is_malicious']:
                f.write(f"{route['raw_route']}\n")
    
    # 提取并保存所有唯一的违规三元组
    violating_triplets = set()
    for route in analyzed_routes:
        if route['is_malicious']:
            for triplet_info in route['found_triplets']:
                # 使用元组来确保可哈希，以便存入集合
                triplet_tuple = triplet_info['triplet']
                reason = triplet_info['reason']
                violating_triplets.add((triplet_tuple, reason))

    violating_triplets_file = os.path.join(output_dir, f"{event_name}_violating_triplets.txt")
    with open(violating_triplets_file, 'w', encoding='utf-8') as f:
        f.write("本次事件中发现的所有唯一违规三元组及其原因:\n")
        f.write("格式: (下游AS, 泄漏AS, 上游AS) - 原因\n\n")
        # 为了输出稳定，进行排序
        for triplet, reason in sorted(list(violating_triplets)):
            f.write(f"{triplet} - {reason}\n")
    
    # 保存未知AS关系
    if unknown_relationships:
        unknown_rels_file = os.path.join(output_dir, f"{event_name}_unknown_relationships.txt")
        with open(unknown_rels_file, 'w', encoding='utf-8') as f:
            f.write("在分析过程中遇到的未知AS关系对:\n")
            f.write("格式: (AS1, AS2)\n\n")
            for pair in sorted(list(unknown_relationships)):
                f.write(f"{pair}\n")

    print("处理结果已保存。")

def main():
    # 设置路径
    # Hardcode paths to ensure correct execution for the specific event
    event_name = 'leak-20181112-Nigeria_MainOne_Cable_leak'
    event_info_file = '/data/data/anomaly-event-info.csv'
    bgp_data_dir = '/data/data/anomaly-event-routedata'
    node_event_dir = '/data/data/node_event'
    as_rel_dir = os.path.join(node_event_dir, 'RIB')
    output_dir = os.path.join(bgp_data_dir, event_name) # Force output to the event's own directory

    print(f"--- 强制模式启动 ---")
    print(f"目标事件: {event_name}")
    print(f"输出目录: {output_dir}")
    print(f"----------------------")
    
    # 解析事件信息
    event_info_df = parse_event_info(event_info_file)
    
    # 查找特定事件
    event_details = event_info_df[event_info_df['event_name'] == event_name]
    if event_details.empty:
        print(f"错误: 在 {event_info_file} 中未找到事件 '{event_name}' 的信息。")
        return

    # 直接调用处理函数，绕过所有循环和命令行解析
    process_leak_event(event_name, event_info_df, bgp_data_dir, node_event_dir, output_dir)
    print(f"\n强制模式处理完成。")

if __name__ == '__main__':
    main() 


    #本文件用于对leak事件的进一步处理