---
description: 
globs: 
alwaysApply: false
---
# Clean

Fix all code formatting and quality issues in the entire codebase.

## Python Projects:
Fix all `black`, `isort`, `flake8`, and `mypy` issues

### Steps:
1. **Format with Black**: `black .`
2. **Sort imports with isort**: `isort .`
3. **Fix flake8 issues**: `flake8 . --extend-ignore=E203`
4. **Resolve mypy type errors**: `mypy .`

## JavaScript/TypeScript Projects:
Fix all ESLint, Prettier, and TypeScript issues

### Steps:
1. **Format with Prettier**: `npx prettier --write .`
2. **Fix ESLint issues**: `npx eslint . --fix`
3. **Check TypeScript**: `npx tsc --noEmit`

## General Process:
1. Run automated formatters first
2. Fix remaining linting issues manually
3. Resolve type checking errors
4. Verify all tools pass with no errors
5. Review changes before committing

## Common Issues:
- Import order conflicts between tools
- Line length violations
- Unused imports/variables
- Type annotation requirements
- Missing return types
- Inconsistent quotes/semicolons