# Create Docs

Create comprehensive documentation for specified components or features.

## Analysis Areas:
1. Code structure and purpose
2. Inputs, outputs, and behavior
3. User interaction flows
4. Edge cases and error handling
5. Integration points with other components/systems

## Documentation Template:

### Overview
Brief 1-2 paragraph overview explaining purpose and value

### Usage
How to use this component/feature with examples

### API / Props / Parameters
Detailed specification of interfaces

### Component Hierarchy
Structure and relationships (if applicable)

### State Management
How state is handled and flows through the system

### Behavior
Expected behavior in different scenarios

### Error Handling
How errors are caught, handled, and reported

### Performance Considerations
Optimization notes and performance characteristics

### Accessibility
Accessibility features and compliance

### Testing
How to test this component/feature

### Related Components/Features
Links to related documentation

## Process:
1. Analyze the target code thoroughly
2. Identify all public interfaces
3. Document expected behaviors
4. Include code examples
5. Add diagrams where helpful
6. Follow project documentation standards
7. Ensure clarity, completeness, and actionability

## Output Formats:
- Markdown for general documentation
- JSDoc/TSDoc for code comments
- API documentation format
- README files
- Architecture decision records (ADRs)