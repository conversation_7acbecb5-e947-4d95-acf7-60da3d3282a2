---
description: 
globs: 
alwaysApply: false
---
# Analyze Issue

Fetch GitHub issue details and create a comprehensive implementation specification.

## Template Sections:

### 1. Issue Summary
Brief overview of the issue

### 2. Problem Statement  
Clear definition of what needs to be solved

### 3. Technical Approach
High-level solution approach and architecture decisions

### 4. Implementation Plan
Step-by-step breakdown of implementation tasks

### 5. Test Plan
Testing strategy and test cases to write

### 6. Files to Modify
List of existing files that need changes

### 7. Files to Create
New files that need to be created

### 8. Existing Utilities to Leverage
Project utilities/helpers that can be reused

### 9. Success Criteria
Measurable criteria for completion

### 10. Out of Scope
What won't be addressed in this implementation

## Process:
1. Fetch issue details using `gh issue view <issue_number>`
2. Review related code and project structure
3. Analyze requirements thoroughly
4. Create detailed technical specification
5. Follow strict TDD principles, KISS approach
6. Enforce 300-line file limit where applicable
7. Output the full technical specification for review