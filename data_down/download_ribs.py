import os
import re
import requests
import subprocess
from datetime import datetime, timedelta
from tqdm import tqdm

base_path = "/data/data/node_event/RIB/" # Define base_path globally

def download_and_parse_rib_for_event(event_folder_name, base_path=base_path, bgpdump_path="/data/bgpdump/bgpdump"):
    print(f"Processing event: {event_folder_name}")
    event_folder_full_path = os.path.join(base_path, event_folder_name)

    # 1. Read event_start_time from start_time.txt
    start_time_file = os.path.join(event_folder_full_path, "start_time.txt")
    if not os.path.exists(start_time_file):
        print(f"Error: {start_time_file} not found. Skipping {event_folder_name}.")
        return

    with open(start_time_file, "r") as f:
        event_start_time_str = f.read().strip()

    try:
        # Adjusted format to match 'YYYY-MM-DD HH:MM:SS'
        event_start_datetime = datetime.strptime(event_start_time_str, '%Y-%m-%d %H:%M:%S')
    except ValueError as e:
        print(f"Error parsing timestamp {event_start_time_str} from {start_time_file}: {e}. Skipping {event_folder_name}.")
        return

    print(f"Event start time for {event_folder_name}: {event_start_datetime}")

    found_rib_url = None
    selected_rib_filename = None

    # Helper to find RIBs for a given date
    def find_ribs_for_date(target_date_dt, filter_before_time=None):
        target_date_str = target_date_dt.strftime('%Y%m%d')
        target_year_month = target_date_dt.strftime('%Y.%m')
        routeviews_url = f"http://archive.routeviews.org/bgpdata/{target_year_month}/RIBS/"
        print(f"Checking RIBs at: {routeviews_url} for date {target_date_str}")

        try:
            response = requests.get(routeviews_url, timeout=10)
            response.raise_for_status()
            html_content = response.text

            rib_candidates = []
            # Corrected regex for matching RIB filenames
            pattern = re.compile(rf'(bview|rib)\.{target_date_str}\.(\d{{4}})\.(gz|bz2)')
            for line in html_content.splitlines():
                match = pattern.search(line)
                if match:
                    rib_filename = match.group(0)
                    rib_time_str = match.group(2)
                    rib_datetime = datetime.strptime(f"{target_date_str}{rib_time_str}", '%Y%m%d%H%M')
                    
                    if filter_before_time and rib_datetime >= filter_before_time:
                        continue # Skip RIBs that are not strictly before the filter time (or at, if not desired)
                    
                    rib_candidates.append((rib_datetime, rib_filename))
            
            return sorted(rib_candidates, key=lambda x: x[0], reverse=True) # Sort latest first

        except requests.exceptions.RequestException as e:
            print(f"Warning: Could not access Route Views for {target_year_month}/{target_date_str}: {e}")
            return []

    # Strategy:
    # 1. Look for RIBs on the event_start_datetime's day, that are *before* event_start_datetime
    ribs_on_event_day = find_ribs_for_date(event_start_datetime, filter_before_time=event_start_datetime)
    if ribs_on_event_day:
        selected_rib_datetime, selected_rib_filename = ribs_on_event_day[0] # Latest RIB before event on same day
        found_rib_url = f"http://archive.routeviews.org/bgpdata/{selected_rib_datetime.strftime('%Y.%m')}/RIBS/{selected_rib_filename}"
        print(f"Found latest RIB on event day before event time: {selected_rib_filename}")
    else:
        # 2. If not found, look for RIBs on the day *before* event_start_datetime
        previous_day = event_start_datetime - timedelta(days=1)
        ribs_on_previous_day = find_ribs_for_date(previous_day) # No time filter, take latest of previous day
        if ribs_on_previous_day:
            selected_rib_datetime, selected_rib_filename = ribs_on_previous_day[0] # Latest RIB of previous day
            found_rib_url = f"http://archive.routeviews.org/bgpdata/{selected_rib_datetime.strftime('%Y.%m')}/RIBS/{selected_rib_filename}"
            print(f"No RIB on event day before event time. Found latest RIB on previous day: {selected_rib_filename}")

    if not found_rib_url:
        print(f"No suitable RIB found for {event_folder_name}. Creating nan.txt.")
        with open(os.path.join(event_folder_full_path, "nan.txt"), "w") as f:
            f.write("No suitable RIB found for this event time.")
        return

    # Download the RIB file
    downloaded_rib_path = os.path.join(event_folder_full_path, selected_rib_filename)
    print(f"Attempting to download {selected_rib_filename} from {found_rib_url}")
    try:
        rib_response = requests.get(found_rib_url, stream=True, timeout=600) # Increased timeout
        rib_response.raise_for_status()

        total_size = int(rib_response.headers.get('content-length', 0))
        with open(downloaded_rib_path, 'wb') as f, tqdm(
            total=total_size, unit='iB', unit_scale=True, unit_divisor=1024,
            desc=f"Downloading {selected_rib_filename}", miniters=1, dynamic_ncols=True
        ) as bar:
            for chunk in rib_response.iter_content(chunk_size=8192):
                size = f.write(chunk)
                bar.update(size)
        print(f"Successfully downloaded: {downloaded_rib_path}")

    except requests.exceptions.RequestException as e:
        print(f"Error downloading RIB for {event_folder_name}: {e}. Creating nan.txt.")
        with open(os.path.join(event_folder_full_path, "nan.txt"), "w") as f:
            f.write(f"Error downloading RIB: {e}")
        return
    except Exception as e:
        print(f"An unexpected error occurred during download for {event_folder_name}: {e}. Creating nan.txt.")
        with open(os.path.join(event_folder_full_path, "nan.txt"), "w") as f:
            f.write(f"Unexpected error during download: {e}")
        return

    # Parse the downloaded RIB using bgpdump
    parsed_rib_output_path = os.path.join(event_folder_full_path, f"{os.path.splitext(selected_rib_filename)[0]}_parsed.txt")
    print(f"Attempting to parse {downloaded_rib_path} with bgpdump to {parsed_rib_output_path}")

    try:
        subprocess.run(
            [bgpdump_path, '-M', downloaded_rib_path],
            stdout=open(parsed_rib_output_path, 'w'),
            stderr=subprocess.PIPE,
            check=True,
            text=True,
            encoding='utf-8'
        )
        print(f"Successfully parsed RIB to {parsed_rib_output_path}")
    except subprocess.CalledProcessError as e:
        print(f"Error parsing RIB with bgpdump for {event_folder_name}: {e}. Stderr: {e.stderr}. Creating nan.txt.")
        with open(os.path.join(event_folder_full_path, "nan.txt"), "a") as f: # Append error to nan.txt
            f.write(f"Error parsing RIB with bgpdump: {e.stderr}")
        os.remove(downloaded_rib_path) # Clean up downloaded file if parsing fails
    except FileNotFoundError:
        print(f"bgpdump command not found at {bgpdump_path}. Creating nan.txt.")
        with open(os.path.join(event_folder_full_path, "nan.txt"), "a") as f: # Append error to nan.txt
            f.write("bgpdump command not found.")
        os.remove(downloaded_rib_path) # Clean up downloaded file if bgpdump is missing
    except Exception as e:
        print(f"An unexpected error occurred during parsing for {event_folder_name}: {e}. Creating nan.txt.")
        with open(os.path.join(event_folder_full_path, "nan.txt"), "a") as f: # Append error to nan.txt
            f.write(f"Unexpected error during parsing: {e}")
        os.remove(downloaded_rib_path) # Clean up downloaded file if parsing fails

# Get list of event folders dynamically
event_folders = [d for d in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, d))]

# Clean up existing nan.txt and downloaded RIBs/parsed files before running
for folder in event_folders:
    folder_full_path = os.path.join(base_path, folder)
    # Remove all .rib.gz, .rib.bz2, _parsed.txt, and nan.txt files
    for file_name in os.listdir(folder_full_path):
        if file_name.endswith(('.rib.gz', '.rib.bz2', '_parsed.txt', 'nan.txt')):
            try:
                os.remove(os.path.join(folder_full_path, file_name))
                print(f"Cleaned up {file_name} in {folder_full_path}")
            except OSError as e:
                print(f"Error removing {file_name} in {folder_full_path}: {e}")

# Process all event folders
for folder in event_folders:
    download_and_parse_rib_for_event(folder)

print("Finished processing all event folders.") 

#根据每个文件夹里面文件的开始时间，去RIB表网站下载异常发生之前的RIB表数据，并通过bgpdump进行解压