import csv
import os
from datetime import datetime

csv_file_path = '/data/data/anomaly-event-info.csv'
base_output_dir = '/data/data/node_event/RIB/'

if not os.path.exists(base_output_dir):
    os.makedirs(base_output_dir)

with open(csv_file_path, 'r') as csvfile:
    reader = csv.DictReader(csvfile)
    for row in reader:
        event_name = row['event_name']
        start_time_str = row['start_time']

        # Ensure start_time_str is in 'YYYY/MM/DD HH:MM' format for consistency
        # It's currently 'YYYY/MM/DD HH:MM', so it should be fine.
        # Convert to datetime object to ensure correct parsing if format varies slightly
        try:
            # Try parsing with the format that includes seconds
            start_time = datetime.strptime(start_time_str, '%Y/%m/%d %H:%M:%S')
        except ValueError:
            # If seconds are not present, try parsing without seconds
            start_time = datetime.strptime(start_time_str, '%Y/%m/%d %H:%M')

        event_folder_path = os.path.join(base_output_dir, event_name)
        os.makedirs(event_folder_path, exist_ok=True)

        start_time_file_path = os.path.join(event_folder_path, 'start_time.txt')
        with open(start_time_file_path, 'w') as f:
            f.write(start_time.strftime('%Y-%m-%d %H:%M:%S')) # Standard format for consistency
        print(f"Created folder: {event_folder_path} and start_time.txt with {start_time_str}")

print("All event folders and start_time.txt files created.") 

#根据38个异常事件表的时间，为38个文件夹添加各个异常事件的开始时间