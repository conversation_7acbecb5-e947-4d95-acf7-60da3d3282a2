import os
import requests
from datetime import datetime
from tqdm import tqdm
from dateutil.relativedelta import relativedelta

base_event_dir = "data/node_event/RIB/"
caida_serial1_base_url = "https://publicdata.caida.org/datasets/as-relationships/serial-1/"
caida_serial2_base_url = "https://publicdata.caida.org/datasets/as-relationships/serial-2/"

def download_file_if_not_exists(event_folder_full_path, download_date_for_filename, event_start_datetime):
    """
    检查文件是否存在，如果不存在则从CAIDA下载。
    """
    download_date_str = download_date_for_filename.strftime('%Y%m%d')
    cutoff_date = datetime(2015, 12, 1, 0, 0, 0)

    if event_start_datetime >= cutoff_date:
        base_url = caida_serial2_base_url
        file_suffix = ".as-rel2.txt"
        download_filename = f"{download_date_str}{file_suffix}.bz2"
    else:
        base_url = caida_serial1_base_url
        file_suffix = ".as-rel.txt"
        download_filename = f"{download_date_str}{file_suffix}.bz2"
    
    full_download_url = f"{base_url}{download_filename}"
    local_save_path = os.path.join(event_folder_full_path, download_filename)

    # 检查文件是否已存在
    if os.path.exists(local_save_path):
        print(f"文件已存在，跳过下载: {local_save_path}")
        return

    print(f"尝试下载: {full_download_url}")
    try:
        response = requests.get(full_download_url, stream=True, timeout=300)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        with open(local_save_path, 'wb') as f, tqdm(
            total=total_size, unit='iB', unit_scale=True, unit_divisor=1024,
            desc=f"下载 {download_filename}", miniters=1, dynamic_ncols=True
        ) as bar:
            for chunk in response.iter_content(chunk_size=8192):
                size = f.write(chunk)
                bar.update(size)
        print(f"成功下载到: {local_save_path}")
    except requests.exceptions.RequestException as e:
        print(f"下载文件时出错: {e}")
    except Exception as e:
        print(f"下载过程中发生未知错误: {e}")

def download_as_relationship_for_event(event_folder_name):
    print(f"处理事件的AS关系: {event_folder_name}")
    event_folder_full_path = os.path.join(base_event_dir, event_folder_name)

    start_time_file = os.path.join(event_folder_full_path, "start_time.txt")
    if not os.path.exists(start_time_file):
        print(f"错误: {start_time_file} 未找到。跳过 {event_folder_name}。")
        return

    with open(start_time_file, "r") as f:
        event_start_time_str = f.read().strip()

    try:
        event_start_datetime = datetime.strptime(event_start_time_str, '%Y-%m-%d %H:%M:%S')
    except ValueError as e:
        print(f"解析时间戳 {event_start_time_str} 出错: {e}。跳过 {event_folder_name}。")
        return

    # 1. 确定并下载"当月"的关系文件
    same_month_date = event_start_datetime.replace(day=1)
    print(f"\n检查当月文件 ({same_month_date.strftime('%Y-%m')})...")
    download_file_if_not_exists(event_folder_full_path, same_month_date, event_start_datetime)

    # 2. 确定并下载"次月"的关系文件
    next_month_date = (event_start_datetime + relativedelta(months=1)).replace(day=1)
    print(f"检查次月文件 ({next_month_date.strftime('%Y-%m')})...")
    download_file_if_not_exists(event_folder_full_path, next_month_date, event_start_datetime)


# Get list of all event folders dynamically
event_folders = [d for d in os.listdir(base_event_dir) if os.path.isdir(os.path.join(base_event_dir, d))]

# # 为了调试，只处理一个事件
# event_folders = ["leak-20210211-Cablevision_Mexico_leak"]
# print(f"为调试目的，仅下载事件: {event_folders[0]}")


# Process all event folders with a progress bar
for folder in tqdm(event_folders, desc="Processing events"):
    download_as_relationship_for_event(folder)

print("下载检查流程完成。")

#下载as-rel文件，2015年12月之前的，我们就下载as-rel 之后的下载as-rel2文件