import os
import subprocess
from tqdm import tqdm

base_event_dir = "data/node_event/RIB/"

# # 为了调试，只处理一个事件
# event_folders = ["leak-20210211-Cablevision_Mexico_leak"]
# print(f"为调试目的，仅解压事件: {event_folders[0]}")

event_folders = [d for d in os.listdir(base_event_dir) if os.path.isdir(os.path.join(base_event_dir, d))]

print("Decompressing AS relationship files in each event folder...")

for folder in tqdm(event_folders, desc="Decompressing AS-rel files"):
    folder_path = os.path.join(base_event_dir, folder)
    
    # 找到并处理目录中所有的 .bz2 AS关系文件
    found_any = False
    for f in os.listdir(folder_path):
        if f.endswith(".as-rel.txt.bz2") or f.endswith(".as-rel2.txt.bz2"):
            found_any = True
            as_rel_bz2_file = os.path.join(folder_path, f)

            # 检查解压后的文件是否已存在
        decompressed_file = as_rel_bz2_file.replace(".bz2", "")
        if os.path.exists(decompressed_file):
                print(f"文件已解压，跳过: {os.path.basename(decompressed_file)}")
                continue # 继续检查下一个文件

            print(f"正在解压: {os.path.basename(as_rel_bz2_file)}")
        try:
                # bzip2 -d -k 解压并保留原文件
            subprocess.run(["bzip2", "-d", "-k", as_rel_bz2_file], check=True)
                print(f"成功解压: {os.path.basename(decompressed_file)}")
        except subprocess.CalledProcessError as e:
                print(f"解压 {os.path.basename(as_rel_bz2_file)} 时出错: {e}")
        except FileNotFoundError:
                print(f"错误: bzip2 命令未找到。请确保 bzip2 已安装并在您的 PATH 中。")

    if not found_any:
        print(f"在 {folder_path} 中未找到 .bz2 压缩的AS关系文件。")

print("解压流程完成。") 

#对每个得到的文件都进行解压